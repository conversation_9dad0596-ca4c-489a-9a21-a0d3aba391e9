package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for hourly activity count statistics
 * Corresponds to HourlyActivityCount type in audit-schema.graphqls
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HourlyActivityCount {
    
    private Integer hour;
    private Long count;
    private LocalDateTime date;
}
