package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionMethod;
import lombok.Builder;
import lombok.Data;

/**
 * DTO for conversion method statistics from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionMethodStats {
    private ConversionMethod method;
    private Long count;
    private Long successCount;
    private Long failureCount;
    private Float averageProcessingTime;
    private Float successRate;
}
