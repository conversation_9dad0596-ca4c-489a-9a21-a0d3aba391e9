package com.ascentbusiness.dms_svc.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;

/**
 * Entity representing workflow notifications
 */
@Entity
@Table(name = "workflow_notifications")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowNotification extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_instance_id", nullable = false)
    @JsonIgnore
    private WorkflowInstance workflowInstance;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_task_id")
    @JsonIgnore
    private WorkflowTask workflowTask;

    @Column(name = "notification_type", nullable = false, length = 50)
    private String notificationType; // TASK_ASSIGNED, TASK_DUE, ESCALATION, COMPLETION, REJECTION

    @Column(name = "recipient_user_id", nullable = false, length = 255)
    private String recipientUserId;

    @Column(name = "recipient_email", length = 255)
    private String recipientEmail;

    @Column(name = "subject", nullable = false, length = 500)
    private String subject;

    @Column(name = "message", columnDefinition = "TEXT")
    private String message;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "notification_data", columnDefinition = "JSON")
    private JsonNode notificationData;

    @Column(name = "status", nullable = false, length = 50)
    @Builder.Default
    private String status = "PENDING"; // PENDING, SENT, DELIVERED, FAILED, CANCELLED

    @Column(name = "sent_date")
    private LocalDateTime sentDate;

    @Column(name = "delivered_date")
    private LocalDateTime deliveredDate;

    @Column(name = "failure_reason", length = 255)
    private String failureReason;

    @Column(name = "retry_count")
    @Builder.Default
    private Integer retryCount = 0;

    @Column(name = "max_retries")
    @Builder.Default
    private Integer maxRetries = 3;

    @Column(name = "scheduled_date")
    private LocalDateTime scheduledDate;

    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    @Column(name = "priority", length = 20)
    @Builder.Default
    private String priority = "NORMAL"; // LOW, NORMAL, HIGH, URGENT

    @Column(name = "is_read", nullable = false)
    @Builder.Default
    private Boolean isRead = false;

    @Column(name = "read_date")
    private LocalDateTime readDate;

    @Column(name = "failure_count", nullable = false)
    @Builder.Default
    private Integer failureCount = 0;

    @Column(name = "last_failure_date")
    private LocalDateTime lastFailureDate;

    /**
     * Check if this notification is pending
     */
    @Transient
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    /**
     * Check if this notification has been sent
     */
    @Transient
    public boolean isSent() {
        return "SENT".equals(status) || "DELIVERED".equals(status);
    }

    /**
     * Check if this notification has failed
     */
    @Transient
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * Check if this notification is ready to be sent
     */
    @Transient
    public boolean isReadyToSend() {
        return isPending() && (scheduledDate == null || LocalDateTime.now().isAfter(scheduledDate));
    }

    /**
     * Check if this notification can be retried
     */
    @Transient
    public boolean canRetry() {
        return isFailed() && retryCount < maxRetries;
    }

    /**
     * Check if this notification is overdue for sending
     */
    @Transient
    public boolean isOverdue() {
        return isPending() && scheduledDate != null && LocalDateTime.now().isAfter(scheduledDate.plusHours(1));
    }

    /**
     * Get the workflow instance name
     */
    @Transient
    public String getWorkflowInstanceName() {
        return workflowInstance != null ? workflowInstance.getInstanceName() : null;
    }

    /**
     * Get the task name if this is a task-related notification
     */
    @Transient
    public String getTaskName() {
        return workflowTask != null ? workflowTask.getTaskName() : null;
    }

    /**
     * Get the document name associated with this notification
     */
    @Transient
    public String getDocumentName() {
        return workflowInstance != null && workflowInstance.getDocument() != null ? 
               workflowInstance.getDocument().getName() : null;
    }

    /**
     * Increment retry count
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount != null ? this.retryCount : 0) + 1;
    }

    /**
     * Mark as sent
     */
    public void markAsSent() {
        this.status = "SENT";
        this.sentDate = LocalDateTime.now();
    }

    /**
     * Mark as delivered
     */
    public void markAsDelivered() {
        this.status = "DELIVERED";
        this.deliveredDate = LocalDateTime.now();
    }

    /**
     * Mark as failed
     */
    public void markAsFailed(String reason) {
        this.status = "FAILED";
        this.failureReason = reason;
        incrementRetryCount();
    }

    /**
     * Mark as cancelled
     */
    public void markAsCancelled() {
        this.status = "CANCELLED";
    }

    @Override
    public String toString() {
        return String.format("WorkflowNotification{id=%d, type='%s', recipient='%s', status='%s'}", 
                           getId(), notificationType, recipientUserId, status);
    }
}
