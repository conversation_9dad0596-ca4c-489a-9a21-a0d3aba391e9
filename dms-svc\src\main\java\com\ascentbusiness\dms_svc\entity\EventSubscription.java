package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.SubscriberType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Entity representing an event subscription
 */
@Entity
@Table(name = "event_subscriptions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EventSubscription extends BaseEntity {

    @Column(name = "name", nullable = false, length = 255)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    // Subscription configuration
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_types", nullable = false, columnDefinition = "JSON")
    private JsonNode eventTypes;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_filters", columnDefinition = "JSON")
    private JsonNode eventFilters;

    // Subscriber information
    @Enumerated(EnumType.STRING)
    @Column(name = "subscriber_type", nullable = false, length = 50)
    private SubscriberType subscriberType;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "subscriber_config", nullable = false, columnDefinition = "JSON")
    private JsonNode subscriberConfig;

    // Status
    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Builder.Default
    @Column(name = "is_paused", nullable = false)
    private Boolean isPaused = false;

    // Processing options
    @Column(name = "batch_size")
    @Builder.Default
    private Integer batchSize = 1;

    @Column(name = "batch_timeout_seconds")
    @Builder.Default
    private Integer batchTimeoutSeconds = 300;

    @Column(name = "delivery_mode", nullable = false, length = 50)
    @Builder.Default
    private String deliveryMode = "IMMEDIATE"; // IMMEDIATE, BATCH, SCHEDULED

    // Scheduling (for scheduled delivery mode)
    @Column(name = "schedule_cron", length = 100)
    private String scheduleCron;

    @Column(name = "schedule_timezone", length = 50)
    @Builder.Default
    private String scheduleTimezone = "UTC";

    // Monitoring
    @Column(name = "events_processed")
    @Builder.Default
    private Integer eventsProcessed = 0;

    @Column(name = "last_processed_date")
    private LocalDateTime lastProcessedDate;

    @Column(name = "created_by", nullable = false, length = 255)
    private String createdBy;

    @Column(name = "last_modified_by", length = 255)
    private String lastModifiedBy;

    // Relationships
    @OneToMany(mappedBy = "eventSubscription", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<EventProcessingLog> processingLogs;

    /**
     * Check if subscription is currently active
     */
    @Transient
    public boolean isCurrentlyActive() {
        return isActive != null && isActive && (isPaused == null || !isPaused);
    }

    /**
     * Check if subscription is paused
     */
    @Transient
    public boolean isCurrentlyPaused() {
        return isPaused != null && isPaused;
    }

    /**
     * Check if subscription uses batch processing
     */
    @Transient
    public boolean usesBatchProcessing() {
        return "BATCH".equals(deliveryMode) && batchSize != null && batchSize > 1;
    }

    /**
     * Check if subscription is scheduled
     */
    @Transient
    public boolean isScheduled() {
        return "SCHEDULED".equals(deliveryMode) && scheduleCron != null;
    }

    /**
     * Check if subscription has event filters
     */
    @Transient
    public boolean hasEventFilters() {
        return eventFilters != null && !eventFilters.isNull() && eventFilters.size() > 0;
    }

    /**
     * Check if subscription is for webhooks
     */
    @Transient
    public boolean isWebhookSubscription() {
        return SubscriberType.WEBHOOK.equals(subscriberType);
    }

    /**
     * Check if subscription is for email notifications
     */
    @Transient
    public boolean isEmailSubscription() {
        return SubscriberType.EMAIL.equals(subscriberType);
    }

    /**
     * Check if subscription is for internal processing
     */
    @Transient
    public boolean isInternalSubscription() {
        return SubscriberType.INTERNAL.equals(subscriberType);
    }

    /**
     * Check if subscription has been used recently (within last 7 days)
     */
    @Transient
    public boolean isRecentlyUsed() {
        return lastProcessedDate != null && 
               lastProcessedDate.isAfter(LocalDateTime.now().minusDays(7));
    }

    /**
     * Check if subscription is active and processing events
     */
    @Transient
    public boolean isActivelyProcessing() {
        return isCurrentlyActive() && eventsProcessed != null && eventsProcessed > 0;
    }

    /**
     * Get the number of event types subscribed to
     */
    @Transient
    public int getEventTypeCount() {
        if (eventTypes == null || eventTypes.isNull()) return 0;
        return eventTypes.size();
    }

    /**
     * Pause the subscription
     */
    public void pause() {
        this.isPaused = true;
    }

    /**
     * Resume the subscription
     */
    public void resume() {
        this.isPaused = false;
    }

    /**
     * Deactivate the subscription
     */
    public void deactivate() {
        this.isActive = false;
    }

    /**
     * Activate the subscription
     */
    public void activate() {
        this.isActive = true;
        this.isPaused = false;
    }

    /**
     * Record event processing
     */
    public void recordEventProcessing() {
        this.eventsProcessed = (this.eventsProcessed != null ? this.eventsProcessed : 0) + 1;
        this.lastProcessedDate = LocalDateTime.now();
    }

    /**
     * Get delivery mode display name
     */
    @Transient
    public String getDeliveryModeDisplayName() {
        if (deliveryMode == null) return "Unknown";
        
        switch (deliveryMode.toUpperCase()) {
            case "IMMEDIATE":
                return "Immediate";
            case "BATCH":
                return "Batch (" + batchSize + " events)";
            case "SCHEDULED":
                return "Scheduled (" + scheduleCron + ")";
            default:
                return deliveryMode;
        }
    }

    /**
     * Get subscriber type display name
     */
    @Transient
    public String getSubscriberTypeDisplayName() {
        if (subscriberType == null) return "Unknown";
        
        switch (subscriberType) {
            case WEBHOOK:
                return "Webhook";
            case EMAIL:
                return "Email";
            case SMS:
                return "SMS";
            case INTERNAL:
                return "Internal Handler";
            case QUEUE:
                return "Message Queue";
            default:
                return subscriberType.name();
        }
    }

    @Override
    public String toString() {
        return String.format("EventSubscription{id=%d, name='%s', type=%s, active=%s, paused=%s}", 
                           getId(), name, subscriberType, isActive, isPaused);
    }
}
