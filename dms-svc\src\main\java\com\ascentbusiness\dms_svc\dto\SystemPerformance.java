package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * System performance DTO from diagnostics-schema.graphqls.
 * Updated to match test expectations.
 */
@Data
@Builder
public class SystemPerformance {
    private OffsetDateTime timestamp;

    // Fields expected by tests
    private Float cpuUsage;
    private Float memoryUsage;
    private Float diskUsage;
    private NetworkIO networkIO;
    private JvmMetrics jvmMetrics;
    private DatabaseMetrics databaseMetrics;
    private ApplicationMetrics applicationMetrics;
}
