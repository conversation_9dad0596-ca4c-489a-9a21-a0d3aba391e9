package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ProcessingStrategy;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for bulk processing options input.
 * Corresponds to BulkProcessingOptionsInput GraphQL input type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkProcessingOptionsInput {

    /**
     * Processing strategy to use for the bulk upload.
     */
    private ProcessingStrategy processingStrategy;

    /**
     * Chunk size for chunked uploads.
     */
    private Integer chunkSize;

    /**
     * Maximum number of concurrent uploads.
     */
    private Integer maxConcurrentUploads;

    /**
     * Whether to continue processing if one file fails.
     */
    private Boolean continueOnError;

    /**
     * Whether to send notification when bulk upload completes.
     */
    private Boolean notifyOnCompletion;

    /**
     * Email address for completion notification.
     */
    private String notificationEmail;
}
