package com.ascentbusiness.dms_svc.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * Input DTO for uploading individual chunks in a chunked upload session.
 */
@Data
public class ChunkUploadInput {
    
    /**
     * The session ID for the chunked upload.
     */
    private String sessionId;
    
    /**
     * The chunk number (1-based).
     */
    private Integer chunkNumber;
    
    /**
     * The chunk file data.
     */
    private MultipartFile chunk;
    
    /**
     * Validate the input parameters.
     * 
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            throw new IllegalArgumentException("Session ID is required");
        }
        
        if (chunkNumber == null || chunkNumber < 1) {
            throw new IllegalArgumentException("Chunk number must be positive");
        }
        
        if (chunk == null || chunk.isEmpty()) {
            throw new IllegalArgumentException("Chunk data is required");
        }
    }
    
    /**
     * Get the chunk size in bytes.
     * 
     * @return chunk size
     */
    public long getChunkSize() {
        return chunk != null ? chunk.getSize() : 0;
    }
    
    /**
     * Get the chunk content type.
     * 
     * @return content type
     */
    public String getChunkContentType() {
        return chunk != null ? chunk.getContentType() : null;
    }
}
