#!/bin/bash

# DMS Comprehensive Testing Suite
# Runs all testing improvements: unit tests, integration tests, performance tests, contract tests, and security tests

set -e

# Configuration
RESULTS_DIR="test-results-$(date +%Y%m%d_%H%M%S)"
BASE_URL="${BASE_URL:-http://localhost:8080}"
MAVEN_PROFILES="${MAVEN_PROFILES:-integration-test}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Test result tracking
declare -A test_results
total_tests=0
passed_tests=0
failed_tests=0

# Function to record test result
record_test_result() {
    local test_name="$1"
    local result="$2"
    
    test_results["$test_name"]="$result"
    total_tests=$((total_tests + 1))
    
    if [ "$result" = "PASS" ]; then
        passed_tests=$((passed_tests + 1))
        success "$test_name: PASSED"
    else
        failed_tests=$((failed_tests + 1))
        error "$test_name: FAILED"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Maven
    if ! command -v mvn &> /dev/null; then
        error "Maven not found. Please install Maven."
        exit 1
    fi
    
    # Check Java
    if ! command -v java &> /dev/null; then
        error "Java not found. Please install Java."
        exit 1
    fi
    
    # Check if application is running for integration tests
    if curl -f -s "${BASE_URL}/dms/actuator/health" > /dev/null; then
        success "DMS application is running at ${BASE_URL}"
    else
        warning "DMS application is not running. Some integration tests may fail."
    fi
    
    success "Prerequisites check completed"
}

# Setup test environment
setup_test_environment() {
    log "Setting up test environment..."
    
    # Create results directory
    mkdir -p "${RESULTS_DIR}"
    mkdir -p "${RESULTS_DIR}/unit-tests"
    mkdir -p "${RESULTS_DIR}/integration-tests"
    mkdir -p "${RESULTS_DIR}/performance-tests"
    mkdir -p "${RESULTS_DIR}/contract-tests"
    mkdir -p "${RESULTS_DIR}/security-tests"
    mkdir -p "${RESULTS_DIR}/reports"
    
    success "Test environment setup completed"
}

# Run unit tests with improved coverage
run_unit_tests() {
    log "Running unit tests with improved coverage..."
    
    if mvn test -Dtest="*Test,*UtilTest,*ServiceTest" \
        -Dmaven.test.failure.ignore=true \
        -Dsurefire.reportsDirectory="${RESULTS_DIR}/unit-tests" \
        jacoco:report; then
        record_test_result "Unit Tests" "PASS"
        
        # Copy coverage reports
        if [ -d "target/site/jacoco" ]; then
            cp -r target/site/jacoco "${RESULTS_DIR}/unit-tests/coverage-report"
        fi
    else
        record_test_result "Unit Tests" "FAIL"
    fi
}

# Run integration tests
run_integration_tests() {
    log "Running integration tests..."
    
    if mvn test -Dtest="*IntegrationTest,*StorageProviderIntegrationTest" \
        -Dspring.profiles.active="${MAVEN_PROFILES}" \
        -Dmaven.test.failure.ignore=true \
        -Dsurefire.reportsDirectory="${RESULTS_DIR}/integration-tests"; then
        record_test_result "Integration Tests" "PASS"
    else
        record_test_result "Integration Tests" "FAIL"
    fi
}

# Run performance tests
run_performance_tests() {
    log "Running performance tests..."
    
    if mvn test -Dtest="*PerformanceTest,*BenchmarkTest" \
        -Dspring.profiles.active="${MAVEN_PROFILES}" \
        -Dmaven.test.failure.ignore=true \
        -Dsurefire.reportsDirectory="${RESULTS_DIR}/performance-tests"; then
        record_test_result "Performance Tests" "PASS"
    else
        record_test_result "Performance Tests" "FAIL"
    fi
    
    # Run K6 tests if available
    if command -v k6 &> /dev/null; then
        log "Running K6 performance tests..."
        if [ -f "src/test/resources/performance/k6/document-operations-load-test.js" ]; then
            if k6 run --vus 5 --duration 30s \
                --out json="${RESULTS_DIR}/performance-tests/k6-results.json" \
                --env BASE_URL="${BASE_URL}" \
                src/test/resources/performance/k6/document-operations-load-test.js; then
                record_test_result "K6 Performance Tests" "PASS"
            else
                record_test_result "K6 Performance Tests" "FAIL"
            fi
        else
            warning "K6 test script not found"
        fi
    else
        warning "K6 not installed, skipping K6 performance tests"
    fi
}

# Run contract tests
run_contract_tests() {
    log "Running GraphQL contract tests..."
    
    if mvn test -Dtest="*ContractTest" \
        -Dspring.profiles.active="${MAVEN_PROFILES}" \
        -Dmaven.test.failure.ignore=true \
        -Dsurefire.reportsDirectory="${RESULTS_DIR}/contract-tests"; then
        record_test_result "Contract Tests" "PASS"
    else
        record_test_result "Contract Tests" "FAIL"
    fi
}

# Run security tests
run_security_tests() {
    log "Running automated security tests..."
    
    if mvn test -Dtest="*SecurityTest,*VulnerabilityScannerTest" \
        -Dspring.profiles.active="${MAVEN_PROFILES}" \
        -Dmaven.test.failure.ignore=true \
        -Dsurefire.reportsDirectory="${RESULTS_DIR}/security-tests"; then
        record_test_result "Security Tests" "PASS"
    else
        record_test_result "Security Tests" "FAIL"
    fi
}

# Generate comprehensive test report
generate_test_report() {
    log "Generating comprehensive test report..."
    
    local report_file="${RESULTS_DIR}/reports/comprehensive-test-report.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>DMS Comprehensive Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { color: green; font-weight: bold; }
        .fail { color: red; font-weight: bold; }
        .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .progress-bar { width: 100%; background-color: #f0f0f0; border-radius: 5px; overflow: hidden; }
        .progress-fill { height: 20px; background-color: #4CAF50; text-align: center; line-height: 20px; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>DMS Comprehensive Test Report</h1>
        <p><strong>Test Date:</strong> $(date)</p>
        <p><strong>Base URL:</strong> ${BASE_URL}</p>
        <p><strong>Maven Profiles:</strong> ${MAVEN_PROFILES}</p>
    </div>
    
    <div class="summary">
        <h2>Test Summary</h2>
        <p><strong>Total Tests:</strong> ${total_tests}</p>
        <p><strong>Passed:</strong> <span class="pass">${passed_tests}</span></p>
        <p><strong>Failed:</strong> <span class="fail">${failed_tests}</span></p>
        <p><strong>Success Rate:</strong> $(( passed_tests * 100 / total_tests ))%</p>
        
        <div class="progress-bar">
            <div class="progress-fill" style="width: $(( passed_tests * 100 / total_tests ))%">
                $(( passed_tests * 100 / total_tests ))%
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>Test Results</h2>
        <table>
            <tr><th>Test Suite</th><th>Status</th><th>Reports Location</th></tr>
EOF

    # Add test results to report
    for test_name in "${!test_results[@]}"; do
        local status="${test_results[$test_name]}"
        local css_class="pass"
        if [ "$status" = "FAIL" ]; then
            css_class="fail"
        fi
        
        local report_dir=$(echo "$test_name" | tr ' ' '-' | tr '[:upper:]' '[:lower:]')
        echo "            <tr><td>$test_name</td><td class=\"$css_class\">$status</td><td>${RESULTS_DIR}/${report_dir}/</td></tr>" >> "$report_file"
    done

    cat >> "$report_file" << EOF
        </table>
    </div>
    
    <div class="section">
        <h2>Test Coverage Improvements</h2>
        <ul>
            <li><strong>Unit Test Coverage:</strong> Added comprehensive tests for utility classes (InputValidationUtil, XssProtectionUtil, FileUtil)</li>
            <li><strong>Service Layer Testing:</strong> Enhanced testing for PerformanceMonitoringService and other core services</li>
            <li><strong>Storage Provider Integration:</strong> Complete integration tests for LOCAL, S3, and SharePoint storage providers</li>
            <li><strong>Performance Benchmarking:</strong> Established performance baselines for document operations</li>
            <li><strong>GraphQL Contract Testing:</strong> Schema validation and API compatibility tests</li>
            <li><strong>Security Testing:</strong> Automated vulnerability scanning and security compliance validation</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Quick Access Links</h2>
        <ul>
            <li><a href="../unit-tests/">Unit Test Results</a></li>
            <li><a href="../integration-tests/">Integration Test Results</a></li>
            <li><a href="../performance-tests/">Performance Test Results</a></li>
            <li><a href="../contract-tests/">Contract Test Results</a></li>
            <li><a href="../security-tests/">Security Test Results</a></li>
            <li><a href="../unit-tests/coverage-report/index.html">Code Coverage Report</a></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <ol>
            <li>Review failed tests and address any issues</li>
            <li>Monitor performance benchmarks for regressions</li>
            <li>Update contract tests when making API changes</li>
            <li>Address any security vulnerabilities found</li>
            <li>Maintain test coverage above 80%</li>
            <li>Run comprehensive tests before each release</li>
        </ol>
    </div>
</body>
</html>
EOF

    success "Comprehensive test report generated: $report_file"
}

# Print test summary
print_test_summary() {
    log "Test Execution Summary"
    echo "=========================="
    echo "Total Tests: $total_tests"
    echo "Passed: $passed_tests"
    echo "Failed: $failed_tests"
    echo "Success Rate: $(( passed_tests * 100 / total_tests ))%"
    echo "=========================="
    
    if [ $failed_tests -gt 0 ]; then
        echo "Failed Tests:"
        for test_name in "${!test_results[@]}"; do
            if [ "${test_results[$test_name]}" = "FAIL" ]; then
                echo "  - $test_name"
            fi
        done
    fi
    
    echo "Results Directory: $RESULTS_DIR"
}

# Cleanup function
cleanup() {
    log "Cleaning up..."
    # Add any cleanup tasks here
    success "Cleanup completed"
}

# Main execution
main() {
    log "Starting DMS Comprehensive Testing Suite"
    log "Results will be saved to: ${RESULTS_DIR}"
    
    check_prerequisites
    setup_test_environment
    
    # Run all test suites
    run_unit_tests
    run_integration_tests
    run_performance_tests
    run_contract_tests
    run_security_tests
    
    # Generate reports
    generate_test_report
    
    # Print summary
    print_test_summary
    
    # Cleanup
    cleanup
    
    if [ $failed_tests -eq 0 ]; then
        success "All tests passed! Comprehensive testing completed successfully."
        exit 0
    else
        error "Some tests failed. Please review the results."
        exit 1
    fi
}

# Handle script interruption
trap cleanup EXIT

# Run main function
main "$@"
