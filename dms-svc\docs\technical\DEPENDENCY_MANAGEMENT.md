# DMS Dependency Management Guide

This document provides comprehensive guidance on dependency management, security monitoring, and automated updates for the Document Management Service (DMS).

## Table of Contents

1. [Overview](#overview)
2. [Automated Dependency Updates](#automated-dependency-updates)
3. [Security Monitoring](#security-monitoring)
4. [Manual Dependency Management](#manual-dependency-management)
5. [License Compliance](#license-compliance)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)

## Overview

The DMS project implements a comprehensive dependency management strategy that includes:

- **Automated Updates**: Dependabot and GitHub Actions for regular updates
- **Security Scanning**: OWASP Dependency Check and Snyk integration
- **License Compliance**: Automated license checking and reporting
- **Compatibility Testing**: Automated testing of dependency updates
- **Monitoring**: Continuous monitoring for vulnerabilities and updates

### Key Components

- **Dependabot**: Automated dependency update PRs
- **GitHub Actions**: Security scanning and compatibility testing
- **Maven Plugins**: OWASP Dependency Check, Versions Plugin, License Plugin
- **Scripts**: Manual dependency management tools

## Automated Dependency Updates

### Dependabot Configuration

Dependabot is configured to automatically create pull requests for dependency updates:

**Schedule**: Weekly updates on Mondays at 9:00 AM UTC
**Scope**: Maven dependencies, GitHub Actions, Docker images
**Grouping**: Related dependencies are grouped together
**Limits**: Maximum 10 open PRs to avoid noise

**Configuration File**: `.github/dependabot.yml`

### Dependency Groups

Dependencies are organized into logical groups for easier review:

- **Spring Boot**: All Spring Framework dependencies
- **AWS SDK**: Amazon Web Services SDK components
- **Azure SDK**: Microsoft Azure SDK components
- **Testing Frameworks**: JUnit, Mockito, TestContainers
- **Monitoring**: Micrometer, OpenTelemetry, Zipkin
- **Document Processing**: Tika, PDFBox, Apache POI
- **Security Libraries**: JWT, Spring Security (patch only)

### Update Policies

- **Patch Updates**: Automatically approved for most dependencies
- **Minor Updates**: Grouped and require review
- **Major Updates**: Ignored for automatic updates, require manual review
- **Security Updates**: Prioritized and created immediately

## Security Monitoring

### Vulnerability Scanning

**OWASP Dependency Check**
- Runs daily via GitHub Actions
- Scans all dependencies for known vulnerabilities
- Fails build on CVSS score ≥ 7.0
- Generates HTML, JSON, and XML reports

**Snyk Integration**
- Continuous vulnerability monitoring
- Integration with GitHub Security tab
- SARIF report upload for code scanning
- Severity threshold: Medium and above

### Security Workflows

**Daily Security Scan**
```bash
# Triggered daily at 6 AM UTC
.github/workflows/dependency-security-scan.yml
```

**Pull Request Security Check**
- Runs on all PRs with dependency changes
- Comments on PR with security scan results
- Blocks merge if critical vulnerabilities found

**Security Issue Creation**
- Automatically creates GitHub issues for critical vulnerabilities
- Includes detailed remediation guidance
- Assigns to security team for review

### Vulnerability Suppressions

Known false positives and mitigated vulnerabilities are suppressed via:
```xml
.github/security/dependency-check-suppressions.xml
```

**Suppression Guidelines**:
- Include clear justification
- Set expiration dates for review
- Reference tracking issues
- Document mitigation measures

## Manual Dependency Management

### Dependency Management Script

Use the comprehensive dependency management script for manual operations:

```bash
# Run vulnerability scan
scripts\dependency-management.bat scan

# Check for updates
scripts\dependency-management.bat update

# Test compatibility
scripts\dependency-management.bat test

# Check license compliance
scripts\dependency-management.bat licenses

# Generate dependency tree
scripts\dependency-management.bat tree

# Create comprehensive report
scripts\dependency-management.bat report

# Run complete workflow
scripts\dependency-management.bat all
```

### Maven Commands

**Check for Updates**
```bash
mvn versions:display-dependency-updates
mvn versions:display-plugin-updates
```

**Apply Updates**
```bash
# Patch updates only
mvn versions:use-latest-releases -DallowMajorUpdates=false -DallowMinorUpdates=false

# Minor updates
mvn versions:use-latest-releases -DallowMajorUpdates=false

# All updates (use with caution)
mvn versions:use-latest-releases
```

**Security Scanning**
```bash
mvn org.owasp:dependency-check-maven:check
```

**Dependency Analysis**
```bash
mvn dependency:analyze
mvn dependency:tree
```

## License Compliance

### License Checking

The project automatically checks for license compliance using the License Maven Plugin:

**Allowed Licenses**:
- Apache License 2.0
- MIT License
- BSD Licenses
- Eclipse Public License
- Mozilla Public License 2.0

**Prohibited Licenses**:
- GPL-2.0, GPL-3.0
- AGPL-3.0
- LGPL-2.1, LGPL-3.0

### License Reports

**Generate License Report**
```bash
mvn org.codehaus.mojo:license-maven-plugin:aggregate-third-party-report
```

**Check File Headers**
```bash
mvn org.codehaus.mojo:license-maven-plugin:check-file-header
```

### License Compliance Workflow

1. **Automated Checking**: Runs on every build
2. **Report Generation**: Creates HTML reports with all dependencies
3. **Violation Detection**: Fails build if prohibited licenses found
4. **Manual Review**: Security team reviews new licenses

## Troubleshooting

### Common Issues

**Dependabot Not Creating PRs**
- Check repository settings for Dependabot
- Verify `.github/dependabot.yml` syntax
- Check for open PR limits

**Security Scan Failures**
- Review vulnerability details in reports
- Check if suppressions are needed
- Update dependencies with patches

**License Compliance Failures**
- Review license report for prohibited licenses
- Update dependency versions
- Add license exceptions if justified

**Compatibility Test Failures**
- Review test logs for specific failures
- Check for breaking changes in dependencies
- Update application code if needed

### Debug Commands

**Verbose Dependency Tree**
```bash
mvn dependency:tree -Dverbose=true
```

**Dependency Conflicts**
```bash
mvn dependency:analyze-duplicate
```

**Effective POM**
```bash
mvn help:effective-pom
```

## Best Practices

### Dependency Selection

1. **Prefer Stable Versions**: Use stable, well-maintained dependencies
2. **Minimize Dependencies**: Avoid unnecessary dependencies
3. **Security First**: Prioritize dependencies with good security records
4. **License Compatibility**: Ensure license compatibility
5. **Community Support**: Choose dependencies with active communities

### Update Strategy

1. **Regular Updates**: Keep dependencies current with regular updates
2. **Security Patches**: Apply security patches immediately
3. **Testing**: Thoroughly test all dependency updates
4. **Gradual Rollout**: Update dependencies incrementally
5. **Rollback Plan**: Have rollback procedures ready

### Security Practices

1. **Continuous Monitoring**: Monitor for new vulnerabilities daily
2. **Rapid Response**: Address critical vulnerabilities within 24 hours
3. **Defense in Depth**: Use multiple security scanning tools
4. **Documentation**: Document all security decisions
5. **Training**: Keep team updated on security best practices

### Monitoring and Alerting

1. **Dashboard**: Monitor dependency health via GitHub Security tab
2. **Notifications**: Set up alerts for critical vulnerabilities
3. **Metrics**: Track dependency update frequency and security posture
4. **Reporting**: Generate regular dependency health reports
5. **Review**: Conduct quarterly dependency reviews

## Automation Workflows

### Weekly Dependency Updates
- **Trigger**: Every Monday at 9 AM UTC
- **Scope**: Patch and minor updates
- **Testing**: Full test suite execution
- **Review**: Automatic PR creation with test results

### Daily Security Scans
- **Trigger**: Daily at 6 AM UTC
- **Scope**: All dependencies
- **Reporting**: Security dashboard updates
- **Alerting**: Critical vulnerability notifications

### Pull Request Checks
- **Trigger**: All PRs with dependency changes
- **Scope**: Security and compatibility testing
- **Blocking**: Critical vulnerabilities block merge
- **Reporting**: PR comments with scan results

## Metrics and KPIs

### Security Metrics
- **Vulnerability Count**: Number of known vulnerabilities
- **MTTR**: Mean time to resolve security issues
- **Coverage**: Percentage of dependencies scanned
- **Compliance**: License compliance percentage

### Update Metrics
- **Update Frequency**: How often dependencies are updated
- **Automation Rate**: Percentage of automated updates
- **Success Rate**: Percentage of successful updates
- **Time to Update**: Time from release to update

### Quality Metrics
- **Test Coverage**: Test coverage after updates
- **Compatibility**: Compatibility test success rate
- **Stability**: Application stability after updates
- **Performance**: Performance impact of updates

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Maintained By**: DMS Development Team

For questions or issues with dependency management, contact the development team or create a GitHub issue.
