-- Add columns for variables and attachments to notification_recipients table
ALTER TABLE notification_recipients 
ADD COLUMN variables TEXT,
ADD COLUMN attachments TEXT,
ADD COLUMN recipient_type VARCHAR(10);

-- Ensure the variables column exists in notifications table (if not already present)
-- This should already exist but adding a check just in case
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' AND column_name = 'variables'
    ) THEN
        ALTER TABLE notifications ADD COLUMN variables TEXT;
    END IF;
END $$;