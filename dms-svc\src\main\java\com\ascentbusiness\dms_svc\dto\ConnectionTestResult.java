package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConnectionTestType;
import com.ascentbusiness.dms_svc.enums.TestStatus;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * Connection test result DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class ConnectionTestResult {
    private String testId;
    private ConnectionTestType testType;
    private String target;
    private TestStatus status;
    private Boolean success;
    private Long responseTime; // milliseconds
    private String message;
    private ConnectionTestDetails details;
    private OffsetDateTime timestamp;
    private Integer retryCount;
}
