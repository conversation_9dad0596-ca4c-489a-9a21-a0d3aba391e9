package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.*;

/**
 * Entity representing classification metadata for documents in the DMS system.
 *
 * <p>This entity stores classification information that helps categorize and organize
 * documents within the GRC (Governance, Risk, Compliance) framework. It provides
 * structured metadata for document classification, confidentiality levels, and
 * organizational categorization.</p>
 *
 * <p>Key features:</p>
 * <ul>
 *   <li><strong>Module Classification</strong>: Categorizes documents by GRC modules</li>
 *   <li><strong>Business Unit Organization</strong>: Links documents to specific business units</li>
 *   <li><strong>Confidentiality Levels</strong>: Manages document security classifications</li>
 *   <li><strong>Tagging System</strong>: Supports JSON-based keyword tagging for search</li>
 *   <li><strong>Multi-language Support</strong>: Tracks document language for international compliance</li>
 * </ul>
 *
 * <p>This metadata is optional during document upload and can be added or updated
 * independently of the document content through dedicated GraphQL mutations.</p>
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 * @see Document
 * @see DocumentOwnershipMetadata
 * @see DocumentComplianceMetadata
 */
@Entity
@Table(name = "document_classification_metadata")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentClassificationMetadata extends BaseEntity {

    /**
     * Primary key for the classification metadata record.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Reference to the document this classification metadata belongs to.
     * Uses lazy loading for performance optimization.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;

    /**
     * GRC module name for document categorization.
     * Examples: "Governance", "Risk", "Compliance", "Audit", "Policy"
     */
    @Column(name = "module", length = 100)
    private String module;

    /**
     * Sub-module within the main GRC module for more granular classification.
     * Examples: within Risk - "Operational", "Financial", "IT", "Strategic"
     */
    @Column(name = "sub_module", length = 100)
    private String subModule;

    /**
     * Business unit or department responsible for or related to the document.
     * Examples: "Finance", "HR", "IT", "Legal", "Operations"
     */
    @Column(name = "business_unit", length = 100)
    private String businessUnit;

    /**
     * Geographic region or business location relevant to the document.
     * Examples: "North America", "EMEA", "APAC", "Global"
     */
    @Column(name = "region_location", length = 100)
    private String regionLocation;

    /**
     * Confidentiality level of the document for access control and security.
     * Standard levels: "Public", "Internal", "Confidential", "Restricted"
     */
    @Column(name = "confidentiality_level", length = 50)
    private String confidentialityLevel;

    /**
     * JSON array of tags and keywords for enhanced search and categorization.
     * Stored as JSON string for flexible keyword management.
     * Example: ["vendor", "ISO27001", "audit", "quarterly"]
     */
    @Column(name = "tags_keywords", columnDefinition = "JSON")
    private String tagsKeywords;

    /**
     * Language of the document content for multilingual support.
     * Uses ISO 639-1 language codes (e.g., "en", "fr", "de", "es")
     */
    @Column(name = "language", length = 10)
    private String language;

    /**
     * Type or category of the document for organizational purposes.
     * Examples: "Policy", "Procedure", "Report", "Contract", "Manual"
     */
    @Column(name = "document_type", length = 100)
    private String documentType;
}
