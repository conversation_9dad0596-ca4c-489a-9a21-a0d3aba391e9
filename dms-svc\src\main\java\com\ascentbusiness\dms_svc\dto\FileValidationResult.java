package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for file validation result.
 * Corresponds to FileValidationResult GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileValidationResult {

    /**
     * Whether the file passed validation.
     */
    private Boolean isValid;

    /**
     * List of validation errors.
     */
    private List<FileValidationError> validationErrors;

    /**
     * List of warning messages.
     */
    private List<String> warnings;

    /**
     * Information about the file.
     */
    private FileInfo fileInfo;
}
