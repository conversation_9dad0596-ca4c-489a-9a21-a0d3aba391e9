# Notification Service Only - Docker Compose Configuration
# Deploy only the Notification Service with its required infrastructure
version: '3.8'

services:
  # =============================================================================
  # NOTIFICATION SERVICE ONLY
  # =============================================================================
  
  # Notification Service
  notification-service:
    build:
      context: ./notification-svc
      dockerfile: Dockerfile
    container_name: notification-service
    ports:
      - "9091:9091"
    environment:
      # Database Configuration
      - SPRING_DATASOURCE_URL=***********************/${MYSQL_NOTIFICATION_DATABASE:-notification_db}?createDatabaseIfNotExist=true&useSSL=false&allowPublicKeyRetrieval=true
      - SPRING_DATASOURCE_USERNAME=${MYSQL_NOTIFICATION_USER:-notification_user}
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_NOTIFICATION_PASSWORD:-notification_password}
      
      # RabbitMQ Configuration
      - SPRING_RABBITMQ_HOST=rabbitmq
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=${RABBITMQ_DEFAULT_USER:-admin}
      - SPRING_RABBITMQ_PASSWORD=${RABBITMQ_DEFAULT_PASS:-admin123}
      
      # Redis Configuration
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-shared_redis_password}
      
      # Application Configuration
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=9091
      - JAVA_OPTS=${NOTIFICATION_JAVA_OPTS:--Xmx1g -Xms512m}
      
      # Security Configuration
      - SECURITY_JWT_ENABLED=true
      - JWT_ISSUER=notification-service
      - JWT_AUDIENCE=notification-clients
      - JWT_SECRET=${JWT_SECRET:-sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      
      # Email Configuration
      - MAIL_HOST=${MAIL_HOST:-smtp.gmail.com}
      - MAIL_PORT=${MAIL_PORT:-587}
      - MAIL_USERNAME=${MAIL_USERNAME:-}
      - MAIL_PASSWORD=${MAIL_PASSWORD:-}
      - NOTIFICATION_FROM_EMAIL=${NOTIFICATION_FROM_EMAIL:-<EMAIL>}
      - EMAIL_ENABLED=${EMAIL_ENABLED:-true}
      - EMAIL_MOCK=${EMAIL_MOCK:-false}
      
    volumes:
      - ./notification-svc/logs:/app/logs
      - ./notification-svc/keys:/app/keys:ro
    depends_on:
      mysql:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - notification-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # =============================================================================
  # REQUIRED INFRASTRUCTURE FOR NOTIFICATION SERVICE
  # =============================================================================

  # MySQL Database (Notification DB only)
  mysql:
    image: mysql:8.0
    container_name: notification-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      - MYSQL_DATABASE=${MYSQL_NOTIFICATION_DATABASE:-notification_db}
      - MYSQL_USER=${MYSQL_NOTIFICATION_USER:-notification_user}
      - MYSQL_PASSWORD=${MYSQL_NOTIFICATION_PASSWORD:-notification_password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/notification-init:/docker-entrypoint-initdb.d:ro
    networks:
      - notification-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-root_password}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: notification-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-shared_redis_password} --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - notification-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-shared_redis_password}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: notification-rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS:-admin123}
      - RABBITMQ_DEFAULT_VHOST=/
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - notification-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # =============================================================================
  # OPTIONAL MONITORING SERVICES
  # =============================================================================

  # Prometheus for Metrics (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: notification-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/notification-prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - notification-network
    restart: unless-stopped

  # Grafana for Dashboards (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: notification-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - notification-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  notification-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
