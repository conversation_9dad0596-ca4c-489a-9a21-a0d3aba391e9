<?xml version="1.0" encoding="UTF-8"?>
<!--
  OWASP Dependency Check Suppressions Configuration
  
  This file contains suppressions for known false positives in dependency vulnerability scanning.
  Each suppression should include:
  - Clear justification for why the vulnerability is not applicable
  - Expiration date to ensure regular review
  - Reference to tracking issue or security assessment
  
  IMPORTANT: Only suppress vulnerabilities after thorough security review.
  All suppressions should be regularly reviewed and updated.
-->
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">

  <!-- Spring Boot Framework Suppressions -->
  <!-- 
    Spring Boot vulnerabilities that don't apply to our usage patterns
    or have been mitigated through configuration
  -->
  <suppress>
    <notes>
      Spring Boot Actuator endpoints are secured and not exposed publicly.
      CVE-2023-20873 affects unsecured actuator endpoints which we don't have.
      Tracked in: DMS-SEC-001
      Expires: 2025-06-01
    </notes>
    <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-boot\-actuator.*$</packageUrl>
    <cve>CVE-2023-20873</cve>
  </suppress>

  <!-- MySQL Connector Suppressions -->
  <!--
    MySQL connector vulnerabilities that don't apply to our deployment model
  -->
  <suppress>
    <notes>
      CVE-2023-21971 affects MySQL server, not the Java connector.
      Our application only uses the JDBC connector, not the server.
      Tracked in: DMS-SEC-002
      Expires: 2025-03-01
    </notes>
    <packageUrl regex="true">^pkg:maven/com\.mysql/mysql\-connector\-j.*$</packageUrl>
    <cve>CVE-2023-21971</cve>
  </suppress>

  <!-- Jackson JSON Processing Suppressions -->
  <!--
    Jackson vulnerabilities that are mitigated by our usage patterns
  -->
  <suppress>
    <notes>
      CVE-2022-42003 affects deserialization of untrusted data.
      We only deserialize trusted JSON from our own API clients.
      Input validation and sanitization are implemented.
      Tracked in: DMS-SEC-003
      Expires: 2025-04-01
    </notes>
    <packageUrl regex="true">^pkg:maven/com\.fasterxml\.jackson\.core/jackson\-databind.*$</packageUrl>
    <cve>CVE-2022-42003</cve>
  </suppress>

  <!-- Apache Tika Suppressions -->
  <!--
    Tika vulnerabilities that are mitigated by sandboxing and input validation
  -->
  <suppress>
    <notes>
      CVE-2023-36478 affects XML parsing in Tika.
      We have implemented input validation and file type restrictions.
      Document parsing is sandboxed and runs with limited privileges.
      Tracked in: DMS-SEC-004
      Expires: 2025-05-01
    </notes>
    <packageUrl regex="true">^pkg:maven/org\.apache\.tika/tika\-core.*$</packageUrl>
    <cve>CVE-2023-36478</cve>
  </suppress>

  <!-- Apache POI Suppressions -->
  <!--
    POI vulnerabilities mitigated by input validation and sandboxing
  -->
  <suppress>
    <notes>
      CVE-2023-33008 affects Excel file processing in POI.
      We validate all uploaded files and restrict file types.
      Processing is done in isolated environment with resource limits.
      Tracked in: DMS-SEC-005
      Expires: 2025-04-15
    </notes>
    <packageUrl regex="true">^pkg:maven/org\.apache\.poi/poi.*$</packageUrl>
    <cve>CVE-2023-33008</cve>
  </suppress>

  <!-- Elasticsearch Client Suppressions -->
  <!--
    Elasticsearch vulnerabilities that don't apply to client-only usage
  -->
  <suppress>
    <notes>
      CVE-2023-31419 affects Elasticsearch server, not the Java client.
      We only use the client library to connect to managed Elasticsearch.
      Tracked in: DMS-SEC-006
      Expires: 2025-06-01
    </notes>
    <packageUrl regex="true">^pkg:maven/org\.elasticsearch.*$</packageUrl>
    <cve>CVE-2023-31419</cve>
  </suppress>

  <!-- Test Dependencies Suppressions -->
  <!--
    Vulnerabilities in test-only dependencies that don't affect production
  -->
  <suppress>
    <notes>
      Test dependencies are not included in production builds.
      Vulnerabilities in test frameworks don't affect runtime security.
      Tracked in: DMS-SEC-007
      Expires: 2025-12-31
    </notes>
    <packageUrl regex="true">^pkg:maven/org\.testcontainers/.*$</packageUrl>
    <vulnerabilityName regex="true">.*</vulnerabilityName>
  </suppress>

  <suppress>
    <notes>
      WireMock is used only in test environments for mocking HTTP services.
      Not included in production builds.
      Tracked in: DMS-SEC-008
      Expires: 2025-12-31
    </notes>
    <packageUrl regex="true">^pkg:maven/com\.github\.tomakehurst/wiremock.*$</packageUrl>
    <vulnerabilityName regex="true">.*</vulnerabilityName>
  </suppress>

  <!-- AWS SDK Suppressions -->
  <!--
    AWS SDK vulnerabilities that are mitigated by IAM policies and network security
  -->
  <suppress>
    <notes>
      AWS SDK vulnerabilities are mitigated by:
      - Strict IAM policies with minimal required permissions
      - VPC network isolation
      - Encrypted data in transit and at rest
      - Regular credential rotation
      Tracked in: DMS-SEC-009
      Expires: 2025-03-15
    </notes>
    <packageUrl regex="true">^pkg:maven/software\.amazon\.awssdk/.*$</packageUrl>
    <cve>CVE-2023-1370</cve>
  </suppress>

  <!-- Azure SDK Suppressions -->
  <!--
    Azure SDK vulnerabilities mitigated by Azure security controls
  -->
  <suppress>
    <notes>
      Azure SDK vulnerabilities are mitigated by:
      - Azure AD authentication and authorization
      - Managed identity for secure authentication
      - Network security groups and private endpoints
      - Azure Security Center monitoring
      Tracked in: DMS-SEC-010
      Expires: 2025-03-15
    </notes>
    <packageUrl regex="true">^pkg:maven/com\.azure/.*$</packageUrl>
    <cve>CVE-2023-2976</cve>
  </suppress>

  <!-- Logback Suppressions -->
  <!--
    Logback vulnerabilities that don't apply to our logging configuration
  -->
  <suppress>
    <notes>
      CVE-2023-6378 affects JNDI lookups in log messages.
      We have disabled JNDI lookups and don't log untrusted user input.
      Log configuration is controlled and doesn't allow external references.
      Tracked in: DMS-SEC-011
      Expires: 2025-04-01
    </notes>
    <packageUrl regex="true">^pkg:maven/ch\.qos\.logback/logback.*$</packageUrl>
    <cve>CVE-2023-6378</cve>
  </suppress>

  <!-- Micrometer/Prometheus Suppressions -->
  <!--
    Monitoring library vulnerabilities that don't affect our usage
  -->
  <suppress>
    <notes>
      Micrometer metrics are only exposed to internal monitoring systems.
      No sensitive data is included in metrics.
      Metrics endpoints are secured with authentication.
      Tracked in: DMS-SEC-012
      Expires: 2025-05-01
    </notes>
    <packageUrl regex="true">^pkg:maven/io\.micrometer/.*$</packageUrl>
    <vulnerabilityName regex="true">.*Information Disclosure.*</vulnerabilityName>
  </suppress>

  <!-- GraphQL Java Suppressions -->
  <!--
    GraphQL vulnerabilities mitigated by query complexity analysis and rate limiting
  -->
  <suppress>
    <notes>
      GraphQL DoS vulnerabilities are mitigated by:
      - Query complexity analysis and limits
      - Query depth limiting
      - Rate limiting per user and IP
      - Query timeout enforcement
      - Input validation and sanitization
      Tracked in: DMS-SEC-013
      Expires: 2025-04-15
    </notes>
    <packageUrl regex="true">^pkg:maven/com\.graphql\-java/.*$</packageUrl>
    <vulnerabilityName regex="true">.*Denial of Service.*</vulnerabilityName>
  </suppress>

  <!-- False Positive Suppressions -->
  <!--
    Known false positives from dependency scanning tools
  -->
  <suppress>
    <notes>
      False positive: CVE-2023-XXXXX incorrectly flagged for our dependency.
      Verified that the vulnerability doesn't apply to our version/usage.
      Confirmed with security team review.
      Tracked in: DMS-SEC-014
      Expires: 2025-02-01
    </notes>
    <packageUrl regex="true">^pkg:maven/example/false\-positive.*$</packageUrl>
    <cve>CVE-2023-XXXXX</cve>
  </suppress>

  <!-- Temporary Suppressions -->
  <!--
    Temporary suppressions for vulnerabilities being actively addressed
  -->
  <suppress until="2025-01-15">
    <notes>
      TEMPORARY: Vulnerability being addressed in upcoming dependency update.
      Mitigation: Additional input validation implemented.
      Tracking issue: DMS-SEC-015
      Planned fix: Update to version X.Y.Z in next release
    </notes>
    <packageUrl regex="true">^pkg:maven/temporary/suppression.*$</packageUrl>
    <cve>CVE-2024-TEMP</cve>
  </suppress>

</suppressions>

<!--
  Suppression Review Guidelines:
  
  1. All suppressions must have clear justification
  2. Include expiration dates for regular review
  3. Reference tracking issues or security assessments
  4. Document mitigation measures implemented
  5. Review suppressions quarterly
  6. Remove suppressions when vulnerabilities are fixed
  7. Escalate any uncertain suppressions to security team
  
  Security Contact: <EMAIL>
  Last Reviewed: December 2024
  Next Review: March 2025
-->
