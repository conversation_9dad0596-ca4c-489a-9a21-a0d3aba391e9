# DMS Service Testing - Quick Start Guide

## 🚀 Quick Start

### Run All Tests (Recommended)

**Windows:**
```cmd
scripts\run-all-tests.bat
```

**Linux/Mac:**
```bash
chmod +x scripts/run-all-tests.sh
./scripts/run-all-tests.sh
```

### Run Specific Test Categories

**Unit Tests Only:**
```bash
mvn test
```

**Integration Tests Only:**
```bash
mvn failsafe:integration-test -Dtest="**/*IntegrationTest"
```

**E2E Tests Only:**
```bash
mvn failsafe:integration-test -Dtest="**/*E2ETest"
```

**With Coverage Report:**
```bash
mvn clean test jacoco:report
```

## 📊 Test Results & Reports

After running tests, check these locations:

### 📁 Report Locations
```
target/test-reports/[timestamp]/
├── 📄 test-report.html          # Main test report
├── 📄 test-report.json          # Machine-readable results
├── 📄 test-summary.txt          # Quick summary
├── 📊 coverage/index.html       # Code coverage report
└── 📋 test-execution.log        # Detailed execution log
```

### 🎯 Key Metrics to Check

| Metric | Location | Target |
|--------|----------|--------|
| **Overall Success Rate** | test-summary.txt | 100% |
| **Code Coverage** | coverage/index.html | >85% |
| **Test Execution Time** | test-execution.log | <5 min |
| **Failed Tests** | test-report.html | 0 |

## 🧪 Test Categories Overview

### 1. Unit Tests (🔬 Fast & Isolated)
- **Purpose**: Test individual components
- **Duration**: ~30 seconds
- **Coverage**: Service layer, utilities, filters
- **Database**: H2 in-memory

### 2. Integration Tests (🔗 Real Infrastructure)
- **Purpose**: Test component interactions
- **Duration**: ~2 minutes
- **Coverage**: Database operations, storage, audit
- **Database**: MySQL (TestContainers)

### 3. E2E Tests (🌐 Full Workflows)
- **Purpose**: Test complete user scenarios
- **Duration**: ~2 minutes
- **Coverage**: GraphQL API, authentication, file operations
- **Database**: MySQL (TestContainers)

## 🎯 Expected Results

### ✅ Success Indicators
```
✓ Unit Tests: PASSED (150+ tests)
✓ Integration Tests: PASSED (50+ tests)  
✓ E2E Tests: PASSED (25+ tests)
✓ Code Coverage: >85%
🎉 OVERALL RESULT: ALL TESTS PASSED
```

### ❌ Failure Indicators
```
✗ Unit Tests: FAILED (X failures)
✗ Integration Tests: FAILED (X failures)
✗ E2E Tests: FAILED (X failures)
❌ OVERALL RESULT: X TEST SUITE(S) FAILED
```

## 🔧 Prerequisites

### Required Software
- ✅ Java 21+
- ✅ Maven 3.8+
- ✅ Docker (for integration tests)

### Environment Setup
```bash
# Verify Java version
java -version

# Verify Maven version
mvn -version

# Verify Docker is running
docker --version
docker ps
```

## 🐛 Troubleshooting

### Common Issues & Solutions

#### 1. Tests Fail to Start
**Problem**: `Cannot connect to Docker daemon`
**Solution**: 
```bash
# Start Docker service
sudo systemctl start docker  # Linux
# Or start Docker Desktop on Windows/Mac
```

#### 2. Compilation Errors
**Problem**: `Compilation failure`
**Solution**:
```bash
# Clean and recompile
mvn clean compile test-compile
```

#### 3. Port Conflicts
**Problem**: `Port already in use`
**Solution**:
```bash
# Kill processes using test ports
# Windows
netstat -ano | findstr :3306
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:3306 | xargs kill -9
```

#### 4. Memory Issues
**Problem**: `OutOfMemoryError`
**Solution**:
```bash
# Increase Maven memory
export MAVEN_OPTS="-Xmx2g -XX:MaxPermSize=512m"
```

### 📋 Verification Checklist

Before running tests, ensure:
- [ ] Docker is running
- [ ] No other services using ports 3306, 6379
- [ ] Java 21+ is installed
- [ ] Maven 3.8+ is available
- [ ] Internet connection (for downloading dependencies)

## 📞 Getting Help

### 🔍 Debug Information
When reporting issues, include:
1. **Test execution log**: `target/test-reports/[timestamp]/test-execution.log`
2. **System information**: Java version, OS, Docker version
3. **Error messages**: Full stack traces from logs
4. **Test command used**: Exact command that failed

### 📚 Additional Resources
- [Comprehensive Testing Strategy](COMPREHENSIVE_TESTING_STRATEGY.md)
- [Test Case Documentation](../TEST_CASE_API_DOCUMENTATION.md)
- [Implementation Summary](../IMPLEMENTATION_SUMMARY.md)

---

## 🎯 Success Criteria

Your DMS service testing is successful when:

1. ✅ **All test suites pass** (Unit + Integration + E2E)
2. ✅ **Code coverage >85%** across all components
3. ✅ **No critical security vulnerabilities** detected
4. ✅ **Performance benchmarks met** (response times <2s)
5. ✅ **Reports generated successfully** with detailed metrics

**Happy Testing! 🧪✨**
