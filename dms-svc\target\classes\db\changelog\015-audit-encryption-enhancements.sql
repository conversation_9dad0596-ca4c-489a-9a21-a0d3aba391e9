--liquibase formatted sql

--changeset dms:015-audit-encryption-enhancements-001
--comment: Add encryption fields to audit_logs table for configurable audit log encryption

-- Add encryption fields to audit_logs table
ALTER TABLE audit_logs 
ADD COLUMN is_encrypted BOOLEAN DEFAULT FALSE,
ADD COLUMN encryption_key_id VARCHAR(100),
ADD COLUMN encryption_iv VARCHAR(200),
ADD COLUMN encrypted_details TEXT,
ADD COLUMN encrypted_technical_details TEXT,
ADD COLUMN encrypted_before_state TEXT,
ADD COLUMN encrypted_after_state TEXT,
ADD COLUMN encrypted_client_info TEXT;

-- Add indexes for performance
CREATE INDEX idx_audit_logs_is_encrypted ON audit_logs(is_encrypted);
CREATE INDEX idx_audit_logs_encryption_key_id ON audit_logs(encryption_key_id);

--changeset dms:015-audit-encryption-enhancements-002
--comment: Add audit encryption configuration to security_config table

-- Insert audit encryption configuration
INSERT INTO security_config (config_key, config_value, description, created_by, is_active, created_date, last_modified_date) VALUES
('AUDIT_ENCRYPTION_ENABLED', 'false', 'Enable encryption for sensitive audit log data', 'SYSTEM', true, NOW(), NOW()),
('AUDIT_ENCRYPTION_KEY_ROTATION_ENABLED', 'true', 'Enable automatic key rotation for audit encryption', 'SYSTEM', true, NOW(), NOW()),
('AUDIT_ENCRYPTION_KEY_ROTATION_INTERVAL_HOURS', '24', 'Key rotation interval in hours', 'SYSTEM', true, NOW(), NOW()),
('AUDIT_ENCRYPTION_MASTER_KEY', '', 'Base64-encoded master key for audit encryption (leave empty for auto-generation)', 'SYSTEM', true, NOW(), NOW()),
('AUDIT_ENCRYPTION_SENSITIVE_EVENTS_ONLY', 'true', 'Only encrypt sensitive event types (DATA_SUBJECT, VIOLATION, etc.)', 'SYSTEM', true, NOW(), NOW()),
('AUDIT_ENCRYPTION_PERFORMANCE_MODE', 'true', 'Enable performance optimizations for encryption', 'SYSTEM', true, NOW(), NOW());

--changeset dms:015-audit-encryption-enhancements-003
--comment: Create audit_encryption_keys table for key management

-- Create table for audit encryption key management
CREATE TABLE audit_encryption_keys (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    key_id VARCHAR(100) NOT NULL UNIQUE,
    key_data TEXT NOT NULL,
    algorithm VARCHAR(50) NOT NULL DEFAULT 'AES-256-GCM',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    rotation_date TIMESTAMP,
    usage_count BIGINT DEFAULT 0,
    last_used_date TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'SYSTEM',
    notes TEXT
);

-- Add indexes
CREATE INDEX idx_audit_encryption_keys_key_id ON audit_encryption_keys(key_id);
CREATE INDEX idx_audit_encryption_keys_is_active ON audit_encryption_keys(is_active);
CREATE INDEX idx_audit_encryption_keys_created_date ON audit_encryption_keys(created_date);

--changeset dms:015-audit-encryption-enhancements-004
--comment: Create audit_encryption_metrics table for monitoring

-- Create table for audit encryption metrics and monitoring
CREATE TABLE audit_encryption_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_date DATE NOT NULL,
    total_audit_logs BIGINT DEFAULT 0,
    encrypted_audit_logs BIGINT DEFAULT 0,
    encryption_success_rate DECIMAL(5,2) DEFAULT 0.00,
    decryption_success_rate DECIMAL(5,2) DEFAULT 0.00,
    key_rotations BIGINT DEFAULT 0,
    encryption_errors BIGINT DEFAULT 0,
    decryption_errors BIGINT DEFAULT 0,
    performance_avg_encrypt_ms DECIMAL(10,3) DEFAULT 0.000,
    performance_avg_decrypt_ms DECIMAL(10,3) DEFAULT 0.000,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add indexes
CREATE UNIQUE INDEX idx_audit_encryption_metrics_date ON audit_encryption_metrics(metric_date);
CREATE INDEX idx_audit_encryption_metrics_created_date ON audit_encryption_metrics(created_date);

--changeset dms:015-audit-encryption-enhancements-005
--comment: Add audit log encryption event types to audit system

-- Note: audit_actions table doesn't exist yet, so these INSERT statements are commented out
-- These will be added when the audit_actions table is created in a future changeset
-- INSERT INTO audit_actions (action_name, description, risk_level, retention_days) VALUES
-- ('AUDIT_ENCRYPTION_ENABLED', 'Audit log encryption was enabled', 'LOW', 2555),
-- ('AUDIT_ENCRYPTION_DISABLED', 'Audit log encryption was disabled', 'MEDIUM', 2555),
-- ('AUDIT_ENCRYPTION_KEY_GENERATED', 'New audit encryption key was generated', 'LOW', 2555),
-- ('AUDIT_ENCRYPTION_KEY_ROTATED', 'Audit encryption key was rotated', 'LOW', 2555),
-- ('AUDIT_ENCRYPTION_ERROR', 'Error occurred during audit log encryption', 'HIGH', 2555),
-- ('AUDIT_DECRYPTION_ERROR', 'Error occurred during audit log decryption', 'HIGH', 2555),
-- ('AUDIT_ENCRYPTION_KEY_COMPROMISED', 'Audit encryption key may be compromised', 'CRITICAL', 2555);

--changeset dms:015-audit-encryption-enhancements-006
--comment: Create stored procedure for audit encryption cleanup - DISABLED

-- Note: Stored procedure creation is disabled due to Liquibase compatibility issues with DELIMITER syntax
-- The cleanup functionality can be implemented in application code instead

-- -- Create stored procedure for cleaning up old encryption keys
-- -- This is commented out due to Liquibase not supporting DELIMITER syntax properly
-- -- The cleanup can be done via application code instead

--changeset dms:015-audit-encryption-enhancements-007
--comment: Create view for audit encryption monitoring

-- Create view for audit encryption monitoring and reporting (using only columns that exist in base schema)
CREATE VIEW audit_encryption_status AS
SELECT 
    DATE(al.timestamp) as audit_date,
    COUNT(*) as total_logs,
    SUM(CASE WHEN al.is_encrypted = TRUE THEN 1 ELSE 0 END) as encrypted_logs,
    ROUND(
        (SUM(CASE WHEN al.is_encrypted = TRUE THEN 1 ELSE 0 END) * 100.0) / COUNT(*), 
        2
    ) as encryption_percentage,
    COUNT(DISTINCT al.encryption_key_id) as unique_keys_used,
    al.action
FROM audit_logs al
WHERE al.timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(al.timestamp), al.action
ORDER BY audit_date DESC, action;

--changeset dms:015-audit-encryption-enhancements-008
--comment: Add comments and documentation

-- Add table comments for documentation
ALTER TABLE audit_logs COMMENT = 'Enhanced audit logs table with configurable encryption support for sensitive data';
ALTER TABLE audit_encryption_keys COMMENT = 'Audit encryption key management table for key rotation and lifecycle';
ALTER TABLE audit_encryption_metrics COMMENT = 'Audit encryption metrics and monitoring data';

-- Add column comments
ALTER TABLE audit_logs 
MODIFY COLUMN is_encrypted BOOLEAN DEFAULT FALSE COMMENT 'Indicates if sensitive fields are encrypted',
MODIFY COLUMN encryption_key_id VARCHAR(100) COMMENT 'ID of the encryption key used',
MODIFY COLUMN encryption_iv VARCHAR(200) COMMENT 'Initialization vector for GCM encryption',
MODIFY COLUMN encrypted_details TEXT COMMENT 'Encrypted version of details field',
MODIFY COLUMN encrypted_technical_details TEXT COMMENT 'Encrypted version of technical_details field',
MODIFY COLUMN encrypted_before_state TEXT COMMENT 'Encrypted version of before_state field',
MODIFY COLUMN encrypted_after_state TEXT COMMENT 'Encrypted version of after_state field',
MODIFY COLUMN encrypted_client_info TEXT COMMENT 'Encrypted version of client_info field';
