package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.*;

/**
 * Entity representing the mapping between documents and regulatory frameworks
 */
@Entity
@Table(name = "regulation_mappings")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RegulationMapping extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "compliance_framework_id", nullable = false)
    private ComplianceFramework complianceFramework;

    @Column(name = "regulation_reference", length = 200)
    private String regulationReference; // Specific regulation section/article

    @Column(name = "compliance_requirement", columnDefinition = "TEXT")
    private String complianceRequirement; // What the document must comply with

    @Builder.Default
    @Column(name = "is_mandatory", nullable = false)
    private Boolean isMandatory = true;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "risk_level", length = 20)
    private String riskLevel; // HIGH, MEDIUM, LOW

    @Column(name = "control_objective", columnDefinition = "TEXT")
    private String controlObjective;

    @Column(name = "evidence_requirements", columnDefinition = "TEXT")
    private String evidenceRequirements;

    @Column(name = "testing_procedures", columnDefinition = "TEXT")
    private String testingProcedures;

    @Column(name = "responsible_party", length = 200)
    private String responsibleParty; // Who is responsible for compliance

    @Column(name = "review_frequency_months")
    private Integer reviewFrequencyMonths;

    @Column(name = "last_review_date")
    private java.time.LocalDateTime lastReviewDate;

    @Column(name = "next_review_date")
    private java.time.LocalDateTime nextReviewDate;

    @Column(name = "compliance_status", length = 50)
    private String complianceStatus; // COMPLIANT, NON_COMPLIANT, UNDER_REVIEW, NOT_APPLICABLE

    @Column(name = "compliance_notes", columnDefinition = "TEXT")
    private String complianceNotes;

    @Column(name = "remediation_plan", columnDefinition = "TEXT")
    private String remediationPlan;

    @Column(name = "remediation_deadline")
    private java.time.LocalDateTime remediationDeadline;

    @Builder.Default
    @Column(name = "priority", nullable = false)
    private Integer priority = 0; // Higher number = higher priority

    /**
     * Check if this mapping is currently active and effective
     */
    @Transient
    public boolean isCurrentlyEffective() {
        return isActive && complianceFramework != null && complianceFramework.isCurrentlyEffective();
    }

    /**
     * Check if review is due
     */
    @Transient
    public boolean isReviewDue() {
        if (nextReviewDate == null) {
            return false;
        }
        return java.time.LocalDateTime.now().isAfter(nextReviewDate);
    }

    /**
     * Check if remediation is overdue
     */
    @Transient
    public boolean isRemediationOverdue() {
        if (remediationDeadline == null || !"NON_COMPLIANT".equals(complianceStatus)) {
            return false;
        }
        return java.time.LocalDateTime.now().isAfter(remediationDeadline);
    }

    /**
     * Get risk score based on various factors
     */
    @Transient
    public int getRiskScore() {
        int score = 0;
        
        // Base score from risk level
        if ("HIGH".equals(riskLevel)) {
            score += 50;
        } else if ("MEDIUM".equals(riskLevel)) {
            score += 30;
        } else if ("LOW".equals(riskLevel)) {
            score += 10;
        }
        
        // Add score for compliance status
        if ("NON_COMPLIANT".equals(complianceStatus)) {
            score += 40;
        } else if ("UNDER_REVIEW".equals(complianceStatus)) {
            score += 20;
        }
        
        // Add score for overdue items
        if (isReviewDue()) {
            score += 15;
        }
        if (isRemediationOverdue()) {
            score += 25;
        }
        
        // Add score for mandatory requirements
        if (isMandatory) {
            score += 10;
        }
        
        return Math.min(score, 100); // Cap at 100
    }

    /**
     * Update review dates based on frequency
     */
    @Transient
    public void updateReviewDates() {
        if (reviewFrequencyMonths != null && reviewFrequencyMonths > 0) {
            lastReviewDate = java.time.LocalDateTime.now();
            nextReviewDate = lastReviewDate.plusMonths(reviewFrequencyMonths);
        }
    }

    /**
     * Get display name for this mapping
     */
    @Transient
    public String getDisplayName() {
        StringBuilder sb = new StringBuilder();
        
        if (complianceFramework != null) {
            sb.append(complianceFramework.getDisplayName());
        }
        
        if (regulationReference != null && !regulationReference.trim().isEmpty()) {
            if (sb.length() > 0) {
                sb.append(" - ");
            }
            sb.append(regulationReference);
        }
        
        return sb.length() > 0 ? sb.toString() : "Unknown Regulation";
    }

    @Override
    public String toString() {
        return String.format("RegulationMapping{id=%d, document=%d, framework=%s, reference='%s', status=%s}", 
                           getId(), 
                           document != null ? document.getId() : null,
                           complianceFramework != null ? complianceFramework.getFrameworkType() : null,
                           regulationReference, 
                           complianceStatus);
    }
}
