package com.ascentbusiness.dms_svc.dto;

import lombok.Data;

@Data
public class DocumentComplianceMetadataInput {
    
    private String complianceStandard; // e.g., ISO 27001, HIPAA, SOX, GDPR
    private String auditRelevance; // Boolean or text (e.g., Yes, No, Internal Audit 2024)
    private String linkedRisksControls; // References to associated risks or controls
    private String controlId; // If document maps to a control
    private String thirdPartyId; // If related to vendors/suppliers
    private String policyId; // If linked to a policy document
}
