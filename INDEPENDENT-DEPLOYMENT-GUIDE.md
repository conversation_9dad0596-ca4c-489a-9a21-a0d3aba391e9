# Independent Service Deployment Guide

## ✅ **Service Independence Confirmed**

Both DMS and Notification services are **completely independent** and can be deployed separately without any dependencies on each other.

## 🏗️ **Deployment Options**

You now have **3 deployment configurations**:

### 1. **Shared Deployment** (Both Services)
- **File**: `docker-compose.yml`
- **Script**: `./docker-manage.sh`
- **Use Case**: Full platform deployment

### 2. **Notification Service Only**
- **File**: `docker-compose.notification-only.yml`
- **Script**: `./docker-manage-notification-only.sh`
- **Use Case**: Only need notification functionality

### 3. **DMS Service Only**
- **File**: `docker-compose.dms-only.yml`
- **Script**: `./docker-manage-dms-only.sh`
- **Use Case**: Only need document management functionality

## 🚀 **Quick Start Examples**

### Deploy Only Notification Service
```bash
# Copy environment template
cp .env.template .env

# Start notification service only
./docker-manage-notification-only.sh start-all

# Check status
./docker-manage-notification-only.sh status

# Access service
# GraphQL: http://localhost:9091/graphql
# GraphiQL: http://localhost:9091/graphiql
```

### Deploy Only DMS Service
```bash
# Copy environment template
cp .env.template .env

# Start DMS service only
./docker-manage-dms-only.sh start-all

# Check status
./docker-manage-dms-only.sh status

# Access service
# GraphQL: http://localhost:9093/dms/graphql
# GraphiQL: http://localhost:9093/graphiql
```

### Deploy Both Services (Shared)
```bash
# Copy environment template
cp .env.template .env

# Start both services with shared infrastructure
./docker-manage.sh start-all

# Check status
./docker-manage.sh status
```

## 📊 **Resource Requirements Comparison**

### Notification Service Only
```
Services: 6 containers
├── notification-service (Port 9091)
├── mysql (Port 3306)
├── redis (Port 6379)
├── rabbitmq (Port 5672, 15672)
├── prometheus (Port 9090) [Optional]
└── grafana (Port 3000) [Optional]

Memory: ~2-3GB
Ports: 6 ports (4 required + 2 optional)
```

### DMS Service Only
```
Services: 7 containers
├── dms-service (Port 9093, 9464)
├── mysql (Port 3306)
├── redis (Port 6379)
├── elasticsearch (Port 9200, 9300)
├── zipkin (Port 9411) [Optional]
├── prometheus (Port 9090) [Optional]
└── grafana (Port 3000) [Optional]

Memory: ~3-4GB
Ports: 8 ports (5 required + 3 optional)
```

### Both Services (Shared)
```
Services: 9 containers
├── dms-service (Port 9093, 9464)
├── notification-service (Port 9091)
├── mysql (Port 3306) [SHARED]
├── redis (Port 6379) [SHARED]
├── rabbitmq (Port 5672, 15672)
├── elasticsearch (Port 9200, 9300)
├── zipkin (Port 9411)
├── prometheus (Port 9090)
└── grafana (Port 3000)

Memory: ~4-5GB
Ports: 9 ports
```

## 🔧 **Infrastructure Dependencies**

### Notification Service Dependencies
- ✅ **MySQL** - For notification data storage
- ✅ **Redis** - For caching and session management
- ✅ **RabbitMQ** - For async message processing
- ❌ **Elasticsearch** - NOT required
- ❌ **DMS Service** - NOT required

### DMS Service Dependencies
- ✅ **MySQL** - For document metadata storage
- ✅ **Redis** - For caching and session management
- ✅ **Elasticsearch** - For document search functionality
- ❌ **RabbitMQ** - NOT required
- ❌ **Notification Service** - NOT required

## 🌐 **Service URLs by Deployment**

### Notification Service Only
```
Application:
  GraphQL API:     http://localhost:9091/graphql
  GraphiQL UI:     http://localhost:9091/graphiql
  Health Check:    http://localhost:9091/actuator/health

Infrastructure:
  MySQL:           localhost:3306 (root/root_password)
  Redis:           localhost:6379 (shared_redis_password)
  RabbitMQ Mgmt:   http://localhost:15672 (admin/admin123)

Monitoring:
  Prometheus:      http://localhost:9090
  Grafana:         http://localhost:3000 (admin/admin)
```

### DMS Service Only
```
Application:
  GraphQL API:     http://localhost:9093/dms/graphql
  GraphiQL UI:     http://localhost:9093/graphiql
  Health Check:    http://localhost:9093/actuator/health
  Metrics:         http://localhost:9464/actuator/prometheus

Infrastructure:
  MySQL:           localhost:3306 (root/root_password)
  Redis:           localhost:6379 (shared_redis_password)
  Elasticsearch:   http://localhost:9200

Monitoring:
  Prometheus:      http://localhost:9090
  Grafana:         http://localhost:3000 (admin/admin)
  Zipkin:          http://localhost:9411
```

## 🗄️ **Database Configuration**

### Notification Service Only
- **Database**: `notification_db`
- **User**: `notification_user` / `notification_password`
- **Tables**: Notifications, Templates, Audit Logs, Recipients

### DMS Service Only
- **Database**: `dms_db`
- **User**: `dms_user` / `dms_password`
- **Tables**: Documents, Permissions, Audit Logs, Versions

### Shared Deployment
- **Database 1**: `dms_db` (dms_user/dms_password)
- **Database 2**: `notification_db` (notification_user/notification_password)
- **Cross-access**: Both users can read from each other's databases

## 🔒 **Security Considerations**

### Production Deployment
For any deployment option, ensure you:

1. **Change Default Passwords**:
   ```bash
   # Edit .env file
   MYSQL_ROOT_PASSWORD=your-secure-password
   REDIS_PASSWORD=your-redis-password
   RABBITMQ_DEFAULT_PASS=your-rabbitmq-password
   JWT_SECRET=your-jwt-secret-key
   ```

2. **Configure Email** (for Notification Service):
   ```bash
   MAIL_HOST=your-smtp-server.com
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-app-password
   ```

3. **Network Security**:
   - Use custom Docker networks
   - Restrict port exposure
   - Configure firewall rules

## 🔄 **Migration Between Deployments**

### From Individual to Shared
```bash
# 1. Backup data from individual deployments
./docker-manage-notification-only.sh stop
./docker-manage-dms-only.sh stop

# 2. Start shared deployment
./docker-manage.sh start-all

# 3. Restore data if needed
```

### From Shared to Individual
```bash
# 1. Backup shared data
docker-compose exec mysql mysqldump -u root -p dms_db > dms_backup.sql
docker-compose exec mysql mysqldump -u root -p notification_db > notification_backup.sql

# 2. Stop shared deployment
./docker-manage.sh stop

# 3. Start individual services
./docker-manage-notification-only.sh start-all
./docker-manage-dms-only.sh start-all

# 4. Restore data to individual deployments
```

## 🎯 **Deployment Decision Matrix**

| Use Case | Recommended Deployment | Reason |
|----------|----------------------|---------|
| Only need notifications | `notification-only` | Minimal resources, faster startup |
| Only need document management | `dms-only` | No unnecessary services |
| Full platform functionality | `shared` | Resource efficient, integrated monitoring |
| Development/Testing | `shared` | Easy to test integrations |
| Production (single service) | `notification-only` or `dms-only` | Better isolation, easier scaling |
| Production (both services) | `shared` | Resource optimization, unified monitoring |

## 🛠️ **Management Commands**

### Notification Service Only
```bash
./docker-manage-notification-only.sh start-all    # Start everything
./docker-manage-notification-only.sh status       # Check status
./docker-manage-notification-only.sh health       # Health check
./docker-manage-notification-only.sh logs         # View logs
./docker-manage-notification-only.sh stop         # Stop all
```

### DMS Service Only
```bash
./docker-manage-dms-only.sh start-all    # Start everything
./docker-manage-dms-only.sh status       # Check status
./docker-manage-dms-only.sh health       # Health check
./docker-manage-dms-only.sh logs         # View logs
./docker-manage-dms-only.sh stop         # Stop all
```

### Shared Deployment
```bash
./docker-manage.sh start-all    # Start everything
./docker-manage.sh status       # Check status
./docker-manage.sh health       # Health check
./docker-manage.sh logs         # View logs
./docker-manage.sh stop         # Stop all
```

## ✅ **Answer to Your Question**

**YES, you can deploy only the Notification Service without the DMS Service and it will work perfectly!**

- ✅ **No runtime dependencies** between services
- ✅ **Separate databases** and business logic
- ✅ **Independent infrastructure** requirements
- ✅ **Complete functionality** when deployed alone
- ✅ **Production-ready** standalone deployment

The services are designed as true microservices with clear boundaries and no tight coupling.
