package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for MIME type counts.
 * Corresponds to MimeTypeCount GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MimeTypeCount {

    /**
     * The MIME type.
     */
    private String mimeType;

    /**
     * Number of uploads with this MIME type.
     */
    private Long count;

    /**
     * Total size of files with this MIME type in bytes.
     */
    private Long totalSize;
}
