--liquibase formatted sql

--changeset system:044-add-source-template-id-to-documents
--comment: Add source_template_id foreign key column to documents table for document template relationships

-- Add source_template_id column to documents table
ALTER TABLE documents
ADD COLUMN source_template_id BIGINT NULL;

-- Add template_field_values column to store template field data
ALTER TABLE documents
ADD COLUMN template_field_values JSON NULL;

-- Add foreign key constraint to document_templates table
ALTER TABLE documents
ADD CONSTRAINT fk_documents_source_template
FOREIGN KEY (source_template_id) REFERENCES document_templates(id)
ON DELETE SET NULL;

-- Add indexes for performance
CREATE INDEX idx_documents_source_template_id ON documents(source_template_id);

-- Add comments for documentation
ALTER TABLE documents MODIFY COLUMN source_template_id BIGINT NULL COMMENT 'Foreign key reference to document_templates table for template-based documents';
ALTER TABLE documents MODIFY COLUMN template_field_values JSON NULL COMMENT 'Stores field values when document is created from a template';
