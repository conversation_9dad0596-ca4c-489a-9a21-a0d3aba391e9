package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionType;
import lombok.Builder;
import lombok.Data;

/**
 * DTO for conversion type statistics from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionTypeStats {
    private ConversionType conversionType;
    private Long count;
    private Long successCount;
    private Long failureCount;
    private Float averageProcessingTime;
    private Float successRate;
}
