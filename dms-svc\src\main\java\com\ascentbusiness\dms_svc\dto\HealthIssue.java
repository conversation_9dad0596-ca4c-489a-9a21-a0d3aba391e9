package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.IssueSeverity;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * Health issue DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class HealthIssue {
    private IssueSeverity severity;
    private String component;
    private String message;
    private String details;
    private OffsetDateTime timestamp;
    private String resolution;
    private String recommendation;
}
