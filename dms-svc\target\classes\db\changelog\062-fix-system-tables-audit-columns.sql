--liquibase formatted sql

--changeset system:062-fix-system-tables-audit-columns
--comment: Fix audit column names in system_events and event_subscriptions tables to match BaseEntity

-- Fix system_events table
ALTER TABLE system_events 
DROP COLUMN created_at,
DROP COLUMN updated_at;

ALTER TABLE system_events 
ADD COLUMN created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN last_modified_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Fix event_subscriptions table  
ALTER TABLE event_subscriptions 
DROP COLUMN created_at,
DROP COLUMN updated_at;

ALTER TABLE event_subscriptions 
ADD COLUMN created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN last_modified_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Also fix the column length for created_by and last_modified_by to match BaseEntity (VARCHAR(100))
ALTER TABLE system_events 
MODIFY COLUMN created_by <PERSON><PERSON><PERSON><PERSON>(100),
MODIFY COLUMN last_modified_by VARCHAR(100);

ALTER TABLE event_subscriptions 
MODIFY COLUMN created_by VARCHAR(100) NOT NULL,
MODIFY COLUMN last_modified_by VARCHAR(100);