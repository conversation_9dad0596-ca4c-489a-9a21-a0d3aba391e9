package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Result of Markdown to Word conversion operation.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarkdownConversionResult {
    
    /**
     * Unique session identifier for the conversion operation.
     */
    private String sessionId;
    
    /**
     * Original Markdown file name.
     */
    private String originalFileName;
    
    /**
     * Converted Word document file name.
     */
    private String convertedFileName;
    
    /**
     * Full path to the converted file in the download directory.
     */
    private String downloadPath;
    
    /**
     * Size of the converted file in bytes.
     */
    private long fileSize;
    
    /**
     * Result of virus scanning performed on the original Markdown file.
     */
    private VirusScanResponse virusScanResponse;
    
    /**
     * Whether the conversion was successful.
     */
    private boolean success;
    
    /**
     * Success or error message.
     */
    private String message;
    
    /**
     * Error details if conversion failed.
     */
    private String errorDetails;
    
    /**
     * Timestamp when conversion was completed.
     */
    private java.time.OffsetDateTime completedAt;
    
    /**
     * Duration of the conversion process in milliseconds.
     */
    private long processingTimeMs;
    
    /**
     * Whether Pandoc was used for the conversion.
     */
    private boolean usedPandoc;
    
    /**
     * Conversion method used (e.g., "pandoc", "fallback").
     */
    private String conversionMethod;
}
