# GRC Platform v4 - Shared Docker Compose Configuration
# This configuration runs both DMS and Notification services with shared infrastructure
version: '3.8'

services:
  # =============================================================================
  # APPLICATION SERVICES
  # =============================================================================
  
  # DMS Service
  dms-service:
    build:
      context: ./dms-svc
      dockerfile: Dockerfile
      target: runtime
    container_name: dms-service
    ports:
      - "9093:9093"
      - "9464:9464"  # Prometheus metrics endpoint
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=9093
      - SPRING_DATASOURCE_URL=*******************************************************************************************
      - SPRING_DATASOURCE_USERNAME=dms_user
      - SPRING_DATASOURCE_PASSWORD=dms_password
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-shared_redis_password}
      - DMS_STORAGE_PROVIDER=${DMS_STORAGE_PROVIDER:-LOCAL}
      - DMS_STORAGE_LOCAL_BASE_PATH=${DMS_STORAGE_LOCAL_BASE_PATH:-/app/storage}
      - JWT_SECRET=${JWT_SECRET:-sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      - OTEL_TRACES_EXPORTER=zipkin
      - OTEL_EXPORTER_ZIPKIN_ENDPOINT=http://zipkin:9411/api/v2/spans
      - OTEL_METRICS_EXPORTER=console
      - OTEL_LOGS_EXPORTER=console
    volumes:
      - dms_storage:/app/storage
      - dms_logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9093/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Notification Service
  notification-service:
    build:
      context: ./notification-svc
      dockerfile: Dockerfile
    container_name: notification-service
    ports:
      - "9091:9091"
    environment:
      # Database Configuration
      - SPRING_DATASOURCE_URL=***********************/${MYSQL_NOTIFICATION_DATABASE:-notification_db}?createDatabaseIfNotExist=true&useSSL=false&allowPublicKeyRetrieval=true
      - SPRING_DATASOURCE_USERNAME=${MYSQL_NOTIFICATION_USER:-notification_user}
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_NOTIFICATION_PASSWORD:-notification_password}

      # RabbitMQ Configuration
      - SPRING_RABBITMQ_HOST=rabbitmq
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=${RABBITMQ_DEFAULT_USER:-admin}
      - SPRING_RABBITMQ_PASSWORD=${RABBITMQ_DEFAULT_PASS:-admin123}

      # Redis Configuration
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-shared_redis_password}
      
      # Application Configuration
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=9091
      - JAVA_OPTS=-Xmx1g -Xms512m
      
      # Security Configuration
      - SECURITY_JWT_ENABLED=true
      - JWT_ISSUER=notification-service
      - JWT_AUDIENCE=notification-clients
      - JWT_SECRET=${JWT_SECRET:-sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      
    volumes:
      - ./notification-svc/logs:/app/logs
      - ./notification-svc/keys:/app/keys:ro
    depends_on:
      mysql:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # =============================================================================
  # SHARED INFRASTRUCTURE SERVICES
  # =============================================================================

  # Shared MySQL Database
  mysql:
    image: mysql:8.0
    container_name: grc-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      # Create multiple databases
      - MYSQL_DATABASE=${MYSQL_DMS_DATABASE:-dms_db}
      - MYSQL_USER=${MYSQL_DMS_USER:-dms_user}
      - MYSQL_PASSWORD=${MYSQL_DMS_PASSWORD:-dms_password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d:ro
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: --default-authentication-plugin=mysql_native_password

  # Shared Redis Cache
  redis:
    image: redis:7-alpine
    container_name: grc-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-shared_redis_password} --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-shared_redis_password}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker (used by Notification Service)
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: grc-rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS:-admin123}
      - RABBITMQ_DEFAULT_VHOST=/
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Elasticsearch for Search (used by DMS Service)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: grc-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # MONITORING & OBSERVABILITY SERVICES
  # =============================================================================

  # Zipkin for Distributed Tracing
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: grc-zipkin
    ports:
      - "9411:9411"
    networks:
      - grc-platform-network
    restart: unless-stopped

  # Prometheus for Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: grc-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./docker/prometheus/alerts.yml:/etc/prometheus/alerts.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - grc-platform-network
    restart: unless-stopped

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: grc-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - grc-platform-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  dms_storage:
    driver: local
  dms_logs:
    driver: local

networks:
  grc-platform-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
