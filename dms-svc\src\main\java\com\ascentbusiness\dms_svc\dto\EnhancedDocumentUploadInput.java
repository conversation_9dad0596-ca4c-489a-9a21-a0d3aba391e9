package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ProcessingStrategy;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Enhanced input DTO for document upload operations from document-upload-schema.graphqls.
 * This is the new comprehensive input type that replaces the REST endpoint functionality.
 * 
 * This class corresponds to the EnhancedDocumentUploadInput type in the GraphQL schema.
 */
@Data
public class EnhancedDocumentUploadInput {
    
    // Basic upload fields
    private MultipartFile file;
    private String name;
    private String description;
    private List<String> keywords;
    private StorageProvider storageProvider;
    private Boolean overrideFile = false;
    
    // Validation options
    private Boolean skipVirusScan = false;
    private VirusScannerType scannerType;
    private Boolean validateFileType = true;
    private List<String> allowedMimeTypes;
    private Long maxFileSize;
    
    // Processing options
    private ProcessingStrategy forceProcessingStrategy;
    private Integer chunkSize;
    private Boolean enableProgressTracking = true;
    
    // Metadata fields
    private DocumentClassificationMetadataInput classificationMetadata;
    private DocumentOwnershipMetadataInput ownershipMetadata;
    private DocumentComplianceMetadataInput complianceMetadata;
    
    // Access control
    private List<DocumentAccessRoleInput> accessRoles;
    private Boolean inheritPermissions = false;
    
    // Notification options
    private Boolean notifyOnCompletion = false;
    private String notificationEmail;
}
