@echo off
REM Local Development Environment Setup Script for Windows
REM This script sets up secure environment variables for local development

echo Setting up DMS Service Local Development Environment...
echo.

REM Database Configuration
set DB_URL=***********************************************************************************************
set DB_USERNAME=root
set DB_PASSWORD=root

REM JWT Configuration - Generate a secure random secret for local development
REM You can generate a new secret using: openssl rand -base64 64
set JWT_SECRET=localDevSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ
set JWT_EXPIRATION=86400000

REM CORS Configuration - Allow local development origins
set CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:9093

REM GraphiQL Configuration
set GRAPHIQL_ENABLED=true

REM Storage Configuration (leave empty for local development)
set S3_BUCKET_NAME=
set S3_REGION=
set S3_ACCESS_KEY=
set S3_SECRET_KEY=
set S3_ENDPOINT=

REM SharePoint Configuration (leave empty for local development)
set SHAREPOINT_CLIENT_ID=
set SHAREPOINT_CLIENT_SECRET=
set SHAREPOINT_TENANT_ID=

REM Elasticsearch Configuration (disabled for local development)
set ELASTICSEARCH_ENABLED=false
set ELASTICSEARCH_HOST=localhost
set ELASTICSEARCH_PORT=9200
set ELASTICSEARCH_USERNAME=
set ELASTICSEARCH_PASSWORD=
set ELASTICSEARCH_PROTOCOL=http

echo Environment variables set for local development.
echo.
echo To run the application with these settings:
echo   mvn spring-boot:run -Dspring.profiles.active=local
echo.
echo Or set the environment variables permanently in your system:
echo   - Windows: System Properties ^> Environment Variables
echo   - Add each variable above to your user or system environment variables
echo.
echo For production deployment, use different values and never commit secrets to version control!
echo.
pause
