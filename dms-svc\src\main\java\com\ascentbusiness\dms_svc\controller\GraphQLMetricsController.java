/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.controller;

import com.ascentbusiness.dms_svc.config.GraphQLMonitoringConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Gauge;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * REST controller for exposing GraphQL metrics and monitoring data.
 * Provides endpoints for monitoring GraphQL performance, error rates, and usage statistics.
 */
@RestController
@RequestMapping("/api/graphql/metrics")
@RequiredArgsConstructor
@Slf4j
public class GraphQLMetricsController {

    private final GraphQLMonitoringConfig monitoringConfig;
    private final MeterRegistry meterRegistry;

    /**
     * Get comprehensive GraphQL metrics summary
     */
    @GetMapping("/summary")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<GraphQLMetricsResponse> getGraphQLMetrics() {
        log.info("Retrieving GraphQL metrics summary");
        
        try {
            GraphQLMonitoringConfig.GraphQLMetricsSummary summary = monitoringConfig.getMetricsSummary();
            
            GraphQLMetricsResponse response = GraphQLMetricsResponse.builder()
                    .timestamp(Instant.now())
                    .activeQueries(summary.getActiveQueries())
                    .totalQueries(summary.getTotalQueries())
                    .totalErrors(summary.getTotalErrors())
                    .errorRate(summary.getErrorRate())
                    .executionMetrics(buildExecutionMetrics())
                    .fieldMetrics(buildFieldMetrics())
                    .errorMetrics(buildErrorMetrics())
                    .build();
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error retrieving GraphQL metrics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get GraphQL execution time metrics
     */
    @GetMapping("/execution")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<Map<String, Object>> getExecutionMetrics() {
        log.info("Retrieving GraphQL execution metrics");
        
        Map<String, Object> metrics = new HashMap<>();
        
        // Get execution time metrics
        meterRegistry.getMeters().stream()
                .filter(meter -> meter.getId().getName().equals("graphql.execution.time"))
                .filter(meter -> meter instanceof Timer)
                .forEach(meter -> {
                    Timer timer = (Timer) meter;
                    String operation = meter.getId().getTag("operation");
                    String status = meter.getId().getTag("status");
                    
                    Map<String, Object> timerMetrics = new HashMap<>();
                    timerMetrics.put("count", timer.count());
                    timerMetrics.put("totalTime", timer.totalTime(java.util.concurrent.TimeUnit.MILLISECONDS));
                    timerMetrics.put("meanTime", timer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));
                    timerMetrics.put("maxTime", timer.max(java.util.concurrent.TimeUnit.MILLISECONDS));
                    
                    metrics.put(String.format("%s_%s", operation, status), timerMetrics);
                });
        
        return ResponseEntity.ok(metrics);
    }

    /**
     * Get GraphQL field-level metrics
     */
    @GetMapping("/fields")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<Map<String, Object>> getFieldMetrics() {
        log.info("Retrieving GraphQL field metrics");
        
        Map<String, Object> metrics = new HashMap<>();
        
        // Get field fetch metrics
        meterRegistry.getMeters().stream()
                .filter(meter -> meter.getId().getName().equals("graphql.field.fetch"))
                .filter(meter -> meter instanceof Counter)
                .forEach(meter -> {
                    Counter counter = (Counter) meter;
                    String field = meter.getId().getTag("field");
                    String type = meter.getId().getTag("type");
                    String status = meter.getId().getTag("status");
                    
                    String key = String.format("%s.%s_%s", type, field, status);
                    metrics.put(key, counter.count());
                });
        
        return ResponseEntity.ok(metrics);
    }

    /**
     * Get GraphQL error metrics
     */
    @GetMapping("/errors")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<Map<String, Object>> getErrorMetrics() {
        log.info("Retrieving GraphQL error metrics");
        
        Map<String, Object> metrics = new HashMap<>();
        
        // Get error metrics
        meterRegistry.getMeters().stream()
                .filter(meter -> meter.getId().getName().startsWith("graphql.errors"))
                .filter(meter -> meter instanceof Counter)
                .forEach(meter -> {
                    Counter counter = (Counter) meter;
                    String operation = meter.getId().getTag("operation");
                    String type = meter.getId().getTag("type");
                    
                    String key = String.format("%s_%s", operation != null ? operation : "unknown", 
                                             type != null ? type : "unknown");
                    metrics.put(key, counter.count());
                });
        
        return ResponseEntity.ok(metrics);
    }

    /**
     * Get GraphQL health status
     */
    @GetMapping("/health")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'USER')")
    public ResponseEntity<GraphQLHealthResponse> getGraphQLHealth() {
        log.info("Retrieving GraphQL health status");
        
        try {
            GraphQLMonitoringConfig.GraphQLMetricsSummary summary = monitoringConfig.getMetricsSummary();
            
            // Determine health status based on error rate and active queries
            String status = "UP";
            String message = "GraphQL endpoint is healthy";
            
            if (summary.getErrorRate() > 10.0) {
                status = "DEGRADED";
                message = String.format("High error rate: %.2f%%", summary.getErrorRate());
            } else if (summary.getActiveQueries() > 100) {
                status = "DEGRADED";
                message = String.format("High query load: %d active queries", summary.getActiveQueries());
            }
            
            GraphQLHealthResponse response = GraphQLHealthResponse.builder()
                    .status(status)
                    .message(message)
                    .timestamp(Instant.now())
                    .activeQueries(summary.getActiveQueries())
                    .totalQueries(summary.getTotalQueries())
                    .errorRate(summary.getErrorRate())
                    .build();
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error retrieving GraphQL health status", e);
            
            GraphQLHealthResponse response = GraphQLHealthResponse.builder()
                    .status("DOWN")
                    .message("Error retrieving health status: " + e.getMessage())
                    .timestamp(Instant.now())
                    .activeQueries(0L)
                    .totalQueries(0L)
                    .errorRate(0.0)
                    .build();
            
            return ResponseEntity.ok(response);
        }
    }

    // Helper methods for building metrics responses

    private Map<String, Object> buildExecutionMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        meterRegistry.getMeters().stream()
                .filter(meter -> meter.getId().getName().equals("graphql.execution.time"))
                .filter(meter -> meter instanceof Timer)
                .forEach(meter -> {
                    Timer timer = (Timer) meter;
                    String operation = meter.getId().getTag("operation");

                    Map<String, Object> timerMetrics = new HashMap<>();
                    timerMetrics.put("count", timer.count());
                    timerMetrics.put("meanTime", timer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));
                    timerMetrics.put("maxTime", timer.max(java.util.concurrent.TimeUnit.MILLISECONDS));

                    metrics.put(operation != null ? operation : "unknown", timerMetrics);
                });

        return metrics;
    }

    private Map<String, Object> buildFieldMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        meterRegistry.getMeters().stream()
                .filter(meter -> meter.getId().getName().equals("graphql.field.fetch"))
                .filter(meter -> meter instanceof Counter)
                .forEach(meter -> {
                    Counter counter = (Counter) meter;
                    String field = meter.getId().getTag("field");
                    String type = meter.getId().getTag("type");

                    String key = String.format("%s.%s", type != null ? type : "unknown",
                                             field != null ? field : "unknown");
                    metrics.put(key, counter.count());
                });

        return metrics;
    }

    private Map<String, Object> buildErrorMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        meterRegistry.getMeters().stream()
                .filter(meter -> meter.getId().getName().startsWith("graphql.errors"))
                .filter(meter -> meter instanceof Counter)
                .forEach(meter -> {
                    Counter counter = (Counter) meter;
                    String operation = meter.getId().getTag("operation");

                    metrics.put(operation != null ? operation : "unknown", counter.count());
                });

        return metrics;
    }

    // Response DTOs
    
    public static class GraphQLMetricsResponse {
        private final Instant timestamp;
        private final long activeQueries;
        private final long totalQueries;
        private final long totalErrors;
        private final double errorRate;
        private final Map<String, Object> executionMetrics;
        private final Map<String, Object> fieldMetrics;
        private final Map<String, Object> errorMetrics;

        private GraphQLMetricsResponse(Instant timestamp, long activeQueries, long totalQueries,
                                      long totalErrors, double errorRate, Map<String, Object> executionMetrics,
                                      Map<String, Object> fieldMetrics, Map<String, Object> errorMetrics) {
            this.timestamp = timestamp;
            this.activeQueries = activeQueries;
            this.totalQueries = totalQueries;
            this.totalErrors = totalErrors;
            this.errorRate = errorRate;
            this.executionMetrics = executionMetrics;
            this.fieldMetrics = fieldMetrics;
            this.errorMetrics = errorMetrics;
        }

        public static Builder builder() {
            return new Builder();
        }

        // Getters
        public Instant getTimestamp() { return timestamp; }
        public long getActiveQueries() { return activeQueries; }
        public long getTotalQueries() { return totalQueries; }
        public long getTotalErrors() { return totalErrors; }
        public double getErrorRate() { return errorRate; }
        public Map<String, Object> getExecutionMetrics() { return executionMetrics; }
        public Map<String, Object> getFieldMetrics() { return fieldMetrics; }
        public Map<String, Object> getErrorMetrics() { return errorMetrics; }

        public static class Builder {
            private Instant timestamp;
            private long activeQueries;
            private long totalQueries;
            private long totalErrors;
            private double errorRate;
            private Map<String, Object> executionMetrics;
            private Map<String, Object> fieldMetrics;
            private Map<String, Object> errorMetrics;

            public Builder timestamp(Instant timestamp) { this.timestamp = timestamp; return this; }
            public Builder activeQueries(long activeQueries) { this.activeQueries = activeQueries; return this; }
            public Builder totalQueries(long totalQueries) { this.totalQueries = totalQueries; return this; }
            public Builder totalErrors(long totalErrors) { this.totalErrors = totalErrors; return this; }
            public Builder errorRate(double errorRate) { this.errorRate = errorRate; return this; }
            public Builder executionMetrics(Map<String, Object> executionMetrics) { this.executionMetrics = executionMetrics; return this; }
            public Builder fieldMetrics(Map<String, Object> fieldMetrics) { this.fieldMetrics = fieldMetrics; return this; }
            public Builder errorMetrics(Map<String, Object> errorMetrics) { this.errorMetrics = errorMetrics; return this; }

            public GraphQLMetricsResponse build() {
                return new GraphQLMetricsResponse(timestamp, activeQueries, totalQueries, totalErrors,
                        errorRate, executionMetrics, fieldMetrics, errorMetrics);
            }
        }
    }

    public static class GraphQLHealthResponse {
        private final String status;
        private final String message;
        private final Instant timestamp;
        private final long activeQueries;
        private final long totalQueries;
        private final double errorRate;

        private GraphQLHealthResponse(String status, String message, Instant timestamp,
                                     long activeQueries, long totalQueries, double errorRate) {
            this.status = status;
            this.message = message;
            this.timestamp = timestamp;
            this.activeQueries = activeQueries;
            this.totalQueries = totalQueries;
            this.errorRate = errorRate;
        }

        public static Builder builder() {
            return new Builder();
        }

        // Getters
        public String getStatus() { return status; }
        public String getMessage() { return message; }
        public Instant getTimestamp() { return timestamp; }
        public long getActiveQueries() { return activeQueries; }
        public long getTotalQueries() { return totalQueries; }
        public double getErrorRate() { return errorRate; }

        public static class Builder {
            private String status;
            private String message;
            private Instant timestamp;
            private long activeQueries;
            private long totalQueries;
            private double errorRate;

            public Builder status(String status) { this.status = status; return this; }
            public Builder message(String message) { this.message = message; return this; }
            public Builder timestamp(Instant timestamp) { this.timestamp = timestamp; return this; }
            public Builder activeQueries(long activeQueries) { this.activeQueries = activeQueries; return this; }
            public Builder totalQueries(long totalQueries) { this.totalQueries = totalQueries; return this; }
            public Builder errorRate(double errorRate) { this.errorRate = errorRate; return this; }

            public GraphQLHealthResponse build() {
                return new GraphQLHealthResponse(status, message, timestamp, activeQueries, totalQueries, errorRate);
            }
        }
    }

    /**
     * Reset GraphQL metrics (useful for testing)
     */
    @PostMapping("/reset")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, String>> resetMetrics() {
        log.info("Resetting GraphQL metrics");

        try {
            monitoringConfig.resetMetrics();

            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "GraphQL metrics reset successfully");
            response.put("timestamp", Instant.now().toString());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error resetting GraphQL metrics", e);

            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Error resetting metrics: " + e.getMessage());
            response.put("timestamp", Instant.now().toString());

            return ResponseEntity.status(500).body(response);
        }
    }
}
