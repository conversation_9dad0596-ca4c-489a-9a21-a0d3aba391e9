# Production Configuration Template
# Copy this file to application-prod.properties and set environment variables
# DO NOT commit application-prod.properties to version control

# Application Configuration
spring.application.name=dms-svc
server.port=${SERVER_PORT:8080}

# Database Configuration - Use environment variables
spring.datasource.url=${DB_URL}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration for Production
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:20}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:5}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}
spring.datasource.hikari.max-lifetime=${DB_MAX_LIFETIME:1800000}

# JWT Configuration - MUST use environment variables
dms.jwt.secret=${JWT_SECRET}
dms.jwt.expiration=${JWT_EXPIRATION:3600000}

# CORS Configuration - Restrict to specific domains
spring.graphql.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID

# Security Configuration
spring.graphql.graphiql.enabled=false
logging.level.org.springframework.security=WARN
logging.level.com.ascentbusiness.dms_svc=INFO

# SSL Configuration
server.ssl.enabled=${SSL_ENABLED:true}
server.ssl.key-store=${SSL_KEYSTORE_PATH}
server.ssl.key-store-password=${SSL_KEYSTORE_PASSWORD}
server.ssl.key-store-type=${SSL_KEYSTORE_TYPE:PKCS12}

# Actuator Security - Minimal exposure
management.endpoints.web.exposure.include=health,metrics
management.endpoint.health.show-details=never
management.endpoints.web.base-path=/actuator
management.server.port=${MANAGEMENT_PORT:8081}

# Redis Configuration for Production
spring.cache.type=redis
spring.data.redis.host=${REDIS_HOST}
spring.data.redis.port=${REDIS_PORT:6379}
spring.data.redis.password=${REDIS_PASSWORD}

# Storage Configuration
dms.storage.s3.bucket-name=${S3_BUCKET_NAME}
dms.storage.s3.region=${S3_REGION}
dms.storage.s3.access-key=${S3_ACCESS_KEY}
dms.storage.s3.secret-key=${S3_SECRET_KEY}

# SharePoint Configuration
dms.storage.sharepoint.client-id=${SHAREPOINT_CLIENT_ID}
dms.storage.sharepoint.client-secret=${SHAREPOINT_CLIENT_SECRET}
dms.storage.sharepoint.tenant-id=${SHAREPOINT_TENANT_ID}

# Elasticsearch Configuration
elasticsearch.enabled=${ELASTICSEARCH_ENABLED:true}
elasticsearch.host=${ELASTICSEARCH_HOST}
elasticsearch.port=${ELASTICSEARCH_PORT:9200}
elasticsearch.username=${ELASTICSEARCH_USERNAME}
elasticsearch.password=${ELASTICSEARCH_PASSWORD}
elasticsearch.protocol=${ELASTICSEARCH_PROTOCOL:https}
