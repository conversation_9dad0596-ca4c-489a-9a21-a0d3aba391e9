package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for file validation options input.
 * Corresponds to FileValidationOptionsInput GraphQL input type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileValidationOptionsInput {

    /**
     * Whether to skip virus scanning.
     */
    private Boolean skipVirusScan;

    /**
     * Type of virus scanner to use.
     */
    private VirusScannerType scannerType;

    /**
     * Whether to validate file type.
     */
    private Boolean validateFileType;

    /**
     * List of allowed MIME types.
     */
    private List<String> allowedMimeTypes;

    /**
     * Maximum file size allowed.
     */
    private Long maxFileSize;

    /**
     * Whether to allow duplicate files.
     */
    private Boolean allowDuplicates;
}
