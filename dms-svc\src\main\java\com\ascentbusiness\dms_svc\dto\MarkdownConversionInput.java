package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * Input DTO for Markdown conversion operations from conversion-schema.graphqls.
 */
@Data
public class MarkdownConversionInput {
    private MultipartFile file;
    private String filePath;
    private VirusScannerType scannerType;
    private ConversionOptionsInput options;
    private String outputFormat = "docx";
}
