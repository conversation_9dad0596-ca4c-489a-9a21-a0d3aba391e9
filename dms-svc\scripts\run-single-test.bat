@echo off
REM Simple script to run a single test and preserve/generate surefire-report.html
REM Usage: run-single-test.bat <TestClassName>
REM Example: run-single-test.bat ComplianceClassificationBasicTest

setlocal enabledelayedexpansion

if "%1"=="" (
    echo Usage: run-single-test.bat ^<TestClassName^>
    echo.
    echo Examples:
    echo   run-single-test.bat ComplianceClassificationBasicTest
    echo   run-single-test.bat ComplianceFrameworkBasicTest
    echo   run-single-test.bat DocumentServiceTest
    echo.
    echo This script runs a single test and ensures surefire-report.html is preserved/generated
    exit /b 1
)

set "TEST_CLASS=%1"
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "SITE_DIR=%PROJECT_ROOT%\target\site"
set "REPORT_FILE=%SITE_DIR%\surefire-report.html"

echo ========================================
echo Running Single Test: %TEST_CLASS%
echo ========================================
echo.

REM Step 1: Check if surefire-report.html exists and back it up
set "BACKUP_NEEDED=0"
if exist "%REPORT_FILE%" (
    echo [1/4] Backing up existing surefire-report.html...
    copy "%REPORT_FILE%" "%REPORT_FILE%.backup" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Existing report backed up
        set "BACKUP_NEEDED=1"
    ) else (
        echo ⚠ Failed to backup existing report
    )
) else (
    echo [1/4] No existing surefire-report.html found
)

REM Step 2: Run the test
echo [2/4] Running test: %TEST_CLASS%
echo Command: mvn test -Dtest="%TEST_CLASS%" -q
echo.

cd /d "%PROJECT_ROOT%"
call mvn test -Dtest="%TEST_CLASS%" -q
set "TEST_RESULT=%ERRORLEVEL%"

if %TEST_RESULT% EQU 0 (
    echo ✓ Test completed successfully
) else (
    echo ⚠ Test completed with issues (exit code: %TEST_RESULT%)
)

REM Step 3: Generate/restore the HTML report
echo [3/4] Ensuring surefire-report.html is available...

REM Check if XML results exist
if exist "%PROJECT_ROOT%\target\surefire-reports\TEST-*.xml" (
    echo Found XML test results, generating HTML report...
    
    REM Use PowerShell to generate the report
    powershell -ExecutionPolicy Bypass -Command "& { cd '%SCRIPT_DIR%'; .\generate-simple-report.ps1 }" 2>nul
    
    if exist "%REPORT_FILE%" (
        echo ✓ HTML report generated successfully
    ) else (
        echo ⚠ Failed to generate HTML report, trying Maven approach...
        call mvn surefire-report:report-only -q >nul 2>&1
        if exist "%REPORT_FILE%" (
            echo ✓ HTML report generated via Maven
        ) else if %BACKUP_NEEDED% EQU 1 (
            echo ⚠ Restoring backed up report...
            copy "%REPORT_FILE%.backup" "%REPORT_FILE%" >nul 2>&1
            if exist "%REPORT_FILE%" (
                echo ✓ Backed up report restored
            )
        )
    )
) else (
    echo ⚠ No XML test results found
    if %BACKUP_NEEDED% EQU 1 (
        echo Restoring backed up report...
        copy "%REPORT_FILE%.backup" "%REPORT_FILE%" >nul 2>&1
        if exist "%REPORT_FILE%" (
            echo ✓ Backed up report restored
        )
    )
)

REM Step 4: Clean up and show results
echo [4/4] Cleaning up and showing results...

REM Clean up backup file
if exist "%REPORT_FILE%.backup" (
    del "%REPORT_FILE%.backup" >nul 2>&1
)

echo.
echo ========================================
echo Test Results Summary
echo ========================================
if %TEST_RESULT% EQU 0 (
    echo ✓ Test: %TEST_CLASS% - PASSED
) else (
    echo ✗ Test: %TEST_CLASS% - FAILED (exit code: %TEST_RESULT%)
)

if exist "%REPORT_FILE%" (
    echo ✓ HTML Test Report: Available
    echo   Location: %REPORT_FILE%
    echo   URL: file:///%PROJECT_ROOT:\=/%/target/site/surefire-report.html
) else (
    echo ✗ HTML Test Report: Not Available
)

echo.
echo Individual test results:
if exist "%PROJECT_ROOT%\target\surefire-reports" (
    echo   XML Reports Directory: %PROJECT_ROOT%\target\surefire-reports\
    for /f %%f in ('dir /b "%PROJECT_ROOT%\target\surefire-reports\TEST-*.xml" 2^>nul ^| find /c /v ""') do (
        if %%f GTR 0 (
            echo   Total XML files: %%f
            echo   Latest test files:
            for /f %%g in ('dir /b /o:d "%PROJECT_ROOT%\target\surefire-reports\TEST-*.xml" 2^>nul') do (
                echo     - %%g
            )
        ) else (
            echo   No XML test files found
        )
    )
) else (
    echo   No surefire-reports directory found
)

echo ========================================

exit /b %TEST_RESULT%
