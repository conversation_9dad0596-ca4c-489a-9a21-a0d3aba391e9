# Error Resolution Guide

This guide provides detailed explanations and solutions for specific error codes and exceptions in the DMS system.

## Table of Contents

1. [HTTP Status Codes](#http-status-codes)
2. [Application Exceptions](#application-exceptions)
3. [Database Exceptions](#database-exceptions)
4. [Security Exceptions](#security-exceptions)
5. [Storage Exceptions](#storage-exceptions)
6. [Integration Exceptions](#integration-exceptions)
7. [System Exceptions](#system-exceptions)

## HTTP Status Codes

### 400 Bad Request

**Error Code:** `INVALID_INPUT`
**Message:** "Invalid request parameters"

**Common Causes:**
- Missing required parameters
- Invalid parameter format
- Malformed JSON in request body

**Resolution Steps:**
1. **Validate request parameters:**
   ```bash
   # Check GraphQL query syntax
   curl -X POST http://localhost:9092/graphql \
     -H "Content-Type: application/json" \
     -d '{"query":"{ __schema { types { name } } }"}'
   ```

2. **Review API documentation:**
   - Check required fields in input types
   - Verify parameter data types
   - Ensure proper JSON formatting

3. **Enable request logging:**
   ```properties
   logging.level.org.springframework.web=DEBUG
   ```

**Step-by-Step Solution Process:**
- **Step 1**: Identify the specific parameter causing the issue
- **Step 2**: Validate the parameter format against API documentation
- **Step 3**: Test with corrected parameters
- **Step 4**: Verify the request succeeds

### 401 Unauthorized

**Error Code:** `UNAUTHORIZED`
**Message:** "JWT token is missing or invalid"

**Common Causes:**
- Missing Authorization header
- Expired JWT token
- Invalid JWT signature
- Malformed token

**Resolution Steps:**
1. **Generate new JWT token:**
   ```graphql
   mutation {
     generateTestToken(input: {
       userId: "test-user"
       roles: ["USER"]
       permissions: ["READ", "WRITE"]
       expirationMinutes: 60
     }) {
       token
       expiresAt
     }
   }
   ```

2. **Verify token format:**
   ```bash
   # Token should be in format: Bearer <token>
   curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   ```

3. **Check JWT configuration:**
   ```properties
   jwt.secret=your-secret-key
   jwt.expiration=3600000
   ```

**Step-by-Step Solution Process:**
- **Step 1**: Check if Authorization header is present in the request
- **Step 2**: Verify the token format (should start with "Bearer ")
- **Step 3**: Decode the JWT token to check expiration
- **Step 4**: Generate a new token if expired
- **Step 5**: Test the request with the new token

### 403 Forbidden

**Error Code:** `FORBIDDEN`
**Message:** "Insufficient permissions to access this resource"

**Common Causes:**
- User lacks required role
- Missing specific permission
- Document-level access denied
- Role hierarchy issues

**Resolution Steps:**
1. **Check user permissions:**
   ```sql
   SELECT dp.* FROM document_permissions dp
   WHERE dp.document_id = ? AND dp.user_id = ?;
   ```

2. **Verify role assignments:**
   ```bash
   # Decode JWT token to check roles
   echo "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." | base64 -d
   ```

3. **Grant required permissions:**
   ```graphql
   mutation {
     grantDocumentPermission(input: {
       documentId: "123"
       userId: "<EMAIL>"
       permissionType: READ
     })
   }
   ```

**Step-by-Step Solution Process:**
- **Step 1**: Identify the specific permission required for the operation
- **Step 2**: Check current user permissions in the database
- **Step 3**: Verify user roles in the JWT token
- **Step 4**: Grant missing permissions if authorized
- **Step 5**: Test the operation again with updated permissions

### 404 Not Found

**Error Code:** `DOCUMENT_NOT_FOUND`
**Message:** "Document with ID 123 not found"

**Common Causes:**
- Document ID doesn't exist
- Document was deleted
- User doesn't have access to document
- Database connectivity issues

**Solutions:**
1. **Verify document exists:**
   ```sql
   SELECT * FROM documents WHERE id = 123;
   ```

2. **Check document status:**
   ```sql
   SELECT id, name, status FROM documents WHERE id = 123;
   ```

3. **Verify user access:**
   ```sql
   SELECT d.*, dp.permission_type 
   FROM documents d 
   LEFT JOIN document_permissions dp ON d.id = dp.document_id 
   WHERE d.id = 123 AND (d.creator_user_id = '<EMAIL>' OR dp.user_id = '<EMAIL>');
   ```

### 500 Internal Server Error

**Error Code:** `INTERNAL_SERVER_ERROR`
**Message:** "An unexpected error occurred"

**Common Causes:**
- Unhandled exceptions
- Database connection failures
- External service unavailability
- Configuration errors

**Solutions:**
1. **Check application logs:**
   ```bash
   tail -f logs/dms-service.log | grep ERROR
   ```

2. **Verify system health:**
   ```bash
   curl http://localhost:9092/actuator/health
   ```

3. **Check correlation ID for detailed tracking:**
   ```bash
   grep "correlation-id-from-error" logs/dms-service.log
   ```

## Application Exceptions

### DuplicateFileException

**Message:** "An ACTIVE document named 'filename.pdf' with identical content already exists"

**Cause:** Attempting to upload a file with identical content when overrideFile is false

**Solutions:**
1. **Use overrideFile parameter:**
   ```graphql
   mutation {
     uploadDocument(input: {
       file: $file
       name: "Document Name"
       overrideFile: true
     })
   }
   ```

2. **Check existing documents:**
   ```sql
   SELECT id, name, original_file_name, status, created_date 
   FROM documents 
   WHERE original_file_name = 'filename.pdf' AND status = 'ACTIVE';
   ```

3. **Delete or archive existing document:**
   ```graphql
   mutation {
     deleteDocument(id: "existing-document-id")
   }
   ```

### DocumentNotFoundException

**Message:** "Document not found with id: 123"

**Cause:** Requested document doesn't exist or user doesn't have access

**Solutions:**
1. **Verify document ID:**
   ```sql
   SELECT COUNT(*) FROM documents WHERE id = 123;
   ```

2. **Check user permissions:**
   ```sql
   SELECT * FROM document_permissions 
   WHERE document_id = 123 AND user_id = 'current-user';
   ```

3. **Use correct document ID:**
   ```graphql
   query {
     searchDocuments(filter: { name: "document-name" }) {
       content { id name }
     }
   }
   ```

### UnauthorizedException

**Message:** "You don't have permission to access this document. READ permission required."

**Cause:** User lacks required permission for the operation

**Solutions:**
1. **Check required permissions:**
   - READ: View document content
   - WRITE: Upload new documents
   - UPDATE: Modify existing documents
   - DELETE: Remove documents
   - ADMIN: Administrative operations

2. **Grant permissions:**
   ```graphql
   mutation {
     grantDocumentPermission(input: {
       documentId: "123"
       userId: "<EMAIL>"
       permissionType: READ
     })
   }
   ```

3. **Verify role hierarchy:**
   ```properties
   # ADMIN role includes all permissions
   # MANAGER role includes READ, WRITE, UPDATE
   # USER role includes READ, WRITE
   ```

### HistoricalDocumentException

**Message:** "Cannot create new version of a HISTORICAL document"

**Cause:** Attempting to modify a document marked as HISTORICAL

**Solutions:**
1. **Check document status:**
   ```sql
   SELECT id, name, status, version FROM documents WHERE id = 123;
   ```

2. **Find current active version:**
   ```sql
   SELECT * FROM documents 
   WHERE (parent_document_id = 123 OR id = 123) AND status = 'ACTIVE';
   ```

3. **Use active version for modifications:**
   ```graphql
   mutation {
     uploadDocumentNewVersion(input: {
       documentId: "active-version-id"
       file: $file
     })
   }
   ```

## Database Exceptions

### DataIntegrityViolationException

**Message:** "Duplicate entry 'value' for key 'unique_constraint'"

**Cause:** Attempting to insert duplicate data that violates unique constraints

**Solutions:**
1. **Check existing data:**
   ```sql
   SELECT * FROM table_name WHERE unique_field = 'value';
   ```

2. **Update instead of insert:**
   ```sql
   UPDATE table_name SET field = 'new_value' WHERE id = 123;
   ```

3. **Use ON DUPLICATE KEY UPDATE:**
   ```sql
   INSERT INTO table_name (field1, field2) VALUES ('value1', 'value2')
   ON DUPLICATE KEY UPDATE field2 = VALUES(field2);
   ```

### QueryTimeoutException

**Message:** "Query execution was interrupted, maximum statement execution time exceeded"

**Cause:** Database query taking too long to execute

**Solutions:**
1. **Optimize query with indexes:**
   ```sql
   EXPLAIN SELECT * FROM documents WHERE name LIKE '%search%';
   CREATE INDEX idx_documents_name ON documents(name);
   ```

2. **Increase query timeout:**
   ```properties
   spring.datasource.hikari.connection-timeout=60000
   spring.jpa.properties.javax.persistence.query.timeout=30000
   ```

3. **Review query performance:**
   ```sql
   SHOW PROCESSLIST;
   SELECT * FROM information_schema.processlist WHERE time > 10;
   ```

### ConnectionPoolExhaustedException

**Message:** "Connection is not available, request timed out after 30000ms"

**Cause:** All database connections in pool are in use

**Solutions:**
1. **Increase pool size:**
   ```properties
   spring.datasource.hikari.maximum-pool-size=20
   spring.datasource.hikari.minimum-idle=5
   ```

2. **Check for connection leaks:**
   ```sql
   SHOW PROCESSLIST;
   SELECT COUNT(*) as connection_count FROM information_schema.processlist;
   ```

3. **Monitor connection usage:**
   ```bash
   curl http://localhost:9092/actuator/metrics/hikaricp.connections.active
   ```

## Security Exceptions

### JwtAuthenticationException

**Message:** "JWT token validation failed"

**Cause:** Invalid or corrupted JWT token

**Solutions:**
1. **Validate token structure:**
   ```bash
   # JWT should have 3 parts separated by dots
   echo "token" | tr '.' '\n' | wc -l  # Should return 3
   ```

2. **Check token expiration:**
   ```bash
   # Decode payload (second part of JWT)
   echo "payload-part" | base64 -d
   ```

3. **Verify signing key:**
   ```properties
   jwt.secret=your-secret-key-must-match
   ```

### SecurityViolationException

**Message:** "Security violation detected: UNAUTHORIZED_ACCESS"

**Cause:** Suspicious activity or security policy violation

**Solutions:**
1. **Review security logs:**
   ```sql
   SELECT * FROM security_violations 
   WHERE user_id = '<EMAIL>' 
   ORDER BY timestamp DESC LIMIT 10;
   ```

2. **Check violation details:**
   ```sql
   SELECT violation_type, violation_details, severity 
   FROM security_violations 
   WHERE id = 'violation-id';
   ```

3. **Resolve violation:**
   ```graphql
   mutation {
     resolveSecurityViolation(
       violationId: "violation-id"
       resolvedBy: "<EMAIL>"
       resolutionNotes: "False positive - legitimate access"
     )
   }
   ```

## Storage Exceptions

### StorageException

**Message:** "Failed to store file in S3: Access Denied"

**Cause:** Storage provider configuration or permission issues

**Solutions:**
1. **Verify S3 credentials:**
   ```bash
   aws s3 ls s3://your-bucket-name
   ```

2. **Check IAM permissions:**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"],
         "Resource": "arn:aws:s3:::your-bucket/*"
       }
     ]
   }
   ```

3. **Test connectivity:**
   ```bash
   curl -I https://s3.amazonaws.com
   ```

### FileNotFoundException

**Message:** "Source file not found: /path/to/file.pdf"

**Cause:** File path doesn't exist or is inaccessible

**Solutions:**
1. **Verify file exists:**
   ```bash
   ls -la /path/to/file.pdf
   ```

2. **Check file permissions:**
   ```bash
   stat /path/to/file.pdf
   ```

3. **Use absolute path:**
   ```bash
   realpath /path/to/file.pdf
   ```

## Integration Exceptions

### ElasticsearchException

**Message:** "NoNodeAvailableException: None of the configured nodes are available"

**Cause:** Elasticsearch cluster is unavailable

**Solutions:**
1. **Check Elasticsearch status:**
   ```bash
   curl http://localhost:9200/_cluster/health
   ```

2. **Start Elasticsearch:**
   ```bash
   docker run -d --name elasticsearch -p 9200:9200 elasticsearch:7.17.0
   ```

3. **Verify configuration:**
   ```properties
   elasticsearch.host=localhost
   elasticsearch.port=9200
   ```

### RedisConnectionException

**Message:** "Unable to connect to Redis"

**Cause:** Redis server is unavailable or misconfigured

**Solutions:**
1. **Start Redis:**
   ```bash
   redis-server
   ```

2. **Test connection:**
   ```bash
   redis-cli ping
   ```

3. **Check configuration:**
   ```properties
   spring.redis.host=localhost
   spring.redis.port=6379
   ```

## System Exceptions

### OutOfMemoryError

**Message:** "java.lang.OutOfMemoryError: Java heap space"

**Cause:** JVM heap memory exhausted

**Solutions:**
1. **Increase heap size:**
   ```bash
   java -Xmx4g -Xms2g -jar dms-service.jar
   ```

2. **Enable heap dump on OOM:**
   ```bash
   java -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heapdump.hprof
   ```

3. **Analyze memory usage:**
   ```bash
   jstat -gc [PID] 5s
   jmap -histo [PID]
   ```

### StackOverflowError

**Message:** "java.lang.StackOverflowError"

**Cause:** Infinite recursion or very deep call stack

**Solutions:**
1. **Increase stack size:**
   ```bash
   java -Xss2m -jar dms-service.jar
   ```

2. **Review recursive methods:**
   - Check for infinite loops
   - Optimize recursive algorithms
   - Add base cases to recursion

3. **Analyze stack trace:**
   ```bash
   grep -A 50 "StackOverflowError" logs/dms-service.log
   ```

---

**Last Updated**: December 2024
**Version**: 1.0.0

For additional error codes or complex issues, consult the development team or create a support ticket with the correlation ID from the error response.
