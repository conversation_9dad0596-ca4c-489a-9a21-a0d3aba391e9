# ===== DOCUMENT SHARING SCHEMA =====

# Share link types
type DocumentShareLink {
  id: ID!
  linkId: String!
  document: Document!
  createdByUserId: String!
  permission: Permission!
  targetUserId: String
  targetRoleName: String
  createdAt: DateTime!
  expiresAt: DateTime!
  isActive: Boolean!
  hasPassword: Boolean!
  maxUses: Int
  useCount: Int!
  notes: String
  shareUrl: String!
}

input CreateShareLinkInput {
  permission: Permission!
  targetUserId: String
  targetRoleName: String
  expiresAt: DateTime
  password: String
  maxUses: Int
  notes: String
}

type ShareLinkResponse {
  success: Boolean!
  message: String
  shareLink: DocumentShareLink
}

# Bulk share types
input BulkShareInput {
  documentIds: [ID!]!
  recipientIds: [String!]!
  roleNames: [String!]!
  permission: Permission!
  expiresAt: DateTime
  notes: String
}

type BulkShareItem {
  documentId: ID!
  document: Document
  recipientId: String!
  isRole: Boolean!
  isSuccessful: Boolean!
  errorMessage: String
  shareLinkId: String
  shareUrl: String
}

type BulkShareOperation {
  id: ID!
  operationId: String!
  createdByUserId: String!
  createdAt: DateTime!
  permission: Permission!
  expiresAt: DateTime!
  notes: String
  isCompleted: Boolean!
  completedAt: DateTime
  totalDocuments: Int!
  totalRecipients: Int!
  successCount: Int!
  failureCount: Int!
  items: [BulkShareItem!]!
}

type BulkShareResponse {
  success: Boolean!
  message: String
  operation: BulkShareOperation
  items: [BulkShareItem!]
}

# Public access types
input AccessSharedDocumentInput {
  linkId: String!
  password: String
}

type AccessSharedDocumentResponse {
  success: Boolean!
  message: String
  document: Document
  requiresPassword: Boolean
  permission: Permission
}

# Add to existing Document type
extend type Document {
  shareLinks: [DocumentShareLink!]
}

# Add to existing Query type
extend type Query {
  # Get share links for a document
  documentShareLinks(documentId: ID!): [DocumentShareLink!]!
  
  # Get a specific share link
  shareLink(linkId: String!): DocumentShareLink
  
  # Get all bulk share operations for the current user
  myBulkShareOperations: [BulkShareOperation!]!
  
  # Get a specific bulk share operation
  bulkShareOperation(operationId: String!): BulkShareOperation
  
  # Access a shared document via link
  accessSharedDocument(input: AccessSharedDocumentInput!): AccessSharedDocumentResponse!
}

# Add to existing Mutation type
extend type Mutation {
  # Create a share link for a document
  createDocumentShareLink(documentId: ID!, input: CreateShareLinkInput!): ShareLinkResponse!
  
  # Revoke a share link
  revokeDocumentShareLink(linkId: String!): ShareLinkResponse!
  
  # Bulk share documents
  bulkShareDocuments(input: BulkShareInput!): BulkShareResponse!
}
