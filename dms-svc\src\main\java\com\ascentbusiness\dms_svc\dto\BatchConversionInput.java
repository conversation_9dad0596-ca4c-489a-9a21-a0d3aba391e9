package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionType;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Input DTO for batch conversion operations from conversion-schema.graphqls.
 */
@Data
public class BatchConversionInput {
    private List<MultipartFile> files;
    private List<String> filePaths;
    private ConversionType conversionType;
    private VirusScannerType scannerType;
    private ConversionOptionsInput options;
    private String outputFormat;
    private Integer maxConcurrentConversions = 3;
    private Boolean continueOnError = true;
    private Boolean notifyOnCompletion = false;
    private String notificationEmail;
}
