--liquibase formatted sql

--changeset dms:014-create-storage-configurations-table

-- Create storage_configurations table for database-driven storage provider configuration
CREATE TABLE IF NOT EXISTS storage_configurations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    provider_type ENUM('LOCAL', 'S3', 'SHAREPOINT') NOT NULL UNIQUE,
    is_active BOOLEAN NOT NULL DEFAULT FALSE,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    configuration_json JSON NOT NULL,
    description TEXT,
    priority INT NOT NULL DEFAULT 0,
    health_check_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    last_health_check TIMESTAMP NULL,
    health_status VARCHAR(255),
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    last_modified_by VARC<PERSON><PERSON>(255),
    
    INDEX idx_provider_type (provider_type),
    INDEX idx_is_active (is_active),
    INDEX idx_is_default (is_default),
    INDEX idx_priority (priority),
    INDEX idx_health_status (health_status),
    INDEX idx_created_date (created_date),
    INDEX idx_last_modified_date (last_modified_date)
) COMMENT = 'Storage provider configurations managed in database for runtime flexibility';

-- Insert default LOCAL storage configuration
INSERT IGNORE INTO storage_configurations (
    provider_type, 
    is_active, 
    is_default, 
    configuration_json, 
    description, 
    priority,
    health_status,
    created_by
) VALUES (
    'LOCAL',
    TRUE,
    TRUE,
    JSON_OBJECT(
        'basePath', './storage/documents',
        'createDirectories', true,
        'filePermissions', '644',
        'maxFileSize', 104857600
    ),
    'Default local file system storage configuration',
    100,
    'HEALTHY',
    'system'
);

-- Insert sample S3 configuration (inactive by default)
INSERT IGNORE INTO storage_configurations (
    provider_type, 
    is_active, 
    is_default, 
    configuration_json, 
    description, 
    priority,
    health_status,
    created_by
) VALUES (
    'S3',
    FALSE,
    FALSE,
    JSON_OBJECT(
        'bucketName', 'dms-documents',
        'region', 'us-east-1',
        'accessKey', 'YOUR_ACCESS_KEY',
        'secretKey', 'YOUR_SECRET_KEY',
        'endpoint', '',
        'pathStyleAccess', false,
        'storageClass', 'STANDARD',
        'serverSideEncryption', true
    ),
    'AWS S3 storage configuration - Update with your credentials',
    90,
    'UNCHECKED',
    'system'
);

-- Insert sample SharePoint configuration (inactive by default)
INSERT IGNORE INTO storage_configurations (
    provider_type, 
    is_active, 
    is_default, 
    configuration_json, 
    description, 
    priority,
    health_status,
    created_by
) VALUES (
    'SHAREPOINT',
    FALSE,
    FALSE,
    JSON_OBJECT(
        'tenantId', 'YOUR_TENANT_ID',
        'clientId', 'YOUR_CLIENT_ID',
        'clientSecret', 'YOUR_CLIENT_SECRET',
        'siteUrl', 'https://yourcompany.sharepoint.com/sites/dms',
        'documentLibrary', 'Documents',
        'driveId', '',
        'useApplicationPermissions', true
    ),
    'Microsoft SharePoint storage configuration - Update with your credentials',
    80,
    'UNCHECKED',
    'system'
);

-- Note: MySQL doesn't support subqueries in CHECK constraints
-- The single default constraint will be enforced at the application level
-- through the StorageConfigurationService
