package com.ascentbusiness.dms_svc.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;

/**
 * Entity representing template usage history for analytics
 */
@Entity
@Table(name = "template_usage_history")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateUsageHistory extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", nullable = false)
    @JsonIgnore
    private DocumentTemplate template;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id")
    @JsonIgnore
    private Document document;

    // Usage details
    @Column(name = "used_by_user_id", nullable = false, length = 255)
    private String usedByUserId;

    @Column(name = "used_by_department", length = 255)
    private String usedByDepartment;

    @Column(name = "usage_type", nullable = false, length = 50)
    private String usageType; // CREATE_DOCUMENT, PREVIEW, DOWNLOAD, COPY

    // Context information
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "field_values", columnDefinition = "JSON")
    private JsonNode fieldValues; // Values filled in template fields

    @Column(name = "generation_time_ms")
    private Integer generationTimeMs; // Time taken to generate document

    @Builder.Default
    @Column(name = "success", nullable = false)
    private Boolean success = true;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    // Correlation and tracking
    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    @Column(name = "session_id", length = 100)
    private String sessionId;

    @Column(name = "usage_date", nullable = false)
    @Builder.Default
    private LocalDateTime usageDate = LocalDateTime.now();

    /**
     * Check if the usage was successful
     */
    @Transient
    public boolean isSuccessful() {
        return success != null && success;
    }

    /**
     * Check if the usage failed
     */
    @Transient
    public boolean isFailed() {
        return !isSuccessful();
    }

    /**
     * Check if this usage created a document
     */
    @Transient
    public boolean createdDocument() {
        return "CREATE_DOCUMENT".equals(usageType) && document != null;
    }

    /**
     * Check if this was a preview usage
     */
    @Transient
    public boolean isPreview() {
        return "PREVIEW".equals(usageType);
    }

    /**
     * Check if this was a download usage
     */
    @Transient
    public boolean isDownload() {
        return "DOWNLOAD".equals(usageType);
    }

    /**
     * Check if field values were provided
     */
    @Transient
    public boolean hasFieldValues() {
        return fieldValues != null && !fieldValues.isNull() && fieldValues.size() > 0;
    }

    /**
     * Get the template name
     */
    @Transient
    public String getTemplateName() {
        return template != null ? template.getName() : null;
    }

    /**
     * Get the document name if created
     */
    @Transient
    public String getDocumentName() {
        return document != null ? document.getName() : null;
    }

    /**
     * Get generation time in seconds
     */
    @Transient
    public double getGenerationTimeSeconds() {
        return generationTimeMs != null ? generationTimeMs / 1000.0 : 0.0;
    }

    /**
     * Check if generation was fast (under 1 second)
     */
    @Transient
    public boolean isFastGeneration() {
        return generationTimeMs != null && generationTimeMs < 1000;
    }

    /**
     * Check if generation was slow (over 10 seconds)
     */
    @Transient
    public boolean isSlowGeneration() {
        return generationTimeMs != null && generationTimeMs > 10000;
    }

    /**
     * Get usage type display name
     */
    @Transient
    public String getUsageTypeDisplayName() {
        if (usageType == null) return "Unknown";
        
        switch (usageType.toUpperCase()) {
            case "CREATE_DOCUMENT":
                return "Create Document";
            case "PREVIEW":
                return "Preview";
            case "DOWNLOAD":
                return "Download";
            case "COPY":
                return "Copy Template";
            default:
                return usageType;
        }
    }

    @Override
    public String toString() {
        return String.format("TemplateUsageHistory{id=%d, template='%s', user='%s', type='%s', success=%s}", 
                           getId(), getTemplateName(), usedByUserId, usageType, success);
    }
}
