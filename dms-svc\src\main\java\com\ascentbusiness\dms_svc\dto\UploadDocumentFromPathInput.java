package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;

import java.util.List;

@Data
public class UploadDocumentFromPathInput {
    private String name;
    private String description;
    private String userId;                                  // Optional - will be filled from security context if not provided
    private Boolean overrideFile = false;                  // Default to false
    private StorageProvider storageProvider;
    private String sourceFilePath;                        // Full path on server to be read
    private List<DocumentAccessRoleInput> accessRoles;   // Uses GraphQL DTO
    private List<String> keywords;                        // Renamed from tags to match GraphQL schema

    // Virus scanning configuration
    private VirusScannerType scannerType; // Optional - will use default scanner if not provided

    // Metadata fields (optional)
    private DocumentClassificationMetadataInput classificationMetadata;
    private DocumentOwnershipMetadataInput ownershipMetadata;
    private DocumentComplianceMetadataInput complianceMetadata;
}
