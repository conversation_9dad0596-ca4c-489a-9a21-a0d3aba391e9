package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Faceted search results for filtering and navigation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchFacets {

    private List<FacetCount> documentTypes;
    private List<FacetCount> storageProviders;
    private List<FacetCount> creators;
    private List<FacetCount> modules;
    private List<FacetCount> businessUnits;
    private List<FacetCount> confidentialityLevels;
    private List<FacetCount> mimeTypes;
    private List<FacetCount> tags;
    private List<FacetCount> languages;
    private List<FacetCount> years;
}
