package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.exception.*;
import com.ascentbusiness.dms_svc.util.ExceptionUtil;
import graphql.ErrorType;
import graphql.GraphQLError;
import graphql.schema.DataFetchingEnvironment;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.execution.DataFetcherExceptionResolverAdapter;
import org.springframework.lang.NonNull;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class GraphQLExceptionHandler extends DataFetcherExceptionResolverAdapter {

    private static final Logger logger = LoggerFactory.getLogger(GraphQLExceptionHandler.class);

    @Override
    protected GraphQLError resolveToSingleError(@NonNull Throwable ex, @NonNull DataFetchingEnvironment env) {
        // Handle specific DMS exceptions first (before general DmsException handling)
        // This ensures specific error codes are used instead of generic ones

        if (ex instanceof UnauthorizedException) {
            logger.warn("Authorization error in GraphQL operation: {} - {}",
                    env.getField().getName(), ex.getMessage());

            return GraphQLError.newError()
                    .errorType(ErrorType.DataFetchingException)
                    .message(ex.getMessage())
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "FORBIDDEN",
                            "type", "AUTHORIZATION_ERROR",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }

        // Handle JWT token validation exceptions (legacy support)
        if (ex instanceof InvalidTokenException) {
            logger.warn("Invalid JWT token in GraphQL operation: {} - {}",
                    env.getField().getName(), ex.getMessage());

            return GraphQLError.newError()
                    .errorType(ErrorType.DataFetchingException)
                    .message(ex.getMessage())
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "INVALID_TOKEN",
                            "type", "AUTHENTICATION_ERROR",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }

        // Handle Spring Security authentication exceptions
        if (ex instanceof AuthenticationException) {
            logger.warn("Authentication error in GraphQL operation: {} - {}",
                    env.getField().getName(), ex.getMessage());

            return GraphQLError.newError()
                    .errorType(ErrorType.DataFetchingException)
                    .message("Authentication required or invalid credentials")
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "AUTHENTICATION_REQUIRED",
                            "type", "AUTHENTICATION_ERROR",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }

        // Handle Spring Security access denied exceptions
        if (ex instanceof AccessDeniedException) {
            logger.warn("Access denied in GraphQL operation: {} - {}",
                    env.getField().getName(), ex.getMessage());

            return GraphQLError.newError()
                    .errorType(ErrorType.DataFetchingException)
                    .message("Access denied - insufficient permissions")
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "ACCESS_DENIED",
                            "type", "AUTHORIZATION_ERROR",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }

        // Handle DMS exceptions with structured error information
        // This comes after specific exception handling to avoid overriding specific error codes
        if (ex instanceof DmsException dmsEx) {
            return handleDmsException(dmsEx, env);
        }
        
        if (ex instanceof DocumentNotFoundException) {
            logger.warn("Document not found in GraphQL operation: {} - {}", 
                    env.getField().getName(), ex.getMessage());
            
            return GraphQLError.newError()
                    .errorType(ErrorType.DataFetchingException)
                    .message(ex.getMessage())
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "NOT_FOUND",
                            "type", "DOCUMENT_NOT_FOUND",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }
        
        if (ex instanceof DuplicateFileException) {
            logger.info("Duplicate file detected in GraphQL operation: {} - {}", 
                    env.getField().getName(), ex.getMessage());
            
            return GraphQLError.newError()
                    .errorType(ErrorType.ValidationError)
                    .message(ex.getMessage())
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "DUPLICATE_FILE",
                            "type", "VALIDATION_ERROR",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }
        
        if (ex instanceof HistoricalDocumentException) {
            logger.info("Historical document versioning attempted in GraphQL operation: {} - {}", 
                    env.getField().getName(), ex.getMessage());
            
            return GraphQLError.newError()
                    .errorType(ErrorType.ValidationError)
                    .message(ex.getMessage())
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "HISTORICAL_DOCUMENT",
                            "type", "VALIDATION_ERROR",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }
        
        if (ex instanceof IllegalArgumentException) {
            logger.warn("Invalid argument in GraphQL operation: {} - {}", 
                    env.getField().getName(), ex.getMessage());
            
            return GraphQLError.newError()
                    .errorType(ErrorType.ValidationError)
                    .message(ex.getMessage())
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "INVALID_ARGUMENT",
                            "type", "VALIDATION_ERROR",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }
        
        if (ex instanceof IllegalStateException) {
            logger.warn("Invalid state in GraphQL operation: {} - {}", 
                    env.getField().getName(), ex.getMessage());
            
            return GraphQLError.newError()
                    .errorType(ErrorType.ValidationError)
                    .message(ex.getMessage())
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "INVALID_STATE",
                            "type", "VALIDATION_ERROR",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }
        
        // Handle file I/O exceptions (like file not found on server)
        if (ex instanceof java.io.IOException) {
            logger.error("I/O error in GraphQL operation: {} - {}", 
                    env.getField().getName(), ex.getMessage());
            
            return GraphQLError.newError()
                    .errorType(ErrorType.DataFetchingException)
                    .message("File operation failed: " + ex.getMessage())
                    .location(env.getField().getSourceLocation())
                    .path(env.getExecutionStepInfo().getPath())
                    .extensions(Map.of(
                            "code", "FILE_ERROR",
                            "type", "IO_ERROR",
                            "operation", env.getField().getName()
                    ))
                    .build();
        }
        
        // For any other unexpected exceptions, log and return a generic error
        logger.error("Unexpected error in GraphQL operation: {} - {}", 
                env.getField().getName(), ex.getMessage(), ex);
        
        return GraphQLError.newError()
                .errorType(ErrorType.DataFetchingException)
                .message("An unexpected error occurred. Please try again later.")
                .location(env.getField().getSourceLocation())
                .path(env.getExecutionStepInfo().getPath())
                .extensions(Map.of(
                        "code", "INTERNAL_ERROR",
                        "type", "SYSTEM_ERROR",
                        "operation", env.getField().getName()
                ))
                .build();
    }

    /**
     * Handle DmsException with structured error information and proper logging
     */
    private GraphQLError handleDmsException(DmsException ex, DataFetchingEnvironment env) {
        // Log the exception using the utility
        ExceptionUtil.logDmsException(ex, env.getField().getName());

        // Determine GraphQL error type based on DMS exception category
        ErrorType errorType = switch (ex.getCategory()) {
            case VALIDATION -> ErrorType.ValidationError;
            case AUTHENTICATION, AUTHORIZATION -> ErrorType.DataFetchingException;
            case BUSINESS_RULE -> ErrorType.ValidationError;
            case SYSTEM, EXTERNAL_SERVICE -> ErrorType.DataFetchingException;
            case DATA_INTEGRITY -> ErrorType.DataFetchingException;
        };

        // Create extensions with structured error information
        Map<String, Object> extensions = Map.of(
            "code", ex.getErrorCode(),
            "type", ex.getCategory().toString(),
            "severity", ex.getSeverity().toString(),
            "correlationId", ex.getCorrelationId(),
            "timestamp", ex.getTimestamp().toString(),
            "operation", env.getField().getName(),
            "details", ex.getErrorDetails()
        );

        return GraphQLError.newError()
                .errorType(errorType)
                .message(ex.getMessage())
                .location(env.getField().getSourceLocation())
                .path(env.getExecutionStepInfo().getPath())
                .extensions(extensions)
                .build();
    }
}
