# ===== TEMPLATE MANAGEMENT SCHEMA =====
# GraphQL schema for document template operations (replacing DocumentTemplateController REST endpoints)
# Note: DocumentTemplate, TemplateType, TemplateApprovalStatus, TemplateAccessLevel are defined in business-features-schema.graphqls

# Enhanced Template Types (extending existing DocumentTemplate)
extend type DocumentTemplate {
  # Enhanced computed fields (isPublished and isPendingApproval already defined in base type)
  canEdit: Boolean!
  canDelete: Boolean!
  canPublish: Boolean!
  canApprove: Boolean!
  
  # Usage analytics
  usageHistory: [TemplateUsageHistory!]!
  recentUsage: [TemplateUsageHistory!]!
  popularityScore: Float!
  
  # Template content and preview
  templateContentBase64: String
  previewUrl: String
  downloadUrl: String
  
  # Validation and health
  validationStatus: TemplateValidationStatus!
  validationErrors: [TemplateValidationError!]!
  healthScore: Float!
}

# Template Usage History
type TemplateUsageHistory {
  id: ID!
  template: DocumentTemplate!
  usedByUserId: String!
  usageType: TemplateUsageType!
  fieldValues: String # JSON string
  success: Boolean!
  errorMessage: String
  usageDate: DateTime!
  correlationId: String
  processingTimeMs: Long
  documentCreated: Document
}

enum TemplateUsageType {
  DOCUMENT_CREATION
  PREVIEW
  DOWNLOAD
  VALIDATION
  EXPORT
}

# Template Validation
type TemplateValidationStatus {
  isValid: Boolean!
  lastValidated: DateTime!
  validatedBy: String
  validationVersion: String!
  # Additional fields expected by tests
  validationErrors: [TemplateValidationError!]!
  warnings: [String!]!
  recommendations: [String!]!
  validatedAt: DateTime!
}

type TemplateValidationError {
  code: String!
  message: String!
  severity: ValidationSeverity!
  field: String
  line: Int
  column: Int
  suggestion: String
}

# Template Statistics Types
type TemplateStatistics {
  totalTemplates: Long!
  totalActive: Long!
  totalDraft: Long!
  totalPending: Long!
  totalPublished: Long!
  totalArchived: Long!
  
  categoryStatistics: [TemplateCategoryStats!]!
  typeStatistics: [TemplateTypeStats!]!
  approvalStatistics: [TemplateApprovalStats!]!
  usageStatistics: [TemplateUsageStats!]!
  
  topCategories: [TemplateCategoryStats!]!
  mostUsedTemplates: [DocumentTemplate!]!
  recentlyCreated: [DocumentTemplate!]!
  pendingApproval: [TemplateApprovalStats!]!
}

type TemplateCategoryStats {
  category: String!
  count: Long!
  percentage: Float!
  averageUsage: Float!
}

type TemplateTypeStats {
  templateType: TemplateType!
  count: Long!
  percentage: Float!
  averageUsage: Float!
}

type TemplateApprovalStats {
  approvalStatus: TemplateApprovalStatus!
  count: Long!
  percentage: Float!
}

type TemplateUsageStats {
  period: String!
  usageCount: Long!
  uniqueUsers: Long!
  documentsCreated: Long!
  averageProcessingTime: Float!
  # Additional fields for statistics queries
  totalUsage: Long!
  averageUsagePerTemplate: Float!
  mostUsedTemplateId: ID
  leastUsedTemplateId: ID
}

# Template Search and Filter Types
type TemplateSearchResult {
  templates: [DocumentTemplate!]!
  totalCount: Long!
  facets: TemplateSearchFacets!
  suggestions: [String!]!
}

type TemplateSearchFacets {
  categories: [FacetCount!]!
  templateTypes: [FacetCount!]!
  approvalStatuses: [FacetCount!]!
  accessLevels: [FacetCount!]!
  owners: [FacetCount!]!
}

type FacetCount {
  value: String!
  count: Long!
}

# Template Preview Types
type TemplatePreview {
  templateId: ID!
  content: String! # Base64 encoded
  mimeType: String!
  fileName: String!
  fileSize: Long!
  previewUrl: String
  generatedAt: DateTime!
  expiresAt: DateTime!
}

# Input Types for Template Management
input TemplateSearchInput {
  query: String
  categories: [String!]
  templateTypes: [TemplateType!]
  approvalStatuses: [TemplateApprovalStatus!]
  accessLevels: [TemplateAccessLevel!]
  ownerUserIds: [String!]
  departments: [String!]
  isActive: Boolean
  isPublic: Boolean
  dateFrom: DateTime
  dateTo: DateTime
  minUsageCount: Int
  maxUsageCount: Int
  sortBy: TemplateSortField = LAST_MODIFIED_DATE
  sortDirection: SortDirection = DESC
  pagination: PaginationInput
}

enum SortDirection {
  ASC
  DESC
}

enum TemplateSortField {
  NAME
  CREATED_DATE
  LAST_MODIFIED_DATE
  USAGE_COUNT
  LAST_USED_DATE
  CATEGORY
  APPROVAL_STATUS
}

input TemplateFilterInput {
  categories: [String!]
  templateTypes: [TemplateType!]
  approvalStatuses: [TemplateApprovalStatus!]
  accessLevels: [TemplateAccessLevel!]
  ownerUserIds: [String!]
  departments: [String!]
  isActive: Boolean
  isPublic: Boolean
  isSystemTemplate: Boolean
}

input CreateDocumentFromTemplateInput {
  templateId: ID!
  fieldValues: JSON! # JSON object of field values (key-value pairs)
  documentName: String
  documentDescription: String
  outputFormat: String # Output format for the generated document
  storageProvider: StorageProvider
  keywords: [String!]
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}

input TemplatePreviewInput {
  templateId: ID!
  fieldValues: String! # JSON string of field values
  outputFormat: String # Optional format override
}

input TemplateBulkActionInput {
  templateIds: [ID!]!
  action: TemplateBulkAction!
  parameters: TemplateBulkActionParameters # Action-specific parameters
}

input TemplateBulkActionParameters {
  publishComments: String
  approvalComments: String
  deactivationReason: String
  deletionReason: String
}

enum TemplateBulkAction {
  PUBLISH
  UNPUBLISH
  APPROVE
  REJECT
  ACTIVATE
  DEACTIVATE
  DELETE
  CHANGE_CATEGORY
  CHANGE_ACCESS_LEVEL
  EXPORT
}

# Template Management Result Types
type TemplateBulkActionResult {
  totalProcessed: Int!
  successfulActions: Int!
  failedActions: Int!
  results: [TemplateActionResult!]!
  processingTimeMs: Long!
  overallStatus: String!
}

type TemplateActionResult {
  templateId: ID!
  templateName: String!
  success: Boolean!
  errorMessage: String
  newStatus: String
}

type TemplateActionError {
  templateId: ID!
  templateName: String!
  errorCode: String!
  errorMessage: String!
}

# Query Extensions
extend type Query {
  # Template retrieval queries (replacing REST endpoints)
  getDocumentTemplate(id: ID!): DocumentTemplate
  getDocumentTemplates(
    pagination: TemplatePaginationInput
    filter: TemplateFilterInput
  ): DocumentTemplatePage!
  
  # Access-based queries
  getAccessibleTemplates(
    userId: String
    department: String
  ): [DocumentTemplate!]!
  getPublishedTemplates: [DocumentTemplate!]!
  getTemplatesPendingApproval: [DocumentTemplate!]!
  
  # Search and filtering
  searchTemplates(input: TemplateSearchInput!): TemplateSearchResult!
  getTemplatesByCategory(category: String!): [DocumentTemplate!]!
  getTemplatesByType(templateType: TemplateType!): [DocumentTemplate!]!
  getTemplatesByOwner(
    ownerUserId: String!
    pagination: TemplatePaginationInput
  ): DocumentTemplatePage!
  
  # Analytics and statistics
  getTemplateStatistics(
    dateFrom: DateTime
    dateTo: DateTime
    categories: [String!]
  ): TemplateStatistics!
  getPopularTemplates(
    minUsageCount: Int = 5
    limit: Int = 10
  ): [DocumentTemplate!]!
  getRecentlyUsedTemplates(
    daysBack: Int = 30
    limit: Int = 10
  ): [DocumentTemplate!]!
  
  # Template usage and history
  getTemplateUsageHistory(
    templateId: ID!
    pagination: PaginationInput
  ): TemplateUsageHistoryPage!
  getTemplateUsageStatistics(
    templateId: ID!
    dateFrom: DateTime
    dateTo: DateTime
  ): TemplateUsageStats!
  
  # Template validation and health
  validateTemplate(templateId: ID!): TemplateValidationStatus!
  getTemplateHealth(templateId: ID!): Float!
}

# Additional Page Types
type TemplateUsageHistoryPage {
  content: [TemplateUsageHistory!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}

# Mutation Extensions
extend type Mutation {
  # Template CRUD operations (replacing REST endpoints)
  createDocumentTemplate(input: DocumentTemplateInput!): DocumentTemplate!
  updateDocumentTemplate(id: ID!, input: DocumentTemplateInput!): DocumentTemplate!
  deleteDocumentTemplate(id: ID!): Boolean!
  
  # Template lifecycle management
  publishTemplate(id: ID!): DocumentTemplate!
  unpublishTemplate(id: ID!): DocumentTemplate!
  approveTemplate(id: ID!, comments: String): DocumentTemplate!
  rejectTemplate(id: ID!, reason: String!): DocumentTemplate!
  activateTemplate(id: ID!): DocumentTemplate!
  deactivateTemplate(id: ID!): DocumentTemplate!
  
  # Document generation from templates
  createDocumentFromTemplate(input: CreateDocumentFromTemplateInput!): Document!
  previewTemplate(input: TemplatePreviewInput!): TemplatePreview!
  
  # Bulk operations
  bulkTemplateAction(input: TemplateBulkActionInput!): TemplateBulkActionResult!
  
  # Template management utilities
  duplicateTemplate(id: ID!, newName: String!): DocumentTemplate!
  exportTemplate(id: ID!, format: String = "JSON"): String!
  importTemplate(templateData: String!, format: String = "JSON"): DocumentTemplate!
  
  # Template validation and optimization
  validateTemplateContent(id: ID!): TemplateValidationStatus!
  optimizeTemplate(id: ID!): DocumentTemplate!
  
  # Template usage tracking
  recordTemplateUsage(
    templateId: ID!
    usageType: TemplateUsageType!
    metadata: String
  ): TemplateUsageHistory!
}
