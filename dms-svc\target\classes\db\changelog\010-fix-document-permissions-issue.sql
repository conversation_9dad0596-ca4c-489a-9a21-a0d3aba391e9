--liquibase formatted sql

--changeset dms:010-fix-document-permissions-issue

-- Insert sample document with ID 6 for testing (if it doesn't exist)
INSERT IGNORE INTO documents (
    id, name, description, original_file_name, file_size, mime_type, 
    storage_provider, storage_path, status, version, tags, 
    creator_user_id, created_date, last_modified_date
) VALUES 
(6, 'Strategic Plan 2026', 'Strategic planning document for 2026', 'strategic-plan-2026.pdf', 5024576, 'application/pdf', 
 'LOCAL', '/storage/documents/strategic-plan-2026.pdf', 'ACTIVE', 1, '["strategic", "planning", "confidential"]', 
 'admin', NOW(), NOW());

-- Grant permissions to user 'anurag' for document ID 6
-- This allows anurag to create new versions of the document
INSERT IGNORE INTO document_permissions (
    document_id, user_id, role_name, permission_type, granted_by, 
    is_active, created_date, last_modified_date, created_by, last_modified_by
) VALUES 
(6, 'anurag', NULL, 'WRITE', 'admin', true, NOW(), NOW(), 'admin', 'admin'),
(6, 'anurag', NULL, 'READ', 'admin', true, NOW(), NOW(), 'admin', 'admin');

-- Grant role-based permissions for ADMIN and USER roles to document ID 6
INSERT IGNORE INTO document_permissions (
    document_id, user_id, role_name, permission_type, granted_by, 
    is_active, created_date, last_modified_date, created_by, last_modified_by
) VALUES 
(6, NULL, 'ADMIN', 'WRITE', 'admin', true, NOW(), NOW(), 'admin', 'admin'),
(6, NULL, 'ADMIN', 'READ', 'admin', true, NOW(), NOW(), 'admin', 'admin'),
(6, NULL, 'USER', 'READ', 'admin', true, NOW(), NOW(), 'admin', 'admin');

-- Add additional test documents with proper permissions for comprehensive testing
INSERT IGNORE INTO documents (
    id, name, description, original_file_name, file_size, mime_type, 
    storage_provider, storage_path, status, version, tags, 
    creator_user_id, created_date, last_modified_date
) VALUES 
(7, 'Test Document for Anurag', 'Document owned by anurag for testing', 'anurag-test-doc.pdf', 1024576, 'application/pdf', 
 'LOCAL', '/storage/documents/anurag-test-doc.pdf', 'ACTIVE', 1, '["test", "anurag"]', 
 'anurag', NOW(), NOW()),
(8, 'Shared Collaboration Document', 'Document for collaborative work', 'collab-doc.docx', 2048576, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 
 'LOCAL', '/storage/documents/collab-doc.docx', 'ACTIVE', 1, '["collaboration", "shared"]', 
 'admin', NOW(), NOW());

-- Grant comprehensive permissions for document 8 (collaborative document)
INSERT IGNORE INTO document_permissions (
    document_id, user_id, role_name, permission_type, granted_by, 
    is_active, created_date, last_modified_date, created_by, last_modified_by
) VALUES 
-- User-specific permissions for anurag on document 8
(8, 'anurag', NULL, 'WRITE', 'admin', true, NOW(), NOW(), 'admin', 'admin'),
(8, 'anurag', NULL, 'READ', 'admin', true, NOW(), NOW(), 'admin', 'admin'),
-- Role-based permissions for document 8
(8, NULL, 'USER', 'WRITE', 'admin', true, NOW(), NOW(), 'admin', 'admin'),
(8, NULL, 'USER', 'READ', 'admin', true, NOW(), NOW(), 'admin', 'admin'),
(8, NULL, 'ADMIN', 'WRITE', 'admin', true, NOW(), NOW(), 'admin', 'admin'),
(8, NULL, 'ADMIN', 'READ', 'admin', true, NOW(), NOW(), 'admin', 'admin'),
(8, NULL, 'ADMIN', 'DELETE', 'admin', true, NOW(), NOW(), 'admin', 'admin');

-- Add audit log entries for the permission grants
INSERT IGNORE INTO audit_logs (
    document_id, user_id, action, details, ip_address, user_agent, correlation_id, timestamp
) VALUES 
(6, 'admin', 'UPDATE', 'Permissions granted to user anurag: READ, WRITE', '*************', 'System', 'fix-001', NOW()),
(6, 'admin', 'UPDATE', 'Role-based permissions granted: ADMIN (READ, WRITE), USER (READ)', '*************', 'System', 'fix-002', NOW()),
(7, 'anurag', 'UPLOAD', 'Test document created for anurag', '*************', 'System', 'fix-003', NOW()),
(8, 'admin', 'UPLOAD', 'Collaborative document created with comprehensive permissions', '*************', 'System', 'fix-004', NOW());
