--liquibase formatted sql

--changeset system:070-fix-workflow-stages-audit-columns
--comment: Add missing created_by and last_modified_by columns to workflow_stages table to match BaseEntity

-- Add missing audit columns to workflow_stages table
ALTER TABLE workflow_stages 
ADD COLUMN created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT 'system' AFTER last_modified_date,
ADD COLUMN last_modified_by VARC<PERSON>R(255) AFTER created_by;

-- Update the default value for existing records
UPDATE workflow_stages SET created_by = 'system' WHERE created_by IS NULL;
UPDATE workflow_stages SET last_modified_by = 'system' WHERE last_modified_by IS NULL;

-- Remove the default constraint after updating existing records
ALTER TABLE workflow_stages ALTER COLUMN created_by DROP DEFAULT;