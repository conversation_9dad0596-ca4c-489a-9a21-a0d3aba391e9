--liquibase formatted sql

--changeset dms:006-insert-sample-data

-- Insert sample documents for testing (using INSERT IGNORE to avoid duplicates)
INSERT IGNORE INTO documents (
    id, name, description, original_file_name, file_size, mime_type, 
    storage_provider, storage_path, status, version, tags, 
    creator_user_id, created_date, last_modified_date
) VALUES 
(1, 'Sample Document 1', 'This is a test document', 'test-document.pdf', 1024576, 'application/pdf', 
 'LOCAL', '/storage/documents/test-document.pdf', 'ACTIVE', 1, '["document", "test", "sample"]', 
 'user123', NOW(), NOW()),
(2, 'Sample Document 2', 'Another test document', 'test-image.jpg', 2048000, 'image/jpeg', 
 'LOCAL', '/storage/documents/test-image.jpg', 'ACTIVE', 1, '["image", "test"]', 
 'user456', NOW(), NOW()),
(3, 'Sample Document 3', 'Third test document', 'test-spreadsheet.xlsx', 512000, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
 'S3', '/s3/bucket/test-spreadsheet.xlsx', 'ACTIVE', 1, '["spreadsheet", "excel", "data"]', 
 'user789', NOW(), NOW());

-- Insert sample audit logs (using INSERT IGNORE to avoid duplicates)
INSERT IGNORE INTO audit_logs (
    document_id, user_id, action, details, ip_address, user_agent, correlation_id, timestamp
) VALUES 
(1, 'user123', 'UPLOAD', 'Document uploaded successfully', '*************', 'Mozilla/5.0', 'corr-001', NOW()),
(2, 'user456', 'UPLOAD', 'Image document uploaded', '*************', 'Mozilla/5.0', 'corr-002', NOW()),
(3, 'user789', 'UPLOAD', 'Spreadsheet uploaded to S3', '*************', 'Mozilla/5.0', 'corr-003', NOW());
