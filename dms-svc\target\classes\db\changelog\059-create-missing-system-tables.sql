--liquibase formatted sql

--changeset system:059-create-missing-system-tables
--comment: Create missing system tables: system_events and event_subscriptions

-- Create system_events table
CREATE TABLE system_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    event_category VARCHAR(50) NOT NULL,
    event_name VARCHAR(255) NOT NULL,
    source_entity_type VARCHAR(50),
    source_entity_id BIGINT,
    event_data JSON,
    event_metadata JSON,
    actor_user_id VARCHAR(255),
    actor_type VARCHAR(50),
    correlation_id VARCHAR(100),
    session_id VARCHAR(100),
    request_id VARCHAR(100),
    event_timestamp DATETIME NOT NULL,
    processing_status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    webhook_delivery_count INT DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON>HAR(255),
    last_modified_by <PERSON>RCHA<PERSON>(255),
    
    INDEX idx_system_events_type (event_type),
    INDEX idx_system_events_category (event_category),
    INDEX idx_system_events_timestamp (event_timestamp),
    INDEX idx_system_events_status (processing_status),
    INDEX idx_system_events_actor (actor_user_id),
    INDEX idx_system_events_correlation (correlation_id)
);

-- Create event_subscriptions table
CREATE TABLE event_subscriptions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    event_types JSON NOT NULL,
    event_filters JSON,
    subscriber_type VARCHAR(50) NOT NULL,
    subscriber_config JSON NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_paused BOOLEAN NOT NULL DEFAULT FALSE,
    batch_size INT DEFAULT 1,
    batch_timeout_seconds INT DEFAULT 300,
    delivery_mode VARCHAR(50) NOT NULL DEFAULT 'IMMEDIATE',
    schedule_cron VARCHAR(100),
    schedule_timezone VARCHAR(50) DEFAULT 'UTC',
    events_processed INT DEFAULT 0,
    last_processed_date DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    last_modified_by VARCHAR(255),
    
    INDEX idx_event_subscriptions_type (subscriber_type),
    INDEX idx_event_subscriptions_active (is_active),
    INDEX idx_event_subscriptions_mode (delivery_mode),
    INDEX idx_event_subscriptions_created_by (created_by)
);