--liquibase formatted sql

--changeset dms:009-consolidate-permissions-tables

-- Step 1: Create new unified document_permissions table with enhanced structure
DROP TABLE IF EXISTS document_permissions_new;

CREATE TABLE document_permissions_new (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    user_id VARCHAR(100),                    -- For individual user permissions
    role_name VARCHAR(100),                  -- For role-based permissions  
    permission_type ENUM('READ', 'WRITE', 'DELETE', 'ADMIN') NOT NULL,
    granted_by VARCHAR(100) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE, -- Enable/disable permissions
    expires_at TIMESTAMP NULL,               -- Optional expiration
    notes TEXT,                              -- Additional context
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by <PERSON><PERSON><PERSON><PERSON>(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    -- Either user_id OR role_name should be specified, not both
    CHECK ((user_id IS NOT NULL AND role_name IS NULL) OR (user_id IS NULL AND role_name IS NOT NULL))
);

-- Step 2: Migrate data from existing document_permissions table
INSERT INTO document_permissions_new (
    document_id, user_id, role_name, permission_type, granted_by, 
    is_active, expires_at, notes, created_by, created_date, last_modified_by, last_modified_date
)
SELECT 
    document_id,
    user_id,
    role_name,
    permission as permission_type,
    granted_by_user_id as granted_by,
    TRUE as is_active,
    NULL as expires_at,
    'Migrated from document_permissions table' as notes,
    granted_by_user_id as created_by,
    created_date,
    granted_by_user_id as last_modified_by,
    last_modified_date
FROM document_permissions;

-- Step 3: Migrate data from existing document_access_roles table
INSERT INTO document_permissions_new (
    document_id, user_id, role_name, permission_type, granted_by, 
    is_active, expires_at, notes, created_by, created_date, last_modified_by, last_modified_date
)
SELECT 
    document_id,
    user_id,
    role_name,
    permission_type,
    granted_by,
    is_active,
    expires_at,
    CONCAT('Migrated from document_access_roles table', 
           CASE WHEN notes IS NOT NULL THEN CONCAT(' - ', notes) ELSE '' END) as notes,
    created_by,
    created_date,
    last_modified_by,
    last_modified_date
FROM document_access_roles;

-- Step 4: Drop old tables
DROP TABLE document_permissions;
DROP TABLE document_access_roles;

-- Step 5: Rename new table to final name
RENAME TABLE document_permissions_new TO document_permissions;

-- Step 6: Create indexes for better performance
CREATE INDEX idx_document_permissions_document_id ON document_permissions(document_id);
CREATE INDEX idx_document_permissions_user_id ON document_permissions(user_id);
CREATE INDEX idx_document_permissions_role_name ON document_permissions(role_name);
CREATE INDEX idx_document_permissions_permission_type ON document_permissions(permission_type);
CREATE INDEX idx_document_permissions_is_active ON document_permissions(is_active);
CREATE INDEX idx_document_permissions_expires_at ON document_permissions(expires_at);
CREATE INDEX idx_document_permissions_granted_by ON document_permissions(granted_by);
CREATE INDEX idx_document_permissions_created_date ON document_permissions(created_date);

-- Step 7: Create unique constraint to prevent duplicate permissions
-- This allows either user_id OR role_name to have a unique permission on a document
ALTER TABLE document_permissions 
ADD CONSTRAINT uk_document_user_permission 
UNIQUE (document_id, user_id, permission_type, is_active);

ALTER TABLE document_permissions 
ADD CONSTRAINT uk_document_role_permission 
UNIQUE (document_id, role_name, permission_type, is_active);
