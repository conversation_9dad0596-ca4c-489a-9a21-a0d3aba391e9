package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO for conversion error information from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionError {
    private String fileName;
    private String errorCode;
    private String errorMessage;
    private String details;
    private LocalDateTime timestamp;
}
