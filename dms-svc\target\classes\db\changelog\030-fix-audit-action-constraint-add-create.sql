--liquibase formatted sql

--changeset dms:030-fix-audit-action-constraint-add-create

-- Fix the audit_logs action constraint to include 'CREATE' action
-- The Java AuditAction enum has CREATE but the database constraint doesn't allow it

-- Drop the existing constraint
ALTER TABLE audit_logs DROP CONSTRAINT chk_audit_action;

-- Recreate the constraint with CREATE included
ALTER TABLE audit_logs 
ADD CONSTRAINT chk_audit_action 
CHECK (action IN (
    -- Basic document operations
    'CREATE', 'READ', 'UPLOAD', 'DOWNLOAD', 'DELETE', 'UPDATE', 'VERSION_CREATE',
    -- Permission operations
    'PERMISSION_GRANTED', 'PERMISSION_REVOKED', 'PERMISSION_EXPIRED', 'PERMISSION_CHECKED',
    -- Security events
    'SECURITY_VIOLATION', 'TOKEN_VALIDATION_FAILED', 'RATE_LIMIT_EXCEEDED',
    -- Migration operations
    'MIGRATION_STARTED', 'MIGRATION_COMPLETED', 'MI<PERSON><PERSON><PERSON>_FAILED',
    'MIGRATION_FILE_PROCESSED', 'MI<PERSON>ATION_FILE_VERIFIED', 'MIGRATION_FILE_CLEANUP',
    'MIGRATION_SECURITY_CHECK', 'MIGRATION_VALIDATION_FAILED',
    -- Retention policy operations
    'RETENTION_POLICY_CREATED', 'RETENTION_POLICY_UPDATED', 'RETENTION_POLICY_DELETED',
    'RETENTION_POLICY_ASSIGNED', 'RETENTION_POLICY_UNASSIGNED', 'RETENTION_EXPIRY_CALCULATED',
    'RETENTION_PROCESSING_STARTED', 'RETENTION_PROCESSING_COMPLETED', 'RETENTION_PROCESSING_FAILED',
    'DOCUMENT_ARCHIVED', 'DOCUMENT_DISPOSED', 'DOCUMENT_RESTORED',
    'LEGAL_HOLD_APPLIED', 'LEGAL_HOLD_RELEASED', 'LEGAL_HOLD_EXPIRED',
    -- Compliance operations
    'COMPLIANCE_FRAMEWORK_CREATED', 'COMPLIANCE_FRAMEWORK_UPDATED', 'COMPLIANCE_FRAMEWORK_DELETED',
    'COMPLIANCE_FRAMEWORK_ACTIVATED', 'COMPLIANCE_FRAMEWORK_DEACTIVATED',
    'COMPLIANCE_CLASSIFICATION_ASSIGNED', 'COMPLIANCE_CLASSIFICATION_UPDATED', 'COMPLIANCE_CLASSIFICATION_REMOVED',
    'COMPLIANCE_VALIDATION_PASSED', 'COMPLIANCE_VALIDATION_FAILED', 'COMPLIANCE_VIOLATION_DETECTED',
    'REGULATION_MAPPING_CREATED', 'REGULATION_MAPPING_UPDATED', 'REGULATION_MAPPING_DELETED',
    'DATA_SUBJECT_REQUEST_RECEIVED', 'DATA_SUBJECT_REQUEST_PROCESSED', 'DATA_SUBJECT_REQUEST_COMPLETED',
    'DATA_SUBJECT_CONSENT_GRANTED', 'DATA_SUBJECT_CONSENT_REVOKED', 'DATA_SUBJECT_CONSENT_EXPIRED',
    'GDPR_RIGHT_TO_BE_FORGOTTEN', 'GDPR_DATA_PORTABILITY', 'GDPR_ACCESS_REQUEST',
    -- Audit system operations
    'AUDIT_ENCRYPTION_ENABLED', 'AUDIT_ENCRYPTION_DISABLED', 'AUDIT_ENCRYPTION_KEY_GENERATED',
    'AUDIT_ENCRYPTION_KEY_ROTATED', 'AUDIT_ENCRYPTION_ERROR', 'AUDIT_TAMPER_DETECTED',
    'AUDIT_CHAIN_VERIFIED', 'AUDIT_CHAIN_BROKEN', 'AUDIT_EXPORT_STARTED',
    'AUDIT_EXPORT_COMPLETED', 'AUDIT_EXPORT_FAILED', 'AUDIT_SIGNATURE_VERIFIED',
    'AUDIT_SIGNATURE_FAILED', 'AUDIT_ARCHIVE_CREATED', 'AUDIT_ARCHIVE_RESTORED',
    -- Monitoring and observability
    'HEALTH_CHECK_PASSED', 'HEALTH_CHECK_FAILED', 'PERFORMANCE_THRESHOLD_EXCEEDED',
    'SYSTEM_STARTUP', 'SYSTEM_SHUTDOWN', 'CONFIGURATION_CHANGED',
    -- Business features
    'WORKFLOW_STARTED', 'WORKFLOW_COMPLETED', 'WORKFLOW_FAILED',
    'TEMPLATE_CREATED', 'TEMPLATE_UPDATED', 'TEMPLATE_DELETED', 'TEMPLATE_APPLIED',
    'WEBHOOK_TRIGGERED', 'WEBHOOK_DELIVERED', 'WEBHOOK_FAILED',
    'EVENT_PUBLISHED', 'EVENT_CONSUMED', 'EVENT_FAILED',
    'SHAREPOINT_SYNC_STARTED', 'SHAREPOINT_SYNC_COMPLETED', 'SHAREPOINT_SYNC_FAILED'
));

-- Add comment to document the change
ALTER TABLE audit_logs 
MODIFY COLUMN action VARCHAR(50) NOT NULL 
COMMENT 'Action performed - supports all AuditAction enum values including CREATE';
