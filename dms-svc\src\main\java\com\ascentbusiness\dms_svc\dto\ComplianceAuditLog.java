package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.AuditLog;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO for compliance-specific audit log information
 * Used in GraphQL compliance audit operations
 */
@Data
@Builder
public class ComplianceAuditLog {
    private String id;
    private AuditLog auditLog;
    private String complianceStandard;
    private String controlId;
    private String requirementId;
    private ComplianceStatus complianceStatus;
    private RiskLevel riskLevel;
    private String findings;
    private String recommendations;
    private String reviewedBy;
    private LocalDateTime reviewedAt;
    private LocalDateTime dueDate;
    private Boolean isOverdue;

    /**
     * Create ComplianceAuditLog from AuditLog entity
     * Maps audit log data to compliance-specific structure
     */
    public static ComplianceAuditLog fromAuditLog(AuditLog auditLog) {
        return ComplianceAuditLog.builder()
                .id(auditLog.getId().toString())
                .auditLog(auditLog)
                .complianceStandard(extractComplianceStandard(auditLog))
                .controlId(extractControlId(auditLog))
                .requirementId(extractRequirementId(auditLog))
                .complianceStatus(determineComplianceStatus(auditLog))
                .riskLevel(determineRiskLevel(auditLog))
                .findings(extractFindings(auditLog))
                .recommendations(extractRecommendations(auditLog))
                .reviewedBy(auditLog.getUserId())
                .reviewedAt(auditLog.getTimestamp() != null ? auditLog.getTimestamp().toLocalDateTime() : null)
                .dueDate(calculateDueDate(auditLog))
                .isOverdue(isOverdue(auditLog))
                .build();
    }

    private static String extractComplianceStandard(AuditLog auditLog) {
        // Extract compliance standard from audit log metadata or use default
        if (auditLog.getComplianceFrameworkId() != null) {
            return "FRAMEWORK_" + auditLog.getComplianceFrameworkId();
        }
        return "GDPR"; // Default compliance standard
    }

    private static String extractControlId(AuditLog auditLog) {
        // Extract control ID from audit log or generate based on action
        return "CTRL_" + auditLog.getAction().name() + "_001";
    }

    private static String extractRequirementId(AuditLog auditLog) {
        // Extract requirement ID or generate based on action
        return "REQ_" + auditLog.getAction().name() + "_001";
    }

    private static ComplianceStatus determineComplianceStatus(AuditLog auditLog) {
        // Determine compliance status based on audit log success and action
        if (auditLog.getSuccess()) {
            return ComplianceStatus.COMPLIANT;
        } else {
            return ComplianceStatus.NON_COMPLIANT;
        }
    }

    private static RiskLevel determineRiskLevel(AuditLog auditLog) {
        // Determine risk level based on audit action and context
        switch (auditLog.getAction()) {
            case SECURITY_VIOLATION:
                return RiskLevel.CRITICAL;
            case DELETE:
            case PERMISSION_REVOKED:
                return RiskLevel.HIGH;
            case UPDATE:
            case PERMISSION_GRANTED:
                return RiskLevel.MEDIUM;
            default:
                return RiskLevel.LOW;
        }
    }

    private static String extractFindings(AuditLog auditLog) {
        // Extract findings from audit log details
        if (!auditLog.getSuccess() && auditLog.getDetails() != null) {
            return "Issue identified: " + auditLog.getDetails();
        }
        return "No issues identified";
    }

    private static String extractRecommendations(AuditLog auditLog) {
        // Generate recommendations based on audit findings
        if (!auditLog.getSuccess()) {
            return "Review and remediate the identified issue";
        }
        return "Continue monitoring";
    }

    private static LocalDateTime calculateDueDate(AuditLog auditLog) {
        // Calculate due date based on risk level and action
        LocalDateTime timestamp = auditLog.getTimestamp() != null ? 
            auditLog.getTimestamp().toLocalDateTime() : LocalDateTime.now();
        
        switch (auditLog.getAction()) {
            case SECURITY_VIOLATION:
                return timestamp.plusDays(1); // Critical - 1 day
            case DELETE:
            case PERMISSION_REVOKED:
                return timestamp.plusDays(7); // High - 1 week
            default:
                return timestamp.plusDays(30); // Normal - 1 month
        }
    }

    private static Boolean isOverdue(AuditLog auditLog) {
        LocalDateTime dueDate = calculateDueDate(auditLog);
        return LocalDateTime.now().isAfter(dueDate);
    }

    public enum ComplianceStatus {
        COMPLIANT,
        NON_COMPLIANT,
        PARTIALLY_COMPLIANT,
        UNDER_REVIEW,
        REMEDIATION_REQUIRED
    }

    public enum RiskLevel {
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL
    }
}
