#!/bin/bash

# DMS Performance Testing Suite
# This script runs comprehensive performance tests using multiple tools

set -e

# Configuration
BASE_URL="${BASE_URL:-http://localhost:8080}"
RESULTS_DIR="performance-results-$(date +%Y%m%d_%H%M%S)"
TEST_DURATION="${TEST_DURATION:-300}"
MAX_USERS="${MAX_USERS:-50}"
RAMP_UP_TIME="${RAMP_UP_TIME:-60}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if application is running
    if ! curl -f -s "${BASE_URL}/dms/actuator/health" > /dev/null; then
        error "DMS application is not running at ${BASE_URL}"
        error "Please start the application before running performance tests"
        exit 1
    fi
    
    # Check for K6
    if ! command -v k6 &> /dev/null; then
        warning "K6 not found. K6 tests will be skipped."
        warning "Install K6 from: https://k6.io/docs/getting-started/installation/"
    fi
    
    # Check for JMeter
    if ! command -v jmeter &> /dev/null; then
        warning "JMeter not found. JMeter tests will be skipped."
        warning "Install JMeter from: https://jmeter.apache.org/download_jmeter.cgi"
    fi
    
    # Check for Maven
    if ! command -v mvn &> /dev/null; then
        error "Maven not found. Maven is required for running Java performance tests."
        exit 1
    fi
    
    success "Prerequisites check completed"
}

# Setup test environment
setup_test_environment() {
    log "Setting up test environment..."
    
    # Create results directory
    mkdir -p "${RESULTS_DIR}"
    mkdir -p "${RESULTS_DIR}/k6"
    mkdir -p "${RESULTS_DIR}/jmeter"
    mkdir -p "${RESULTS_DIR}/maven"
    mkdir -p "${RESULTS_DIR}/reports"
    
    # Create test data files
    create_test_data
    
    success "Test environment setup completed"
}

# Create test data
create_test_data() {
    log "Creating test data files..."
    
    # Create CSV file with test users
    cat > "${RESULTS_DIR}/test-users.csv" << EOF
username,token
testuser1,eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlcjEiLCJpYXQiOjE2MzAwMDAwMDAsImV4cCI6MTYzMDAwMzYwMH0.test
testuser2,eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlcjIiLCJpYXQiOjE2MzAwMDAwMDAsImV4cCI6MTYzMDAwMzYwMH0.test
testuser3,eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlcjMiLCJpYXQiOjE2MzAwMDAwMDAsImV4cCI6MTYzMDAwMzYwMH0.test
testuser4,eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlcjQiLCJpYXQiOjE2MzAwMDAwMDAsImV4cCI6MTYzMDAwMzYwMH0.test
testuser5,eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlcjUiLCJpYXQiOjE2MzAwMDAwMDAsImV4cCI6MTYzMDAwMzYwMH0.test
EOF
    
    success "Test data files created"
}

# Run K6 performance tests
run_k6_tests() {
    if ! command -v k6 &> /dev/null; then
        warning "Skipping K6 tests - K6 not installed"
        return
    fi
    
    log "Running K6 performance tests..."
    
    local k6_script="src/test/resources/performance/k6/document-operations-load-test.js"
    
    if [ ! -f "$k6_script" ]; then
        error "K6 script not found: $k6_script"
        return
    fi
    
    # Run K6 test with different scenarios
    local scenarios=("smoke" "load" "stress" "spike")
    
    for scenario in "${scenarios[@]}"; do
        log "Running K6 $scenario test..."
        
        case $scenario in
            "smoke")
                k6 run --vus 1 --duration 30s \
                   --out json="${RESULTS_DIR}/k6/k6-${scenario}-results.json" \
                   --env BASE_URL="${BASE_URL}" \
                   "$k6_script" || warning "K6 $scenario test failed"
                ;;
            "load")
                k6 run --vus 10 --duration "${TEST_DURATION}s" \
                   --out json="${RESULTS_DIR}/k6/k6-${scenario}-results.json" \
                   --env BASE_URL="${BASE_URL}" \
                   "$k6_script" || warning "K6 $scenario test failed"
                ;;
            "stress")
                k6 run --vus "${MAX_USERS}" --duration "${TEST_DURATION}s" \
                   --out json="${RESULTS_DIR}/k6/k6-${scenario}-results.json" \
                   --env BASE_URL="${BASE_URL}" \
                   "$k6_script" || warning "K6 $scenario test failed"
                ;;
            "spike")
                k6 run --stage 10s:1 --stage 10s:"${MAX_USERS}" --stage 10s:1 \
                   --out json="${RESULTS_DIR}/k6/k6-${scenario}-results.json" \
                   --env BASE_URL="${BASE_URL}" \
                   "$k6_script" || warning "K6 $scenario test failed"
                ;;
        esac
        
        sleep 30 # Cool down between tests
    done
    
    success "K6 tests completed"
}

# Run JMeter performance tests
run_jmeter_tests() {
    if ! command -v jmeter &> /dev/null; then
        warning "Skipping JMeter tests - JMeter not installed"
        return
    fi
    
    log "Running JMeter performance tests..."
    
    local jmeter_script="src/test/resources/performance/jmeter/DMS-GraphQL-Performance-Test.jmx"
    
    if [ ! -f "$jmeter_script" ]; then
        error "JMeter script not found: $jmeter_script"
        return
    fi
    
    # Run JMeter test
    jmeter -n -t "$jmeter_script" \
           -Jbase.url="${BASE_URL}" \
           -Jthreads="${MAX_USERS}" \
           -Jramp.up="${RAMP_UP_TIME}" \
           -Jduration="${TEST_DURATION}" \
           -Juser.csv="${RESULTS_DIR}/test-users.csv" \
           -Jresults.file="${RESULTS_DIR}/jmeter/jmeter-results.jtl" \
           -l "${RESULTS_DIR}/jmeter/jmeter-results.jtl" \
           -e -o "${RESULTS_DIR}/jmeter/html-report" || warning "JMeter test failed"
    
    success "JMeter tests completed"
}

# Run Maven performance tests
run_maven_tests() {
    log "Running Maven performance tests..."
    
    # Run performance benchmark tests
    mvn test -Dtest="*BenchmarkTest,*PerformanceTest" \
        -Dspring.profiles.active=integration-test \
        -Dmaven.test.failure.ignore=true \
        -Dsurefire.reportFormat=xml \
        -Dsurefire.reportsDirectory="${RESULTS_DIR}/maven" || warning "Some Maven tests failed"
    
    success "Maven tests completed"
}

# Generate performance report
generate_report() {
    log "Generating performance report..."
    
    local report_file="${RESULTS_DIR}/reports/performance-summary.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>DMS Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>DMS Performance Test Report</h1>
        <p><strong>Test Date:</strong> $(date)</p>
        <p><strong>Base URL:</strong> ${BASE_URL}</p>
        <p><strong>Test Duration:</strong> ${TEST_DURATION} seconds</p>
        <p><strong>Max Users:</strong> ${MAX_USERS}</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <table>
            <tr><th>Test Type</th><th>Status</th><th>Results Location</th></tr>
EOF

    # Add test results to report
    if [ -d "${RESULTS_DIR}/k6" ] && [ "$(ls -A ${RESULTS_DIR}/k6)" ]; then
        echo "            <tr><td>K6 Load Tests</td><td class=\"success\">Completed</td><td>${RESULTS_DIR}/k6/</td></tr>" >> "$report_file"
    else
        echo "            <tr><td>K6 Load Tests</td><td class=\"warning\">Skipped</td><td>-</td></tr>" >> "$report_file"
    fi
    
    if [ -d "${RESULTS_DIR}/jmeter" ] && [ "$(ls -A ${RESULTS_DIR}/jmeter)" ]; then
        echo "            <tr><td>JMeter Tests</td><td class=\"success\">Completed</td><td>${RESULTS_DIR}/jmeter/</td></tr>" >> "$report_file"
    else
        echo "            <tr><td>JMeter Tests</td><td class=\"warning\">Skipped</td><td>-</td></tr>" >> "$report_file"
    fi
    
    if [ -d "${RESULTS_DIR}/maven" ] && [ "$(ls -A ${RESULTS_DIR}/maven)" ]; then
        echo "            <tr><td>Maven Benchmark Tests</td><td class=\"success\">Completed</td><td>${RESULTS_DIR}/maven/</td></tr>" >> "$report_file"
    else
        echo "            <tr><td>Maven Benchmark Tests</td><td class=\"error\">Failed</td><td>-</td></tr>" >> "$report_file"
    fi

    cat >> "$report_file" << EOF
        </table>
    </div>
    
    <div class="section">
        <h2>Quick Access Links</h2>
        <ul>
            <li><a href="../jmeter/html-report/index.html">JMeter HTML Report</a></li>
            <li><a href="../k6/">K6 Results</a></li>
            <li><a href="../maven/">Maven Test Results</a></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Next Steps</h2>
        <ol>
            <li>Review individual test results for performance bottlenecks</li>
            <li>Compare results with previous test runs</li>
            <li>Identify areas for optimization</li>
            <li>Update performance baselines if needed</li>
        </ol>
    </div>
</body>
</html>
EOF

    success "Performance report generated: $report_file"
}

# Cleanup function
cleanup() {
    log "Cleaning up..."
    # Add any cleanup tasks here
    success "Cleanup completed"
}

# Main execution
main() {
    log "Starting DMS Performance Testing Suite"
    log "Results will be saved to: ${RESULTS_DIR}"
    
    check_prerequisites
    setup_test_environment
    
    # Run tests
    run_maven_tests
    run_k6_tests
    run_jmeter_tests
    
    # Generate report
    generate_report
    
    # Cleanup
    cleanup
    
    success "Performance testing completed successfully!"
    log "Results available in: ${RESULTS_DIR}"
    log "Open ${RESULTS_DIR}/reports/performance-summary.html to view the summary report"
}

# Handle script interruption
trap cleanup EXIT

# Run main function
main "$@"
