package com.ascentbusiness.dms_svc.dto;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class DocumentOwnershipMetadataInput {
    
    private String owner; // User/Role/Team responsible for the document
    private String approver; // Assigned user/role who approved the document
    private String status; // Draft, Under Review, Approved, Archived
    private LocalDateTime approvalDate; // When the document was approved
    private LocalDateTime expiryDate; // When the document becomes obsolete
    private LocalDateTime renewalReminder; // Date to notify for renewal or review
    private String retentionPeriod; // How long the document should be stored
    private Boolean archived; // Boolean to mark if the document is archived
}
