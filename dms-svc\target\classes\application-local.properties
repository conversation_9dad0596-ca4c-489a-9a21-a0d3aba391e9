# Local Development Configuration
# This file contains secure defaults for local development
# Override these values using environment variables or application-dev.properties

# Application Configuration
spring.application.name=dms-svc-local
server.port=9093

# MySQL Database Configuration for Local Development
spring.datasource.url=***********************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JWT Configuration - Generate a secure random secret for local development
# Use: openssl rand -base64 64 to generate a new secret
dms.jwt.secret=localDevSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ
dms.jwt.expiration=86400000

# GraphQL Configuration - Restrictive CORS for local development
spring.graphql.graphiql.enabled=true
spring.graphql.cors.allowed-origins=http://localhost:3000,http://localhost:8080,http://localhost:9093
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID

# Security Headers Configuration - Relaxed for local development to allow GraphiQL
# Content Security Policy - Allow GraphiQL to load its resources
dms.security.headers.csp.enabled=true
dms.security.headers.csp.policy=default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://unpkg.com https://cdn.jsdelivr.net; img-src 'self' data: https:; font-src 'self' data: https://unpkg.com https://cdn.jsdelivr.net; connect-src 'self' ws: wss:; frame-ancestors 'self'; base-uri 'self'; form-action 'self'
dms.security.headers.csp.report-only=false

# Cross-Origin policies - Relaxed for local development
dms.security.headers.cross-origin-embedder-policy=unsafe-none
dms.security.headers.cross-origin-opener-policy=unsafe-none
dms.security.headers.cross-origin-resource-policy=cross-origin

# Frame options - Allow same origin for GraphiQL
dms.security.headers.frame-options=SAMEORIGIN

# Security Configuration
logging.level.org.springframework.security=INFO
management.endpoints.web.exposure.include=health,info,metrics

# Local Storage Configuration
dms.storage.local.base-path=./storage/documents
dms.storage.local.create-directories=true

# Enable external services for local development
elasticsearch.enabled=true
dms.storage.test.create-sample-configurations=false
dms.storage.test.run-tests-on-startup=false

# Cache Configuration - Redis cache for local development
spring.cache.type=redis

# Actuator Security - Allow detailed health info in local development
management.endpoint.health.show-details=always
management.endpoints.web.base-path=/actuator

# Health Indicators Configuration
management.endpoint.health.show-components=always
management.health.defaults.enabled=true
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=false
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Disk Space Health Indicator
management.health.diskspace.path=./
management.health.diskspace.threshold=100MB

# Database Health Indicator
management.health.db.enabled=true

# Custom Health Indicators
management.health.custom.enabled=true
