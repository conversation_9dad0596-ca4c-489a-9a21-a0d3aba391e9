package com.ascentbusiness.dms_svc.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TestCaseCollection {
    private String category;
    private String displayName;
    private Integer totalTests;
    private String testRange;
    private String coverageAreas;
    private String description;
    private List<TestCaseResponse> testCases;

    public TestCaseCollection() {}

    public TestCaseCollection(String category, String displayName, Integer totalTests, 
                             String testRange, String coverageAreas, String description,
                             List<TestCaseResponse> testCases) {
        this.category = category;
        this.displayName = displayName;
        this.totalTests = totalTests;
        this.testRange = testRange;
        this.coverageAreas = coverageAreas;
        this.description = description;
        this.testCases = testCases;
    }

    // Getters and Setters
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Integer getTotalTests() {
        return totalTests;
    }

    public void setTotalTests(Integer totalTests) {
        this.totalTests = totalTests;
    }

    public String getTestRange() {
        return testRange;
    }

    public void setTestRange(String testRange) {
        this.testRange = testRange;
    }

    public String getCoverageAreas() {
        return coverageAreas;
    }

    public void setCoverageAreas(String coverageAreas) {
        this.coverageAreas = coverageAreas;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<TestCaseResponse> getTestCases() {
        return testCases;
    }

    public void setTestCases(List<TestCaseResponse> testCases) {
        this.testCases = testCases;
    }
}
