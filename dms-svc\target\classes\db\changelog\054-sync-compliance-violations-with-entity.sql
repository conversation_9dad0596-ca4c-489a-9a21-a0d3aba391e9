--liquibase formatted sql

--changeset dms:054-sync-compliance-violations-with-entity
--comment: Add missing columns to compliance_violations table to match entity definition

-- Add missing columns that exist in entity but not in database
ALTER TABLE compliance_violations 
ADD COLUMN ip_address VARCHAR(45) COMMENT 'IP address of the user when violation occurred';

ALTER TABLE compliance_violations 
ADD COLUMN user_agent TEXT COMMENT 'User agent string when violation occurred';

ALTER TABLE compliance_violations 
ADD COLUMN notification_sent_date TIMESTAMP NULL COMMENT 'Date when notification was sent';

ALTER TABLE compliance_violations 
ADD COLUMN notification_recipient VARCHAR(200) COMMENT 'Recipient of the notification';

ALTER TABLE compliance_violations 
ADD COLUMN regulatory_report_sent_date TIMESTAMP NULL COMMENT 'Date when regulatory report was sent';

ALTER TABLE compliance_violations 
ADD COLUMN regulatory_authority VARCHAR(200) COMMENT 'Regulatory authority that received the report';

ALTER TABLE compliance_violations 
ADD COLUMN potential_fine_amount DECIMAL(15,2) COMMENT 'Potential fine amount for this violation';

ALTER TABLE compliance_violations 
ADD COLUMN actual_fine_amount DECIMAL(15,2) COMMENT 'Actual fine amount imposed for this violation';

ALTER TABLE compliance_violations 
ADD COLUMN business_impact TEXT COMMENT 'Description of business impact from this violation';

ALTER TABLE compliance_violations 
ADD COLUMN lessons_learned TEXT COMMENT 'Lessons learned from this violation';

ALTER TABLE compliance_violations 
ADD COLUMN recurrence_count INTEGER NOT NULL DEFAULT 1 COMMENT 'Number of times this type of violation has occurred';

ALTER TABLE compliance_violations 
ADD COLUMN previous_violation_id BIGINT COMMENT 'Reference to previous occurrence of similar violation';

-- Add foreign key constraint for previous_violation_id
ALTER TABLE compliance_violations 
ADD CONSTRAINT fk_compliance_violations_previous 
FOREIGN KEY (previous_violation_id) REFERENCES compliance_violations(id);

--rollback ALTER TABLE compliance_violations DROP FOREIGN KEY fk_compliance_violations_previous;
--rollback ALTER TABLE compliance_violations DROP COLUMN previous_violation_id;
--rollback ALTER TABLE compliance_violations DROP COLUMN recurrence_count;
--rollback ALTER TABLE compliance_violations DROP COLUMN lessons_learned;
--rollback ALTER TABLE compliance_violations DROP COLUMN business_impact;
--rollback ALTER TABLE compliance_violations DROP COLUMN actual_fine_amount;
--rollback ALTER TABLE compliance_violations DROP COLUMN potential_fine_amount;
--rollback ALTER TABLE compliance_violations DROP COLUMN regulatory_authority;
--rollback ALTER TABLE compliance_violations DROP COLUMN regulatory_report_sent_date;
--rollback ALTER TABLE compliance_violations DROP COLUMN notification_recipient;
--rollback ALTER TABLE compliance_violations DROP COLUMN notification_sent_date;
--rollback ALTER TABLE compliance_violations DROP COLUMN user_agent;
--rollback ALTER TABLE compliance_violations DROP COLUMN ip_address;