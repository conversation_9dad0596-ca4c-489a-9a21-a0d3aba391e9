--liquibase formatted sql

--changeset dms:018-fix-audit-action-enum

-- Fix the audit_logs action column to support all AuditAction enum values
-- The current ENUM only supports 5 values but the Java enum has many more

-- First, change the action column from ENUM to VARCHAR to support all values
ALTER TABLE audit_logs 
MODIFY COLUMN action VARCHAR(50) NOT NULL;

-- Update the column comment to reflect the change
ALTER TABLE audit_logs 
MODIFY COLUMN action VARCHAR(50) NOT NULL 
COMMENT 'Action performed - supports all AuditAction enum values';

-- Recreate the index for better performance
-- MySQL syntax for dropping index
DROP INDEX idx_audit_logs_action ON audit_logs;
CREATE INDEX idx_audit_logs_action ON audit_logs(action);

-- Add a check constraint to ensure only valid enum values are inserted
-- This provides similar validation to ENUM but with more flexibility
ALTER TABLE audit_logs
ADD CONSTRAINT chk_audit_action
CHECK (action IN (
    'CREATE', 'READ', 'UPLOAD', 'DOWNLOAD', 'DELETE', 'UPDATE', 'VERSION_CREATE',
    'PERMISSION_GRANTED', 'PERMISSION_REVOKED', 'PERMISSION_EXPIRED', 'PERMISSION_CHECKED',
    'SECURITY_VIOLATION', 'TOKEN_VALIDATION_FAILED', 'RATE_LIMIT_EXCEEDED',
    'MIGRATION_STARTED', 'MIGRATION_COMPLETED', 'MIGRATION_FAILED',
    'MIGRATION_FILE_PROCESSED', 'MIGRATION_FILE_VERIFIED', 'MIGRATION_FILE_CLEANUP',
    'MIGRATION_SECURITY_CHECK', 'MIGRATION_VALIDATION_FAILED'
));
