# GRC Platform v4 - Shared Docker Compose Setup

This document explains how to run both the DMS Service and Notification Service using a shared Docker Compose configuration that eliminates duplicate infrastructure services.

## 🏗️ Architecture Overview

The shared setup includes:

### Application Services
- **DMS Service** (Port 9093) - Document Management Service
- **Notification Service** (Port 9091) - Notification Management Service

### Shared Infrastructure Services
- **MySQL** (Port 3306) - Shared database server with separate databases
- **Redis** (Port 6379) - Shared cache with service-specific key prefixes
- **RabbitMQ** (Port 5672, Management UI: 15672) - Message broker for notifications
- **Elasticsearch** (Port 9200) - Search engine for DMS

### Monitoring & Observability
- **Zipkin** (Port 9411) - Distributed tracing
- **Prometheus** (Port 9090) - Metrics collection
- **Grafana** (Port 3000) - Dashboards and visualization

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 4GB RAM available for containers
- Ports 3000, 3306, 5672, 6379, 9090, 9091, 9093, 9200, 9411, 15672 available

### 1. Start All Services
```bash
# From the root directory (grc-platform-v4)
docker-compose up -d
```

### 2. Check Service Health
```bash
# Check all services status
docker-compose ps

# Check logs for specific service
docker-compose logs -f dms-service
docker-compose logs -f notification-service
```

### 3. Access Services

#### Application Services
- **DMS Service GraphQL**: http://localhost:9093/dms/graphql
- **DMS Service GraphiQL**: http://localhost:9093/graphiql
- **Notification Service GraphQL**: http://localhost:9091/graphql
- **Notification Service GraphiQL**: http://localhost:9091/graphiql

#### Infrastructure Services
- **MySQL**: localhost:3306 (root/root_password)
- **Redis**: localhost:6379 (password: shared_redis_password)
- **RabbitMQ Management**: http://localhost:15672 (admin/admin123)
- **Elasticsearch**: http://localhost:9200

#### Monitoring Services
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Zipkin**: http://localhost:9411

## 🗄️ Database Configuration

The shared MySQL instance creates separate databases:

### Databases Created
- `dms_db` - DMS Service database
- `notification_db` - Notification Service database

### Users Created
- `dms_user` - Full access to `dms_db`, read access to `notification_db`
- `notification_user` - Full access to `notification_db`, read access to `dms_db`

### Cross-Service Access
Both services have read access to each other's databases for future integration needs.

## 🔧 Service-Specific Configuration

### DMS Service
- **Database**: `dms_db` with user `dms_user`
- **Redis Prefix**: `dms:`
- **Storage**: Local filesystem at `/app/storage`
- **Metrics**: Available at port 9464

### Notification Service
- **Database**: `notification_db` with user `notification_user`
- **Redis Prefix**: `notification:`
- **Message Queue**: RabbitMQ for async processing
- **Email**: Configurable SMTP (defaults to mock in Docker)

## 🔍 Monitoring & Observability

### Prometheus Metrics
Both services expose metrics at `/actuator/prometheus`:
- DMS Service: http://localhost:9464/actuator/prometheus
- Notification Service: http://localhost:9091/actuator/prometheus

### Health Checks
- DMS Service: http://localhost:9093/actuator/health
- Notification Service: http://localhost:9091/actuator/health

### Distributed Tracing
Both services send traces to Zipkin for request correlation across services.

## 🛠️ Development Commands

### Start Specific Services
```bash
# Start only application services
docker-compose up -d dms-service notification-service

# Start only infrastructure services
docker-compose up -d mysql redis rabbitmq elasticsearch

# Start only monitoring services
docker-compose up -d prometheus grafana zipkin
```

### Scaling Services
```bash
# Scale application services (load balancer needed for multiple instances)
docker-compose up -d --scale dms-service=2 --scale notification-service=2
```

### View Logs
```bash
# Follow logs for all services
docker-compose logs -f

# Follow logs for specific services
docker-compose logs -f dms-service notification-service

# View logs for infrastructure
docker-compose logs mysql redis rabbitmq
```

### Restart Services
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart dms-service
docker-compose restart notification-service
```

## 🧹 Cleanup

### Stop Services
```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This deletes all data)
docker-compose down -v

# Stop and remove images
docker-compose down --rmi all
```

### Clean Up Resources
```bash
# Remove unused Docker resources
docker system prune -f

# Remove unused volumes
docker volume prune -f
```

## 🔒 Security Considerations

### Production Deployment
Before deploying to production:

1. **Change Default Passwords**:
   - MySQL root password
   - Redis password
   - RabbitMQ admin password
   - JWT secrets

2. **Configure HTTPS**:
   - Add SSL certificates
   - Configure reverse proxy (nginx/traefik)

3. **Network Security**:
   - Use custom networks
   - Restrict port exposure
   - Configure firewall rules

4. **Secrets Management**:
   - Use Docker secrets or external secret management
   - Avoid hardcoded credentials in environment variables

## 🐛 Troubleshooting

### Common Issues

1. **Port Conflicts**:
   ```bash
   # Check what's using a port
   netstat -tulpn | grep :3306
   
   # Stop conflicting services
   sudo systemctl stop mysql
   ```

2. **Memory Issues**:
   ```bash
   # Check Docker memory usage
   docker stats
   
   # Increase Docker memory limit in Docker Desktop
   ```

3. **Database Connection Issues**:
   ```bash
   # Check MySQL logs
   docker-compose logs mysql
   
   # Connect to MySQL directly
   docker-compose exec mysql mysql -u root -p
   ```

4. **Service Dependencies**:
   ```bash
   # Restart services in dependency order
   docker-compose up -d mysql redis rabbitmq elasticsearch
   docker-compose up -d dms-service notification-service
   ```

## 📊 Performance Tuning

### Resource Allocation
Adjust memory limits in docker-compose.yml:
```yaml
services:
  dms-service:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

### Database Optimization
- Increase MySQL buffer pool size for production
- Configure Redis maxmemory policy
- Set up database connection pooling

### Monitoring Alerts
Configure Prometheus alerts for:
- High memory usage (>80%)
- High CPU usage (>80%)
- Service downtime
- Database connection failures

## 🔄 Migration from Individual Setups

If migrating from individual docker-compose setups:

1. **Backup Data**:
   ```bash
   # Backup DMS data
   docker-compose -f dms-svc/docker-compose.yml exec mysql mysqldump -u root -p dms_db > dms_backup.sql
   
   # Backup Notification data
   docker-compose -f notification-svc/docker-compose.yml exec mysql mysqldump -u root -p notificationsvc > notification_backup.sql
   ```

2. **Stop Individual Services**:
   ```bash
   cd dms-svc && docker-compose down
   cd ../notification-svc && docker-compose down
   ```

3. **Start Shared Setup**:
   ```bash
   cd .. && docker-compose up -d
   ```

4. **Restore Data** (if needed):
   ```bash
   # Restore to shared MySQL
   docker-compose exec mysql mysql -u root -p dms_db < dms_backup.sql
   docker-compose exec mysql mysql -u root -p notification_db < notification_backup.sql
   ```
