-- liquibase formatted sql

-- changeset dms:017-remove-user-tables
-- comment: Remove user management tables as user info will come from JWT security context only

-- Drop foreign key constraints first
ALTER TABLE user_roles DROP FOREIGN KEY user_roles_ibfk_1;
ALTER TABLE user_permissions DROP FOREIGN KEY user_permissions_ibfk_1;

-- Drop user-related tables
DROP TABLE IF EXISTS user_permissions;
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS users;

-- rollback CREATE TABLE users (id VARCHAR(100) PRIMARY KEY, username VARCHAR(100) NOT NULL UNIQUE, email VARCHAR(255) NOT NULL UNIQUE, first_name <PERSON><PERSON><PERSON><PERSON>(100), last_name <PERSON><PERSON><PERSON><PERSON>(100), is_active BOOLEAN NOT NULL DEFAULT TRUE, created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, last_login_date TIMES<PERSON>MP NULL, created_by <PERSON><PERSON><PERSON><PERSON>(100), last_modified_by <PERSON><PERSON><PERSON><PERSON>(100), INDEX idx_users_username (username), INDEX idx_users_email (email), INDEX idx_users_is_active (is_active));
-- rollback CREATE TABLE user_roles (user_id VARCHAR(100) NOT NULL, role VARCHAR(100) NOT NULL, PRIMARY KEY (user_id, role), FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE, INDEX idx_user_roles_user_id (user_id), INDEX idx_user_roles_role (role));
-- rollback CREATE TABLE user_permissions (user_id VARCHAR(100) NOT NULL, permission VARCHAR(100) NOT NULL, PRIMARY KEY (user_id, permission), FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE, INDEX idx_user_permissions_user_id (user_id), INDEX idx_user_permissions_permission (permission));
-- rollback INSERT INTO users (id, username, email, first_name, last_name, is_active, created_by) VALUES ('admin', 'admin', '<EMAIL>', 'System', 'Administrator', TRUE, 'system');
-- rollback INSERT INTO user_roles (user_id, role) VALUES ('admin', 'ADMIN'), ('admin', 'USER');
-- rollback INSERT INTO user_permissions (user_id, permission) VALUES ('admin', 'READ'), ('admin', 'WRITE'), ('admin', 'DELETE'), ('admin', 'ADMIN');
