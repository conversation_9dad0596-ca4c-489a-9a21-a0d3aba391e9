package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Base DTO for storage configuration with polymorphic support for different providers.
 */
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "providerType")
@JsonSubTypes({
    @JsonSubTypes.Type(value = LocalStorageConfigDto.class, name = "LOCAL"),
    @JsonSubTypes.Type(value = S3StorageConfigDto.class, name = "S3"),
    @JsonSubTypes.Type(value = SharePointStorageConfigDto.class, name = "SHAREPOINT")
})
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class StorageConfigurationDto {
    
    private Long id;
    private StorageProvider providerType;
    private Boolean isActive;
    private Boolean isDefault;
    private String description;
    private Integer priority;
    private Boolean healthCheckEnabled;
    private LocalDateTime lastHealthCheck;
    private String healthStatus;
    private LocalDateTime createdDate;
    private LocalDateTime lastModifiedDate;
    private String createdBy;
    private String lastModifiedBy;

    /**
     * Validate the configuration specific to each provider.
     * @return true if configuration is valid
     */
    public abstract boolean isValid();

    /**
     * Get configuration as JSON string for database storage.
     * @return JSON representation of configuration
     */
    public abstract String toJsonString();

    /**
     * Create configuration from JSON string.
     * @param json JSON string from database
     */
    public abstract void fromJsonString(String json);
}

/**
 * Local storage configuration DTO.
 */
@Data
@EqualsAndHashCode(callSuper = true)
class LocalStorageConfigDto extends StorageConfigurationDto {
    private String basePath;
    private Boolean createDirectories;
    private String filePermissions;
    private Long maxFileSize;

    @Override
    public boolean isValid() {
        return basePath != null && !basePath.trim().isEmpty();
    }

    @Override
    public String toJsonString() {
        return String.format(
            "{\"basePath\":\"%s\",\"createDirectories\":%b,\"filePermissions\":\"%s\",\"maxFileSize\":%d}",
            basePath, createDirectories, filePermissions, maxFileSize
        );
    }

    @Override
    public void fromJsonString(String json) {
        // Implementation would parse JSON and set fields
        // For now, simplified implementation
    }
}

/**
 * AWS S3 storage configuration DTO.
 */
@Data
@EqualsAndHashCode(callSuper = true)
class S3StorageConfigDto extends StorageConfigurationDto {
    private String bucketName;
    private String region;
    private String accessKey;
    private String secretKey;
    private String endpoint;
    private Boolean pathStyleAccess;
    private String storageClass;
    private Boolean serverSideEncryption;

    @Override
    public boolean isValid() {
        return bucketName != null && !bucketName.trim().isEmpty() &&
               region != null && !region.trim().isEmpty() &&
               accessKey != null && !accessKey.trim().isEmpty() &&
               secretKey != null && !secretKey.trim().isEmpty();
    }

    @Override
    public String toJsonString() {
        return String.format(
            "{\"bucketName\":\"%s\",\"region\":\"%s\",\"accessKey\":\"%s\",\"secretKey\":\"***\",\"endpoint\":\"%s\",\"pathStyleAccess\":%b,\"storageClass\":\"%s\",\"serverSideEncryption\":%b}",
            bucketName, region, accessKey, endpoint, pathStyleAccess, storageClass, serverSideEncryption
        );
    }

    @Override
    public void fromJsonString(String json) {
        // Implementation would parse JSON and set fields
        // For now, simplified implementation
    }
}

/**
 * SharePoint storage configuration DTO.
 */
@Data
@EqualsAndHashCode(callSuper = true)
class SharePointStorageConfigDto extends StorageConfigurationDto {
    private String tenantId;
    private String clientId;
    private String clientSecret;
    private String siteUrl;
    private String documentLibrary;
    private String driveId;
    private Boolean useApplicationPermissions;

    @Override
    public boolean isValid() {
        return tenantId != null && !tenantId.trim().isEmpty() &&
               clientId != null && !clientId.trim().isEmpty() &&
               clientSecret != null && !clientSecret.trim().isEmpty() &&
               siteUrl != null && !siteUrl.trim().isEmpty() &&
               documentLibrary != null && !documentLibrary.trim().isEmpty();
    }

    @Override
    public String toJsonString() {
        return String.format(
            "{\"tenantId\":\"%s\",\"clientId\":\"%s\",\"clientSecret\":\"***\",\"siteUrl\":\"%s\",\"documentLibrary\":\"%s\",\"driveId\":\"%s\",\"useApplicationPermissions\":%b}",
            tenantId, clientId, siteUrl, documentLibrary, driveId, useApplicationPermissions
        );
    }

    @Override
    public void fromJsonString(String json) {
        // Implementation would parse JSON and set fields
        // For now, simplified implementation
    }
}

