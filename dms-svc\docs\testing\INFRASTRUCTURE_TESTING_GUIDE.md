# Infrastructure Testing Guide

This guide provides comprehensive information about testing the Infrastructure & DevOps components of the DMS Service.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Test Categories](#test-categories)
3. [Test Execution](#test-execution)
4. [Test Environment Setup](#test-environment-setup)
5. [Continuous Integration](#continuous-integration)
6. [Troubleshooting](#troubleshooting)

## 🎯 Overview

The Infrastructure Testing Suite validates all aspects of the DMS Service infrastructure including:

- **Docker Containerization**: Container build, security, and runtime validation
- **Kubernetes Deployment**: Manifest validation, deployment testing, and resource management
- **CI/CD Pipeline**: Workflow validation, security scanning, and deployment automation
- **Configuration Management**: Environment-specific configurations and feature flags
- **Security Policies**: Container security, RBAC, and network policies
- **Performance Optimization**: Resource allocation, JVM tuning, and caching

## 🧪 Test Categories

### 1. Docker Container Tests

**Location**: `tests/infrastructure/DockerContainerTest.java`

**Purpose**: Validates Docker containerization and runtime behavior

**Test Coverage**:
- Container build and startup
- Health check endpoints
- Database and Redis connectivity
- Security configuration (non-root user)
- Resource management
- Graceful shutdown
- Environment variables
- File permissions

**Prerequisites**:
- Docker installed and running
- TestContainers support

**Example Execution**:
```bash
# Run Docker tests only
mvn test -Dtest=DockerContainerTest

# Run with environment variable
DOCKER_TEST_ENABLED=true mvn test -Dtest=DockerContainerTest
```

### 2. Kubernetes Deployment Tests

**Location**: `tests/infrastructure/KubernetesDeploymentTest.java`

**Purpose**: Validates Kubernetes deployment manifests and cluster operations

**Test Coverage**:
- Namespace creation and management
- ConfigMap and Secret deployment
- Service and Deployment validation
- Resource limits and requests
- Health probe configuration
- RBAC policies
- Scaling operations

**Prerequisites**:
- kubectl installed and configured
- Access to Kubernetes cluster (optional for manifest validation)

**Example Execution**:
```bash
# Run Kubernetes tests (requires cluster access)
KUBERNETES_TEST_ENABLED=true mvn test -Dtest=KubernetesDeploymentTest

# Validate manifests only (no cluster required)
kubectl apply --dry-run=client -f k8s/
```

### 3. CI/CD Pipeline Tests

**Location**: `tests/infrastructure/CiCdPipelineTest.java`

**Purpose**: Validates CI/CD pipeline configuration and automation

**Test Coverage**:
- GitHub Actions workflow validation
- Pipeline stage configuration
- Security scanning integration
- Docker build and push
- Deployment automation
- Environment-specific deployments

**Prerequisites**:
- GitHub repository access (for API tests)
- YAML parser dependencies

**Example Execution**:
```bash
# Run CI/CD tests
mvn test -Dtest=CiCdPipelineTest

# With GitHub API access
GITHUB_TOKEN=your_token mvn test -Dtest=CiCdPipelineTest
```

### 4. Configuration Validation Tests

**Location**: `tests/infrastructure/ConfigurationValidationTest.java`

**Purpose**: Validates application configurations across environments

**Test Coverage**:
- Application properties validation
- Environment-specific configurations
- Database and Redis settings
- Security configuration
- Monitoring and observability
- Storage configuration

**Example Execution**:
```bash
# Run configuration tests
mvn test -Dtest=ConfigurationValidationTest
```

### 5. Infrastructure Test Suite

**Location**: `tests/infrastructure/InfrastructureTestSuite.java`

**Purpose**: Aggregates all infrastructure tests into a comprehensive suite

**Example Execution**:
```bash
# Run complete infrastructure test suite
mvn test -Dtest=InfrastructureTestSuite

# Run with specific tags
mvn test -Dgroups=infrastructure
```

## 🚀 Test Execution

### Quick Start

```bash
# Run all infrastructure tests
./scripts/run-infrastructure-tests.sh

# Run specific test categories
./scripts/run-infrastructure-tests.sh --docker-only
./scripts/run-infrastructure-tests.sh --kubernetes-only
./scripts/run-infrastructure-tests.sh --cicd-only
```

### Windows PowerShell

```powershell
# Run all infrastructure tests
.\scripts\run-infrastructure-tests.ps1

# Run specific test categories
.\scripts\run-infrastructure-tests.ps1 -DockerOnly
.\scripts\run-infrastructure-tests.ps1 -KubernetesOnly
.\scripts\run-infrastructure-tests.ps1 -CiCdOnly
```

### Maven Commands

```bash
# Run all tests
mvn test

# Run infrastructure tests only
mvn test -Dgroups=infrastructure

# Run specific test class
mvn test -Dtest=DockerContainerTest

# Run with coverage
mvn test jacoco:report

# Generate reports
mvn test surefire-report:report
```

### Environment Variables

Control test execution with environment variables:

```bash
# Enable/disable test categories
export DOCKER_TEST_ENABLED=true
export KUBERNETES_TEST_ENABLED=true
export CICD_TEST_ENABLED=true

# Test configuration
export FAIL_FAST=true
export GENERATE_REPORTS=true

# Run tests
./scripts/run-infrastructure-tests.sh
```

## 🔧 Test Environment Setup

### Local Development

1. **Install Prerequisites**:
   ```bash
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   
   # Install kubectl (optional)
   curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
   sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
   ```

2. **Configure Test Environment**:
   ```bash
   # Set up local environment
   ./scripts/setup-local-env.sh
   
   # Start test services
   docker-compose up -d mysql redis elasticsearch
   ```

3. **Run Tests**:
   ```bash
   # Run infrastructure tests
   ./scripts/run-infrastructure-tests.sh --docker-only
   ```

### CI/CD Environment

The tests are automatically executed in the CI/CD pipeline with the following configuration:

```yaml
# GitHub Actions workflow excerpt
- name: Run Infrastructure Tests
  run: |
    export DOCKER_TEST_ENABLED=true
    export KUBERNETES_TEST_ENABLED=false
    export CICD_TEST_ENABLED=true
    ./scripts/run-infrastructure-tests.sh
```

### Production Validation

For production environment validation:

```bash
# Run deployment validation
./scripts/deploy.sh -e prod -t latest --dry-run

# Run health checks
./scripts/comprehensive-health-check.ps1

# Validate infrastructure
KUBERNETES_TEST_ENABLED=true ./scripts/run-infrastructure-tests.sh --kubernetes-only
```

## 🔄 Continuous Integration

### GitHub Actions Integration

The infrastructure tests are integrated into the CI/CD pipeline:

1. **Code Quality Stage**: Configuration validation
2. **Test Stage**: Unit and integration tests
3. **Security Stage**: Security configuration validation
4. **Build Stage**: Docker image build and test
5. **Deploy Stage**: Deployment validation

### Test Reports

Test reports are generated and published:

- **JUnit XML**: For CI/CD integration
- **HTML Reports**: For detailed analysis
- **Coverage Reports**: JaCoCo coverage analysis
- **Security Reports**: SARIF format for security scanning

### Failure Handling

- **Fail Fast**: Stop on first test failure (configurable)
- **Retry Logic**: Automatic retry for flaky tests
- **Notifications**: Slack notifications for failures
- **Rollback**: Automatic rollback on deployment failures

## 🔍 Troubleshooting

### Common Issues

#### 1. Docker Tests Failing

**Symptoms**: Docker container tests fail to start or connect

**Solutions**:
```bash
# Check Docker daemon
docker info

# Check available resources
docker system df

# Clean up resources
docker system prune -f

# Restart Docker service
sudo systemctl restart docker
```

#### 2. Kubernetes Tests Failing

**Symptoms**: Kubernetes tests fail with connection errors

**Solutions**:
```bash
# Check cluster connectivity
kubectl cluster-info

# Check authentication
kubectl auth can-i create pods

# Update kubeconfig
aws eks update-kubeconfig --region us-east-1 --name your-cluster
```

#### 3. Configuration Validation Failures

**Symptoms**: Configuration tests fail with missing properties

**Solutions**:
```bash
# Check file existence
ls -la config/environments/
ls -la src/main/resources/

# Validate property syntax
grep -n "=" config/environments/dev.properties

# Check for encoding issues
file config/environments/dev.properties
```

#### 4. CI/CD Pipeline Validation Failures

**Symptoms**: GitHub Actions workflow validation fails

**Solutions**:
```bash
# Validate YAML syntax
python3 -c "import yaml; yaml.safe_load(open('.github/workflows/ci-cd.yml'))"

# Check workflow file
cat .github/workflows/ci-cd.yml

# Validate against GitHub Actions schema
act --list  # If act is installed
```

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
export MAVEN_OPTS="-X"

# Run tests with verbose output
mvn test -Dtest=InfrastructureTestSuite -X
```

### Log Analysis

Check log files for detailed error information:

```bash
# Test execution logs
tail -f /tmp/dms-infrastructure-tests-*.log

# Maven logs
tail -f target/surefire-reports/*.txt

# Docker logs
docker logs dms-app
```

## 📊 Test Metrics

### Coverage Targets

- **Unit Tests**: > 80% line coverage
- **Integration Tests**: > 70% line coverage
- **Infrastructure Tests**: 100% configuration coverage

### Performance Benchmarks

- **Container Startup**: < 60 seconds
- **Health Check Response**: < 5 seconds
- **Test Execution**: < 10 minutes (full suite)

### Quality Gates

- All tests must pass
- No security vulnerabilities (high/critical)
- Configuration validation 100% pass rate
- Docker image security scan pass

## 📚 Additional Resources

- [Docker Testing Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [Kubernetes Testing Guide](https://kubernetes.io/docs/tasks/debug-application-cluster/debug-application/)
- [TestContainers Documentation](https://www.testcontainers.org/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)

## 🆘 Support

For testing issues or questions:

1. Check this troubleshooting guide
2. Review test logs and error messages
3. Consult the infrastructure documentation
4. Contact the DevOps team for assistance

---

**Note**: This testing guide is part of the comprehensive Infrastructure & DevOps implementation for the DMS Service. Ensure all prerequisites are met before running tests.
