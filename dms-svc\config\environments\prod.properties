# Production Environment Configuration for DMS Service
# This file contains production-specific settings

# Server Configuration
server.port=8080
server.servlet.context-path=/dms

# Database Configuration - Production
spring.datasource.url=${DB_URL:*****************************************************************************************************}
spring.datasource.username=${DB_USERNAME:dms_prod_user}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration - Production optimized
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# JPA Configuration - Production settings
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.use_sql_comments=false

# Liquibase Configuration
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml
spring.liquibase.enabled=true
spring.liquibase.drop-first=false

# Redis Configuration - Production
spring.redis.host=${REDIS_HOST:prod-redis-server}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD}
spring.redis.timeout=5000ms
spring.redis.jedis.pool.max-active=50
spring.redis.jedis.pool.max-idle=20
spring.redis.jedis.pool.min-idle=10

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=3600000
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dms:prod:

# Elasticsearch Configuration - Production
spring.elasticsearch.uris=${ELASTICSEARCH_URIS:http://prod-elasticsearch:9200}
spring.elasticsearch.connection-timeout=30s
spring.elasticsearch.socket-timeout=60s

# Storage Configuration - S3 for production
dms.storage.provider=${STORAGE_PROVIDER:S3}
dms.storage.local.base-path=/app/storage
dms.storage.local.create-directories=true

# S3 Configuration
dms.storage.s3.bucket-name=${S3_BUCKET_NAME:dms-prod-documents}
dms.storage.s3.region=${S3_REGION:us-east-1}

# File Upload Configuration - Production limits
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
spring.servlet.multipart.enabled=true

# JWT Configuration - Production
dms.jwt.secret=${JWT_SECRET}
dms.jwt.expiration=${JWT_EXPIRATION:86400000}
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# Actuator Configuration - Limited exposure in production
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized
management.endpoint.health.show-components=when-authorized
management.health.defaults.enabled=true

# Health Indicators
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=true
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Health indicator thresholds
management.health.diskspace.threshold=1GB

# Metrics Configuration
management.metrics.export.prometheus.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.percentiles.http.server.requests=0.5,0.95,0.99
management.metrics.tags.application=dms-service
management.metrics.tags.environment=production

# OpenTelemetry Configuration - Production
otel.service.name=dms-svc
otel.service.version=1.0.0
otel.resource.attributes=service.name=dms-svc,service.version=1.0.0,deployment.environment=production

# Tracing Configuration - OTLP for production
otel.traces.exporter=${OTEL_TRACES_EXPORTER:otlp}
otel.exporter.otlp.endpoint=${OTEL_EXPORTER_OTLP_ENDPOINT:http://jaeger-collector:14250}
otel.traces.sampler=traceidratio
otel.traces.sampler.arg=0.01

# Metrics Configuration
otel.metrics.exporter=${OTEL_METRICS_EXPORTER:prometheus}
otel.metric.export.interval=60s

# Logs Configuration
otel.logs.exporter=${OTEL_LOGS_EXPORTER:otlp}

# Logging Configuration - Production levels
logging.level.root=WARN
logging.level.com.ascentbusiness.dms=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN

# Structured Logging for Production
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n

# Log Files Configuration - Production retention
logging.file.name=/app/logs/dms-application.log
logging.logback.rollingpolicy.max-file-size=200MB
logging.logback.rollingpolicy.max-history=60
logging.logback.rollingpolicy.total-size-cap=10GB

# Security Configuration - Strict for production
dms.security.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:https://dms.company.com}
dms.security.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
dms.security.cors.allowed-headers=Content-Type,Authorization,X-Requested-With
dms.security.cors.allow-credentials=true

# Rate Limiting Configuration - Production limits
dms.security.rate-limit.enabled=true
dms.security.rate-limit.requests-per-minute=100
dms.security.rate-limit.burst-capacity=200

# Security Headers Configuration - Strict for production
dms.security.headers.frame-options=DENY
dms.security.headers.content-type-options=nosniff
dms.security.headers.xss-protection=1; mode=block
dms.security.headers.referrer-policy=strict-origin-when-cross-origin
dms.security.headers.hsts.enabled=true
dms.security.headers.hsts.max-age=31536000
dms.security.headers.hsts.include-subdomains=true

# Content Security Policy
dms.security.headers.csp.enabled=true
dms.security.headers.csp.policy=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'

# API Versioning Configuration
dms.api.version.current=1.0
dms.api.version.supported=1.0,1.1
dms.api.version.deprecation-warning=true
dms.api.version.strict-mode=true

# Performance Configuration - Production optimized
spring.jpa.properties.hibernate.jdbc.batch_size=50
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Production-specific optimizations
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=false

# Connection Pool Monitoring
management.health.db.enabled=true
spring.datasource.hikari.register-mbeans=true

# Elasticsearch Production Settings
dms.search.elasticsearch.index-prefix=dms-prod
dms.search.elasticsearch.refresh-policy=wait_for
dms.search.elasticsearch.number-of-shards=3
dms.search.elasticsearch.number-of-replicas=1

# Audit Configuration - Full auditing for production
dms.audit.enabled=true
dms.audit.async=true
dms.audit.include-request-body=false
dms.audit.include-response-body=false
dms.audit.encryption.enabled=true

# Retention Policy - Production settings
dms.retention.default-period=2555
dms.retention.check-interval=PT24H
dms.retention.batch-size=1000

# Compliance Configuration - Full compliance for production
dms.compliance.enabled=true
dms.compliance.framework=${COMPLIANCE_FRAMEWORK:SOC2}
dms.compliance.audit-level=FULL
dms.compliance.encryption.enabled=true

# Workflow Configuration
dms.workflow.enabled=true
dms.workflow.async=true
dms.workflow.timeout=PT30M

# SharePoint Integration - Production
dms.sharepoint.enabled=${SHAREPOINT_ENABLED:true}
dms.sharepoint.mock-mode=false
dms.sharepoint.tenant-id=${AZURE_TENANT_ID}
dms.sharepoint.client-id=${AZURE_CLIENT_ID}
dms.sharepoint.client-secret=${AZURE_CLIENT_SECRET}

# Backup Configuration
dms.backup.enabled=true
dms.backup.schedule=0 2 * * *
dms.backup.retention-days=30

# Disaster Recovery
dms.disaster-recovery.enabled=true
dms.disaster-recovery.backup-region=${BACKUP_REGION:us-west-2}

# Monitoring and Alerting
dms.monitoring.enabled=true
dms.monitoring.alert-threshold.cpu=80
dms.monitoring.alert-threshold.memory=85
dms.monitoring.alert-threshold.disk=90

# Circuit Breaker Configuration
resilience4j.circuitbreaker.instances.database.failure-rate-threshold=50
resilience4j.circuitbreaker.instances.database.wait-duration-in-open-state=30s
resilience4j.circuitbreaker.instances.database.sliding-window-size=10

resilience4j.circuitbreaker.instances.redis.failure-rate-threshold=50
resilience4j.circuitbreaker.instances.redis.wait-duration-in-open-state=30s
resilience4j.circuitbreaker.instances.redis.sliding-window-size=10

# Retry Configuration
resilience4j.retry.instances.database.max-attempts=3
resilience4j.retry.instances.database.wait-duration=1s

resilience4j.retry.instances.redis.max-attempts=3
resilience4j.retry.instances.redis.wait-duration=1s

# Environment
ENVIRONMENT=production
LOG_LEVEL=INFO
