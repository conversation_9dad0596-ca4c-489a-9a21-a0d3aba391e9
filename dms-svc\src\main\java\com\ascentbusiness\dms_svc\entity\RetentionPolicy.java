package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.DispositionAction;
import com.ascentbusiness.dms_svc.enums.RetentionPeriodUnit;
import jakarta.persistence.*;
import lombok.*;

import java.util.List;

/**
 * Entity representing a document retention policy
 */
@Entity
@Table(name = "retention_policies")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RetentionPolicy extends BaseEntity {
    
    @Column(name = "name", nullable = false, unique = true, length = 255)
    private String name;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "scope", length = 100)
    private String scope; // e.g., "GLOBAL", "DEPARTMENT", "PROJECT"
    
    @Column(name = "retention_period", nullable = false)
    private Integer retentionPeriod;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "retention_period_unit", nullable = false, length = 10)
    private RetentionPeriodUnit retentionPeriodUnit;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "disposition_action", nullable = false, length = 20)
    private DispositionAction dispositionAction;
    
    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;
    
    @Column(name = "allow_legal_hold", nullable = false)
    @Builder.Default
    private Boolean allowLegalHold = true;
    
    @Column(name = "auto_apply", nullable = false)
    @Builder.Default
    private Boolean autoApply = false;
    
    @Column(name = "priority", nullable = false)
    @Builder.Default
    private Integer priority = 0; // Higher number = higher priority
    
    @Column(name = "trigger_event", length = 50)
    private String triggerEvent; // e.g., "CREATION", "LAST_ACCESS", "LAST_MODIFIED"
    
    @Column(name = "business_justification", columnDefinition = "TEXT")
    private String businessJustification;
    
    @Column(name = "legal_basis", columnDefinition = "TEXT")
    private String legalBasis;
    
    @Column(name = "review_frequency_months")
    private Integer reviewFrequencyMonths;
    
    @Column(name = "notification_before_days")
    private Integer notificationBeforeDays; // Days before expiry to send notification
    
    // Relationships
    @OneToMany(mappedBy = "retentionPolicy", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RetentionPolicyAssignment> assignments;
    
    @OneToMany(mappedBy = "retentionPolicy", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Document> documents;
    
    /**
     * Calculate retention expiry date from a given start date
     */
    @Transient
    public java.time.LocalDateTime calculateExpiryDate(java.time.LocalDateTime startDate) {
        if (startDate == null) {
            return null;
        }
        
        switch (retentionPeriodUnit) {
            case DAYS:
                return startDate.plusDays(retentionPeriod);
            case MONTHS:
                return startDate.plusMonths(retentionPeriod);
            case YEARS:
                return startDate.plusYears(retentionPeriod);
            default:
                throw new IllegalStateException("Unknown retention period unit: " + retentionPeriodUnit);
        }
    }
    
    /**
     * Get human-readable retention period description
     */
    @Transient
    public String getRetentionPeriodDescription() {
        return retentionPeriod + " " + retentionPeriodUnit.name().toLowerCase();
    }
}
