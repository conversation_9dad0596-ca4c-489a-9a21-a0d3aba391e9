--liquibase formatted sql

--changeset system:068-fix-workflow-instances-audit-columns
--comment: Add missing BaseEntity audit columns to workflow_instances table

-- Add only the missing BaseEntity audit columns to workflow_instances table
-- (created_date and last_modified_date already exist)
ALTER TABLE workflow_instances
ADD COLUMN created_by VARCHAR(100),
ADD COLUMN last_modified_by VARCHAR(100);

-- Update existing records with default audit values for the new columns
UPDATE workflow_instances
SET created_by = COALESCE(created_by, 'system'),
    last_modified_by = COALESCE(last_modified_by, 'system')
WHERE created_by IS NULL OR last_modified_by IS NULL;