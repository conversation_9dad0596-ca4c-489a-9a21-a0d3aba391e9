package com.ascentbusiness.dms_svc.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Entity representing workflow history and audit trail
 */
@Entity
@Table(name = "workflow_history")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowHistory extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_instance_id", nullable = false)
    @JsonIgnore
    private WorkflowInstance workflowInstance;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_task_id")
    @JsonIgnore
    private WorkflowTask workflowTask;

    @Column(name = "event_type", nullable = false, length = 50)
    private String eventType; // STARTED, STAGE_ENTERED, TASK_ASSIGNED, TASK_COMPLETED, ESCALATED, DELEGATED, COMPLETED, CANCELLED

    @Column(name = "event_description", columnDefinition = "TEXT")
    private String eventDescription;

    // Actor information
    @Column(name = "actor_user_id", length = 255)
    private String actorUserId;

    @Column(name = "actor_role", length = 255)
    private String actorRole;

    @Column(name = "on_behalf_of_user_id", length = 255)
    private String onBehalfOfUserId;

    // Event data
    @Column(name = "old_status", length = 50)
    private String oldStatus;

    @Column(name = "new_status", length = 50)
    private String newStatus;

    @Column(name = "stage_name", length = 255)
    private String stageName;

    @Column(name = "task_name", length = 255)
    private String taskName;

    @Column(name = "action_taken", length = 50)
    private String actionTaken;

    @Column(name = "comments", columnDefinition = "TEXT")
    private String comments;

    @Column(name = "event_timestamp", nullable = false)
    @Builder.Default
    private LocalDateTime eventTimestamp = LocalDateTime.now();

    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    /**
     * Check if this is a task-related event
     */
    @Transient
    public boolean isTaskEvent() {
        return workflowTask != null;
    }

    /**
     * Check if this is a workflow-level event
     */
    @Transient
    public boolean isWorkflowEvent() {
        return workflowTask == null;
    }

    /**
     * Check if this event represents a status change
     */
    @Transient
    public boolean isStatusChange() {
        return oldStatus != null && newStatus != null && !oldStatus.equals(newStatus);
    }

    /**
     * Check if this event has comments
     */
    @Transient
    public boolean hasComments() {
        return comments != null && !comments.trim().isEmpty();
    }

    /**
     * Get the workflow instance name
     */
    @Transient
    public String getWorkflowInstanceName() {
        return workflowInstance != null ? workflowInstance.getInstanceName() : null;
    }

    /**
     * Get the document name associated with this history entry
     */
    @Transient
    public String getDocumentName() {
        return workflowInstance != null && workflowInstance.getDocument() != null ? 
               workflowInstance.getDocument().getName() : null;
    }

    /**
     * Get a formatted description of the event
     */
    @Transient
    public String getFormattedDescription() {
        StringBuilder description = new StringBuilder();
        
        if (actorUserId != null) {
            description.append(actorUserId);
        } else if (actorRole != null) {
            description.append(actorRole);
        } else {
            description.append("System");
        }
        
        description.append(" ").append(eventType.toLowerCase().replace("_", " "));
        
        if (stageName != null) {
            description.append(" in stage '").append(stageName).append("'");
        }
        
        if (taskName != null) {
            description.append(" for task '").append(taskName).append("'");
        }
        
        if (actionTaken != null) {
            description.append(" with action '").append(actionTaken).append("'");
        }
        
        return description.toString();
    }

    @Override
    public String toString() {
        return String.format("WorkflowHistory{id=%d, type='%s', actor='%s', timestamp=%s}", 
                           getId(), eventType, actorUserId, eventTimestamp);
    }
}
