package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ExportFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;

/**
 * Request DTO for audit export operations
 * Used in GraphQL audit export mutations and REST controller
 */
@Data
public class AuditExportRequest {
    // GraphQL-specific fields
    private AuditLogFilterInput filterCriteria;
    private ExportFormat format;
    private Boolean includeMetadata = true;
    private Boolean includeUserDetails = true;
    private DateRangeInput dateRange;
    private Integer maxRecords = 10000;
    private Boolean compressionEnabled = false;
    private Boolean encryptionEnabled = false;
    private String notificationEmail;

    // REST controller compatibility fields
    private String exportType;
    private String exportFormat;
    private LocalDateTime dateFrom;
    private LocalDateTime dateTo;
    private Long documentId;
    private String userId;
    private Long complianceFrameworkId;
    private String regulatoryRequirement;
    private Boolean includeSensitiveData;

    @Data
    public static class DateRangeInput {
        private OffsetDateTime startDate;
        private OffsetDateTime endDate;
    }

    @Data
    public static class AuditLogFilterInput {
        private Long documentId;
        private String userId;
        private String action;
        private OffsetDateTime dateFrom;
        private OffsetDateTime dateTo;
        private String ipAddress;
        private String correlationId;
        private String sessionId;
        private String entityType;
        private String entityId;
        private Boolean success;
        private String searchTerm;
    }
}
