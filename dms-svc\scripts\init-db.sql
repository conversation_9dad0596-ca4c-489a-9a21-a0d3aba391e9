-- Database initialization script for Docker environment
-- This script is executed when the MySQL container starts for the first time

-- Create the DMS database if it doesn't exist
CREATE DATABASE IF NOT EXISTS dms_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create the DMS user if it doesn't exist
CREATE USER IF NOT EXISTS 'dms_user'@'%' IDENTIFIED BY 'dms_password';

-- Grant all privileges on the DMS database to the DMS user
GRANT ALL PRIVILEGES ON dms_db.* TO 'dms_user'@'%';

-- Grant SELECT privilege on mysql.proc for Liquibase
GRANT SELECT ON mysql.proc TO 'dms_user'@'%';

-- Flush privileges to ensure they take effect
FLUSH PRIVILEGES;

-- Use the DMS database
USE dms_db;

-- Create a simple health check table for monitoring
CREATE TABLE IF NOT EXISTS health_check (
    id INT PRIMARY KEY AUTO_INCREMENT,
    check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(10) DEFAULT 'OK'
);

-- Insert initial health check record
INSERT INTO health_check (status) VALUES ('OK');

-- Create indexes for better performance
-- Note: Main tables will be created by Liquibase migrations

-- Set MySQL configuration for better performance
SET GLOBAL innodb_buffer_pool_size = 268435456; -- 256MB
SET GLOBAL innodb_log_file_size = 67108864; -- 64MB
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL innodb_flush_method = O_DIRECT;

-- Enable query cache for better read performance
SET GLOBAL query_cache_type = ON;
SET GLOBAL query_cache_size = 67108864; -- 64MB

-- Optimize for Docker environment
SET GLOBAL max_connections = 200;
SET GLOBAL thread_cache_size = 16;
SET GLOBAL table_open_cache = 2000;

-- Set timezone
SET GLOBAL time_zone = '+00:00';

-- Log successful initialization
INSERT INTO health_check (status) VALUES ('INITIALIZED');
