package com.ascentbusiness.dms_svc.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.util.Set;

/**
 * Entity representing a stage within a workflow definition
 */
@Entity
@Table(name = "workflow_stages")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowStage extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_definition_id", nullable = false)
    @JsonIgnore
    private WorkflowDefinition workflowDefinition;

    @Column(name = "stage_name", nullable = false, length = 255)
    private String stageName;

    @Column(name = "stage_order", nullable = false)
    private Integer stageOrder;

    @Column(name = "stage_type", nullable = false, length = 50)
    @Builder.Default
    private String stageType = "APPROVAL"; // APPROVAL, REVIEW, NOTIFICATION, CONDITIONAL

    @Builder.Default
    @Column(name = "is_required", nullable = false)
    private Boolean isRequired = true;

    @Builder.Default
    @Column(name = "is_parallel", nullable = false)
    private Boolean isParallel = false;

    @Column(name = "min_approvals_required")
    @Builder.Default
    private Integer minApprovalsRequired = 1;

    @Column(name = "approval_percentage_required", precision = 5, scale = 2)
    private BigDecimal approvalPercentageRequired;

    // Assignee configuration
    @Column(name = "assignee_type", nullable = false, length = 50)
    private String assigneeType; // USER, ROLE, DEPARTMENT, DYNAMIC

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "assignee_values", columnDefinition = "JSON")
    private JsonNode assigneeValues;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "fallback_assignees", columnDefinition = "JSON")
    private JsonNode fallbackAssignees;

    // Timing and escalation
    @Column(name = "timeout_hours")
    @Builder.Default
    private Integer timeoutHours = 24;

    @Builder.Default
    @Column(name = "escalation_enabled", nullable = false)
    private Boolean escalationEnabled = true;

    @Column(name = "escalation_hours")
    @Builder.Default
    private Integer escalationHours = 8;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "escalation_assignees", columnDefinition = "JSON")
    private JsonNode escalationAssignees;

    // Conditions and rules
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "entry_conditions", columnDefinition = "JSON")
    private JsonNode entryConditions;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "exit_conditions", columnDefinition = "JSON")
    private JsonNode exitConditions;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "auto_approve_conditions", columnDefinition = "JSON")
    private JsonNode autoApproveConditions;

    // Relationships
    @OneToMany(mappedBy = "workflowStage", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowTask> tasks;

    @OneToMany(mappedBy = "fromStage", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowTransition> outgoingTransitions;

    @OneToMany(mappedBy = "toStage", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowTransition> incomingTransitions;

    /**
     * Check if this stage requires multiple approvals
     */
    @Transient
    public boolean requiresMultipleApprovals() {
        return minApprovalsRequired != null && minApprovalsRequired > 1;
    }

    /**
     * Check if this stage uses percentage-based approval
     */
    @Transient
    public boolean usesPercentageApproval() {
        return approvalPercentageRequired != null && approvalPercentageRequired.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Check if this stage can be auto-approved
     */
    @Transient
    public boolean canBeAutoApproved() {
        return autoApproveConditions != null && !autoApproveConditions.isNull();
    }

    /**
     * Check if this stage has escalation configured
     */
    @Transient
    public boolean hasEscalation() {
        return escalationEnabled != null && escalationEnabled && 
               escalationAssignees != null && !escalationAssignees.isNull();
    }

    /**
     * Check if this stage has fallback assignees
     */
    @Transient
    public boolean hasFallbackAssignees() {
        return fallbackAssignees != null && !fallbackAssignees.isNull();
    }

    /**
     * Get the effective timeout for this stage
     */
    @Transient
    public int getEffectiveTimeoutHours() {
        return timeoutHours != null ? timeoutHours : 24;
    }

    /**
     * Get the effective escalation timeout for this stage
     */
    @Transient
    public int getEffectiveEscalationHours() {
        return escalationHours != null ? escalationHours : 8;
    }

    @Override
    public String toString() {
        return String.format("WorkflowStage{id=%d, name='%s', order=%d, type='%s', required=%s}", 
                           getId(), stageName, stageOrder, stageType, isRequired);
    }
}
