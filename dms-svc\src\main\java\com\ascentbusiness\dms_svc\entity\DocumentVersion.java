package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.DocumentStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Entity representing different versions of a document.
 * Stores version-specific information including file content,
 * metadata, and version tracking information.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@Entity
@Table(name = "document_versions")
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentVersion extends BaseEntity {

    /** Primary key for the document version */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** The parent document this version belongs to */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;

    /** Version number for this document version */
    @Column(name = "version_number", nullable = false)
    private Integer versionNumber;

    /** Original file name for this version */
    @Column(name = "file_name", nullable = false)
    private String fileName;

    /** File size in bytes for this version */
    @Column(name = "file_size", nullable = false)
    private Long fileSize;

    /** MIME type of the file for this version */
    @Column(name = "mime_type", nullable = false)
    private String mimeType;

    /** Storage path where the file is stored */
    @Column(name = "storage_path", nullable = false)
    private String storagePath;

    /** Current status of this document version */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private DocumentStatus status = DocumentStatus.ACTIVE;

    /** Binary content of the file (for database storage) */
    @Lob
    @Column(name = "file_content", columnDefinition = "LONGBLOB")
    private byte[] fileContent;

    /** Checksum for file integrity verification */
    @Column(name = "checksum")
    private String checksum;

    /** Notes about this version */
    @Column(name = "version_notes")
    private String versionNotes;

    /** Whether this is the current/active version of the document */
    @Column(name = "is_current", nullable = false)
    private Boolean isCurrent = false;
}
