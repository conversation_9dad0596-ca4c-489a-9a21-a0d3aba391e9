package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response object for cleanup operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CleanupResult {
    
    /**
     * Whether the cleanup operation was successful
     */
    private boolean success;
    
    /**
     * Number of items deleted during cleanup
     */
    private int deletedCount;
    
    /**
     * Descriptive message about the cleanup operation
     */
    private String message;
}
