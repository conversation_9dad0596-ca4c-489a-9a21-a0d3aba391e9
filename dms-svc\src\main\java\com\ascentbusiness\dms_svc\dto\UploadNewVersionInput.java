package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class UploadNewVersionInput {
    private Long documentId;                     // Required - ID of the existing document to create a new version for
    private MultipartFile file;                 // Required - the new version file
    private String name;                         // Optional - can inherit from original if not provided
    private String description;                  // Optional - can inherit from original if not provided
    private StorageProvider storageProvider;     // Optional - defaults to application.properties setting
    private List<String> keywords;               // Optional in GraphQL (using keywords to match existing schema pattern)
    private Boolean overrideFile = false;        // NEW - duplicate control (default: false)
    private String comment;                      // NEW - optional comment for audit trail

    // Metadata fields (optional) - will update existing metadata if provided
    private DocumentClassificationMetadataInput classificationMetadata;
    private DocumentOwnershipMetadataInput ownershipMetadata;
    private DocumentComplianceMetadataInput complianceMetadata;
}
