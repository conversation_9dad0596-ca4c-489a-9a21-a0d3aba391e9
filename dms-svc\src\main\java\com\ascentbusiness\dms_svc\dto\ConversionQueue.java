package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * DTO for conversion queue information from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionQueue {
    private String queueId;
    private Integer totalJobs;
    private Integer pendingJobs;
    private Integer processingJobs;
    private Integer completedJobs;
    private Integer failedJobs;
    private Long estimatedWaitTime; // milliseconds
    private List<ConversionJob> jobs;
}
