@echo off
REM DMS Service - VSCode-Friendly Test Runner
REM Lightweight version designed to work well with VSCode
REM Runs essential tests without overwhelming system resources

echo ========================================
echo DMS Service VSCode Test Runner
echo ========================================
echo Version: 1.0 - VSCode Development Friendly
echo.

REM Set variables
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set REPORT_DIR=target\vscode-test-reports\%TIMESTAMP%
set LOG_FILE=%REPORT_DIR%\test-execution.log

REM Set lightweight Maven options
set MAVEN_OPTS=-Xmx512m -XX:MaxMetaspaceSize=128m -Djava.awt.headless=true

REM Parse command line arguments
set RUN_UNIT_TESTS=1
set RUN_BASIC_INTEGRATION=1
set GENERATE_REPORTS=0

:parse_args
if "%1"=="" goto :args_done
if /i "%1"=="--unit-only" set RUN_BASIC_INTEGRATION=0
if /i "%1"=="--with-reports" set GENERATE_REPORTS=1
if /i "%1"=="--help" goto :show_help
shift
goto :parse_args

:args_done

echo Starting VSCode-friendly test execution...
echo Logs will be saved to: %LOG_FILE%
echo.
goto :main_execution

:show_help
echo.
echo Usage: run-vscode-tests.bat [OPTIONS]
echo.
echo Options:
echo   --unit-only      Run only unit tests (fastest)
echo   --with-reports   Generate test reports
echo   --help           Show this help message
echo.
echo Examples:
echo   run-vscode-tests.bat                # Run essential tests
echo   run-vscode-tests.bat --unit-only    # Run only unit tests
echo   run-vscode-tests.bat --with-reports # Include reports
echo.
exit /b 0

:main_execution

REM Create report directory
if not exist "%REPORT_DIR%" mkdir "%REPORT_DIR%"
echo Test execution started at %date% %time% > "%LOG_FILE%"

REM Check prerequisites
echo Checking prerequisites...
where mvn >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Maven is not installed or not in PATH
    goto :error
)

REM Quick compile (no clean to avoid VSCode conflicts)
echo [1/3] Quick compilation...
call mvn compile test-compile -Denforcer.skip=true -q --batch-mode >> "%LOG_FILE%" 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Compilation failed. Check %LOG_FILE% for details.
    goto :error
)
echo ✓ Compilation successful

REM Run Unit Tests (essential ones only)
if %RUN_UNIT_TESTS%==1 (
    echo [2/3] Running Essential Unit Tests...
    call mvn test -Dtest="**/service/*Test,**/util/*Test,**/config/*Test" -Denforcer.skip=true -q --batch-mode >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Some unit tests failed. Check %LOG_FILE% for details.
    ) else (
        echo ✓ Unit tests passed
    )
) else (
    echo [2/3] Skipping Unit Tests...
)

REM Run Basic Integration Tests (if enabled)
if %RUN_BASIC_INTEGRATION%==1 (
    echo [3/3] Running Basic Integration Tests...
    call mvn test -Dtest="**/*BasicTest" -Denforcer.skip=true -q --batch-mode >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Some basic integration tests failed. Check %LOG_FILE% for details.
    ) else (
        echo ✓ Basic integration tests passed
    )
) else (
    echo [3/3] Skipping Basic Integration Tests...
)

REM Generate minimal reports if requested
if %GENERATE_REPORTS%==1 (
    echo Generating test reports...
    call mvn surefire-report:report-only -Denforcer.skip=true -q --batch-mode >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Report generation had issues
    ) else (
        echo ✓ Reports generated
    )
)

echo.
echo ========================================
echo VSCODE TEST EXECUTION SUMMARY
echo ========================================
echo Execution completed at %date% %time%
echo.
echo ✓ VSCode-friendly tests completed!
echo.
echo Reports available in: %REPORT_DIR%
echo Execution Log: %LOG_FILE%
echo.
echo For comprehensive testing, use: run-all-tests.bat
echo ========================================
exit /b 0

:error
echo.
echo ❌ VSCODE TEST EXECUTION FAILED
echo Check %LOG_FILE% for details
echo ========================================
exit /b 1
