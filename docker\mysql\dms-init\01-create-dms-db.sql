-- MySQL Initialization Script for DMS Service Only
-- This script creates database and user for DMS service

-- Create database
CREATE DATABASE IF NOT EXISTS `dms_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user and grant permissions
CREATE USER IF NOT EXISTS 'dms_user'@'%' IDENTIFIED BY 'dms_password';
GRANT ALL PRIVILEGES ON `dms_db`.* TO 'dms_user'@'%';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Display created database
SHOW DATABASES;

-- Display user
SELECT User, Host FROM mysql.user WHERE User = 'dms_user';
