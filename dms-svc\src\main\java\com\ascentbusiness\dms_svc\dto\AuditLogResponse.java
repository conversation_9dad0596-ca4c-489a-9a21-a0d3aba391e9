package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.AuditLog;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.util.XssProtectionUtil;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Response DTO for AuditLog that excludes sensitive fields
 */
@Data
public class AuditLogResponse {
    
    private Long id;
    private Long documentId;
    private String userId;
    private AuditAction action;
    private String details;
    private String userAgent;
    private String correlationId;
    private LocalDateTime timestamp;
    
    public static AuditLogResponse fromEntity(AuditLog auditLog) {
        AuditLogResponse response = new AuditLogResponse();
        response.setId(auditLog.getId());
        response.setDocumentId(auditLog.getDocumentId());
        response.setUserId(auditLog.getUserId());
        response.setAction(auditLog.getAction());
        response.setDetails(XssProtectionUtil.sanitizeAndRemoveScripts(auditLog.getDetails()));
        response.setUserAgent(auditLog.getUserAgent());
        response.setCorrelationId(auditLog.getCorrelationId());
        response.setTimestamp(auditLog.getTimestamp() != null ? auditLog.getTimestamp().toLocalDateTime() : null);
        return response;
    }
}
