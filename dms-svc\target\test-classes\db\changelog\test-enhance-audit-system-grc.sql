-- Liquibase formatted SQL
-- changeset dms:test-enhance-audit-system-grc

-- Add tamper-proof fields to existing audit_logs table (H2 compatible)
ALTER TABLE audit_logs 
ADD COLUMN event_type VARCHAR(50) DEFAULT 'DOCUMENT_OPERATION';

ALTER TABLE audit_logs 
ADD COLUMN event_category VARCHAR(50) DEFAULT 'GENERAL';

ALTER TABLE audit_logs 
ADD COLUMN compliance_framework_id BIGINT NULL;

ALTER TABLE audit_logs 
ADD COLUMN regulation_reference VARCHAR(255) NULL;

ALTER TABLE audit_logs 
ADD COLUMN data_subject_category VARCHAR(50) NULL;

ALTER TABLE audit_logs 
ADD COLUMN geographic_region VARCHAR(50) NULL;

ALTER TABLE audit_logs 
ADD COLUMN risk_level VARCHAR(20) DEFAULT 'LOW';

ALTER TABLE audit_logs 
ADD COLUMN business_impact VARCHAR(20) DEFAULT 'LOW';

ALTER TABLE audit_logs
ADD COLUMN technical_details TEXT NULL;

ALTER TABLE audit_logs
ADD COLUMN before_state TEXT NULL;

ALTER TABLE audit_logs
ADD COLUMN after_state TEXT NULL;

ALTER TABLE audit_logs 
ADD COLUMN session_id VARCHAR(100) NULL;

ALTER TABLE audit_logs 
ADD COLUMN request_id VARCHAR(100) NULL;

ALTER TABLE audit_logs
ADD COLUMN client_info TEXT NULL;

ALTER TABLE audit_logs 
ADD COLUMN hash_value VARCHAR(128) NULL;

ALTER TABLE audit_logs 
ADD COLUMN previous_hash VARCHAR(128) NULL;

ALTER TABLE audit_logs 
ADD COLUMN chain_sequence BIGINT NULL;

ALTER TABLE audit_logs
ADD COLUMN digital_signature TEXT NULL;

ALTER TABLE audit_logs 
ADD COLUMN signature_algorithm VARCHAR(50) NULL;

ALTER TABLE audit_logs 
ADD COLUMN signature_timestamp TIMESTAMP NULL;

ALTER TABLE audit_logs 
ADD COLUMN verification_status VARCHAR(20) DEFAULT 'PENDING';

ALTER TABLE audit_logs 
ADD COLUMN is_tampered BOOLEAN DEFAULT FALSE;

ALTER TABLE audit_logs 
ADD COLUMN tamper_detection_date TIMESTAMP NULL;

ALTER TABLE audit_logs 
ADD COLUMN retention_period_days INTEGER NULL;

ALTER TABLE audit_logs 
ADD COLUMN archive_date TIMESTAMP NULL;

ALTER TABLE audit_logs 
ADD COLUMN is_archived BOOLEAN DEFAULT FALSE;

ALTER TABLE audit_logs 
ADD COLUMN export_count INTEGER DEFAULT 0;

ALTER TABLE audit_logs 
ADD COLUMN last_export_date TIMESTAMP NULL;

ALTER TABLE audit_logs 
ADD COLUMN ip_address VARCHAR(45) NULL;

ALTER TABLE audit_logs 
ADD COLUMN user_agent VARCHAR(500) NULL;

-- Create audit_chain_metadata table for cryptographic chaining (H2 compatible)
CREATE TABLE audit_chain_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chain_id VARCHAR(100) NOT NULL UNIQUE,
    chain_name VARCHAR(255) NOT NULL,
    description TEXT,
    hash_algorithm VARCHAR(50) NOT NULL DEFAULT 'SHA-256',
    signature_algorithm VARCHAR(50) NOT NULL DEFAULT 'RSA-2048',
    current_sequence BIGINT NOT NULL DEFAULT 0,
    genesis_hash VARCHAR(128) NOT NULL,
    last_block_hash VARCHAR(128),
    last_block_timestamp TIMESTAMP,
    total_entries BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    verification_key TEXT,
    signing_key_fingerprint VARCHAR(64),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

-- Create indexes for audit_chain_metadata
CREATE INDEX idx_chain_id ON audit_chain_metadata(chain_id);
CREATE INDEX idx_chain_active ON audit_chain_metadata(is_active);
CREATE INDEX idx_chain_sequence ON audit_chain_metadata(current_sequence);

-- Create audit_exports table for tracking exports (H2 compatible)
CREATE TABLE audit_exports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    export_id VARCHAR(100) NOT NULL UNIQUE,
    export_type VARCHAR(50) NOT NULL,
    export_format VARCHAR(20) NOT NULL,
    export_reason VARCHAR(100),
    requested_by VARCHAR(100) NOT NULL,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    filter_criteria TEXT,
    total_records BIGINT,
    file_path VARCHAR(500),
    file_size_bytes BIGINT,
    file_hash VARCHAR(128),
    export_status VARCHAR(20) DEFAULT 'PENDING',
    completion_date TIMESTAMP,
    error_message TEXT,
    digital_signature TEXT,
    signature_timestamp TIMESTAMP,
    retention_period_days INTEGER DEFAULT 2555,
    archive_date TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    last_download_date TIMESTAMP,
    correlation_id VARCHAR(100),
    compliance_framework_id BIGINT,
    regulatory_requirement VARCHAR(255),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for audit_exports
CREATE INDEX idx_export_id ON audit_exports(export_id);
CREATE INDEX idx_export_type ON audit_exports(export_type);
CREATE INDEX idx_export_status ON audit_exports(export_status);
CREATE INDEX idx_export_requested_by ON audit_exports(requested_by);
CREATE INDEX idx_export_request_date ON audit_exports(request_date);

-- Create audit_verification_log table for tamper detection (H2 compatible)
CREATE TABLE audit_verification_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    verification_id VARCHAR(100) NOT NULL UNIQUE,
    audit_log_id BIGINT,
    chain_id VARCHAR(100),
    verification_type VARCHAR(50) NOT NULL,
    verification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_by VARCHAR(100),
    verification_method VARCHAR(50),
    expected_hash VARCHAR(128),
    actual_hash VARCHAR(128),
    hash_match BOOLEAN,
    signature_valid BOOLEAN,
    chain_integrity_valid BOOLEAN,
    overall_status VARCHAR(20),
    anomalies_detected TEXT,
    verification_details TEXT,
    correlation_id VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for audit_verification_log
CREATE INDEX idx_verification_id ON audit_verification_log(verification_id);
CREATE INDEX idx_verification_audit_log ON audit_verification_log(audit_log_id);
CREATE INDEX idx_verification_chain ON audit_verification_log(chain_id);
CREATE INDEX idx_verification_status ON audit_verification_log(overall_status);
CREATE INDEX idx_verification_date ON audit_verification_log(verification_date);

-- Create audit_summary_reports table for scheduled reporting (H2 compatible)
CREATE TABLE audit_summary_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    report_id VARCHAR(100) NOT NULL UNIQUE,
    report_type VARCHAR(50) NOT NULL,
    report_period VARCHAR(20) NOT NULL,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    generated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generated_by VARCHAR(100),
    total_events BIGINT,
    compliance_events BIGINT,
    security_events BIGINT,
    violation_events BIGINT,
    high_risk_events BIGINT,
    summary_data TEXT,
    report_file_path VARCHAR(500),
    report_file_size BIGINT,
    report_hash VARCHAR(128),
    digital_signature TEXT,
    is_published BOOLEAN DEFAULT FALSE,
    publication_date TIMESTAMP,
    retention_period_days INTEGER DEFAULT 2555,
    archive_date TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE,
    correlation_id VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for audit_summary_reports
CREATE INDEX idx_summary_report_id ON audit_summary_reports(report_id);
CREATE INDEX idx_summary_report_type ON audit_summary_reports(report_type);
CREATE INDEX idx_summary_period ON audit_summary_reports(period_start, period_end);
CREATE INDEX idx_summary_generated_date ON audit_summary_reports(generated_date);
CREATE INDEX idx_summary_published ON audit_summary_reports(is_published);

-- Insert initial audit chain metadata
INSERT INTO audit_chain_metadata (
    chain_id, 
    chain_name, 
    description, 
    genesis_hash, 
    created_by
) VALUES (
    'DMS_MAIN_AUDIT_CHAIN',
    'DMS Main Audit Chain',
    'Primary audit chain for DMS document operations and compliance events',
    'genesis_hash_' || CAST(CURRENT_TIMESTAMP AS VARCHAR),
    'SYSTEM'
);
