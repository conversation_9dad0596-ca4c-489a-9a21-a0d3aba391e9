# Document Retention Policy Framework

## Overview

The Document Retention Policy Framework provides comprehensive document lifecycle management with automated retention processing, legal hold capabilities, and disposition workflows. This framework ensures compliance with regulatory requirements while maintaining operational efficiency.

## Features

### 1. Retention Policy Management
- **Policy Definition**: Create policies with retention periods (days/months/years)
- **Disposition Actions**: Configure automatic actions (archive/delete/review/extend/transfer)
- **Policy Assignment**: Assign policies to document types, categories, departments, or MIME types
- **Priority System**: Handle multiple matching policies with priority-based selection
- **Legal Hold Support**: Enable/disable legal hold capability per policy

### 2. Document Retention Lifecycle
- **Automatic Assignment**: Auto-assign policies to documents based on criteria
- **Expiry Calculation**: Calculate retention expiry dates based on trigger events
- **Status Tracking**: Monitor disposition status throughout document lifecycle
- **Batch Processing**: Scheduled processing of retention actions

### 3. Legal Hold Management
- **Hold Application**: Apply legal holds with reasons and tracking
- **Bulk Operations**: Apply/release holds on multiple documents
- **Hold Override**: Legal holds prevent disposition regardless of expiry
- **Audit Trail**: Complete audit trail for legal hold actions

### 4. Disposition Workflows
- **Archive**: Move documents to long-term storage
- **Delete**: Permanently remove documents
- **Review**: Flag documents for manual review
- **Extend**: Automatically extend retention periods
- **Transfer**: Mark documents for transfer to other systems

## Database Schema

### Core Tables

#### retention_policies
- Policy definition with retention rules
- Disposition actions and legal hold settings
- Business justification and legal basis
- Review frequency and notification settings

#### retention_policy_assignments
- Links policies to document criteria
- Assignment types: DOCUMENT_TYPE, CATEGORY, DEPARTMENT, TAG, MIME_TYPE, CREATOR
- Priority-based matching with conditions

#### documents (enhanced)
- Added retention metadata fields:
  - `retention_policy_id`: Reference to assigned policy
  - `retention_expiry_date`: Calculated expiry date
  - `legal_hold_status`: Current legal hold status
  - `disposition_status`: Current disposition status
  - Legal hold tracking fields

## GraphQL API

### Queries

```graphql
# Get retention policy
getRetentionPolicy(id: ID!): RetentionPolicy

# Get all active policies with pagination
getAllRetentionPolicies(pagination: PaginationInput): RetentionPolicyPage!

# Get documents by retention policy
getDocumentsByRetentionPolicy(policyId: ID!, pagination: PaginationInput): DocumentPage!

# Get documents under legal hold
getDocumentsUnderLegalHold(pagination: PaginationInput): DocumentPage!

# Get documents requiring review
getDocumentsRequiringReview(pagination: PaginationInput): DocumentPage!

# Get documents expiring within specified days
getDocumentsExpiringWithin(days: Int!): [Document!]!
```

### Mutations

```graphql
# Create retention policy
createRetentionPolicy(input: RetentionPolicyInput!): RetentionPolicy!

# Update retention policy
updateRetentionPolicy(id: ID!, input: RetentionPolicyInput!): RetentionPolicy!

# Delete retention policy
deleteRetentionPolicy(id: ID!): Boolean!

# Apply legal hold
applyLegalHold(input: LegalHoldInput!): [Document!]!

# Release legal hold
releaseLegalHold(documentIds: [ID!]!): [Document!]!

# Assign retention policy to document
assignRetentionPolicyToDocument(documentId: ID!, policyId: ID!): Document!

# Approve/reject disposition
approveDisposition(input: DispositionReviewInput!): Document!
```

## Configuration

### Application Properties

```properties
# Retention Policy Configuration
dms.retention.processing.enabled=true
dms.retention.processing.batch-size=100
dms.retention.processing.cron=0 0 2 * * *
dms.retention.notification.enabled=true
dms.retention.notification.days-before-expiry=30,7,1
dms.retention.auto-assignment.enabled=true
dms.retention.legal-hold.enabled=true
```

### Scheduled Processing

The framework includes a scheduled task that runs daily at 2 AM to:
1. Assign retention policies to documents without policies
2. Calculate retention expiry dates
3. Process expired documents according to disposition actions
4. Generate audit logs for all retention activities

## Usage Examples

### Creating a Retention Policy

```graphql
mutation CreateRetentionPolicy {
  createRetentionPolicy(input: {
    name: "Contract Retention Policy"
    description: "7-year retention for contracts"
    retentionPeriod: 7
    retentionPeriodUnit: YEARS
    dispositionAction: ARCHIVE
    isActive: true
    allowLegalHold: true
    autoApply: true
    priority: 10
    businessJustification: "Legal requirement for contract retention"
    assignments: [
      {
        assignmentType: "DOCUMENT_TYPE"
        assignmentValue: "CONTRACT"
        isActive: true
        priority: 10
      }
    ]
  }) {
    id
    name
    retentionPeriodDescription
  }
}
```

### Applying Legal Hold

```graphql
mutation ApplyLegalHold {
  applyLegalHold(input: {
    documentIds: ["1", "2", "3"]
    reason: "Ongoing litigation - Case #2024-001"
  }) {
    id
    name
    legalHoldStatus
    isUnderLegalHold
  }
}
```

## Testing

The framework includes comprehensive tests:

### Unit Tests
- `RetentionPolicyServiceTest`: Service layer business logic
- `LegalHoldServiceTest`: Legal hold management
- `RetentionProcessingServiceTest`: Scheduled processing

### Integration Tests
- `RetentionPolicyIntegrationTest`: End-to-end policy lifecycle
- Database integration and repository tests

### E2E Tests
- `RetentionPolicyE2ETest`: Complete GraphQL workflow testing
- Full retention lifecycle validation

### Running Tests

```bash
# Run all retention tests
./mvnw test -Dtest="com.ascentbusiness.dms_svc.retention.RetentionTestSuite"

# Run specific test categories
./mvnw test -Dtest="*RetentionPolicy*Test"
./mvnw test -Dtest="*LegalHold*Test"
```

## Security and Compliance

### Audit Logging
All retention operations are logged with:
- User context and correlation IDs
- Detailed action descriptions
- Timestamp and reason tracking
- Legal hold audit trail

### Access Control
- JWT-based authentication
- Document-level permissions
- Admin-only policy management
- User context validation

### Data Protection
- Soft delete for policies
- Legal hold override protection
- Secure disposition processing
- Compliance reporting

## Monitoring and Reporting

### Key Metrics
- Documents processed per retention cycle
- Legal holds applied/released
- Disposition actions executed
- Policy assignment success rates

### Alerts and Notifications
- Documents expiring within notification window
- Failed retention processing
- Legal hold violations
- Policy review requirements

## Troubleshooting

### Common Issues

1. **Documents not getting policies assigned**
   - Check assignment criteria and priorities
   - Verify policy is active and auto-apply enabled
   - Review audit logs for assignment attempts

2. **Legal hold not preventing disposition**
   - Verify legal hold status is ACTIVE
   - Check disposition status is ON_HOLD
   - Ensure retention processing respects legal holds

3. **Scheduled processing not running**
   - Verify `@EnableScheduling` is configured
   - Check retention processing is enabled in properties
   - Review application logs for scheduling errors

### Debug Logging

Enable debug logging for retention operations:

```properties
logging.level.com.ascentbusiness.dms_svc.service.RetentionPolicyService=DEBUG
logging.level.com.ascentbusiness.dms_svc.service.RetentionProcessingService=DEBUG
logging.level.com.ascentbusiness.dms_svc.service.LegalHoldService=DEBUG
```

## Migration and Deployment

### Database Migration
The framework includes Liquibase migration scripts:
- `025-create-retention-policy-tables.sql`: Creates all retention tables
- Adds retention fields to existing documents table
- Updates audit action enums

### Backward Compatibility
- Existing documents remain unaffected
- Gradual policy assignment through scheduled processing
- Optional feature flags for controlled rollout

## Future Enhancements

- Advanced policy conditions with JSON rules
- Integration with external compliance systems
- Automated policy recommendations
- Machine learning for document classification
- Advanced reporting and analytics dashboard
