# Notification Service Application Only - Connects to Shared Infrastructure
# Use this when shared infrastructure is already running
version: '3.8'

services:
  # Notification Service Application
  notification-service-app:
    build:
      context: ./notification-svc
      dockerfile: Dockerfile
    container_name: notification-service-app
    ports:
      - "9091:9091"
    environment:
      # Database Configuration - Connect to shared MySQL
      - SPRING_DATASOURCE_URL=**********************************/${MYSQL_NOTIFICATION_DATABASE:-notification_db}?createDatabaseIfNotExist=true&useSSL=false&allowPublicKeyRetrieval=true
      - SPRING_DATASOURCE_USERNAME=${MYSQL_NOTIFICATION_USER:-notification_user}
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_NOTIFICATION_PASSWORD:-notification_password}
      
      # RabbitMQ Configuration - Connect to shared RabbitMQ
      - SPRING_RABBITMQ_HOST=grc-rabbitmq-shared
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=${RABBITMQ_DEFAULT_USER:-admin}
      - SPRING_RABBITMQ_PASSWORD=${RABBITMQ_DEFAULT_PASS:-admin123}
      
      # Redis Configuration - Connect to shared Redis
      - SPRING_REDIS_HOST=grc-redis-shared
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-shared_redis_password}
      
      # Application Configuration
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=9091
      - JAVA_OPTS=${NOTIFICATION_JAVA_OPTS:--Xmx1g -Xms512m}
      
      # Security Configuration
      - SECURITY_JWT_ENABLED=true
      - JWT_ISSUER=notification-service
      - JWT_AUDIENCE=notification-clients
      - JWT_SECRET=${JWT_SECRET:-sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      
      # Email Configuration
      - MAIL_HOST=${MAIL_HOST:-smtp.gmail.com}
      - MAIL_PORT=${MAIL_PORT:-587}
      - MAIL_USERNAME=${MAIL_USERNAME:-}
      - MAIL_PASSWORD=${MAIL_PASSWORD:-}
      - NOTIFICATION_FROM_EMAIL=${NOTIFICATION_FROM_EMAIL:-<EMAIL>}
      - EMAIL_ENABLED=${EMAIL_ENABLED:-true}
      - EMAIL_MOCK=${EMAIL_MOCK:-false}
      
    volumes:
      - ./notification-svc/logs:/app/logs
      - ./notification-svc/keys:/app/keys:ro
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # Note: No depends_on since infrastructure should already be running

networks:
  grc-shared-network:
    external: true
