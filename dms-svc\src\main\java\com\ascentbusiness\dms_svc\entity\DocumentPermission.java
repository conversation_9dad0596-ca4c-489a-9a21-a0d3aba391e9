package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.Permission;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Entity representing document permissions for users and roles.
 * Manages access control for documents including permission types,
 * expiration dates, and audit information.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@Entity
@Table(name = "document_permissions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentPermission extends BaseEntity {

    /** The document this permission is associated with */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;

    /** User ID for user-specific permissions */
    @Column(name = "user_id", length = 100)
    private String userId;

    /** Role name for role-based permissions */
    @Column(name = "role_name", length = 100)
    private String roleName;

    /** Type of permission granted (READ, WRITE, DELETE, ADMIN) */
    @Enumerated(EnumType.STRING)
    @Column(name = "permission_type", nullable = false, length = 20)
    private Permission permissionType;

    /** User ID of the person who granted this permission */
    @Column(name = "granted_by", nullable = false, length = 100)
    private String grantedBy;

    /** Whether this permission is currently active */
    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    /** Optional expiration date for the permission */
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    /** Additional notes about the permission */
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    /**
     * Gets the permission type for backward compatibility.
     * @return the permission type
     */
    public Permission getPermission() {
        return this.permissionType;
    }

    /**
     * Sets the permission type for backward compatibility.
     * @param permission the permission type to set
     */
    public void setPermission(Permission permission) {
        this.permissionType = permission;
    }

    /**
     * Gets the user ID who granted this permission for backward compatibility.
     * @return the user ID who granted this permission
     */
    public String getGrantedByUserId() {
        return this.grantedBy;
    }

    /**
     * Sets the user ID who granted this permission for backward compatibility.
     * @param grantedByUserId the user ID who granted this permission
     */
    public void setGrantedByUserId(String grantedByUserId) {
        this.grantedBy = grantedByUserId;
    }

    /**
     * Custom builder methods for backward compatibility.
     */
    public static class DocumentPermissionBuilder {
        /**
         * Sets the permission type for backward compatibility.
         * @param permission the permission type
         * @return the builder instance
         */
        public DocumentPermissionBuilder permission(Permission permission) {
            this.permissionType = permission;
            return this;
        }

        /**
         * Sets the user ID who granted this permission for backward compatibility.
         * @param grantedByUserId the user ID who granted this permission
         * @return the builder instance
         */
        public DocumentPermissionBuilder grantedByUserId(String grantedByUserId) {
            this.grantedBy = grantedByUserId;
            return this;
        }
    }
}
