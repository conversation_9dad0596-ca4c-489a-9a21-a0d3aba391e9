@echo off
REM Virus Scanning Test Suite Runner
REM This script runs all virus scanning related tests

echo ========================================
echo DMS Virus Scanning Test Suite
echo ========================================
echo.

REM Set test environment variables
set SPRING_PROFILES_ACTIVE=test
set DMS_VIRUS_SCANNER_MOCK_ENABLED=true
set DMS_VIRUS_SCANNING_ENABLED=true

echo Running virus scanning tests...
echo.

REM Run virus scanner unit tests
echo [1/6] Running MockVirusScanner tests...
call mvn test -Dtest=MockVirusScannerTest -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: MockVirusScanner tests
    goto :error
)
echo PASSED: MockVirusScanner tests
echo.

echo [2/6] Running VirusScannerFactory tests...
call mvn test -Dtest=VirusScannerFactoryTest -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: VirusScannerFactory tests
    goto :error
)
echo PASSED: VirusScannerFactory tests
echo.

echo [3/6] Running VirusScanningService tests...
call mvn test -Dtest=VirusScanningServiceTest -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: VirusScanningService tests
    goto :error
)
echo PASSED: VirusScanningService tests
echo.

echo [4/6] Running BulkUploadService tests...
call mvn test -Dtest=BulkUploadServiceTest -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: BulkUploadService tests
    goto :error
)
echo PASSED: BulkUploadService tests
echo.

echo [5/6] Running DocumentResolver virus scan tests...
call mvn test -Dtest=DocumentResolverVirusScanTest -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: DocumentResolver virus scan tests
    goto :error
)
echo PASSED: DocumentResolver virus scan tests
echo.

echo [6/6] Running virus scanning integration tests...
call mvn test -Dtest=VirusScanningIntegrationTest -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: Virus scanning integration tests
    goto :error
)
echo PASSED: Virus scanning integration tests
echo.

echo ========================================
echo All virus scanning tests completed successfully!
echo ========================================
echo.

REM Run all virus scanning tests together for final verification
echo Running complete virus scanning test suite...
call mvn test -Dtest="*VirusScanner*,*VirusScanning*,*BulkUpload*,*DocumentResolverVirusScan*" -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: Complete virus scanning test suite
    goto :error
)

echo.
echo ========================================
echo VIRUS SCANNING TEST SUITE: ALL TESTS PASSED
echo ========================================
echo.
echo Test Summary:
echo - MockVirusScanner unit tests: PASSED
echo - VirusScannerFactory unit tests: PASSED  
echo - VirusScanningService unit tests: PASSED
echo - BulkUploadService unit tests: PASSED
echo - DocumentResolver virus scan tests: PASSED
echo - Virus scanning integration tests: PASSED
echo - Complete test suite verification: PASSED
echo.
echo Total test categories: 6
echo Status: SUCCESS
echo.

goto :end

:error
echo.
echo ========================================
echo VIRUS SCANNING TEST SUITE: TESTS FAILED
echo ========================================
echo.
echo One or more test categories failed.
echo Please check the test output above for details.
echo.
echo To run individual test categories:
echo   mvn test -Dtest=MockVirusScannerTest
echo   mvn test -Dtest=VirusScannerFactoryTest
echo   mvn test -Dtest=VirusScanningServiceTest
echo   mvn test -Dtest=BulkUploadServiceTest
echo   mvn test -Dtest=DocumentResolverVirusScanTest
echo   mvn test -Dtest=VirusScanningIntegrationTest
echo.
exit /b 1

:end
echo Virus scanning test suite completed.
echo.
pause
