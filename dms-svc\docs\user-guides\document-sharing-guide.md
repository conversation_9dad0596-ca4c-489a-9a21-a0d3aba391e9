# Document Sharing User Guide

## Overview

The Document Sharing feature allows you to securely share documents with other users, roles, or make them publicly accessible through time-limited, secure links. This guide covers all aspects of document sharing, from basic link creation to advanced bulk operations.

## Getting Started

### Prerequisites

- You must have appropriate permissions on the document you want to share
- For document creators: You can share any document you created
- For administrators: You can share any document in the system
- For other users: You need explicit sharing permissions on the document

### Basic Concepts

- **Share Link**: A unique, secure URL that provides access to a document
- **Permission Level**: The type of access granted (READ, WRITE, DELETE, ADMIN)
- **Target Recipient**: Specific user, role, or public access
- **Expiration**: When the share link becomes invalid
- **Usage Limit**: Maximum number of times the link can be used

## Creating Share Links

### Simple Share Link

To create a basic share link for a document:

1. Navigate to the document you want to share
2. Use the GraphQL API or client interface
3. Specify the permission level (READ, WRITE, DELETE, ADMIN)
4. Set an expiration date (optional, defaults to 7 days)

**Example GraphQL Mutation:**
```graphql
mutation {
  createDocumentShareLink(
    documentId: "123"
    input: {
      permission: READ
      expiresAt: "2024-12-31T23:59:59Z"
    }
  ) {
    success
    shareLink {
      shareUrl
      expiresAt
    }
  }
}
```

### Targeted Share Link

Share a document with a specific user or role:

**For a specific user:**
```graphql
mutation {
  createDocumentShareLink(
    documentId: "123"
    input: {
      permission: READ
      targetUserId: "<EMAIL>"
      expiresAt: "2024-12-31T23:59:59Z"
    }
  ) {
    success
    shareLink {
      shareUrl
      targetUserId
    }
  }
}
```

**For a role:**
```graphql
mutation {
  createDocumentShareLink(
    documentId: "123"
    input: {
      permission: READ
      targetRoleName: "ROLE_MANAGER"
      expiresAt: "2024-12-31T23:59:59Z"
    }
  ) {
    success
    shareLink {
      shareUrl
      targetRoleName
    }
  }
}
```

### Password-Protected Share Link

For sensitive documents, add password protection:

```graphql
mutation {
  createDocumentShareLink(
    documentId: "123"
    input: {
      permission: READ
      password: "SecurePassword123!"
      expiresAt: "2024-12-31T23:59:59Z"
    }
  ) {
    success
    shareLink {
      shareUrl
      hasPassword
    }
  }
}
```

### Usage-Limited Share Link

Limit how many times a link can be used:

```graphql
mutation {
  createDocumentShareLink(
    documentId: "123"
    input: {
      permission: READ
      maxUses: 10
      expiresAt: "2024-12-31T23:59:59Z"
    }
  ) {
    success
    shareLink {
      shareUrl
      maxUses
    }
  }
}
```

## Bulk Sharing

### Sharing Multiple Documents

Share several documents with multiple recipients in one operation:

```graphql
mutation {
  bulkShareDocuments(
    input: {
      documentIds: ["123", "456", "789"]
      recipientIds: ["<EMAIL>", "<EMAIL>"]
      roleNames: ["ROLE_VIEWER", "ROLE_EDITOR"]
      permission: READ
      expiresAt: "2024-12-31T23:59:59Z"
      notes: "Q4 Financial Reports"
    }
  ) {
    success
    operation {
      operationId
      totalDocuments
      totalRecipients
      successCount
      failureCount
    }
  }
}
```

This operation will create share links for:
- 3 documents × (2 users + 2 roles) = 12 total share links

### Monitoring Bulk Operations

Check the status of a bulk sharing operation:

```graphql
query {
  bulkShareOperation(operationId: "bulk-op-123") {
    isCompleted
    successCount
    failureCount
    items {
      documentId
      recipientId
      isSuccessful
      errorMessage
      shareUrl
    }
  }
}
```

## Managing Share Links

### Viewing Existing Share Links

See all active share links for a document:

```graphql
query {
  documentShareLinks(documentId: "123") {
    linkId
    permission
    targetUserId
    targetRoleName
    isActive
    useCount
    maxUses
    expiresAt
    shareUrl
  }
}
```

### Revoking Share Links

Deactivate a share link before it expires:

```graphql
mutation {
  revokeDocumentShareLink(linkId: "abc123-def456") {
    success
    message
  }
}
```

## Accessing Shared Documents

### Using a Share Link

Recipients can access shared documents using the provided URL:

```graphql
query {
  accessSharedDocument(
    input: {
      linkId: "abc123-def456"
    }
  ) {
    success
    document {
      id
      name
      mimeType
      fileSize
    }
    permission
  }
}
```

### Password-Protected Access

For password-protected links, include the password:

```graphql
query {
  accessSharedDocument(
    input: {
      linkId: "abc123-def456"
      password: "SecurePassword123!"
    }
  ) {
    success
    document {
      id
      name
    }
    permission
  }
}
```

## Best Practices

### Security

1. **Use Appropriate Permissions**: Grant the minimum permission level required
2. **Set Expiration Dates**: Always set reasonable expiration dates
3. **Password Protection**: Use passwords for sensitive documents
4. **Regular Audits**: Review and revoke unnecessary share links
5. **Monitor Usage**: Check usage counts for suspicious activity

### Performance

1. **Bulk Operations**: Use bulk sharing for multiple documents/recipients
2. **Reasonable Limits**: Don't create excessive numbers of share links
3. **Cleanup**: Regularly clean up expired or unused links

### Compliance

1. **Audit Trail**: All sharing activities are logged automatically
2. **Data Classification**: Consider document sensitivity when sharing
3. **Retention Policies**: Respect organizational data retention policies
4. **Access Reviews**: Periodically review who has access to what

## Troubleshooting

### Common Issues

#### "Share link not found"
- The link may have been revoked or expired
- Check if the link ID is correct
- Verify the link hasn't reached its usage limit

#### "Password required"
- The link is password-protected
- Obtain the password from the person who shared the link
- Include the password in your access request

#### "Permission denied"
- You may not have permission to share the document
- Contact the document owner or administrator
- Verify you're using the correct user account

#### "Link has expired"
- The share link has passed its expiration date
- Request a new share link from the document owner
- Check if there are other active links for the same document

### Error Messages

| Error | Meaning | Solution |
|-------|---------|----------|
| `INVALID_SHARE_LINK` | Link is invalid, expired, or inactive | Get a new share link |
| `UNAUTHORIZED` | No permission to perform action | Contact administrator |
| `VALIDATION_ERROR` | Invalid input parameters | Check input format and requirements |
| `RESOURCE_NOT_FOUND` | Document or link doesn't exist | Verify IDs are correct |

## Advanced Features

### Custom Expiration Logic

Set specific expiration times based on business rules:

```graphql
# Expire at end of business day
expiresAt: "2024-06-22T17:00:00Z"

# Expire in 24 hours
expiresAt: "2024-06-23T10:30:00Z"
```

### Bulk Operation Monitoring

Track progress of large bulk operations:

```graphql
query {
  myBulkShareOperations {
    operationId
    isCompleted
    successCount
    failureCount
    createdAt
    completedAt
  }
}
```

### Usage Analytics

Monitor how share links are being used:

```graphql
query {
  documentShareLinks(documentId: "123") {
    useCount
    maxUses
    createdAt
    expiresAt
    targetUserId
  }
}
```

## Support

For additional help with document sharing:

1. Check the API documentation for technical details
2. Review audit logs for sharing activity
3. Contact your system administrator for permission issues
4. Refer to the troubleshooting section for common problems
