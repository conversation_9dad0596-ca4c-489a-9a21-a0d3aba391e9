package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.ViolationSeverity;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Entity representing a compliance violation
 */
@Entity
@Table(name = "compliance_violations")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ComplianceViolation extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_compliance_mapping_id", nullable = false)
    private DocumentComplianceMapping documentComplianceMapping;

    @Column(name = "violation_type", length = 100, nullable = false)
    private String violationType;

    @Enumerated(EnumType.STRING)
    @Column(name = "severity", nullable = false, length = 20)
    private ViolationSeverity severity;

    @Column(name = "description", columnDefinition = "TEXT", nullable = false)
    private String description;

    @Column(name = "detected_by", length = 100)
    private String detectedBy; // System or user who detected the violation

    @Column(name = "detection_method", length = 100)
    private String detectionMethod; // AUTOMATED, MANUAL, AUDIT

    @Column(name = "violation_date", nullable = false)
    private LocalDateTime violationDate;

    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    @Column(name = "user_id", length = 100)
    private String userId; // User involved in the violation

    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;

    @Column(name = "action_attempted", length = 200)
    private String actionAttempted;

    @Column(name = "regulation_violated", length = 200)
    private String regulationViolated;

    @Column(name = "control_failed", length = 200)
    private String controlFailed;

    @Builder.Default
    @Column(name = "is_resolved", nullable = false)
    private Boolean isResolved = false;

    @Column(name = "resolved_by", length = 100)
    private String resolvedBy;

    @Column(name = "resolved_date")
    private LocalDateTime resolvedDate;

    @Column(name = "resolution_notes", columnDefinition = "TEXT")
    private String resolutionNotes;

    @Column(name = "remediation_action", columnDefinition = "TEXT")
    private String remediationAction;

    @Column(name = "remediation_deadline")
    private LocalDateTime remediationDeadline;

    @Builder.Default
    @Column(name = "requires_notification", nullable = false)
    private Boolean requiresNotification = false;

    @Column(name = "notification_sent_date")
    private LocalDateTime notificationSentDate;

    @Column(name = "notification_recipient", length = 200)
    private String notificationRecipient;

    @Builder.Default
    @Column(name = "requires_regulatory_report", nullable = false)
    private Boolean requiresRegulatoryReport = false;

    @Column(name = "regulatory_report_sent_date")
    private LocalDateTime regulatoryReportSentDate;

    @Column(name = "regulatory_authority", length = 200)
    private String regulatoryAuthority;

    @Column(name = "potential_fine_amount")
    private java.math.BigDecimal potentialFineAmount;

    @Column(name = "actual_fine_amount")
    private java.math.BigDecimal actualFineAmount;

    @Column(name = "business_impact", columnDefinition = "TEXT")
    private String businessImpact;

    @Column(name = "lessons_learned", columnDefinition = "TEXT")
    private String lessonsLearned;

    @Builder.Default
    @Column(name = "recurrence_count", nullable = false)
    private Integer recurrenceCount = 1;

    @Column(name = "previous_violation_id")
    private Long previousViolationId;

    /**
     * Check if this violation is overdue for resolution
     */
    @Transient
    public boolean isOverdue() {
        if (isResolved || remediationDeadline == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(remediationDeadline);
    }

    /**
     * Check if notification is overdue
     */
    @Transient
    public boolean isNotificationOverdue() {
        if (!requiresNotification || notificationSentDate != null) {
            return false;
        }
        
        // Notification should be sent within 72 hours for high/critical violations
        LocalDateTime deadline = violationDate.plusHours(
            severity == ViolationSeverity.CRITICAL ? 24 : 
            severity == ViolationSeverity.HIGH ? 72 : 168 // 1 week for medium/low
        );
        
        return LocalDateTime.now().isAfter(deadline);
    }

    /**
     * Check if regulatory report is overdue
     */
    @Transient
    public boolean isRegulatoryReportOverdue() {
        if (!requiresRegulatoryReport || regulatoryReportSentDate != null) {
            return false;
        }
        
        // Regulatory reports typically due within 72 hours for critical violations
        LocalDateTime deadline = violationDate.plusHours(
            severity == ViolationSeverity.CRITICAL ? 72 : 168 // 1 week for others
        );
        
        return LocalDateTime.now().isAfter(deadline);
    }

    /**
     * Get risk score based on severity and other factors
     */
    @Transient
    public int getRiskScore() {
        int score = severity.getScore();
        
        // Add points for overdue items
        if (isOverdue()) {
            score += 20;
        }
        if (isNotificationOverdue()) {
            score += 15;
        }
        if (isRegulatoryReportOverdue()) {
            score += 25;
        }
        
        // Add points for recurrence
        score += Math.min(recurrenceCount * 5, 25);
        
        // Add points for potential financial impact
        if (potentialFineAmount != null && potentialFineAmount.compareTo(java.math.BigDecimal.ZERO) > 0) {
            if (potentialFineAmount.compareTo(new java.math.BigDecimal("100000")) > 0) {
                score += 30; // High financial impact
            } else if (potentialFineAmount.compareTo(new java.math.BigDecimal("10000")) > 0) {
                score += 15; // Medium financial impact
            } else {
                score += 5; // Low financial impact
            }
        }
        
        return Math.min(score, 100); // Cap at 100
    }

    /**
     * Resolve the violation
     */
    @Transient
    public void resolve(String resolvedBy, String resolutionNotes) {
        this.isResolved = true;
        this.resolvedBy = resolvedBy;
        this.resolvedDate = LocalDateTime.now();
        this.resolutionNotes = resolutionNotes;
    }

    /**
     * Mark notification as sent
     */
    @Transient
    public void markNotificationSent(String recipient) {
        this.notificationSentDate = LocalDateTime.now();
        this.notificationRecipient = recipient;
    }

    /**
     * Mark regulatory report as sent
     */
    @Transient
    public void markRegulatoryReportSent(String authority) {
        this.regulatoryReportSentDate = LocalDateTime.now();
        this.regulatoryAuthority = authority;
    }

    /**
     * Get age of violation in days
     */
    @Transient
    public long getAgeInDays() {
        return java.time.temporal.ChronoUnit.DAYS.between(violationDate, LocalDateTime.now());
    }

    /**
     * Get display name for this violation
     */
    @Transient
    public String getDisplayName() {
        return String.format("%s - %s (%s)", violationType, severity.name(), 
                           violationDate.toLocalDate().toString());
    }

    @Override
    public String toString() {
        return String.format("ComplianceViolation{id=%d, type='%s', severity=%s, resolved=%s, date=%s}", 
                           getId(), violationType, severity, isResolved, violationDate);
    }
}
