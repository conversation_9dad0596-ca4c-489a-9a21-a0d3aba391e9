# Check if the DMS application is running and ready
$baseUrl = "http://localhost:9093"
$maxAttempts = 30
$attempt = 0

Write-Host "Checking if DMS application is running..." -ForegroundColor Yellow

while ($attempt -lt $maxAttempts) {
    $attempt++
    Write-Host "Attempt $attempt/$maxAttempts..." -ForegroundColor Gray

    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/actuator/health" -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "Application is running and healthy!" -ForegroundColor Green
            Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Cyan
            exit 0
        }
    } catch {
        Write-Host "Application not ready yet: $($_.Exception.Message)" -ForegroundColor Gray
    }

    Start-Sleep -Seconds 2
}

Write-Host "Application did not start within expected time" -ForegroundColor Red
exit 1
