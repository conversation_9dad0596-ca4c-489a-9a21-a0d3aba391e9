package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ValidationSeverity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for template validation error
 * Corresponds to TemplateValidationError GraphQL type
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateValidationError {
    
    private String code;
    private String message;
    private ValidationSeverity severity;
    private String field;
    private Integer line;
    private Integer column;
    private String suggestion;
}
