package com.ascentbusiness.dms_svc.dto;

import lombok.Data;

/**
 * Input DTO for retention policy assignments
 */
@Data
public class RetentionPolicyAssignmentInput {
    
    private String assignmentType; // e.g., "DOCUMENT_TYPE", "CATEGORY", "DEPARTMENT", "TAG"
    private String assignmentValue; // The actual value to match against
    private Boolean isActive;
    private Integer priority;
    private String conditions; // Additional JSON conditions for complex matching
    private String description;
}
