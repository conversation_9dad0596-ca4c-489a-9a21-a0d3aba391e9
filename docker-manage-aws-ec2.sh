#!/bin/bash

# AWS EC2 Production Deployment - Docker Management Script
# This script helps manage the AWS EC2 production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

COMPOSE_FILE="docker-compose.aws-ec2.yml"
ENV_FILE=".env.aws-ec2"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
}

# Function to check environment file
check_env_file() {
    if [ ! -f "$ENV_FILE" ]; then
        print_error "Environment file $ENV_FILE not found!"
        print_status "Please copy .env.aws-ec2.template to $ENV_FILE and configure it:"
        print_status "cp .env.aws-ec2.template $ENV_FILE"
        print_status "nano $ENV_FILE"
        exit 1
    fi
}

# Function to validate CORS configuration
validate_cors() {
    print_header "Validating CORS Configuration"
    
    if grep -q "CORS_ALLOWED_ORIGINS=\*" "$ENV_FILE"; then
        print_error "CORS configuration uses wildcard (*) which will fail with credentials!"
        print_error "Please update CORS_ALLOWED_ORIGINS in $ENV_FILE with specific origins:"
        print_error "Example: CORS_ALLOWED_ORIGINS=https://yourapp.com,https://www.yourapp.com"
        exit 1
    fi
    
    if ! grep -q "CORS_ALLOWED_ORIGINS=" "$ENV_FILE"; then
        print_error "CORS_ALLOWED_ORIGINS not configured in $ENV_FILE"
        print_error "Please add your frontend domain(s) to allow API access"
        exit 1
    fi
    
    print_status "CORS configuration looks good!"
}

# Function to start all services
start_all() {
    print_header "Starting GRC Platform v4 - AWS EC2 Production Deployment"
    check_docker
    check_docker_compose
    check_env_file
    validate_cors
    
    print_status "Starting infrastructure services first..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d mysql redis rabbitmq elasticsearch
    
    print_status "Waiting for infrastructure services to be ready..."
    sleep 30
    
    print_status "Starting application services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d notification-svc dms-svc
    
    print_status "Starting monitoring services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d zipkin
    
    print_status "All services started successfully!"
    show_status
}

# Function to start only application services
start_apps() {
    print_header "Starting Application Services Only"
    check_docker
    check_docker_compose
    check_env_file
    
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d notification-svc dms-svc
    print_status "Application services started!"
    show_status
}

# Function to start only infrastructure services
start_infra() {
    print_header "Starting Infrastructure Services Only"
    check_docker
    check_docker_compose
    check_env_file
    
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d mysql redis rabbitmq elasticsearch
    print_status "Infrastructure services started!"
    show_status
}

# Function to show service status
show_status() {
    print_header "AWS EC2 Production Service Status"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    print_header "Service URLs"
    
    # Get EC2 public IP from environment file
    EC2_IP=$(grep "AWS_EC2_PUBLIC_IP=" "$ENV_FILE" | cut -d'=' -f2)
    EC2_DOMAIN=$(grep "AWS_EC2_DOMAIN=" "$ENV_FILE" | cut -d'=' -f2)
    
    if [ -n "$EC2_DOMAIN" ] && [ "$EC2_DOMAIN" != "your-domain.com" ]; then
        echo "Application Services (Domain):"
        echo "  DMS Service GraphQL:        https://$EC2_DOMAIN/dms/graphql"
        echo "  DMS Service GraphiQL:       https://$EC2_DOMAIN/graphiql"
        echo "  Notification Service GraphQL: https://$EC2_DOMAIN/notifications/graphql"
        echo "  Notification Service GraphiQL: https://$EC2_DOMAIN/notifications/graphiql"
        echo ""
    fi
    
    if [ -n "$EC2_IP" ] && [ "$EC2_IP" != "your-ec2-public-ip" ]; then
        echo "Application Services (Direct IP):"
        echo "  DMS Service GraphQL:        http://$EC2_IP:9093/dms/graphql"
        echo "  DMS Service GraphiQL:       http://$EC2_IP:9093/graphiql"
        echo "  Notification Service GraphQL: http://$EC2_IP:9091/graphql"
        echo "  Notification Service GraphiQL: http://$EC2_IP:9091/graphiql"
        echo ""
    fi
    
    echo "Infrastructure Services:"
    echo "  MySQL:                      $EC2_IP:3306"
    echo "  Redis:                      $EC2_IP:6379"
    echo "  RabbitMQ Management:        http://$EC2_IP:15672"
    echo "  Elasticsearch:              http://$EC2_IP:9200"
    echo ""
    echo "Monitoring Services:"
    echo "  Zipkin:                     http://$EC2_IP:9411"
}

# Function to show logs
show_logs() {
    if [ -z "$2" ]; then
        print_header "Showing All Service Logs"
        docker-compose -f $COMPOSE_FILE logs -f
    else
        print_header "Showing Logs for: $2"
        docker-compose -f $COMPOSE_FILE logs -f "$2"
    fi
}

# Function to stop all services
stop_all() {
    print_header "Stopping All Services"
    docker-compose -f $COMPOSE_FILE down
    print_status "All services stopped!"
}

# Function to restart services
restart_service() {
    if [ -z "$2" ]; then
        print_header "Restarting All Services"
        docker-compose -f $COMPOSE_FILE restart
    else
        print_header "Restarting Service: $2"
        docker-compose -f $COMPOSE_FILE restart "$2"
    fi
    print_status "Restart completed!"
}

# Function to clean up everything
cleanup() {
    print_warning "This will stop all services and remove all data!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_header "Cleaning Up Everything"
        docker-compose -f $COMPOSE_FILE down -v --rmi all
        docker system prune -f
        print_status "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show health status
health_check() {
    print_header "Health Check"
    
    EC2_IP=$(grep "AWS_EC2_PUBLIC_IP=" "$ENV_FILE" | cut -d'=' -f2)
    
    echo "Checking DMS Service..."
    if curl -f http://localhost:9093/actuator/health > /dev/null 2>&1; then
        print_status "DMS Service: HEALTHY"
    else
        print_error "DMS Service: UNHEALTHY"
    fi
    
    echo "Checking Notification Service..."
    if curl -f http://localhost:9091/actuator/health > /dev/null 2>&1; then
        print_status "Notification Service: HEALTHY"
    else
        print_error "Notification Service: UNHEALTHY"
    fi
    
    echo "Checking MySQL..."
    if docker-compose -f $COMPOSE_FILE exec -T mysql mysqladmin ping -h localhost -u root -proot_password > /dev/null 2>&1; then
        print_status "MySQL: HEALTHY"
    else
        print_error "MySQL: UNHEALTHY"
    fi
    
    echo "Checking Redis..."
    if docker-compose -f $COMPOSE_FILE exec -T redis redis-cli -a shared_redis_password ping > /dev/null 2>&1; then
        print_status "Redis: HEALTHY"
    else
        print_error "Redis: UNHEALTHY"
    fi
}

# Function to test CORS configuration
test_cors() {
    print_header "Testing CORS Configuration"
    
    EC2_IP=$(grep "AWS_EC2_PUBLIC_IP=" "$ENV_FILE" | cut -d'=' -f2)
    CORS_ORIGINS=$(grep "CORS_ALLOWED_ORIGINS=" "$ENV_FILE" | cut -d'=' -f2)
    
    if [ -z "$CORS_ORIGINS" ]; then
        print_error "CORS_ALLOWED_ORIGINS not configured"
        return 1
    fi
    
    # Test preflight request
    print_status "Testing CORS preflight request..."
    
    FIRST_ORIGIN=$(echo "$CORS_ORIGINS" | cut -d',' -f1)
    
    echo "Testing with origin: $FIRST_ORIGIN"
    
    curl -X OPTIONS \
        -H "Origin: $FIRST_ORIGIN" \
        -H "Access-Control-Request-Method: POST" \
        -H "Access-Control-Request-Headers: Content-Type,Authorization" \
        -v \
        "http://$EC2_IP:9093/dms/graphql" 2>&1 | grep -i "access-control"
    
    print_status "CORS test completed. Check output above for CORS headers."
}

# Function to show help
show_help() {
    echo "AWS EC2 Production Deployment - Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start-all          Start all services (infrastructure + applications + monitoring)"
    echo "  start-apps         Start only application services (notification-svc, dms-svc)"
    echo "  start-infra        Start only infrastructure services (mysql, redis, rabbitmq, elasticsearch)"
    echo "  status             Show service status and URLs"
    echo "  logs [SERVICE]     Show logs for all services or specific service"
    echo "  stop               Stop all services"
    echo "  restart [SERVICE]  Restart all services or specific service"
    echo "  health             Check health status of all services"
    echo "  test-cors          Test CORS configuration"
    echo "  cleanup            Stop all services and remove all data (WARNING: destructive)"
    echo "  help               Show this help message"
    echo ""
    echo "Prerequisites:"
    echo "  1. Copy .env.aws-ec2.template to .env.aws-ec2"
    echo "  2. Configure your AWS EC2 IP and CORS origins in .env.aws-ec2"
    echo "  3. Ensure Docker and Docker Compose are installed"
    echo ""
    echo "Examples:"
    echo "  $0 start-all                 # Start everything"
    echo "  $0 logs notification-svc     # Show notification service logs"
    echo "  $0 restart dms-svc           # Restart DMS service"
    echo "  $0 test-cors                 # Test CORS configuration"
    echo "  $0 health                    # Check service health"
}

# Main script logic
case "$1" in
    "start-all")
        start_all
        ;;
    "start-apps")
        start_apps
        ;;
    "start-infra")
        start_infra
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs "$@"
        ;;
    "stop")
        stop_all
        ;;
    "restart")
        restart_service "$@"
        ;;
    "health")
        health_check
        ;;
    "test-cors")
        test_cors
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        print_error "No command specified. Use '$0 help' for usage information."
        exit 1
        ;;
    *)
        print_error "Unknown command: $1. Use '$0 help' for usage information."
        exit 1
        ;;
esac
