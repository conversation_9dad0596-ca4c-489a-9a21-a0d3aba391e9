# Integration Test Configuration for DMS Service
# This configuration is used for integration tests with TestContainers

# Completely disable OpenTelemetry for integration tests
spring.autoconfigure.exclude=io.opentelemetry.instrumentation.spring.autoconfigure.OpenTelemetryAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration,name.nkonev.multipart.spring.graphql.MultipartGraphQlAutoConfiguration
otel.instrumentation.spring-boot.enabled=false
otel.sdk.disabled=true
otel.traces.exporter=none
otel.metrics.exporter=none

# Disable tracing completely for integration tests
management.tracing.enabled=false
management.zipkin.tracing.enabled=false

# Database Configuration - Use H2 in-memory database for integration tests
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE;LOCK_TIMEOUT=10000
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.hbm2ddl.auto=create-drop
spring.jpa.properties.hibernate.globally_quoted_identifiers=false
spring.jpa.properties.hibernate.hbm2ddl.halt_on_error=false
spring.jpa.properties.hibernate.boot.allow_jdbc_metadata_access=false

# Connection Pool Configuration for better concurrency handling
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# Liquibase Configuration - Disabled for integration tests using H2 with create-drop
spring.liquibase.enabled=false

# JWT Configuration for Integration Testing
jwt.secret=integration-test-secret-key-for-testing-only-not-for-production
jwt.expiration=3600000
dms.jwt.secret=integration-test-secret-key-for-testing-only-not-for-production
dms.jwt.expiration=3600000
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# GraphQL Configuration
spring.graphql.graphiql.enabled=true
spring.graphql.graphiql.path=/graphiql

# Server Configuration
server.port=0
server.servlet.context-path=/

# Logging Configuration
logging.level.com.ascentbusiness.dms_svc=DEBUG
logging.level.com.ascentbusiness.dms_svc.security.JwtAuthenticationFilter=DEBUG
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.tool.schema.internal.ExceptionHandlerLoggedImpl=ERROR
logging.level.org.hibernate.tool.schema.internal.Helper=ERROR
logging.level.org.hibernate.tool.schema.internal.SchemaDropperImpl=ERROR
logging.level.org.testcontainers=INFO
logging.pattern.console=%d{HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Correlation ID Configuration
dms.correlation.header-name=X-Correlation-ID

# Cache Configuration - Redis for integration tests
spring.cache.type=simple
spring.cache.redis.time-to-live=3600000

# Test Case Configuration
testcase.directory=src/test/resources/test-cases

# Storage Configuration for Integration Tests
dms.storage.test.create-sample-configurations=true
dms.storage.test.run-tests-on-startup=false

# Default Storage Provider for Integration Tests
dms.storage.default.provider=LOCAL
dms.storage.local.base-path=target/integration-test-storage
dms.storage.local.create-directories=true

# Security Configuration for Integration Tests
spring.security.user.name=integrationuser
spring.security.user.password=integrationpass
spring.security.user.roles=USER,ADMIN

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always
# Disable problematic health indicators for integration tests
management.health.elasticsearch.enabled=false
management.health.redis.enabled=false
management.health.ping.enabled=false
management.health.livenessstate.enabled=false
management.health.readinessstate.enabled=false

# Elasticsearch Configuration (disabled for integration tests)
elasticsearch.enabled=false
spring.data.elasticsearch.repositories.enabled=false

# Redis Configuration (disabled for integration tests)
spring.data.redis.repositories.enabled=false

# Disable rate limiting for integration tests
dms.rate-limit.enabled=false
dms.rate-limit.use-redis=false

# Integration Test specific properties
test.data.cleanup.enabled=true
test.mock.external.services=true
test.performance.enabled=false
test.containers.mysql.version=8.0
test.containers.redis.version=7.0

# TestContainers Configuration
testcontainers.reuse.enable=true
