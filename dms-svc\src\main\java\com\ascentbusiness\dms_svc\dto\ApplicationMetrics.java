package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

/**
 * Application metrics DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class ApplicationMetrics {
    private Long uptime;
    private Long totalRequests;
    private Long totalErrors;
    private Double averageResponseTime;
    private Double peakResponseTime;
    private Integer activeUsers;
    private Long documentsProcessed;
    private Long storageUsed;
    private CacheStatistics cacheStatistics;

    // Fields expected by tests
    private Long requestCount;
    private Long errorCount;
}
