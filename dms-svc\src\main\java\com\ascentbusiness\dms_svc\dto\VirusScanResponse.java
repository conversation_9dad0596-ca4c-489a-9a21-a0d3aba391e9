package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.VirusScanResult;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Data Transfer Object representing the response from a virus scan operation.
 * 
 * <p>This DTO encapsulates all information returned by a virus scanner after
 * scanning a file, including the scan result, detected threats, scanner
 * information, and timing details.
 * 
 * <p>The response provides comprehensive information for audit logging,
 * security monitoring, and decision-making regarding file upload approval.
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VirusScanResponse {
    
    /**
     * The result of the virus scan operation.
     */
    private VirusScanResult result;
    
    /**
     * The type of scanner that performed the scan.
     */
    private VirusScannerType scannerType;
    
    /**
     * Name of the file that was scanned.
     */
    private String fileName;
    
    /**
     * Size of the scanned file in bytes.
     */
    private Long fileSize;
    
    /**
     * Timestamp when the scan was initiated.
     */
    private LocalDateTime scanStartTime;
    
    /**
     * Timestamp when the scan was completed.
     */
    private LocalDateTime scanEndTime;
    
    /**
     * Duration of the scan operation in milliseconds.
     */
    private Long scanDurationMs;
    
    /**
     * List of detected threats (viruses, malware, etc.).
     * Empty if no threats were detected.
     */
    private List<String> detectedThreats;
    
    /**
     * Detailed message from the scanner.
     * May contain additional information about the scan result.
     */
    private String scannerMessage;
    
    /**
     * Error message if the scan failed.
     * Null if the scan completed successfully.
     */
    private String errorMessage;
    
    /**
     * Version of the virus scanner engine.
     */
    private String scannerVersion;
    
    /**
     * Version of the virus definitions used.
     */
    private String definitionsVersion;
    
    /**
     * Unique identifier for this scan operation.
     * Used for correlation and audit purposes.
     */
    private String scanId;
    
    /**
     * Additional metadata from the scanner.
     * May contain scanner-specific information.
     */
    private String metadata;
    
    /**
     * Checks if the scan result allows the file to be uploaded.
     * 
     * @return true if the file can be uploaded, false if it should be blocked
     */
    public boolean isUploadAllowed() {
        return result != null && result.isAllowUpload();
    }
    
    /**
     * Checks if the scan completed successfully.
     * 
     * @return true if the scan completed (regardless of result), false if scan failed
     */
    public boolean isScanSuccessful() {
        return result != null && result.isScanSuccessful();
    }
    
    /**
     * Checks if any threats were detected.
     * 
     * @return true if threats were detected, false otherwise
     */
    public boolean hasThreats() {
        return detectedThreats != null && !detectedThreats.isEmpty();
    }
    
    /**
     * Gets the number of detected threats.
     * 
     * @return the count of detected threats
     */
    public int getThreatCount() {
        return detectedThreats != null ? detectedThreats.size() : 0;
    }
    
    /**
     * Creates a clean scan response for successful scans with no threats.
     * 
     * @param scannerType the type of scanner used
     * @param fileName name of the scanned file
     * @param fileSize size of the scanned file
     * @param scanId unique scan identifier
     * @return a clean scan response
     */
    public static VirusScanResponse clean(VirusScannerType scannerType, String fileName, 
                                        Long fileSize, String scanId) {
        return VirusScanResponse.builder()
                .result(VirusScanResult.CLEAN)
                .scannerType(scannerType)
                .fileName(fileName)
                .fileSize(fileSize)
                .scanId(scanId)
                .scanStartTime(LocalDateTime.now())
                .scanEndTime(LocalDateTime.now())
                .scanDurationMs(0L)
                .detectedThreats(List.of())
                .scannerMessage("File is clean")
                .build();
    }
    
    /**
     * Creates an infected scan response for files with detected threats.
     * 
     * @param scannerType the type of scanner used
     * @param fileName name of the scanned file
     * @param fileSize size of the scanned file
     * @param scanId unique scan identifier
     * @param threats list of detected threats
     * @return an infected scan response
     */
    public static VirusScanResponse infected(VirusScannerType scannerType, String fileName, 
                                           Long fileSize, String scanId, List<String> threats) {
        return VirusScanResponse.builder()
                .result(VirusScanResult.INFECTED)
                .scannerType(scannerType)
                .fileName(fileName)
                .fileSize(fileSize)
                .scanId(scanId)
                .scanStartTime(LocalDateTime.now())
                .scanEndTime(LocalDateTime.now())
                .scanDurationMs(0L)
                .detectedThreats(threats)
                .scannerMessage("Threats detected: " + String.join(", ", threats))
                .build();
    }
    
    /**
     * Creates an error scan response for failed scans.
     * 
     * @param scannerType the type of scanner used
     * @param fileName name of the scanned file
     * @param fileSize size of the scanned file
     * @param scanId unique scan identifier
     * @param errorMessage the error message
     * @return an error scan response
     */
    public static VirusScanResponse error(VirusScannerType scannerType, String fileName, 
                                        Long fileSize, String scanId, String errorMessage) {
        return VirusScanResponse.builder()
                .result(VirusScanResult.ERROR)
                .scannerType(scannerType)
                .fileName(fileName)
                .fileSize(fileSize)
                .scanId(scanId)
                .scanStartTime(LocalDateTime.now())
                .scanEndTime(LocalDateTime.now())
                .scanDurationMs(0L)
                .detectedThreats(List.of())
                .errorMessage(errorMessage)
                .scannerMessage("Scan failed: " + errorMessage)
                .build();
    }
}
