# Local Development Environment - Docker Compose Configuration
# This configuration is for local development with shared infrastructure
version: '3.8'

services:
  # =============================================================================
  # APPLICATION SERVICES
  # =============================================================================
  
  # DMS Service
  dms-svc:
    build:
      context: ./dms-svc
      dockerfile: Dockerfile
      target: runtime
    container_name: dms-svc-local
    ports:
      - "9093:9093"
      - "9464:9464"  # Prometheus metrics endpoint
    environment:
      - SPRING_PROFILES_ACTIVE=local-dev
      - SERVER_PORT=9093
      - SPRING_DATASOURCE_URL=******************************/${MYSQL_DMS_DATABASE:-dms_db}?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
      - SPRING_DATASOURCE_USERNAME=${MYSQL_DMS_USER:-dms_user}
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_DMS_PASSWORD:-dms_password}
      - SPRING_REDIS_HOST=redis-shared
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-local_redis_password}
      - DMS_STORAGE_PROVIDER=${DMS_STORAGE_PROVIDER:-LOCAL}
      - DMS_STORAGE_LOCAL_BASE_PATH=${DMS_STORAGE_LOCAL_BASE_PATH:-/app/storage}
      - DMS_BASE_URL=${DMS_BASE_URL:-http://localhost:9093}
      - JWT_SECRET=${JWT_SECRET:-localDevSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      
      # Local Development CORS Configuration
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:4200,http://localhost:3000,http://localhost:8080}
      
      # Tracing Configuration
      - OTEL_TRACES_EXPORTER=zipkin
      - OTEL_EXPORTER_ZIPKIN_ENDPOINT=http://zipkin-shared:9411/api/v2/spans
      - OTEL_METRICS_EXPORTER=console
      - OTEL_LOGS_EXPORTER=console
      
    volumes:
      - dms_storage_local:/app/storage
      - dms_logs_local:/app/logs
    depends_on:
      mysql-shared:
        condition: service_healthy
      redis-shared:
        condition: service_healthy
      elasticsearch-shared:
        condition: service_healthy
    networks:
      - local-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9093/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Notification Service
  notification-svc:
    build:
      context: ./notification-svc
      dockerfile: Dockerfile
    container_name: notification-svc-local
    ports:
      - "9091:9091"
    environment:
      # Database Configuration
      - SPRING_DATASOURCE_URL=******************************/${MYSQL_NOTIFICATION_DATABASE:-notification_db}?createDatabaseIfNotExist=true&useSSL=false&allowPublicKeyRetrieval=true
      - SPRING_DATASOURCE_USERNAME=${MYSQL_NOTIFICATION_USER:-notification_user}
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_NOTIFICATION_PASSWORD:-notification_password}
      
      # RabbitMQ Configuration
      - SPRING_RABBITMQ_HOST=rabbitmq-shared
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=${RABBITMQ_DEFAULT_USER:-admin}
      - SPRING_RABBITMQ_PASSWORD=${RABBITMQ_DEFAULT_PASS:-admin123}
      
      # Redis Configuration
      - SPRING_REDIS_HOST=redis-shared
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-local_redis_password}
      
      # Application Configuration
      - SPRING_PROFILES_ACTIVE=local-dev
      - SERVER_PORT=9091
      - JAVA_OPTS=${NOTIFICATION_JAVA_OPTS:--Xmx1g -Xms512m}
      
      # Service URLs Configuration
      - NOTIFICATION_SERVICE_URL=${NOTIFICATION_SERVICE_URL:-http://localhost:9091}
      - DMS_SERVICE_URL=${DMS_BASE_URL:-http://localhost:9093}
      
      # Local Development CORS Configuration
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:4200,http://localhost:3000,http://localhost:8080}
      
      # Security Configuration
      - SECURITY_JWT_ENABLED=true
      - JWT_ISSUER=notification-service
      - JWT_AUDIENCE=notification-clients
      - JWT_SECRET=${JWT_SECRET:-localDevSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      
      # Email Configuration (Mock for local development)
      - MAIL_HOST=${MAIL_HOST:-smtp.gmail.com}
      - MAIL_PORT=${MAIL_PORT:-587}
      - MAIL_USERNAME=${MAIL_USERNAME:-}
      - MAIL_PASSWORD=${MAIL_PASSWORD:-}
      - NOTIFICATION_FROM_EMAIL=${NOTIFICATION_FROM_EMAIL:-noreply@localhost}
      - EMAIL_ENABLED=${EMAIL_ENABLED:-true}
      - EMAIL_MOCK=${EMAIL_MOCK:-true}
      
    volumes:
      - ./notification-svc/logs:/app/logs
      - ./notification-svc/keys:/app/keys:ro
    depends_on:
      mysql-shared:
        condition: service_healthy
      rabbitmq-shared:
        condition: service_healthy
      redis-shared:
        condition: service_healthy
    networks:
      - local-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # =============================================================================
  # SHARED INFRASTRUCTURE SERVICES
  # =============================================================================

  # Shared MySQL Database
  mysql-shared:
    image: mysql:8.0
    container_name: mysql-shared-local
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      - MYSQL_DATABASE=${MYSQL_DMS_DATABASE:-dms_db}
      - MYSQL_USER=${MYSQL_DMS_USER:-dms_user}
      - MYSQL_PASSWORD=${MYSQL_DMS_PASSWORD:-dms_password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data_local:/var/lib/mysql
      - ./docker/mysql/local-dev-init:/docker-entrypoint-initdb.d:ro
    networks:
      - local-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-root_password}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: --default-authentication-plugin=mysql_native_password

  # Shared Redis Cache
  redis-shared:
    image: redis:7-alpine
    container_name: redis-shared-local
    command: redis-server --requirepass ${REDIS_PASSWORD:-local_redis_password} --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data_local:/data
    networks:
      - local-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-local_redis_password}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker (for Notification Service)
  rabbitmq-shared:
    image: rabbitmq:3.12-management-alpine
    container_name: rabbitmq-shared-local
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS:-admin123}
      - RABBITMQ_DEFAULT_VHOST=/
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data_local:/var/lib/rabbitmq
    networks:
      - local-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Elasticsearch for Search (for DMS Service)
  elasticsearch-shared:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: elasticsearch-shared-local
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data_local:/usr/share/elasticsearch/data
    networks:
      - local-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # MONITORING & OBSERVABILITY SERVICES (Optional for Local Dev)
  # =============================================================================

  # Zipkin for Distributed Tracing
  zipkin-shared:
    image: openzipkin/zipkin:latest
    container_name: zipkin-shared-local
    ports:
      - "9411:9411"
    networks:
      - local-dev-network
    restart: unless-stopped

  # Prometheus for Metrics (Optional)
  prometheus-shared:
    image: prom/prometheus:latest
    container_name: prometheus-shared-local
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/local-dev-prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data_local:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - local-dev-network
    restart: unless-stopped

  # Grafana for Dashboards (Optional)
  grafana-shared:
    image: grafana/grafana:latest
    container_name: grafana-shared-local
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD:-admin}
    volumes:
      - grafana_data_local:/var/lib/grafana
    networks:
      - local-dev-network
    restart: unless-stopped

volumes:
  mysql_data_local:
    driver: local
  redis_data_local:
    driver: local
  rabbitmq_data_local:
    driver: local
  elasticsearch_data_local:
    driver: local
  prometheus_data_local:
    driver: local
  grafana_data_local:
    driver: local
  dms_storage_local:
    driver: local
  dms_logs_local:
    driver: local

networks:
  local-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
