# Test Isolation Configuration for DMS Service
# This configuration completely isolates tests from external dependencies

# Disable all external service auto-configurations
spring.autoconfigure.exclude=\
  io.opentelemetry.instrumentation.spring.autoconfigure.OpenTelemetryAutoConfiguration,\
  org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,\
  org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration,\
  org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchRepositoriesAutoConfiguration,\
  org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration

# Completely disable OpenTelemetry
otel.instrumentation.spring-boot.enabled=false
otel.sdk.disabled=true
otel.traces.exporter=none
otel.metrics.exporter=none
otel.logs.exporter=none

# Disable tracing completely
management.tracing.enabled=false
management.zipkin.tracing.enabled=false
management.tracing.sampling.probability=0.0

# Database Configuration - H2 In-Memory Database
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE;LOCK_TIMEOUT=10000
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.h2.console.enabled=false

# Disable Liquibase
spring.liquibase.enabled=false
spring.sql.init.mode=never

# Elasticsearch Configuration (completely disabled)
elasticsearch.enabled=false
spring.data.elasticsearch.repositories.enabled=false

# Redis Configuration (completely disabled)
spring.data.redis.repositories.enabled=false

# Cache Configuration - Use simple cache for tests
spring.cache.type=simple

# Disable all health indicators that require external services
management.health.elasticsearch.enabled=false
management.health.redis.enabled=false
management.health.ping.enabled=false
management.health.livenessstate.enabled=false
management.health.readinessstate.enabled=false
management.health.db.enabled=false
management.health.diskspace.enabled=false

# Disable rate limiting
dms.rate-limit.enabled=false
dms.rate-limit.use-redis=false

# Disable scheduled tasks
spring.task.scheduling.enabled=false

# Server Configuration
server.port=0

# Logging Configuration
logging.level.com.ascentbusiness.dms_svc=WARN
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN
logging.level.org.testcontainers=WARN

# Test-specific properties
test.data.cleanup.enabled=true
test.mock.external.services=true
test.performance.enabled=false

# Storage Configuration for Tests
dms.storage.default.provider=LOCAL
dms.storage.local.base-path=target/test-storage
dms.storage.local.create-directories=true

# JWT Configuration for Testing
dms.jwt.secret=test-secret-key-for-unit-tests-only-not-for-production-use
dms.jwt.expiration=3600000

# Disable virus scanning for tests
dms.virus-scanning.enabled=false
dms.virus-scanning.default-scanner=MOCK

# Disable audit encryption for tests
dms.audit.encryption.enabled=false
dms.pii.encryption.enabled=false

# Disable retention processing for tests
dms.retention.processing.enabled=false

# Disable Pandoc for tests
dms.pandoc.enabled=false
dms.pdf-conversion.enabled=false
dms.word-conversion.enabled=false
