-- Performance optimization indexes for DMS service
-- These indexes are designed to improve query performance for common operations
-- Note: Only creating indexes on existing columns and tables

-- Documents table indexes for performance optimization
-- Index for document lookup by creator and status (common in permission checks)
CREATE INDEX idx_documents_creator_status ON documents(creator_user_id, status);

-- Index for document lookup by filename and creator (duplicate detection)
CREATE INDEX idx_documents_filename_creator ON documents(original_file_name, creator_user_id);

-- Index for document lookup by storage provider (migration and storage operations)
CREATE INDEX idx_documents_storage_provider ON documents(storage_provider);

-- Index for document lookup by creation date (date range queries)
CREATE INDEX idx_documents_created_date ON documents(created_date);

-- Index for document lookup by parent document (version queries)
CREATE INDEX idx_documents_parent_id ON documents(parent_document_id);

-- Composite index for version queries (parent + version)
CREATE INDEX idx_documents_parent_version ON documents(parent_document_id, version);

-- Index for document status queries (active/historical/deleted filtering)
CREATE INDEX idx_documents_status ON documents(status);

-- Additional document permissions indexes (basic indexes already exist from migration 004)
-- Composite index for document + user permission checks
CREATE INDEX idx_doc_permissions_doc_user ON document_permissions(document_id, user_id);

-- Composite index for document + role permission checks
CREATE INDEX idx_doc_permissions_doc_role ON document_permissions(document_id, role_name);

-- Audit logs table indexes for performance
-- Index for audit lookup by document ID (document audit history)
CREATE INDEX idx_audit_logs_document_id ON audit_logs(document_id);

-- Index for audit lookup by user ID (user activity tracking)
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);

-- Index for audit lookup by action (action-specific queries)
CREATE INDEX idx_audit_logs_action ON audit_logs(action);

-- Index for audit lookup by timestamp (time-based queries)
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);

-- Composite index for document + timestamp (document audit timeline)
CREATE INDEX idx_audit_logs_doc_timestamp ON audit_logs(document_id, timestamp);

-- Composite index for user + timestamp (user activity timeline)
CREATE INDEX idx_audit_logs_user_timestamp ON audit_logs(user_id, timestamp);

-- Security violations table indexes (if table exists)
-- Index for security violation lookup by user ID
CREATE INDEX idx_security_violations_user_id ON security_violations(user_id);

-- Index for security violation lookup by violation type
CREATE INDEX idx_security_violations_type ON security_violations(violation_type);

-- Index for security violation lookup by timestamp
CREATE INDEX idx_security_violations_timestamp ON security_violations(timestamp);
