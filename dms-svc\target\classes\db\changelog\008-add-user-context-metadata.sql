--liquibase formatted sql

--changeset anurag:008-add-user-context-metadata
--comment: Add user context metadata fields to documents table

ALTER TABLE documents 
ADD COLUMN creator_roles JSON COMMENT 'Roles of the user who created the document',
ADD COLUMN creator_permissions <PERSON>SO<PERSON> COMMENT 'Permissions of the user who created the document',
ADD COLUMN last_modifier_roles JSON COMMENT 'Roles of the user who last modified the document',
ADD COLUMN last_modifier_permissions <PERSON><PERSON><PERSON> COMMENT 'Permissions of the user who last modified the document';

--rollback ALTER TABLE documents DROP COLUMN creator_roles, DROP COLUMN creator_permissions, DROP COLUMN last_modifier_roles, DROP COLUMN last_modifier_permissions;
