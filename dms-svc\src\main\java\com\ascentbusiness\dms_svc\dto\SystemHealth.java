package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.HealthStatus;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * System health DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class SystemHealth {
    private HealthStatus status;
    private OffsetDateTime timestamp;
    private String version;
    private Long uptime; // milliseconds
    private List<ComponentHealth> components;
    private Float overallScore; // 0.0 to 1.0
    private List<HealthIssue> issues;
    private List<String> recommendations;
}
