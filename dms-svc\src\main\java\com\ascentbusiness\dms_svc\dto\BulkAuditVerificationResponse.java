/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response for bulk audit verification operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkAuditVerificationResponse {
    
    private Boolean success;
    private Integer verifiedCount;
    private Boolean tamperingDetected;
    private String message;
    private List<AuditVerificationResponse> verificationResults;
}
