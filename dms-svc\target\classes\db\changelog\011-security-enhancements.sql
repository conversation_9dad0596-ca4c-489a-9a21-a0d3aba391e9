--liquibase formatted sql

--changeset dms:011-security-enhancements

-- Add security configuration table
CREATE TABLE security_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add permission limits table
CREATE TABLE permission_limits (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100),
    role_name VA<PERSON>HA<PERSON>(100),
    permission_type ENUM('READ', 'WRITE', 'DELETE', 'ADMIN') NOT NULL,
    max_documents INT NOT NULL DEFAULT 100,
    current_count INT NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_permission_limits_user_id (user_id),
    INDEX idx_permission_limits_role_name (role_name),
    INDEX idx_permission_limits_permission_type (permission_type)
);

-- Add permission inheritance rules table
CREATE TABLE permission_inheritance_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL UNIQUE,
    inherit_read BOOLEAN NOT NULL DEFAULT TRUE,
    inherit_write BOOLEAN NOT NULL DEFAULT FALSE,
    inherit_delete BOOLEAN NOT NULL DEFAULT FALSE,
    inherit_admin BOOLEAN NOT NULL DEFAULT FALSE,
    allow_override BOOLEAN NOT NULL DEFAULT TRUE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add security violation log table
CREATE TABLE security_violations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    violation_type ENUM('PERMISSION_DENIED', 'TOKEN_EXPIRED', 'RATE_LIMIT_EXCEEDED', 'INVALID_ACCESS', 'PRIVILEGE_ESCALATION') NOT NULL,
    document_id BIGINT,
    attempted_action VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    violation_details TEXT,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL DEFAULT 'MEDIUM',
    is_resolved BOOLEAN NOT NULL DEFAULT FALSE,
    resolved_by VARCHAR(100),
    resolved_date TIMESTAMP NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id),
    INDEX idx_security_violations_user_id (user_id),
    INDEX idx_security_violations_violation_type (violation_type),
    INDEX idx_security_violations_severity (severity),
    INDEX idx_security_violations_created_date (created_date)
);

-- Insert default security configurations
INSERT INTO security_config (config_key, config_value, description, created_by) VALUES
('MAX_PERMISSIONS_PER_USER', '50', 'Maximum number of document permissions a user can have', 'SYSTEM'),
('MAX_PERMISSIONS_PER_ROLE', '100', 'Maximum number of document permissions a role can have', 'SYSTEM'),
('PERMISSION_EXPIRATION_WARNING_DAYS', '7', 'Days before expiration to warn users', 'SYSTEM'),
('TOKEN_VALIDATION_STRICT_MODE', 'true', 'Enable strict JWT token validation', 'SYSTEM'),
('RATE_LIMIT_PERMISSION_OPERATIONS', '100', 'Maximum permission operations per hour per user', 'SYSTEM'),
('AUDIT_PERMISSION_CHANGES', 'true', 'Enable detailed auditing of permission changes', 'SYSTEM'),
('ENABLE_PERMISSION_INHERITANCE', 'true', 'Enable automatic permission inheritance for document versions', 'SYSTEM'),
('SECURITY_VIOLATION_THRESHOLD', '5', 'Number of violations before account review', 'SYSTEM'),
-- Migration-specific security configurations
('MIGRATION_RATE_LIMITING', 'true', 'Enable rate limiting for migration operations', 'SYSTEM'),
('MAX_MIGRATIONS_PER_HOUR', '5', 'Maximum number of migrations per user per hour', 'SYSTEM'),
('MIGRATION_ADMIN_ONLY', 'true', 'Restrict migration operations to admin users only', 'SYSTEM'),
('MIGRATION_AUDIT_DETAILED', 'true', 'Enable detailed audit logging for migration operations', 'SYSTEM'),
('MIGRATION_SECURITY_VALIDATION', 'true', 'Enable comprehensive security validation for migrations', 'SYSTEM');

-- Insert default permission inheritance rule
INSERT INTO permission_inheritance_rules (rule_name, inherit_read, inherit_write, inherit_delete, inherit_admin, allow_override, created_by) VALUES
('DEFAULT_INHERITANCE', true, false, false, false, true, 'SYSTEM');

-- Update the database changelog master to include this migration
-- (This will be handled separately)

-- Add indexes for better performance on permission checks with expiration
CREATE INDEX idx_document_permissions_expires_at_active ON document_permissions(expires_at, is_active);
CREATE INDEX idx_document_permissions_user_permission_active ON document_permissions(user_id, permission_type, is_active);
CREATE INDEX idx_document_permissions_role_permission_active ON document_permissions(role_name, permission_type, is_active);
