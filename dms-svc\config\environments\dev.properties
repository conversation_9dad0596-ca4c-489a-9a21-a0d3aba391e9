# Development Environment Configuration for DMS Service
# This file contains development-specific settings

# Server Configuration
server.port=8080
server.servlet.context-path=/dms

# Database Configuration - Development
spring.datasource.url=************************************************************************************************
spring.datasource.username=dms_dev_user
spring.datasource.password=${DB_PASSWORD:dev_password}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration - Smaller pool for dev
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.max-lifetime=1200000

# JPA Configuration - Show SQL in development
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Liquibase Configuration
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml
spring.liquibase.enabled=true
spring.liquibase.drop-first=false

# Redis Configuration - Development
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.timeout=2000ms
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-idle=4
spring.redis.jedis.pool.min-idle=2

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=600000
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dms:dev:

# Elasticsearch Configuration - Development
spring.elasticsearch.uris=${ELASTICSEARCH_URIS:http://localhost:9200}
spring.elasticsearch.connection-timeout=10s
spring.elasticsearch.socket-timeout=30s

# Storage Configuration - Local storage for development
dms.storage.provider=${STORAGE_PROVIDER:LOCAL}
dms.storage.local.base-path=${STORAGE_PATH:./storage/documents}
dms.storage.local.create-directories=true

# S3 Configuration (for testing S3 integration)
dms.storage.s3.bucket-name=${S3_BUCKET_NAME:dms-dev-documents}
dms.storage.s3.region=${S3_REGION:us-east-1}

# File Upload Configuration - Smaller limits for dev
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
spring.servlet.multipart.enabled=true

# JWT Configuration - Development
dms.jwt.secret=${JWT_SECRET:devSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
dms.jwt.expiration=${JWT_EXPIRATION:86400000}
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# Actuator Configuration - More endpoints exposed in dev
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always
management.health.defaults.enabled=true

# Health Indicators
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=true
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.percentiles.http.server.requests=0.5,0.95,0.99
management.metrics.tags.application=dms-service
management.metrics.tags.environment=development

# OpenTelemetry Configuration - Console output for dev
otel.service.name=dms-svc
otel.service.version=1.0.0-dev
otel.resource.attributes=service.name=dms-svc,service.version=1.0.0-dev,deployment.environment=development

# Tracing Configuration - Console exporter for dev
otel.traces.exporter=console
otel.traces.sampler=traceidratio
otel.traces.sampler.arg=1.0

# Metrics Configuration
otel.metrics.exporter=console
otel.metric.export.interval=30s

# Logs Configuration
otel.logs.exporter=console

# Logging Configuration - Debug level for development
logging.level.root=INFO
logging.level.com.ascentbusiness.dms=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Log Files Configuration
logging.file.name=./logs/dms-application-dev.log
logging.logback.rollingpolicy.max-file-size=50MB
logging.logback.rollingpolicy.max-history=7
logging.logback.rollingpolicy.total-size-cap=500MB

# Security Configuration - Relaxed for development
dms.security.cors.allowed-origins=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000
dms.security.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
dms.security.cors.allowed-headers=*
dms.security.cors.allow-credentials=true

# Rate Limiting Configuration - Higher limits for dev
dms.security.rate-limit.enabled=false
dms.security.rate-limit.requests-per-minute=1000
dms.security.rate-limit.burst-capacity=2000

# Security Headers Configuration - Relaxed for dev
dms.security.headers.frame-options=SAMEORIGIN
dms.security.headers.content-type-options=nosniff
dms.security.headers.xss-protection=1; mode=block
dms.security.headers.referrer-policy=strict-origin-when-cross-origin
dms.security.headers.hsts.enabled=false

# API Versioning Configuration
dms.api.version.current=1.0
dms.api.version.supported=1.0,1.1
dms.api.version.deprecation-warning=true
dms.api.version.strict-mode=false

# Performance Configuration - Optimized for development
spring.jpa.properties.hibernate.jdbc.batch_size=10
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Development-specific features
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

# H2 Console (for testing)
spring.h2.console.enabled=false

# DevTools Configuration
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# Test Configuration
spring.test.database.replace=none

# Elasticsearch Development Settings
dms.search.elasticsearch.index-prefix=dms-dev
dms.search.elasticsearch.refresh-policy=immediate
dms.search.elasticsearch.number-of-shards=1
dms.search.elasticsearch.number-of-replicas=0

# Audit Configuration - Simplified for dev
dms.audit.enabled=true
dms.audit.async=false
dms.audit.include-request-body=true
dms.audit.include-response-body=true

# Retention Policy - Shorter periods for dev
dms.retention.default-period=30
dms.retention.check-interval=PT1H
dms.retention.batch-size=100

# Compliance Configuration - Relaxed for dev
dms.compliance.enabled=false
dms.compliance.framework=DEV
dms.compliance.audit-level=BASIC

# Workflow Configuration
dms.workflow.enabled=true
dms.workflow.async=false
dms.workflow.timeout=PT5M

# SharePoint Integration - Mock for dev
dms.sharepoint.enabled=false
dms.sharepoint.mock-mode=true

# Environment
ENVIRONMENT=development
LOG_LEVEL=DEBUG
