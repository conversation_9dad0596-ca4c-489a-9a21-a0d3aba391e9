# Kubernetes Namespace for DMS Service
apiVersion: v1
kind: Namespace
metadata:
  name: dms-system
  labels:
    name: dms-system
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: dms-system
    app.kubernetes.io/managed-by: kubectl
  annotations:
    description: "Document Management System namespace"
    contact: "<EMAIL>"
---
# Resource Quota for the namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: dms-resource-quota
  namespace: dms-system
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"
---
# Limit Range for the namespace
apiVersion: v1
kind: LimitRange
metadata:
  name: dms-limit-range
  namespace: dms-system
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
