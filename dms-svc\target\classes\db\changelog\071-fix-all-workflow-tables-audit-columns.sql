--liquibase formatted sql

--changeset system:071-fix-all-workflow-tables-audit-columns
--comment: Add missing created_by and last_modified_by columns to all workflow tables to match BaseEntity

-- Add missing audit columns to workflow_tasks table
ALTER TABLE workflow_tasks 
ADD COLUMN created_by VA<PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT 'system' AFTER last_modified_date,
ADD COLUMN last_modified_by VARCHAR(255) <PERSON><PERSON><PERSON> created_by;

-- Add missing audit columns to workflow_transitions table
ALTER TABLE workflow_transitions 
ADD COLUMN last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_date,
ADD COLUMN created_by VA<PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT 'system' AFTER last_modified_date,
ADD COLUMN last_modified_by VA<PERSON>HAR(255) AFTER created_by;

-- Add missing audit columns to workflow_instances table
ALTER TABLE workflow_instances 
ADD COLUMN created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT 'system' AFTER last_modified_date,
ADD COLUMN last_modified_by <PERSON><PERSON><PERSON><PERSON>(255) AFTER created_by;

-- Add missing audit columns to workflow_history table
ALTER TABLE workflow_history 
ADD COLUMN created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER event_timestamp,
ADD COLUMN last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_date,
ADD COLUMN created_by VARCHAR(255) NOT NULL DEFAULT 'system' AFTER last_modified_date,
ADD COLUMN last_modified_by VARCHAR(255) AFTER created_by;

-- Update the default value for existing records
UPDATE workflow_tasks SET created_by = 'system' WHERE created_by IS NULL;
UPDATE workflow_tasks SET last_modified_by = 'system' WHERE last_modified_by IS NULL;

UPDATE workflow_transitions SET created_by = 'system' WHERE created_by IS NULL;
UPDATE workflow_transitions SET last_modified_by = 'system' WHERE last_modified_by IS NULL;

UPDATE workflow_instances SET created_by = 'system' WHERE created_by IS NULL;
UPDATE workflow_instances SET last_modified_by = 'system' WHERE last_modified_by IS NULL;

UPDATE workflow_history SET created_by = 'system' WHERE created_by IS NULL;
UPDATE workflow_history SET last_modified_by = 'system' WHERE last_modified_by IS NULL;

-- Remove the default constraint after updating existing records
ALTER TABLE workflow_tasks ALTER COLUMN created_by DROP DEFAULT;
ALTER TABLE workflow_transitions ALTER COLUMN created_by DROP DEFAULT;
ALTER TABLE workflow_instances ALTER COLUMN created_by DROP DEFAULT;
ALTER TABLE workflow_history ALTER COLUMN created_by DROP DEFAULT;