-- Initial schema for notification service

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sender_id VARCHAR(255) NOT NULL,
    content TEXT,
    template_name TEXT,
    variables TEXT,
    type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create notification_recipients table
CREATE TABLE IF NOT EXISTS notification_recipients (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    notification_id BIGINT NOT NULL,
    recipient_id VARCHAR(255) NOT NULL,
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    delivered BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE
);

-- Create notification_templates table
CREATE TABLE IF NOT EXISTS notification_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    alias VA<PERSON>HAR(255) NOT NULL UNIQUE,
    subject VA<PERSON>HA<PERSON>(255),
    body TEXT
);

-- <PERSON><PERSON> audit logs table
CREATE TABLE IF NOT EXISTS notification_audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    correlation_id VARCHAR(36),
    request_id VARCHAR(36),
    user_id VARCHAR(255),
    source_service VARCHAR(100),
    action VARCHAR(50) NOT NULL,
    result VARCHAR(20) NOT NULL,
    entity_type VARCHAR(50),
    entity_id VARCHAR(255),
    details TEXT,
    additional_data TEXT,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_notification_audit_correlation_id (correlation_id),
    INDEX idx_notification_audit_request_id (request_id),
    INDEX idx_notification_audit_user_id (user_id),
    INDEX idx_notification_audit_action (action),
    INDEX idx_notification_audit_timestamp (timestamp),
    INDEX idx_notification_audit_entity (entity_type, entity_id)
);

-- Insert default notification templates
INSERT IGNORE INTO notification_templates (alias, subject, body) VALUES 
('default-notification', 'New Notification', 
'<html><body><h2>Hello ${recipient.userid}!</h2><p>You have a new notification from ${senderId}:</p><p>${content}</p></body></html>'),
('welcome', 'Welcome!', 
'<html><body><h2>Welcome ${recipient.userid}!</h2><p>Welcome to our notification service!</p></body></html>'); 