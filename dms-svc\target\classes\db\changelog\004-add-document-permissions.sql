--liquibase formatted sql

--changeset dms:004-add-document-permissions

-- Create document_permissions table
CREATE TABLE document_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    user_id VARCHAR(100),
    role_name VARCHAR(100),
    permission ENUM('READ', 'WRITE', 'DELETE', 'ADMIN') NOT NULL,
    granted_by_user_id VARCHAR(100) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    UNIQUE KEY uk_document_permissions (document_id, user_id, role_name)
);

-- Create indexes for better performance
CREATE INDEX idx_document_permissions_document_id ON document_permissions(document_id);
CREATE INDEX idx_document_permissions_user_id ON document_permissions(user_id);
CREATE INDEX idx_document_permissions_role_name ON document_permissions(role_name);
CREATE INDEX idx_document_permissions_permission ON document_permissions(permission);
