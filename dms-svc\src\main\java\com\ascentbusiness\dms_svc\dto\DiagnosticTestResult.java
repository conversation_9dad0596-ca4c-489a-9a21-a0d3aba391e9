package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.TestStatus;
import lombok.Builder;
import lombok.Data;

/**
 * Diagnostic test result DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class DiagnosticTestResult {
    private String testName;
    private String category;
    private TestStatus status;
    private Boolean success;
    private String message;
    private String details;
    private Long duration; // milliseconds
    private String expectedResult;
    private String actualResult;
    private String errorMessage;
    private String stackTrace;
}
