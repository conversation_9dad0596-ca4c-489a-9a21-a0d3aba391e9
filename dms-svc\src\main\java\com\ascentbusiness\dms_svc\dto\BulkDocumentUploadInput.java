package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * DTO for bulk document upload input.
 * Corresponds to BulkDocumentUploadInput GraphQL input type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkDocumentUploadInput {

    /**
     * List of files to upload.
     */
    private List<MultipartFile> files;

    /**
     * Common metadata to apply to all uploaded documents.
     */
    private DocumentUploadMetadataInput commonMetadata;

    /**
     * Processing options for the bulk upload.
     */
    private BulkProcessingOptionsInput processingOptions;

    /**
     * Validation options for the files.
     */
    private FileValidationOptionsInput validationOptions;
}
