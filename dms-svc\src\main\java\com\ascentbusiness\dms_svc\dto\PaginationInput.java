package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.SortDirection;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PaginationInput {
    @Builder.Default
    private Integer page = 0;
    @Builder.Default
    private Integer size = 10;
    @Builder.Default
    private String sortBy = "createdDate";
    @Builder.Default
    private SortDirection sortDirection = SortDirection.DESC;
}
