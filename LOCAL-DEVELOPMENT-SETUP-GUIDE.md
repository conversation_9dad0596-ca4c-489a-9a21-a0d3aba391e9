# Local Development Setup Guide - Infrastructure Team

## 🎯 **Overview**

This guide provides step-by-step instructions for setting up the GRC Platform v4 local development environment with shared infrastructure. The setup demonstrates how services can share infrastructure resources efficiently.

## 🏗️ **Architecture - Shared Infrastructure Approach**

### **Shared Infrastructure Layer**
- **MySQL** (Port 3306) - Single instance with multiple databases
- **Redis** (Port 6379) - Single instance with service-specific prefixes
- **RabbitMQ** (Port 5672, 15672) - Message broker for notifications
- **Elasticsearch** (Port 9200, 9300) - Search engine for DMS

### **Application Layer**
- **DMS Service** (Port 9093) - Document Management Service
- **Notification Service** (Port 9091) - Notification Management Service

### **Monitoring Layer** (Optional)
- **Zipkin** (Port 9411) - Distributed tracing
- **Prometheus** (Port 9090) - Metrics collection
- **Grafana** (Port 3000) - Dashboards

## 📋 **Prerequisites**

### **System Requirements**
- Docker Desktop 4.0+ installed and running
- Docker Compose 2.0+ installed
- At least 4GB RAM available for containers
- At least 10GB free disk space

### **Port Requirements**
Ensure these ports are available on your local machine:
- **3000** - Grafana
- **3306** - MySQL
- **5672** - RabbitMQ
- **6379** - Redis
- **9090** - Prometheus
- **9091** - Notification Service
- **9093** - DMS Service
- **9200, 9300** - Elasticsearch
- **9411** - Zipkin
- **9464** - DMS Metrics
- **15672** - RabbitMQ Management UI

## 🚀 **Step-by-Step Setup Instructions**

### **Step 1: Clone and Navigate to Project**

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd grc-platform-v4

# Verify files are present
ls -la docker-compose.local-dev.yml
ls -la docker-manage-local-dev.sh
ls -la .env.local-dev
```

### **Step 2: Configure Environment (Optional)**

```bash
# The system works with default values, but you can customize if needed
cp .env.local-dev .env.local-dev.custom

# Edit custom settings (optional)
nano .env.local-dev.custom

# Key settings you might want to change:
# - CORS_ALLOWED_ORIGINS (add your frontend URLs)
# - Database passwords
# - Redis password
# - JWT secret
```

### **Step 3: Start Shared Infrastructure First**

```bash
# Start shared infrastructure services
./docker-manage-local-dev.sh start-infra

# This starts:
# - MySQL (with both dms_db and notification_db)
# - Redis (with service-specific prefixes)
# - RabbitMQ (for notifications)
# - Elasticsearch (for document search)

# Wait for services to be ready (about 30 seconds)
# Check status
./docker-manage-local-dev.sh status
```

### **Step 4: Start DMS Service (Uses Shared Infrastructure)**

```bash
# Start DMS service - it will connect to shared infrastructure
./docker-manage-local-dev.sh start-dms

# Verify DMS service is running
curl http://localhost:9093/actuator/health

# Access DMS GraphiQL interface
# Open browser: http://localhost:9093/graphiql
```

### **Step 5: Start Notification Service (Uses Same Shared Infrastructure)**

```bash
# Start Notification service - it will connect to the SAME infrastructure
./docker-manage-local-dev.sh start-notification

# Verify Notification service is running
curl http://localhost:9091/actuator/health

# Access Notification GraphiQL interface
# Open browser: http://localhost:9091/graphiql
```

### **Step 6: Start Monitoring (Optional)**

```bash
# Start monitoring services
./docker-manage-local-dev.sh start-monitoring

# Access monitoring interfaces:
# Grafana: http://localhost:3000 (admin/admin)
# Prometheus: http://localhost:9090
# Zipkin: http://localhost:9411
```

## 🔧 **Alternative: Start Everything at Once**

```bash
# Quick start - everything at once
./docker-manage-local-dev.sh start-all

# This will:
# 1. Start shared infrastructure
# 2. Wait for infrastructure to be ready
# 3. Start both application services
# 4. Start monitoring services

# Check complete status
./docker-manage-local-dev.sh status
```

## 🌐 **Service URLs and Access Points**

### **Application Services**
- **DMS Service GraphQL**: http://localhost:9093/dms/graphql
- **DMS Service GraphiQL**: http://localhost:9093/graphiql
- **DMS Service Health**: http://localhost:9093/actuator/health
- **DMS Service Metrics**: http://localhost:9464/actuator/prometheus

- **Notification Service GraphQL**: http://localhost:9091/graphql
- **Notification Service GraphiQL**: http://localhost:9091/graphiql
- **Notification Service Health**: http://localhost:9091/actuator/health
- **Notification Service Metrics**: http://localhost:9091/actuator/prometheus

### **Shared Infrastructure**
- **MySQL**: localhost:3306
  - Username: `root`, Password: `root_password`
  - DMS Database: `dms_db` (User: `dms_user`/`dms_password`)
  - Notification Database: `notification_db` (User: `notification_user`/`notification_password`)
  - Dev User: `dev_user`/`dev_password` (access to both databases)

- **Redis**: localhost:6379
  - Password: `local_redis_password`
  - DMS Prefix: `dms:local:`
  - Notification Prefix: `notification:local:`

- **RabbitMQ**: localhost:5672
  - Management UI: http://localhost:15672
  - Username: `admin`, Password: `admin123`

- **Elasticsearch**: http://localhost:9200

### **Monitoring Services**
- **Zipkin**: http://localhost:9411
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)

## 🛠️ **Management Commands**

### **Infrastructure Management**
```bash
# Start shared infrastructure only
./docker-manage-local-dev.sh start-infra

# Stop shared infrastructure (stops dependent services too)
./docker-manage-local-dev.sh stop-infra
```

### **Application Management**
```bash
# Start DMS service only (requires infrastructure)
./docker-manage-local-dev.sh start-dms

# Start Notification service only (requires infrastructure)
./docker-manage-local-dev.sh start-notification

# Start both application services
./docker-manage-local-dev.sh start-apps

# Stop individual services
./docker-manage-local-dev.sh stop-dms
./docker-manage-local-dev.sh stop-notification
./docker-manage-local-dev.sh stop-apps
```

### **Monitoring and Debugging**
```bash
# Check status of all services
./docker-manage-local-dev.sh status

# Check health of all services
./docker-manage-local-dev.sh health

# View logs
./docker-manage-local-dev.sh logs                    # All services
./docker-manage-local-dev.sh logs dms               # DMS service only
./docker-manage-local-dev.sh logs notification      # Notification service only
./docker-manage-local-dev.sh logs infra             # Infrastructure only
./docker-manage-local-dev.sh logs monitoring        # Monitoring only
```

### **Complete Management**
```bash
# Start everything
./docker-manage-local-dev.sh start-all

# Stop everything
./docker-manage-local-dev.sh stop-all

# Clean up everything (removes all data)
./docker-manage-local-dev.sh cleanup
```

## 🧪 **Testing the Setup**

### **Test DMS Service**
```bash
# Test health endpoint
curl http://localhost:9093/actuator/health

# Test GraphQL endpoint
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query":"{ __schema { types { name } } }"}' \
  http://localhost:9093/dms/graphql
```

### **Test Notification Service**
```bash
# Test health endpoint
curl http://localhost:9091/actuator/health

# Test GraphQL endpoint
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query":"{ __schema { types { name } } }"}' \
  http://localhost:9091/graphql
```

### **Test Shared Infrastructure**
```bash
# Test MySQL connection
docker exec -it mysql-shared-local mysql -u root -proot_password -e "SHOW DATABASES;"

# Test Redis connection
docker exec -it redis-shared-local redis-cli -a local_redis_password ping

# Test RabbitMQ
curl -u admin:admin123 http://localhost:15672/api/overview

# Test Elasticsearch
curl http://localhost:9200/_cluster/health
```

## 🔍 **Verifying Shared Infrastructure**

### **Database Sharing Verification**
```bash
# Connect to MySQL and verify both databases exist
docker exec -it mysql-shared-local mysql -u root -proot_password

# In MySQL prompt:
SHOW DATABASES;
# Should show: dms_db, notification_db

# Check users
SELECT User, Host FROM mysql.user WHERE User IN ('dms_user', 'notification_user', 'dev_user');
```

### **Redis Sharing Verification**
```bash
# Connect to Redis
docker exec -it redis-shared-local redis-cli -a local_redis_password

# In Redis prompt:
# Set a key with DMS prefix
SET dms:local:test "DMS data"

# Set a key with Notification prefix
SET notification:local:test "Notification data"

# List all keys
KEYS *
# Should show both prefixed keys
```

## 🐛 **Troubleshooting**

### **Common Issues**

1. **Port Conflicts**
   ```bash
   # Check what's using a port
   netstat -tulpn | grep :3306
   
   # Stop conflicting services
   sudo systemctl stop mysql  # Linux
   brew services stop mysql   # macOS
   ```

2. **Docker Memory Issues**
   ```bash
   # Check Docker memory usage
   docker stats
   
   # Increase Docker memory in Docker Desktop settings
   # Recommended: 4GB+ for complete setup
   ```

3. **Services Not Starting**
   ```bash
   # Check logs for specific service
   ./docker-manage-local-dev.sh logs dms
   
   # Check Docker Compose status
   docker-compose -f docker-compose.local-dev.yml ps
   ```

4. **Database Connection Issues**
   ```bash
   # Restart infrastructure
   ./docker-manage-local-dev.sh stop-infra
   ./docker-manage-local-dev.sh start-infra
   
   # Check MySQL logs
   docker logs mysql-shared-local
   ```

### **Reset Everything**
```bash
# Complete reset (removes all data)
./docker-manage-local-dev.sh cleanup

# Start fresh
./docker-manage-local-dev.sh start-all
```

## 📊 **Resource Usage**

### **Memory Usage (Approximate)**
- **MySQL**: 400MB
- **Redis**: 50MB
- **RabbitMQ**: 150MB
- **Elasticsearch**: 1GB
- **DMS Service**: 800MB
- **Notification Service**: 600MB
- **Monitoring**: 300MB
- **Total**: ~3.3GB

### **Disk Usage**
- **Docker Images**: ~2GB
- **Data Volumes**: ~500MB (grows with usage)
- **Logs**: ~100MB

## ✅ **Success Criteria**

Your setup is successful when:

1. ✅ All services show "HEALTHY" status: `./docker-manage-local-dev.sh health`
2. ✅ Both GraphiQL interfaces are accessible
3. ✅ Both services can query their GraphQL schemas
4. ✅ MySQL shows both databases with proper users
5. ✅ Redis shows keys with different prefixes
6. ✅ RabbitMQ management UI is accessible
7. ✅ Elasticsearch cluster health is green

## 🎉 **Next Steps**

After successful setup:

1. **Frontend Integration**: Configure your frontend to call:
   - DMS: `http://localhost:9093/dms/graphql`
   - Notifications: `http://localhost:9091/graphql`

2. **Development**: Both services are ready for development with:
   - Hot reload enabled
   - Debug logging
   - GraphiQL interfaces
   - Shared infrastructure

3. **Testing**: Use the shared infrastructure to test:
   - Cross-service communication
   - Database transactions
   - Cache sharing
   - Message queuing

The local development environment demonstrates the shared infrastructure approach that can be scaled to production! 🚀
