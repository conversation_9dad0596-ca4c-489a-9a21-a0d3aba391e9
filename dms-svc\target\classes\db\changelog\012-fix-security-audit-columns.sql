--liquibase formatted sql

--changeset dms:012-fix-security-audit-columns

-- Add missing audit columns to security_violations table
ALTER TABLE security_violations 
ADD COLUMN last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

ALTER TABLE security_violations 
ADD COLUMN created_by VARCHAR(100);

ALTER TABLE security_violations 
ADD COLUMN last_modified_by VARCHAR(100);

-- Ensure the security_config table has the correct audit columns (if missing)
-- Check if columns exist first to avoid errors if they're already there

-- Add last_modified_date if it doesn't exist (but it should already exist)
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'security_config' AND COLUMN_NAME = 'last_modified_date');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE security_config ADD COLUMN last_modified_date TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 
    'SELECT "security_config.last_modified_date already exists" as status');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add created_by if it doesn't exist (but it should already exist)  
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'security_config' AND COLUMN_NAME = 'created_by');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE security_config ADD COLUMN created_by VARCHAR(100)', 
    'SELECT "security_config.created_by already exists" as status');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add last_modified_by if it doesn't exist (but it should already exist)
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'security_config' AND COLUMN_NAME = 'last_modified_by');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE security_config ADD COLUMN last_modified_by VARCHAR(100)', 
    'SELECT "security_config.last_modified_by already exists" as status');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
