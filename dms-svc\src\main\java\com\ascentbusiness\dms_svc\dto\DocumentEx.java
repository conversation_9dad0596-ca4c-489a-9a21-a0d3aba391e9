package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.entity.DocumentPermission;
import com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata;
import com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata;
import com.ascentbusiness.dms_svc.entity.DocumentComplianceMetadata;
import com.ascentbusiness.dms_svc.enums.DocumentStatus;
import com.ascentbusiness.dms_svc.enums.ProcessingStatus;
import com.ascentbusiness.dms_svc.enums.ProcessingStrategy;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * Extended Document DTO with processing information.
 * 
 * <p>This DTO extends the standard Document with additional fields
 * for tracking processing status, strategy, and progress information
 * for asynchronous and chunked upload operations.
 */
@Data
public class DocumentEx {
    
    // Base document fields
    private Long id;
    private String name;
    private String description;
    private Integer version;
    private DocumentStatus status;
    private StorageProvider storageProvider;
    private String storagePath;
    private String originalFileName;
    private Long fileSize;
    private String mimeType;
    private OffsetDateTime createdDate;
    private OffsetDateTime lastModifiedDate;
    private String creatorUserId;
    private List<String> tags;
    
    // Processing fields
    private ProcessingStrategy processingStrategy;
    private ProcessingStatus processingStatus;
    private String processingJobId;
    private Float processingProgress;
    private String statusCheckUrl;
    
    // Metadata fields
    private List<DocumentPermission> permissions;
    private DocumentClassificationMetadata classificationMetadata;
    private DocumentOwnershipMetadata ownershipMetadata;
    private DocumentComplianceMetadata complianceMetadata;
    
    /**
     * Create a DocumentEx from a standard Document with processing information.
     * 
     * @param document the source document
     * @param processingStrategy the processing strategy used
     * @param processingStatus the current processing status
     * @return DocumentEx instance
     */
    public static DocumentEx fromDocument(Document document, ProcessingStrategy processingStrategy, 
                                        ProcessingStatus processingStatus) {
        DocumentEx documentEx = new DocumentEx();
        
        // Copy base document fields
        documentEx.setId(document.getId());
        documentEx.setName(document.getName());
        documentEx.setDescription(document.getDescription());
        documentEx.setVersion(document.getVersion());
        documentEx.setStatus(document.getStatus());
        documentEx.setStorageProvider(document.getStorageProvider());
        documentEx.setStoragePath(document.getFilePath());
        documentEx.setOriginalFileName(document.getName());
        documentEx.setFileSize(document.getFileSize());
        documentEx.setMimeType(document.getMimeType());
        documentEx.setCreatedDate(document.getCreatedDateTime());
        documentEx.setLastModifiedDate(document.getLastModifiedDateTime());
        documentEx.setCreatorUserId(document.getCreatedBy());
        documentEx.setTags(document.getKeywords());
        
        // Set processing fields
        documentEx.setProcessingStrategy(processingStrategy);
        documentEx.setProcessingStatus(processingStatus);
        
        // Copy metadata
        documentEx.setPermissions(document.getPermissions() != null ?
            new java.util.ArrayList<>(document.getPermissions()) : null);
        documentEx.setClassificationMetadata(document.getClassificationMetadata());
        documentEx.setOwnershipMetadata(document.getOwnershipMetadata());
        documentEx.setComplianceMetadata(document.getComplianceMetadata());
        
        return documentEx;
    }
    
    /**
     * Create a DocumentEx from a standard Document with default processing information.
     * 
     * @param document the source document
     * @return DocumentEx instance with DIRECT/COMPLETED processing
     */
    public static DocumentEx fromDocument(Document document) {
        return fromDocument(document, ProcessingStrategy.DIRECT, ProcessingStatus.COMPLETED);
    }
    
    /**
     * Create a pending DocumentEx for async or chunked processing.
     * 
     * @param name the document name
     * @param description the document description
     * @param fileSize the file size
     * @param processingStrategy the processing strategy
     * @param processingJobId the job/session ID
     * @return pending DocumentEx instance
     */
    public static DocumentEx createPending(String name, String description, Long fileSize,
                                         ProcessingStrategy processingStrategy, String processingJobId) {
        DocumentEx documentEx = new DocumentEx();
        
        // Set basic fields
        documentEx.setName(name);
        documentEx.setDescription(description);
        documentEx.setFileSize(fileSize);
        documentEx.setOriginalFileName(name);
        
        // Set processing fields
        documentEx.setProcessingStrategy(processingStrategy);
        documentEx.setProcessingStatus(ProcessingStatus.QUEUED);
        documentEx.setProcessingJobId(processingJobId);
        documentEx.setProcessingProgress(0.0f);
        
        return documentEx;
    }
    
    /**
     * Check if the document processing is complete.
     * 
     * @return true if processing is complete
     */
    public boolean isProcessingComplete() {
        return processingStatus == ProcessingStatus.COMPLETED;
    }
    
    /**
     * Check if the document processing has failed.
     * 
     * @return true if processing has failed
     */
    public boolean isProcessingFailed() {
        return processingStatus == ProcessingStatus.FAILED;
    }
    
    /**
     * Check if the document is still being processed.
     * 
     * @return true if processing is active
     */
    public boolean isProcessingActive() {
        return processingStatus == ProcessingStatus.QUEUED || 
               processingStatus == ProcessingStatus.PROCESSING;
    }
    
    /**
     * Check if progress tracking is supported for this processing strategy.
     * 
     * @return true if progress tracking is supported
     */
    public boolean supportsProgressTracking() {
        return processingStrategy != null && processingStrategy.supportsProgressTracking();
    }
    
    /**
     * Get the status check URL for tracking progress.
     * 
     * @return status check URL or null if not applicable
     */
    public String getStatusCheckUrl() {
        if (statusCheckUrl != null) {
            return statusCheckUrl;
        }
        
        if (processingJobId != null && supportsProgressTracking()) {
            if (processingStrategy == ProcessingStrategy.ASYNC) {
                return "/graphql?query={documentProcessingStatus(jobId:\"" + processingJobId + "\")}";
            } else if (processingStrategy == ProcessingStrategy.CHUNKED) {
                return "/graphql?query={chunkedUploadStatus(sessionId:\"" + processingJobId + "\")}";
            }
        }
        
        return null;
    }
    
    /**
     * Update processing progress.
     * 
     * @param progress the progress percentage (0.0 to 100.0)
     */
    public void updateProgress(Float progress) {
        this.processingProgress = progress;
        
        if (progress != null && progress >= 100.0f) {
            this.processingStatus = ProcessingStatus.COMPLETED;
        } else if (progress != null && progress > 0.0f) {
            this.processingStatus = ProcessingStatus.PROCESSING;
        }
    }
}
