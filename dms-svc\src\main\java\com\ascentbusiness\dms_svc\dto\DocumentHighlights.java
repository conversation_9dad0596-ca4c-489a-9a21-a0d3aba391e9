package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Highlighted text snippets from search matches
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentHighlights {

    /**
     * Highlighted matches in document name
     */
    private List<String> name;

    /**
     * Highlighted matches in document description
     */
    private List<String> description;

    /**
     * Highlighted matches in document content
     */
    private List<String> content;

    /**
     * Highlighted matches in keywords
     */
    private List<String> keywords;

    /**
     * Highlighted matches in tags
     */
    private List<String> tags;
}
