package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * Input DTO for PDF conversion operations from conversion-schema.graphqls.
 */
@Data
public class PdfConversionInput {
    private MultipartFile file;
    private String filePath;
    private VirusScannerType scannerType;
    private ConversionOptionsInput options;
    private String outputFormat = "docx";
}
