package com.ascentbusiness.dms_svc.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TestCategorySummary {
    private String categoryName;
    private String displayName;
    private String testRange;
    private Integer testCount;
    private String coverageAreas;
    private String description;
    private List<String> featuresTestedSummary;

    public TestCategorySummary() {}

    public TestCategorySummary(String categoryName, String displayName, String testRange, 
                              Integer testCount, String coverageAreas, String description) {
        this.categoryName = categoryName;
        this.displayName = displayName;
        this.testRange = testRange;
        this.testCount = testCount;
        this.coverageAreas = coverageAreas;
        this.description = description;
    }

    // Getters and Setters
    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getTestRange() {
        return testRange;
    }

    public void setTestRange(String testRange) {
        this.testRange = testRange;
    }

    public Integer getTestCount() {
        return testCount;
    }

    public void setTestCount(Integer testCount) {
        this.testCount = testCount;
    }

    public String getCoverageAreas() {
        return coverageAreas;
    }

    public void setCoverageAreas(String coverageAreas) {
        this.coverageAreas = coverageAreas;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getFeaturesTestedSummary() {
        return featuresTestedSummary;
    }

    public void setFeaturesTestedSummary(List<String> featuresTestedSummary) {
        this.featuresTestedSummary = featuresTestedSummary;
    }
}
