# Local Development Environment Configuration
# This file contains settings for local development with shared infrastructure

# =============================================================================
# LOCAL DEVELOPMENT CONFIGURATION
# =============================================================================
# Service URLs for local development
DMS_BASE_URL=http://localhost:9093
NOTIFICATION_SERVICE_URL=http://localhost:9091

# =============================================================================
# CORS CONFIGURATION - LOCAL DEVELOPMENT
# =============================================================================
# Allow common local development ports
CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:3000,http://localhost:8080,http://localhost:8081,http://127.0.0.1:4200,http://127.0.0.1:3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DMS_DATABASE=dms_db
MYSQL_DMS_USER=dms_user
MYSQL_DMS_PASSWORD=dms_password
MYSQL_NOTIFICATION_DATABASE=notification_db
MYSQL_NOTIFICATION_USER=notification_user
MYSQL_NOTIFICATION_PASSWORD=notification_password

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=local_redis_password

# =============================================================================
# RABBITMQ CONFIGURATION
# =============================================================================
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin123

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=localDevSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ

# =============================================================================
# EMAIL CONFIGURATION (Mock for Local Development)
# =============================================================================
# Email settings - using mock for local development
NOTIFICATION_FROM_EMAIL=noreply@localhost
EMAIL_ENABLED=true
EMAIL_MOCK=true

# If you want to test real email in local development, configure these:
# MAIL_HOST=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
DMS_STORAGE_PROVIDER=LOCAL
DMS_STORAGE_LOCAL_BASE_PATH=/app/storage

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Grafana admin password
GF_SECURITY_ADMIN_PASSWORD=admin

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Enable GraphiQL for development
GRAPHIQL_ENABLED=true

# Logging Levels for development
DMS_LOG_LEVEL=DEBUG
NOTIFICATION_LOG_LEVEL=DEBUG

# JVM Options for local development (lighter memory usage)
DMS_JAVA_OPTS=-Xmx1g -Xms512m
NOTIFICATION_JAVA_OPTS=-Xmx512m -Xms256m

# =============================================================================
# PERFORMANCE CONFIGURATION (Local Development)
# =============================================================================
# Database Connection Pool (smaller for local dev)
DB_CONNECTION_POOL_SIZE=5
DB_CONNECTION_TIMEOUT=30000

# Redis Connection Pool (smaller for local dev)
REDIS_POOL_MAX_ACTIVE=5
REDIS_POOL_MAX_IDLE=3
REDIS_POOL_MIN_IDLE=1

# Cache TTL (shorter for development)
CACHE_TTL=300

# =============================================================================
# FEATURE FLAGS (Development)
# =============================================================================
# DMS Features
DMS_VIRUS_SCANNING_ENABLED=false
DMS_ELASTICSEARCH_ENABLED=true

# Notification Features
NOTIFICATION_WEBHOOK_ENABLED=true
NOTIFICATION_BATCH_PROCESSING_ENABLED=true
