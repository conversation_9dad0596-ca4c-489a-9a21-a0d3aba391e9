package com.ascentbusiness.notification_svc.service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import com.ascentbusiness.notification_svc.entity.NotificationTemplate;
import com.ascentbusiness.notification_svc.graphqlInput.AttachmentInput;
import com.ascentbusiness.notification_svc.graphqlInput.NotificationTemplateInput;
import com.ascentbusiness.notification_svc.graphqlInput.RecipientInput;
import com.ascentbusiness.notification_svc.repository.NotificationTemplateRepository;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeBodyPart;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private NotificationTemplateRepository templateRepository;

    @Autowired
    private Configuration freemarkerConfig;
    
    @Autowired
    private TemplateService templateService;
    
    @Autowired
    private AttachmentProcessingService attachmentProcessingService;

    public void sendEmailWithTemplate(String recipientEmail, Map<String, Object> templateModel) 
            throws MessagingException, IOException, TemplateException {
        
        log.info("Attempting to send email to {}", recipientEmail);
        
        // Get template name from model or use default
        String templateName = templateModel.get("templateName") != null ? 
            templateModel.get("templateName").toString() : "default-notification";
        
        // Synchronize template between classpath and database, then get the final template
        NotificationTemplate emailTemplate = templateService.synchronizeAndGetTemplate(templateName);
        
        log.info("Using synchronized email template: {}", emailTemplate.getAlias());
        
        try {
            // Process template
            String htmlBody = processTemplate(emailTemplate.getBody(), templateModel);
            log.info("Template processed successfully");
            
            // Create email message
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());
            
            helper.setTo(recipientEmail);
            helper.setFrom(templateModel.get("senderEmail").toString());
            helper.setSubject(emailTemplate.getSubject());
            helper.setText(htmlBody, true);
            
            log.info("Attempting to send email from {} to {}", templateModel.get("senderEmail"), recipientEmail);
            mailSender.send(message);
            log.info("Email sent successfully with template {} to {}", templateName, recipientEmail);
            
        } catch (Exception e) {
            log.error("Failed to send email with template {}: {}", templateName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Enhanced email sending with attachments and proper CC/BCC support
     * This method sends a single email with proper TO/CC/BCC recipient visibility
     */
    public void sendEmailWithTemplateAndAttachments(
            List<RecipientInput> recipients,
            List<AttachmentInput> commonAttachments,
            Map<String, Object> templateModel) 
            throws MessagingException, IOException, TemplateException {

        if (templateModel == null) {
            throw new IllegalArgumentException("Template model cannot be null");
        }
        String senderEmail = templateModel.get("senderEmail").toString();
        if (senderEmail == null) {
            throw new IllegalArgumentException("Sender email cannot be null");
        }
        if (templateModel.get("templateName") == null) {
            throw new IllegalArgumentException("Template name cannot be null");
        }
        String templateAlias = templateModel.get("templateName").toString();
        
        log.info("Sending email with proper CC/BCC visibility using template {}", templateAlias);
        
        // Synchronize template between classpath and database, then get the final template
        NotificationTemplate emailTemplate = templateService.synchronizeAndGetTemplate(templateAlias);
        
        log.info("Using synchronized email template: {}", emailTemplate.getAlias());
        
        try {
            // Create merged template model with variables from all recipients
            Map<String, Object> mergedTemplateModel = createMergedTemplateModel(templateModel, recipients);
            String htmlBody = processTemplate(emailTemplate.getBody(), mergedTemplateModel);
            log.info("Template processed successfully with merged variables");
            
            // Create email message
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());
            
            helper.setFrom(senderEmail);
            helper.setSubject(emailTemplate.getSubject());
            helper.setText(htmlBody, true);
            
            // Group recipients by type for proper CC/BCC visibility
            List<String> toRecipients = new ArrayList<>();
            List<String> ccRecipients = new ArrayList<>();
            List<String> bccRecipients = new ArrayList<>();
            
            for (RecipientInput recipient : recipients) {
                RecipientInput.RecipientType type = recipient.getType() != null ? 
                    recipient.getType() : RecipientInput.RecipientType.TO;
                
                switch (type) {
                    case TO:
                        toRecipients.add(recipient.getEmail());
                        break;
                    case CC:
                        ccRecipients.add(recipient.getEmail());
                        break;
                    case BCC:
                        bccRecipients.add(recipient.getEmail());
                        break;
                }
            }
            
            // Set all recipients at once for proper CC/BCC visibility
            if (!toRecipients.isEmpty()) {
                helper.setTo(toRecipients.toArray(new String[0]));
                log.info("Added {} TO recipients: {}", toRecipients.size(), String.join(", ", toRecipients));
            }
            
            if (!ccRecipients.isEmpty()) {
                helper.setCc(ccRecipients.toArray(new String[0]));
                log.info("Added {} CC recipients: {}", ccRecipients.size(), String.join(", ", ccRecipients));
            }
            
            if (!bccRecipients.isEmpty()) {
                helper.setBcc(bccRecipients.toArray(new String[0]));
                log.info("Added {} BCC recipients (hidden from other recipients)", bccRecipients.size());
            }
            
            // Ensure we have at least one recipient
            if (toRecipients.isEmpty() && ccRecipients.isEmpty() && bccRecipients.isEmpty()) {
                throw new MessagingException("No valid recipients found");
            }
            
            // Add only common attachments when sending to multiple recipients
            // Recipient-specific attachments are handled in individual emails
            addCommonAttachmentsToEmail(helper, commonAttachments);
            
            int totalRecipients = toRecipients.size() + ccRecipients.size() + bccRecipients.size();
            log.info("Sending email from {} to {} total recipients (TO: {}, CC: {}, BCC: {})", 
                    senderEmail, totalRecipients, toRecipients.size(), ccRecipients.size(), bccRecipients.size());
            
            mailSender.send(message);
            log.info("Email sent successfully with proper CC/BCC visibility - TO recipients can see CC, BCC is hidden from all");
            
        } catch (Exception e) {
            log.error("Failed to send email with proper CC/BCC visibility: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * Helper method to create merged template model with variables from all recipients
     */
    private Map<String, Object> createMergedTemplateModel(Map<String, Object> baseModel, List<RecipientInput> recipients) {
        Map<String, Object> mergedModel = new HashMap<>(baseModel);
        
        // If all recipients have the same variables, merge them
        // Otherwise, use base model only
        if (recipients != null && !recipients.isEmpty()) {
            Map<String, String> commonVariables = new HashMap<>();
            boolean hasCommonVariables = true;
            
            // Check if all recipients have the same variables
            for (RecipientInput recipient : recipients) {
                if (recipient.getVariables() != null && !recipient.getVariables().isEmpty()) {
                    if (commonVariables.isEmpty()) {
                        // First recipient with variables
                        for (var variable : recipient.getVariables()) {
                            commonVariables.put(variable.getKey(), variable.getValue());
                        }
                    } else {
                        // Check if variables match
                        Map<String, String> currentVariables = new HashMap<>();
                        for (var variable : recipient.getVariables()) {
                            currentVariables.put(variable.getKey(), variable.getValue());
                        }
                        if (!commonVariables.equals(currentVariables)) {
                            hasCommonVariables = false;
                            break;
                        }
                    }
                }
            }
            
            // If all recipients have the same variables, add them to the template model
            if (hasCommonVariables && !commonVariables.isEmpty()) {
                mergedModel.putAll(commonVariables);
                log.info("Added {} common variables to template model", commonVariables.size());
            }
        }
        
        return mergedModel;
    }
    
    /**
     * Enhanced helper method to add only common attachments to email
     * Recipient-specific attachments should be handled separately when sending individual emails
     */
    private void addCommonAttachmentsToEmail(MimeMessageHelper helper, List<AttachmentInput> commonAttachments) 
            throws MessagingException {
        
        try {
            // Process and add only common attachments
            if (commonAttachments != null && !commonAttachments.isEmpty()) {
                log.info("Processing {} common attachments", commonAttachments.size());
                List<AttachmentInput> processedCommonAttachments = attachmentProcessingService.processAttachments(commonAttachments);
                for (AttachmentInput attachment : processedCommonAttachments) {
                    addAttachmentToEmail(helper, attachment, "common");
                }
            }
            
        } catch (IOException e) {
            log.error("Failed to process common attachments: {}", e.getMessage(), e);
            throw new MessagingException("Failed to process common attachments: " + e.getMessage(), e);
        }
    }
    
    /**
     * Enhanced helper method to add all attachments to email with file path support
     */
    private void addAllAttachmentsToEmail(MimeMessageHelper helper, List<RecipientInput> recipients, 
            List<AttachmentInput> commonAttachments) throws MessagingException {
        
        try {
            // Process and add common attachments
            if (commonAttachments != null && !commonAttachments.isEmpty()) {
                log.info("Processing {} common attachments", commonAttachments.size());
                List<AttachmentInput> processedCommonAttachments = attachmentProcessingService.processAttachments(commonAttachments);
                for (AttachmentInput attachment : processedCommonAttachments) {
                    addAttachmentToEmail(helper, attachment, "common");
                }
            }
            
            // Add recipient-specific attachments (collect unique ones)
            Map<String, AttachmentInput> uniqueAttachments = new HashMap<>();
            for (RecipientInput recipient : recipients) {
                if (recipient.getAttachments() != null && !recipient.getAttachments().isEmpty()) {
                    log.info("Processing {} attachments for recipient {}", recipient.getAttachments().size(), recipient.getEmail());
                    List<AttachmentInput> processedRecipientAttachments = attachmentProcessingService.processAttachments(recipient.getAttachments());
                    for (AttachmentInput attachment : processedRecipientAttachments) {
                        String attachmentKey = attachment.getFilename() + "|" + attachment.getContentType();
                        if (!uniqueAttachments.containsKey(attachmentKey)) {
                            uniqueAttachments.put(attachmentKey, attachment);
                        }
                    }
                }
            }
            
            // Add unique recipient-specific attachments
            for (AttachmentInput attachment : uniqueAttachments.values()) {
                addAttachmentToEmail(helper, attachment, "recipient-specific");
            }
            
        } catch (IOException e) {
            log.error("Failed to process attachments: {}", e.getMessage(), e);
            throw new MessagingException("Failed to process attachments: " + e.getMessage(), e);
        }
    }

    /**
     * Send email to individual recipient with their specific configuration
     */
    public void sendEmailToRecipient(
            RecipientInput recipient,
            List<AttachmentInput> commonAttachments,
            Map<String, Object> baseTemplateModel) 
            throws MessagingException, IOException, TemplateException {
        
        log.info("Sending individual email to {}", recipient.getEmail());
        
        // Create recipient-specific template model
        Map<String, Object> templateModel = new HashMap<>(baseTemplateModel);
        
        // Add recipient-specific variables
        if (recipient.getVariables() != null && !recipient.getVariables().isEmpty()) {
            for (var variable : recipient.getVariables()) {
                templateModel.put(variable.getKey(), variable.getValue());
            }
        }
        
        // Get template name from model or use default
        String templateName = templateModel.get("templateName") != null ? 
            templateModel.get("templateName").toString() : "default-notification";
        
        // Synchronize template between classpath and database, then get the final template
        NotificationTemplate emailTemplate = templateService.synchronizeAndGetTemplate(templateName);
        
        try {
            // Process template
            String htmlBody = processTemplate(emailTemplate.getBody(), templateModel);
            
            // Create email message
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());
            
            helper.setTo(recipient.getEmail());
            helper.setFrom(templateModel.get("senderEmail").toString());
            helper.setSubject(emailTemplate.getSubject());
            helper.setText(htmlBody, true);
            
            // Handle recipient type for individual emails
            RecipientInput.RecipientType type = recipient.getType() != null ? 
                recipient.getType() : RecipientInput.RecipientType.TO;
            
            switch (type) {
                case TO:
                    helper.setTo(recipient.getEmail());
                    break;
                case CC:
                    // For CC, we need at least one TO recipient, so we'll use the sender
                    helper.setTo(templateModel.get("senderEmail").toString());
                    helper.addCc(recipient.getEmail());
                    break;
                case BCC:
                    // For BCC, we need at least one TO recipient, so we'll use the sender
                    helper.setTo(templateModel.get("senderEmail").toString());
                    helper.addBcc(recipient.getEmail());
                    break;
            }
            
            // Process and add recipient-specific attachments
            if (recipient.getAttachments() != null && !recipient.getAttachments().isEmpty()) {
                List<AttachmentInput> processedAttachments = attachmentProcessingService.processAttachments(recipient.getAttachments());
                for (AttachmentInput attachment : processedAttachments) {
                    addAttachmentToEmail(helper, attachment, recipient.getEmail());
                }
            }
            
            // Process and add common attachments
            if (commonAttachments != null && !commonAttachments.isEmpty()) {
                List<AttachmentInput> processedCommonAttachments = attachmentProcessingService.processAttachments(commonAttachments);
                for (AttachmentInput attachment : processedCommonAttachments) {
                    addAttachmentToEmail(helper, attachment, "common");
                }
            }
            
            mailSender.send(message);
            log.info("Individual email sent successfully to {}", recipient.getEmail());
            
        } catch (Exception e) {
            log.error("Failed to send individual email to {}: {}", recipient.getEmail(), e.getMessage(), e);
            throw e;
        }
    }

    private void addAttachmentToEmail(MimeMessageHelper helper, AttachmentInput attachment, String context) 
            throws MessagingException {
        try {
            log.info("Adding attachment {} for context: {}", attachment.getFilename(), context);
            
            // Decode base64 content
            byte[] decodedContent = java.util.Base64.getDecoder().decode(attachment.getContent());
            
            // Create attachment
            MimeBodyPart attachmentPart = new MimeBodyPart();
            attachmentPart.setContent(decodedContent, attachment.getContentType());
            attachmentPart.setFileName(attachment.getFilename());
            
            // Add to email
            helper.addAttachment(attachment.getFilename(), () -> new java.io.ByteArrayInputStream(decodedContent));
            
            log.info("Successfully added attachment: {} (size: {} bytes, type: {})", 
                    attachment.getFilename(), decodedContent.length, attachment.getContentType());
        } catch (Exception e) {
            log.error("Failed to add attachment {}: {}", attachment.getFilename(), e.getMessage(), e);
            throw new MessagingException("Failed to add attachment: " + attachment.getFilename(), e);
        }
    }

    public NotificationTemplate addOrUpdateTemplate(NotificationTemplateInput input) {
        log.info("Adding/updating template with alias: {}", input.getAlias());
        
        NotificationTemplate template = templateRepository.findByAlias(input.getAlias())
                .orElse(new NotificationTemplate());
        
        template.setAlias(input.getAlias());
        template.setSubject(input.getSubject());
        template.setBody(input.getBody());
        
        NotificationTemplate savedTemplate = templateRepository.save(template);
        log.info("Template saved with ID: {}", savedTemplate.getId());
        
        return savedTemplate;
    }

    public List<NotificationTemplate> getAllTemplates() {
        log.info("Retrieving all email templates");
        return templateRepository.findAll();
    }

    public NotificationTemplate getTemplateByAlias(String alias) {
        log.info("Retrieving template by alias: {}", alias);
        return templateRepository.findByAlias(alias)
                .orElseThrow(() -> new RuntimeException("Template not found with alias: " + alias));
    }

    private String processTemplate(String templateContent, Map<String, Object> model) 
            throws IOException, TemplateException {
        freemarkerConfig.setClassForTemplateLoading(this.getClass(), "/templates/email/");
        Template template = new Template("dynamicTemplate", templateContent, freemarkerConfig);
        return FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
    }
}
