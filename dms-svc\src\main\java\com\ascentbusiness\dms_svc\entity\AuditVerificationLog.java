package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Entity representing audit verification activities and tamper detection
 */
@Entity
@Table(name = "audit_verification_log")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuditVerificationLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "verification_id", nullable = false, unique = true, length = 100)
    private String verificationId;
    
    @Column(name = "audit_log_id")
    private Long auditLogId;
    
    @Column(name = "chain_id", length = 100)
    private String chainId;
    
    @Column(name = "verification_type", nullable = false, length = 50)
    private String verificationType;
    
    @Column(name = "verification_date")
    private LocalDateTime verificationDate;
    
    @Column(name = "verified_by", length = 100)
    private String verifiedBy;
    
    @Column(name = "verification_method", length = 50)
    private String verificationMethod;
    
    @Column(name = "expected_hash", length = 128)
    private String expectedHash;
    
    @Column(name = "actual_hash", length = 128)
    private String actualHash;
    
    @Column(name = "hash_match")
    private Boolean hashMatch;
    
    @Column(name = "signature_valid")
    private Boolean signatureValid;
    
    @Column(name = "chain_integrity_valid")
    private Boolean chainIntegrityValid;
    
    @Column(name = "overall_status", length = 20)
    private String overallStatus;
    
    @Column(name = "anomalies_detected", columnDefinition = "JSON")
    private String anomaliesDetected;
    
    @Column(name = "verification_details", columnDefinition = "JSON")
    private String verificationDetails;
    
    @Column(name = "correlation_id", length = 100)
    private String correlationId;
    
    @Column(name = "created_date")
    private LocalDateTime createdDate;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audit_log_id", referencedColumnName = "id", insertable = false, updatable = false)
    private AuditLog auditLog;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chain_id", referencedColumnName = "chain_id", insertable = false, updatable = false)
    private AuditChainMetadata auditChainMetadata;
    
    @PrePersist
    protected void onCreate() {
        if (createdDate == null) {
            createdDate = LocalDateTime.now();
        }
        if (verificationDate == null) {
            verificationDate = LocalDateTime.now();
        }
    }
    
    /**
     * Check if verification passed all checks
     */
    @Transient
    public boolean isVerificationSuccessful() {
        return "VERIFIED".equals(overallStatus) &&
               Boolean.TRUE.equals(hashMatch) &&
               Boolean.TRUE.equals(signatureValid) &&
               Boolean.TRUE.equals(chainIntegrityValid);
    }
    
    /**
     * Check if verification failed
     */
    @Transient
    public boolean isVerificationFailed() {
        return "FAILED".equals(overallStatus) ||
               Boolean.FALSE.equals(hashMatch) ||
               Boolean.FALSE.equals(signatureValid) ||
               Boolean.FALSE.equals(chainIntegrityValid);
    }
    
    /**
     * Check if tampering was detected
     */
    @Transient
    public boolean isTamperingDetected() {
        return isVerificationFailed() && 
               (Boolean.FALSE.equals(hashMatch) || Boolean.FALSE.equals(chainIntegrityValid));
    }
    
    /**
     * Get verification score (0-100)
     */
    @Transient
    public int getVerificationScore() {
        int score = 0;
        
        if (Boolean.TRUE.equals(hashMatch)) {
            score += 40;
        }
        
        if (Boolean.TRUE.equals(signatureValid)) {
            score += 30;
        }
        
        if (Boolean.TRUE.equals(chainIntegrityValid)) {
            score += 30;
        }
        
        return score;
    }
    
    /**
     * Get verification status summary
     */
    @Transient
    public String getVerificationSummary() {
        if (isVerificationSuccessful()) {
            return "All verification checks passed - audit log integrity confirmed";
        } else if (isTamperingDetected()) {
            return "TAMPERING DETECTED - audit log integrity compromised";
        } else if (isVerificationFailed()) {
            return "Verification failed - unable to confirm audit log integrity";
        } else {
            return "Verification in progress";
        }
    }
    
    /**
     * Get risk level based on verification results
     */
    @Transient
    public String getRiskLevel() {
        if (isTamperingDetected()) {
            return "CRITICAL";
        } else if (isVerificationFailed()) {
            return "HIGH";
        } else if (isVerificationSuccessful()) {
            return "LOW";
        } else {
            return "MEDIUM";
        }
    }
    
    /**
     * Check if immediate action is required
     */
    @Transient
    public boolean requiresImmediateAction() {
        return isTamperingDetected() || "CRITICAL".equals(getRiskLevel());
    }
    
    /**
     * Set verification results
     */
    public void setVerificationResults(boolean hashMatch, boolean signatureValid, boolean chainIntegrityValid) {
        this.hashMatch = hashMatch;
        this.signatureValid = signatureValid;
        this.chainIntegrityValid = chainIntegrityValid;
        
        if (hashMatch && signatureValid && chainIntegrityValid) {
            this.overallStatus = "VERIFIED";
        } else {
            this.overallStatus = "FAILED";
        }
    }
    
    @Override
    public String toString() {
        return String.format("AuditVerificationLog{id=%d, verificationId='%s', type='%s', status='%s', score=%d}", 
                           id, verificationId, verificationType, overallStatus, getVerificationScore());
    }
}
