package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Entity representing storage provider configurations stored in database.
 * This allows for runtime configuration changes without application restart.
 */
@Entity
@Table(name = "storage_configurations")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StorageConfiguration {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "provider_type", nullable = false, unique = true)
    private StorageProvider providerType;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = false;

    @Builder.Default
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    @Column(name = "configuration_json", columnDefinition = "JSON", nullable = false)
    private String configurationJson;

    @Column(name = "description")
    private String description;

    @Builder.Default
    @Column(name = "priority", nullable = false)
    private Integer priority = 0;

    @Builder.Default
    @Column(name = "health_check_enabled", nullable = false)
    private Boolean healthCheckEnabled = true;

    @Column(name = "last_health_check")
    private LocalDateTime lastHealthCheck;

    @Column(name = "health_status")
    private String healthStatus;

    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    @Column(name = "last_modified_date")
    private LocalDateTime lastModifiedDate;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "last_modified_by")
    private String lastModifiedBy;

    @PrePersist
    protected void onCreate() {
        createdDate = LocalDateTime.now();
        lastModifiedDate = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        lastModifiedDate = LocalDateTime.now();
    }

    // Helper methods
    public boolean isHealthy() {
        return "HEALTHY".equals(healthStatus);
    }

    public void markHealthy() {
        this.healthStatus = "HEALTHY";
        this.lastHealthCheck = LocalDateTime.now();
    }

    public void markUnhealthy(String reason) {
        this.healthStatus = "UNHEALTHY: " + reason;
        this.lastHealthCheck = LocalDateTime.now();
    }

    // Validation methods
    public boolean isValidConfiguration() {
        return configurationJson != null && !configurationJson.trim().isEmpty();
    }

    public boolean canBeDefault() {
        return isActive && isValidConfiguration() && isHealthy();
    }
}





