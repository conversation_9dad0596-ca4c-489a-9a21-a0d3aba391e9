package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;

import java.util.List;

/**
 * Input DTO for document upload metadata.
 * Corresponds to DocumentUploadMetadataInput GraphQL input type.
 */
@Data
public class DocumentUploadMetadataInput {
    
    /**
     * Description of the document.
     */
    private String description;
    
    /**
     * Keywords/tags for the document.
     */
    private List<String> keywords;
    
    /**
     * Storage provider for the document.
     */
    private StorageProvider storageProvider;
    
    /**
     * Classification metadata for the document.
     */
    private DocumentClassificationMetadataInput classificationMetadata;
    
    /**
     * Ownership metadata for the document.
     */
    private DocumentOwnershipMetadataInput ownershipMetadata;
    
    /**
     * Compliance metadata for the document.
     */
    private DocumentComplianceMetadataInput complianceMetadata;
}
