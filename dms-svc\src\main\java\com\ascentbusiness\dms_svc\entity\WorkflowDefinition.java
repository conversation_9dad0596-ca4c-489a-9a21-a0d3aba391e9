package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.ApprovalType;
import com.ascentbusiness.dms_svc.enums.WorkflowType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Set;

/**
 * Entity representing a workflow definition template
 */
@Entity
@Table(name = "workflow_definitions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowDefinition extends BaseEntity {

    @Column(name = "name", nullable = false, length = 255)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "version", nullable = false, length = 50)
    @Builder.Default
    private String version = "1.0";

    @Enumerated(EnumType.STRING)
    @Column(name = "workflow_type", nullable = false, length = 50)
    private WorkflowType workflowType;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Builder.Default
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    // Workflow configuration stored as JSON
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "configuration_json", columnDefinition = "JSON")
    private JsonNode configurationJson;

    @Enumerated(EnumType.STRING)
    @Column(name = "approval_type", nullable = false, length = 50)
    @Builder.Default
    private ApprovalType approvalType = ApprovalType.SEQUENTIAL;

    @Builder.Default
    @Column(name = "auto_start", nullable = false)
    private Boolean autoStart = false;

    @Column(name = "timeout_hours")
    @Builder.Default
    private Integer timeoutHours = 72;

    @Builder.Default
    @Column(name = "escalation_enabled", nullable = false)
    private Boolean escalationEnabled = true;

    @Column(name = "escalation_hours")
    @Builder.Default
    private Integer escalationHours = 24;

    // Trigger conditions stored as JSON
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "trigger_conditions", columnDefinition = "JSON")
    private JsonNode triggerConditions;

    // Document types this workflow applies to
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "document_types", columnDefinition = "JSON")
    private JsonNode documentTypes;

    // Department restrictions
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "department_restrictions", columnDefinition = "JSON")
    private JsonNode departmentRestrictions;

    // Classification requirements
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "classification_requirements", columnDefinition = "JSON")
    private JsonNode classificationRequirements;

    // Relationships
    @OneToMany(mappedBy = "workflowDefinition", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowStage> stages;

    @OneToMany(mappedBy = "workflowDefinition", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowTransition> transitions;

    @OneToMany(mappedBy = "workflowDefinition", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowInstance> instances;

    /**
     * Check if this workflow definition is currently active and effective
     */
    @Transient
    public boolean isCurrentlyActive() {
        return isActive != null && isActive;
    }

    /**
     * Get the number of stages in this workflow
     */
    @Transient
    public int getStageCount() {
        return stages != null ? stages.size() : 0;
    }

    /**
     * Check if this workflow supports parallel processing
     */
    @Transient
    public boolean supportsParallelProcessing() {
        return ApprovalType.PARALLEL.equals(approvalType) || 
               ApprovalType.CONDITIONAL.equals(approvalType);
    }

    /**
     * Get estimated completion time in hours
     */
    @Transient
    public int getEstimatedCompletionHours() {
        if (stages == null || stages.isEmpty()) {
            return timeoutHours != null ? timeoutHours : 72;
        }

        if (ApprovalType.PARALLEL.equals(approvalType)) {
            // For parallel workflows, use the maximum stage timeout
            return stages.stream()
                    .mapToInt(stage -> stage.getTimeoutHours() != null ? stage.getTimeoutHours() : 24)
                    .max()
                    .orElse(timeoutHours != null ? timeoutHours : 72);
        } else {
            // For sequential workflows, sum all stage timeouts
            return stages.stream()
                    .mapToInt(stage -> stage.getTimeoutHours() != null ? stage.getTimeoutHours() : 24)
                    .sum();
        }
    }

    @Override
    public String toString() {
        return String.format("WorkflowDefinition{id=%d, name='%s', type=%s, version='%s', active=%s}", 
                           getId(), name, workflowType, version, isActive);
    }
}
