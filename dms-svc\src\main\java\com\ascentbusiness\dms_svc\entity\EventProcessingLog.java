package com.ascentbusiness.dms_svc.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Entity representing event processing log entries
 */
@Entity
@Table(name = "event_processing_log")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EventProcessingLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "system_event_id", nullable = false)
    @JsonIgnore
    private SystemEvent systemEvent;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "event_subscription_id")
    @JsonIgnore
    private EventSubscription eventSubscription;

    // Processing details
    @Column(name = "processing_stage", nullable = false, length = 50)
    private String processingStage; // RECEIVED, FILTERED, QUEUED, PROCESSING, DELIVERED, FAILED

    @Column(name = "processing_status", nullable = false, length = 50)
    private String processingStatus; // SUCCESS, FAILED, SKIPPED

    // Processing metadata
    @Column(name = "processor_name", length = 100)
    private String processorName;

    @Column(name = "processing_duration_ms")
    private Integer processingDurationMs;

    // Error information
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "error_stack_trace", columnDefinition = "TEXT")
    private String errorStackTrace;

    @Column(name = "processing_timestamp", nullable = false)
    @Builder.Default
    private LocalDateTime processingTimestamp = LocalDateTime.now();

    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    /**
     * Check if processing was successful
     */
    @Transient
    public boolean isSuccessful() {
        return "SUCCESS".equals(processingStatus);
    }

    /**
     * Check if processing failed
     */
    @Transient
    public boolean isFailed() {
        return "FAILED".equals(processingStatus);
    }

    /**
     * Check if processing was skipped
     */
    @Transient
    public boolean isSkipped() {
        return "SKIPPED".equals(processingStatus);
    }

    /**
     * Check if this is a received stage log
     */
    @Transient
    public boolean isReceivedStage() {
        return "RECEIVED".equals(processingStage);
    }

    /**
     * Check if this is a filtered stage log
     */
    @Transient
    public boolean isFilteredStage() {
        return "FILTERED".equals(processingStage);
    }

    /**
     * Check if this is a queued stage log
     */
    @Transient
    public boolean isQueuedStage() {
        return "QUEUED".equals(processingStage);
    }

    /**
     * Check if this is a processing stage log
     */
    @Transient
    public boolean isProcessingStage() {
        return "PROCESSING".equals(processingStage);
    }

    /**
     * Check if this is a delivered stage log
     */
    @Transient
    public boolean isDeliveredStage() {
        return "DELIVERED".equals(processingStage);
    }

    /**
     * Check if this log entry has error information
     */
    @Transient
    public boolean hasError() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }

    /**
     * Get processing duration in seconds
     */
    @Transient
    public double getProcessingDurationSeconds() {
        return processingDurationMs != null ? processingDurationMs / 1000.0 : 0.0;
    }

    /**
     * Check if processing was fast (under 100ms)
     */
    @Transient
    public boolean isFastProcessing() {
        return processingDurationMs != null && processingDurationMs < 100;
    }

    /**
     * Check if processing was slow (over 5 seconds)
     */
    @Transient
    public boolean isSlowProcessing() {
        return processingDurationMs != null && processingDurationMs > 5000;
    }

    /**
     * Get the event type
     */
    @Transient
    public String getEventType() {
        return systemEvent != null && systemEvent.getEventType() != null ? 
               systemEvent.getEventType().name() : null;
    }

    /**
     * Get the subscription name
     */
    @Transient
    public String getSubscriptionName() {
        return eventSubscription != null ? eventSubscription.getName() : null;
    }

    /**
     * Get processing stage display name
     */
    @Transient
    public String getProcessingStageDisplayName() {
        if (processingStage == null) return "Unknown";
        
        switch (processingStage.toUpperCase()) {
            case "RECEIVED":
                return "Event Received";
            case "FILTERED":
                return "Event Filtered";
            case "QUEUED":
                return "Event Queued";
            case "PROCESSING":
                return "Event Processing";
            case "DELIVERED":
                return "Event Delivered";
            case "FAILED":
                return "Processing Failed";
            default:
                return processingStage;
        }
    }

    /**
     * Get processing status display name
     */
    @Transient
    public String getProcessingStatusDisplayName() {
        if (processingStatus == null) return "Unknown";
        
        switch (processingStatus.toUpperCase()) {
            case "SUCCESS":
                return "Success";
            case "FAILED":
                return "Failed";
            case "SKIPPED":
                return "Skipped";
            default:
                return processingStatus;
        }
    }

    @Override
    public String toString() {
        return String.format("EventProcessingLog{id=%d, event='%s', stage='%s', status='%s', processor='%s'}", 
                           getId(), getEventType(), processingStage, processingStatus, processorName);
    }
}
