package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionQuality;
import com.ascentbusiness.dms_svc.enums.PageOrientation;
import lombok.Data;

/**
 * Options input DTO for conversion operations from conversion-schema.graphqls.
 */
@Data
public class ConversionOptionsInput {
    private ConversionQuality quality = ConversionQuality.STANDARD;
    private Integer compressionLevel; // 1-9 for applicable formats
    private Boolean preserveFormatting = true;
    private Boolean includeImages = true;
    private Boolean includeMetadata = true;
    private String pageSize; // A4, Letter, etc.
    private PageOrientation orientation = PageOrientation.PORTRAIT;
    private MarginsInput margins;
    private String customOptions; // JSON string for format-specific options
}
