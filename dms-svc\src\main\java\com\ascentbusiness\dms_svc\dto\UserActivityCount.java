package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for user activity count statistics
 * Corresponds to UserActivityCount type in audit-schema.graphqls
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserActivityCount {
    
    private String userId;
    private Long actionCount;
    private LocalDateTime lastActivity;
    private Float riskScore;
}
