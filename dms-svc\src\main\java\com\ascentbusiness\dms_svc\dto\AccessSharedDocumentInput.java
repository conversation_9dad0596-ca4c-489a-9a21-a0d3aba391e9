package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Input DTO for accessing shared documents via share links.
 * 
 * This DTO encapsulates the parameters needed to access a document
 * through a shareable link, including the link ID and optional password.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccessSharedDocumentInput {

    /** The share link ID */
    private String linkId;
    
    /** Optional password for password-protected links */
    private String password;

    /**
     * Validate the input parameters
     */
    public void validate() {
        if (linkId == null || linkId.trim().isEmpty()) {
            throw new IllegalArgumentException("Link ID is required");
        }
    }

    /**
     * Check if a password is provided
     */
    public boolean hasPassword() {
        return password != null && !password.trim().isEmpty();
    }
}
