package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Input DTO for bulk document permission operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkDocumentPermissionInput {
    
    private Long documentId;
    private List<SinglePermissionInput> permissions;
    
    public void validate() {
        if (documentId == null) {
            throw new IllegalArgumentException("Document ID is required");
        }
        
        if (permissions == null || permissions.isEmpty()) {
            throw new IllegalArgumentException("At least one permission must be provided");
        }
        
        // Validate each permission
        for (SinglePermissionInput permission : permissions) {
            permission.validate();
        }
    }
}
