-- Liquibase formatted SQL
-- changeset dms:027-enhance-audit-system-grc

-- Add tamper-proof fields to existing audit_logs table
ALTER TABLE audit_logs 
ADD COLUMN event_type VARCHAR(50) DEFAULT 'DOCUMENT_OPERATION',
ADD COLUMN event_category VARCHAR(50) DEFAULT 'GENERAL',
ADD COLUMN compliance_framework_id BIGINT NULL,
ADD COLUMN regulation_reference VARCHAR(255) NULL,
ADD COLUMN data_subject_category VARCHAR(50) NULL,
ADD COLUMN geographic_region VARCHAR(50) NULL,
ADD COLUMN risk_level VARCHAR(20) DEFAULT 'LOW',
ADD COLUMN business_impact VARCHAR(20) DEFAULT 'LOW',
ADD COLUMN technical_details JSON NULL,
ADD COLUMN before_state JSON NULL,
ADD COLUMN after_state JSON NULL,
ADD COLUMN session_id VARCHAR(100) NULL,
ADD COLUMN request_id VARCHAR(100) NULL,
ADD COLUMN client_info JSON NULL,
ADD COLUMN hash_value VARCHAR(128) NULL,
ADD COLUMN previous_hash VARCHAR(128) NULL,
ADD COLUMN chain_sequence BIGINT NULL,
ADD COLUMN digital_signature TEXT NULL,
ADD COLUMN signature_algorithm VARCHAR(50) NULL,
ADD COLUMN signature_timestamp TIMESTAMP NULL,
ADD COLUMN verification_status VARCHAR(20) DEFAULT 'PENDING',
ADD COLUMN is_tampered BOOLEAN DEFAULT FALSE,
ADD COLUMN tamper_detection_date TIMESTAMP NULL,
ADD COLUMN retention_period_days INTEGER NULL,
ADD COLUMN archive_date TIMESTAMP NULL,
ADD COLUMN is_archived BOOLEAN DEFAULT FALSE,
ADD COLUMN export_count INTEGER DEFAULT 0,
ADD COLUMN last_export_date TIMESTAMP NULL;

-- Add indexes for performance
ALTER TABLE audit_logs 
ADD INDEX idx_audit_event_type (event_type),
ADD INDEX idx_audit_event_category (event_category),
ADD INDEX idx_audit_compliance_framework (compliance_framework_id),
ADD INDEX idx_audit_risk_level (risk_level),
ADD INDEX idx_audit_chain_sequence (chain_sequence),
ADD INDEX idx_audit_verification_status (verification_status),
ADD INDEX idx_audit_tampered (is_tampered),
ADD INDEX idx_audit_archived (is_archived),
ADD INDEX idx_audit_timestamp_category (timestamp, event_category),
ADD INDEX idx_audit_user_event_type (user_id, event_type);

-- Add foreign key constraint for compliance framework
ALTER TABLE audit_logs 
ADD CONSTRAINT fk_audit_compliance_framework 
FOREIGN KEY (compliance_framework_id) REFERENCES compliance_frameworks(id);

-- Create audit_chain_metadata table for cryptographic chaining
CREATE TABLE audit_chain_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chain_id VARCHAR(100) NOT NULL UNIQUE,
    chain_name VARCHAR(255) NOT NULL,
    description TEXT,
    hash_algorithm VARCHAR(50) NOT NULL DEFAULT 'SHA-256',
    signature_algorithm VARCHAR(50) NOT NULL DEFAULT 'RSA-2048',
    current_sequence BIGINT NOT NULL DEFAULT 0,
    genesis_hash VARCHAR(128) NOT NULL,
    last_block_hash VARCHAR(128),
    last_block_timestamp TIMESTAMP,
    total_entries BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    verification_key TEXT,
    signing_key_fingerprint VARCHAR(64),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    INDEX idx_chain_id (chain_id),
    INDEX idx_chain_active (is_active),
    INDEX idx_chain_sequence (current_sequence)
);

-- Create audit_exports table for tracking exports
CREATE TABLE audit_exports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    export_id VARCHAR(100) NOT NULL UNIQUE,
    export_type VARCHAR(50) NOT NULL,
    export_format VARCHAR(20) NOT NULL,
    export_reason VARCHAR(100),
    requested_by VARCHAR(100) NOT NULL,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    filter_criteria JSON,
    total_records BIGINT,
    file_path VARCHAR(500),
    file_size_bytes BIGINT,
    file_hash VARCHAR(128),
    export_status VARCHAR(20) DEFAULT 'PENDING',
    completion_date TIMESTAMP,
    error_message TEXT,
    digital_signature TEXT,
    signature_timestamp TIMESTAMP,
    retention_period_days INTEGER DEFAULT 2555,
    archive_date TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    last_download_date TIMESTAMP,
    correlation_id VARCHAR(100),
    compliance_framework_id BIGINT,
    regulatory_requirement VARCHAR(255),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (compliance_framework_id) REFERENCES compliance_frameworks(id),
    INDEX idx_export_id (export_id),
    INDEX idx_export_type (export_type),
    INDEX idx_export_status (export_status),
    INDEX idx_export_requested_by (requested_by),
    INDEX idx_export_request_date (request_date),
    INDEX idx_export_compliance (compliance_framework_id)
);

-- Create audit_verification_log table for tamper detection
CREATE TABLE audit_verification_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    verification_id VARCHAR(100) NOT NULL UNIQUE,
    audit_log_id BIGINT,
    chain_id VARCHAR(100),
    verification_type VARCHAR(50) NOT NULL,
    verification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_by VARCHAR(100),
    verification_method VARCHAR(50),
    expected_hash VARCHAR(128),
    actual_hash VARCHAR(128),
    hash_match BOOLEAN,
    signature_valid BOOLEAN,
    chain_integrity_valid BOOLEAN,
    overall_status VARCHAR(20),
    anomalies_detected JSON,
    verification_details JSON,
    correlation_id VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (audit_log_id) REFERENCES audit_logs(id),
    FOREIGN KEY (chain_id) REFERENCES audit_chain_metadata(chain_id),
    INDEX idx_verification_id (verification_id),
    INDEX idx_verification_audit_log (audit_log_id),
    INDEX idx_verification_chain (chain_id),
    INDEX idx_verification_status (overall_status),
    INDEX idx_verification_date (verification_date)
);

-- Create audit_summary_reports table for scheduled reporting
CREATE TABLE audit_summary_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    report_id VARCHAR(100) NOT NULL UNIQUE,
    report_type VARCHAR(50) NOT NULL,
    report_period VARCHAR(20) NOT NULL,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    generated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generated_by VARCHAR(100),
    total_events BIGINT,
    compliance_events BIGINT,
    security_events BIGINT,
    violation_events BIGINT,
    high_risk_events BIGINT,
    summary_data JSON,
    report_file_path VARCHAR(500),
    report_file_size BIGINT,
    report_hash VARCHAR(128),
    digital_signature TEXT,
    is_published BOOLEAN DEFAULT FALSE,
    publication_date TIMESTAMP,
    retention_period_days INTEGER DEFAULT 2555,
    archive_date TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE,
    correlation_id VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_summary_report_id (report_id),
    INDEX idx_summary_report_type (report_type),
    INDEX idx_summary_period (period_start, period_end),
    INDEX idx_summary_generated_date (generated_date),
    INDEX idx_summary_published (is_published)
);

-- Update existing audit action enum to include compliance events
ALTER TABLE audit_logs 
DROP CONSTRAINT IF EXISTS chk_audit_action;

ALTER TABLE audit_logs 
ADD CONSTRAINT chk_audit_action 
CHECK (action IN (
    -- Existing document operations
    'UPLOAD', 'DOWNLOAD', 'DELETE', 'UPDATE', 'VERSION_CREATE',
    'PERMISSION_GRANTED', 'PERMISSION_REVOKED', 'PERMISSION_EXPIRED', 'PERMISSION_CHECKED',
    'SECURITY_VIOLATION', 'TOKEN_VALIDATION_FAILED', 'RATE_LIMIT_EXCEEDED',
    'MIGRATION_STARTED', 'MIGRATION_COMPLETED', 'MIGRATION_FAILED',
    'MIGRATION_FILE_PROCESSED', 'MIGRATION_FILE_VERIFIED', 'MIGRATION_FILE_CLEANUP',
    'MIGRATION_SECURITY_CHECK', 'MIGRATION_VALIDATION_FAILED',
    'RETENTION_POLICY_CREATED', 'RETENTION_POLICY_UPDATED', 'RETENTION_POLICY_DELETED',
    'RETENTION_POLICY_ASSIGNED', 'RETENTION_POLICY_UNASSIGNED', 'RETENTION_EXPIRY_CALCULATED',
    'RETENTION_PROCESSING_STARTED', 'RETENTION_PROCESSING_COMPLETED', 'RETENTION_PROCESSING_FAILED',
    'DOCUMENT_ARCHIVED', 'DOCUMENT_DISPOSED',
    -- New compliance-specific events
    'COMPLIANCE_CLASSIFICATION_ASSIGNED', 'COMPLIANCE_CLASSIFICATION_CHANGED', 'COMPLIANCE_CLASSIFICATION_REMOVED',
    'REGULATORY_FRAMEWORK_ASSIGNED', 'REGULATORY_FRAMEWORK_UPDATED', 'REGULATORY_FRAMEWORK_REMOVED',
    'COMPLIANCE_REVIEW_STARTED', 'COMPLIANCE_REVIEW_COMPLETED', 'COMPLIANCE_REVIEW_FAILED',
    'COMPLIANCE_VIOLATION_DETECTED', 'COMPLIANCE_VIOLATION_RESOLVED', 'COMPLIANCE_VIOLATION_ESCALATED',
    'DATA_SUBJECT_REQUEST_RECEIVED', 'DATA_SUBJECT_REQUEST_PROCESSED', 'DATA_SUBJECT_REQUEST_COMPLETED',
    'CONSENT_GRANTED', 'CONSENT_REVOKED', 'CONSENT_EXPIRED', 'CONSENT_RENEWED',
    'ENCRYPTION_APPLIED', 'ENCRYPTION_REMOVED', 'ENCRYPTION_KEY_ROTATED',
    'ANONYMIZATION_APPLIED', 'PSEUDONYMIZATION_APPLIED', 'DATA_MASKING_APPLIED',
    'AUDIT_EXPORT_REQUESTED', 'AUDIT_EXPORT_COMPLETED', 'AUDIT_EXPORT_FAILED',
    'AUDIT_VERIFICATION_STARTED', 'AUDIT_VERIFICATION_COMPLETED', 'AUDIT_TAMPERING_DETECTED',
    'LEGAL_HOLD_APPLIED', 'LEGAL_HOLD_RELEASED', 'LEGAL_HOLD_EXTENDED',
    'REGULATORY_REPORT_GENERATED', 'REGULATORY_REPORT_SUBMITTED', 'REGULATORY_NOTIFICATION_SENT'
));

-- Insert initial audit chain metadata
INSERT INTO audit_chain_metadata (
    chain_id, 
    chain_name, 
    description, 
    genesis_hash, 
    created_by
) VALUES (
    'DMS_MAIN_AUDIT_CHAIN',
    'DMS Main Audit Chain',
    'Primary audit chain for DMS document operations and compliance events',
    SHA2(CONCAT('DMS_GENESIS_', NOW(), '_', CONNECTION_ID()), 256),
    'SYSTEM'
);

-- Add comments for documentation
ALTER TABLE audit_logs COMMENT = 'Enhanced audit logs with tamper-proof storage and compliance event tracking';
ALTER TABLE audit_chain_metadata COMMENT = 'Metadata for cryptographic audit chains ensuring tamper-proof storage';
ALTER TABLE audit_exports COMMENT = 'Tracks audit log exports for regulatory reporting and compliance';
ALTER TABLE audit_verification_log COMMENT = 'Logs audit verification activities and tamper detection results';
ALTER TABLE audit_summary_reports COMMENT = 'Stores scheduled audit summary reports for compliance dashboards';
