package com.ascentbusiness.dms_svc.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/**
 * Entity representing a transition between workflow stages
 */
@Entity
@Table(name = "workflow_transitions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowTransition extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_definition_id", nullable = false)
    @JsonIgnore
    private WorkflowDefinition workflowDefinition;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "from_stage_id")
    @JsonIgnore
    private WorkflowStage fromStage;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "to_stage_id")
    @JsonIgnore
    private WorkflowStage toStage;

    @Column(name = "transition_name", nullable = false, length = 255)
    private String transitionName;

    @Column(name = "transition_type", nullable = false, length = 50)
    @Builder.Default
    private String transitionType = "APPROVE"; // APPROVE, REJECT, DELEGATE, ESCALATE, CONDITIONAL

    // Transition conditions stored as JSON
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "conditions", columnDefinition = "JSON")
    private JsonNode conditions;

    @Builder.Default
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    @Builder.Default
    @Column(name = "requires_comment", nullable = false)
    private Boolean requiresComment = false;

    /**
     * Check if this is a start transition (no from stage)
     */
    @Transient
    public boolean isStartTransition() {
        return fromStage == null;
    }

    /**
     * Check if this is an end transition (no to stage)
     */
    @Transient
    public boolean isEndTransition() {
        return toStage == null;
    }

    /**
     * Check if this transition has conditions
     */
    @Transient
    public boolean hasConditions() {
        return conditions != null && !conditions.isNull();
    }

    /**
     * Get the from stage name
     */
    @Transient
    public String getFromStageName() {
        return fromStage != null ? fromStage.getStageName() : "START";
    }

    /**
     * Get the to stage name
     */
    @Transient
    public String getToStageName() {
        return toStage != null ? toStage.getStageName() : "END";
    }

    @Override
    public String toString() {
        return String.format("WorkflowTransition{id=%d, name='%s', type='%s', from='%s', to='%s'}", 
                           getId(), transitionName, transitionType, getFromStageName(), getToStageName());
    }
}
