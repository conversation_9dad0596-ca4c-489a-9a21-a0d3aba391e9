package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Result DTO for bulk document upload operations.
 * 
 * <p>This DTO encapsulates the complete results of a bulk upload operation,
 * including overall statistics, individual file results, and summary information
 * about virus scanning and upload outcomes.
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkUploadResult {
    
    /**
     * Unique identifier for this bulk upload operation.
     */
    private String operationId;
    
    /**
     * Total number of files processed.
     */
    private Integer totalFiles;
    
    /**
     * Number of files successfully uploaded.
     */
    private Integer successCount;
    
    /**
     * Number of files that failed to upload.
     */
    private Integer failureCount;
    
    /**
     * Number of files that were infected and skipped.
     */
    private Integer virusDetectedCount;
    
    /**
     * Number of files that failed virus scanning.
     */
    private Integer scanErrorCount;
    
    /**
     * Whether the overall operation was successful.
     * True if at least one file was uploaded successfully.
     */
    private Boolean overallSuccess;
    
    /**
     * Operation start time.
     */
    private LocalDateTime operationStartTime;
    
    /**
     * Operation end time.
     */
    private LocalDateTime operationEndTime;
    
    /**
     * Total operation duration in milliseconds.
     */
    private Long operationDurationMs;
    
    /**
     * Results for individual files.
     */
    private List<BulkUploadItemResult> itemResults;
    
    /**
     * Summary message describing the operation outcome.
     */
    private String summaryMessage;
    
    /**
     * Creates a bulk upload result from individual item results.
     * 
     * @param operationId the operation ID
     * @param itemResults the individual file results
     * @param startTime the operation start time
     * @param endTime the operation end time
     * @return the bulk upload result
     */
    public static BulkUploadResult fromItemResults(String operationId, List<BulkUploadItemResult> itemResults,
                                                 LocalDateTime startTime, LocalDateTime endTime) {
        int totalFiles = itemResults.size();
        int successCount = (int) itemResults.stream().filter(BulkUploadItemResult::getSuccessful).count();
        int failureCount = totalFiles - successCount;
        int virusDetectedCount = (int) itemResults.stream().filter(BulkUploadItemResult::isVirusDetected).count();
        int scanErrorCount = (int) itemResults.stream().filter(BulkUploadItemResult::isScanError).count();
        
        long durationMs = java.time.Duration.between(startTime, endTime).toMillis();
        
        String summaryMessage = buildSummaryMessage(totalFiles, successCount, failureCount, 
                                                  virusDetectedCount, scanErrorCount);
        
        return BulkUploadResult.builder()
                .operationId(operationId)
                .totalFiles(totalFiles)
                .successCount(successCount)
                .failureCount(failureCount)
                .virusDetectedCount(virusDetectedCount)
                .scanErrorCount(scanErrorCount)
                .overallSuccess(successCount > 0)
                .operationStartTime(startTime)
                .operationEndTime(endTime)
                .operationDurationMs(durationMs)
                .itemResults(itemResults)
                .summaryMessage(summaryMessage)
                .build();
    }
    
    /**
     * Gets the success rate as a percentage.
     * 
     * @return success rate (0-100)
     */
    public double getSuccessRate() {
        if (totalFiles == null || totalFiles == 0) {
            return 0.0;
        }
        return (successCount.doubleValue() / totalFiles.doubleValue()) * 100.0;
    }
    
    /**
     * Gets the virus detection rate as a percentage.
     * 
     * @return virus detection rate (0-100)
     */
    public double getVirusDetectionRate() {
        if (totalFiles == null || totalFiles == 0) {
            return 0.0;
        }
        return (virusDetectedCount.doubleValue() / totalFiles.doubleValue()) * 100.0;
    }
    
    /**
     * Gets all successfully uploaded documents.
     * 
     * @return list of successful upload results
     */
    public List<BulkUploadItemResult> getSuccessfulUploads() {
        return itemResults.stream()
                .filter(BulkUploadItemResult::getSuccessful)
                .collect(Collectors.toList());
    }
    
    /**
     * Gets all failed upload results.
     * 
     * @return list of failed upload results
     */
    public List<BulkUploadItemResult> getFailedUploads() {
        return itemResults.stream()
                .filter(result -> !result.getSuccessful())
                .collect(Collectors.toList());
    }
    
    /**
     * Gets all results where viruses were detected.
     * 
     * @return list of virus-infected file results
     */
    public List<BulkUploadItemResult> getVirusDetectedFiles() {
        return itemResults.stream()
                .filter(BulkUploadItemResult::isVirusDetected)
                .collect(Collectors.toList());
    }
    
    /**
     * Gets all results where virus scanning failed.
     * 
     * @return list of scan error results
     */
    public List<BulkUploadItemResult> getScanErrorFiles() {
        return itemResults.stream()
                .filter(BulkUploadItemResult::isScanError)
                .collect(Collectors.toList());
    }
    
    /**
     * Checks if any viruses were detected during the operation.
     * 
     * @return true if viruses were detected, false otherwise
     */
    public boolean hasVirusDetections() {
        return virusDetectedCount != null && virusDetectedCount > 0;
    }
    
    /**
     * Checks if any scan errors occurred during the operation.
     * 
     * @return true if scan errors occurred, false otherwise
     */
    public boolean hasScanErrors() {
        return scanErrorCount != null && scanErrorCount > 0;
    }
    
    /**
     * Builds a summary message describing the operation outcome.
     * 
     * @param totalFiles total number of files
     * @param successCount number of successful uploads
     * @param failureCount number of failed uploads
     * @param virusDetectedCount number of virus detections
     * @param scanErrorCount number of scan errors
     * @return summary message
     */
    private static String buildSummaryMessage(int totalFiles, int successCount, int failureCount,
                                            int virusDetectedCount, int scanErrorCount) {
        StringBuilder message = new StringBuilder();
        
        message.append(String.format("Bulk upload completed: %d/%d files uploaded successfully", 
                                    successCount, totalFiles));
        
        if (virusDetectedCount > 0) {
            message.append(String.format(", %d files infected and skipped", virusDetectedCount));
        }
        
        if (scanErrorCount > 0) {
            message.append(String.format(", %d files had scan errors", scanErrorCount));
        }
        
        if (failureCount > virusDetectedCount + scanErrorCount) {
            int otherErrors = failureCount - virusDetectedCount - scanErrorCount;
            message.append(String.format(", %d files failed for other reasons", otherErrors));
        }
        
        return message.toString();
    }
}
