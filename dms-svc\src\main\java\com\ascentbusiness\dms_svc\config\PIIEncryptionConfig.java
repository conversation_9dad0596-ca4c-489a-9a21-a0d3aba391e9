package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.service.PIIEncryptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * Configuration for PII field-level encryption
 */
@Configuration
public class PIIEncryptionConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(PIIEncryptionConfig.class);
    
    @Autowired
    private PIIEncryptionService piiEncryptionService;
    
    /**
     * Initialize PII encryption service when application is ready
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializePIIEncryption() {
        try {
            piiEncryptionService.initialize();
            logger.info("PII encryption service initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize PII encryption service", e);
        }
    }
    
    /**
     * Scheduled task to clean up old PII encryption keys
     * Runs weekly on Sunday at 3 AM
     */
    @Scheduled(cron = "0 0 3 * * SUN")
    public void cleanupOldPIIEncryptionKeys() {
        try {
            // Keep keys for 90 days for decryption purposes (longer than audit keys)
            long ninetyDaysInMillis = 90L * 24L * 60L * 60L * 1000L;
            // Note: PIIEncryptionService doesn't have clearOldKeys method yet
            // This would need to be implemented similar to AuditEncryptionService
            
            logger.info("Completed cleanup of old PII encryption keys");
        } catch (Exception e) {
            logger.error("Failed to cleanup old PII encryption keys", e);
        }
    }
}
