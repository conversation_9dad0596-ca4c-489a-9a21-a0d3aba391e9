package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.enums.ProcessingStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Result DTO for document upload operations.
 * Corresponds to DocumentUploadResult GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentUploadResult {

    /**
     * Whether the upload was successful.
     */
    private Boolean success;

    /**
     * The uploaded document (if successful).
     */
    private Document document;

    /**
     * Unique identifier for the upload operation.
     */
    private String uploadId;

    /**
     * Human-readable message about the upload result.
     */
    private String message;

    /**
     * Name of the uploaded file.
     */
    private String fileName;

    /**
     * Size of the uploaded file in bytes.
     */
    private Long fileSize;

    /**
     * Current processing status of the upload.
     */
    private ProcessingStatus processingStatus;

    /**
     * URL to check the status of the upload (optional).
     */
    private String statusCheckUrl;

    /**
     * List of warnings encountered during upload (optional).
     */
    private List<String> warnings;

    /**
     * Create a successful upload result.
     */
    public static DocumentUploadResult success(Document document, String uploadId, String fileName, Long fileSize) {
        return DocumentUploadResult.builder()
                .success(true)
                .document(document)
                .uploadId(uploadId)
                .fileName(fileName)
                .fileSize(fileSize)
                .processingStatus(ProcessingStatus.COMPLETED)
                .build();
    }

    /**
     * Create a failed upload result.
     */
    public static DocumentUploadResult failure(String uploadId, String fileName, Long fileSize, ProcessingStatus status) {
        return DocumentUploadResult.builder()
                .success(false)
                .uploadId(uploadId)
                .fileName(fileName)
                .fileSize(fileSize)
                .processingStatus(status != null ? status : ProcessingStatus.FAILED)
                .build();
    }
}
