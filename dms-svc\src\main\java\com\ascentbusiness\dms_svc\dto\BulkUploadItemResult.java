package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.enums.VirusScanResult;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Result DTO for individual file in a bulk upload operation.
 * 
 * <p>This DTO represents the outcome of uploading a single file within
 * a bulk upload operation, including virus scan results, upload status,
 * and any error information.
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkUploadItemResult {
    
    /**
     * Original filename of the uploaded file.
     */
    private String fileName;
    
    /**
     * Size of the file in bytes.
     */
    private Long fileSize;
    
    /**
     * Index of this file in the bulk upload batch.
     */
    private Integer fileIndex;
    
    /**
     * Whether the upload was successful.
     */
    private Boolean successful;
    
    /**
     * The uploaded document (if successful).
     */
    private Document document;
    
    /**
     * Error message (if upload failed).
     */
    private String errorMessage;
    
    /**
     * Error code for categorizing failures.
     */
    private String errorCode;
    
    /**
     * Virus scan result for this file.
     */
    private VirusScanResult virusScanResult;
    
    /**
     * Type of scanner used for virus scanning.
     */
    private VirusScannerType scannerType;
    
    /**
     * List of detected threats (if any).
     */
    private List<String> detectedThreats;
    
    /**
     * Virus scan duration in milliseconds.
     */
    private Long scanDurationMs;
    
    /**
     * Unique scan ID for correlation.
     */
    private String scanId;
    
    /**
     * Upload processing start time.
     */
    private LocalDateTime processStartTime;
    
    /**
     * Upload processing end time.
     */
    private LocalDateTime processEndTime;
    
    /**
     * Total processing duration in milliseconds.
     */
    private Long processingDurationMs;
    
    /**
     * Creates a successful upload result.
     * 
     * @param fileName the file name
     * @param fileSize the file size
     * @param fileIndex the file index
     * @param document the uploaded document
     * @param virusScanResult the virus scan result
     * @param scannerType the scanner type used
     * @param scanId the scan ID
     * @return successful upload result
     */
    public static BulkUploadItemResult success(String fileName, Long fileSize, Integer fileIndex,
                                             Document document, VirusScanResult virusScanResult,
                                             VirusScannerType scannerType, String scanId) {
        return BulkUploadItemResult.builder()
                .fileName(fileName)
                .fileSize(fileSize)
                .fileIndex(fileIndex)
                .successful(true)
                .document(document)
                .virusScanResult(virusScanResult)
                .scannerType(scannerType)
                .scanId(scanId)
                .detectedThreats(List.of())
                .processEndTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * Creates a failed upload result due to virus detection.
     * 
     * @param fileName the file name
     * @param fileSize the file size
     * @param fileIndex the file index
     * @param virusScanResult the virus scan result
     * @param scannerType the scanner type used
     * @param detectedThreats the detected threats
     * @param scanId the scan ID
     * @return failed upload result due to virus
     */
    public static BulkUploadItemResult virusDetected(String fileName, Long fileSize, Integer fileIndex,
                                                   VirusScanResult virusScanResult, VirusScannerType scannerType,
                                                   List<String> detectedThreats, String scanId) {
        String errorMessage = String.format("File '%s' is infected with: %s", 
                fileName, String.join(", ", detectedThreats));
        
        return BulkUploadItemResult.builder()
                .fileName(fileName)
                .fileSize(fileSize)
                .fileIndex(fileIndex)
                .successful(false)
                .errorMessage(errorMessage)
                .errorCode("VIRUS_DETECTED")
                .virusScanResult(virusScanResult)
                .scannerType(scannerType)
                .detectedThreats(detectedThreats)
                .scanId(scanId)
                .processEndTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * Creates a failed upload result due to scan error.
     * 
     * @param fileName the file name
     * @param fileSize the file size
     * @param fileIndex the file index
     * @param errorMessage the error message
     * @param scannerType the scanner type used
     * @param scanId the scan ID
     * @return failed upload result due to scan error
     */
    public static BulkUploadItemResult scanError(String fileName, Long fileSize, Integer fileIndex,
                                               String errorMessage, VirusScannerType scannerType, String scanId) {
        return BulkUploadItemResult.builder()
                .fileName(fileName)
                .fileSize(fileSize)
                .fileIndex(fileIndex)
                .successful(false)
                .errorMessage(errorMessage)
                .errorCode("SCAN_ERROR")
                .virusScanResult(VirusScanResult.ERROR)
                .scannerType(scannerType)
                .detectedThreats(List.of())
                .scanId(scanId)
                .processEndTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * Creates a failed upload result due to general error.
     * 
     * @param fileName the file name
     * @param fileSize the file size
     * @param fileIndex the file index
     * @param errorMessage the error message
     * @param errorCode the error code
     * @return failed upload result due to general error
     */
    public static BulkUploadItemResult error(String fileName, Long fileSize, Integer fileIndex,
                                           String errorMessage, String errorCode) {
        return BulkUploadItemResult.builder()
                .fileName(fileName)
                .fileSize(fileSize)
                .fileIndex(fileIndex)
                .successful(false)
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .processEndTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * Checks if this result represents a virus detection.
     * 
     * @return true if virus was detected, false otherwise
     */
    public boolean isVirusDetected() {
        return virusScanResult == VirusScanResult.INFECTED || 
               virusScanResult == VirusScanResult.SUSPICIOUS;
    }
    
    /**
     * Checks if this result represents a scan error.
     * 
     * @return true if scan failed, false otherwise
     */
    public boolean isScanError() {
        return virusScanResult == VirusScanResult.ERROR || 
               virusScanResult == VirusScanResult.UNAVAILABLE || 
               virusScanResult == VirusScanResult.TIMEOUT;
    }
    
    /**
     * Gets the number of detected threats.
     * 
     * @return the threat count
     */
    public int getThreatCount() {
        return detectedThreats != null ? detectedThreats.size() : 0;
    }
}
