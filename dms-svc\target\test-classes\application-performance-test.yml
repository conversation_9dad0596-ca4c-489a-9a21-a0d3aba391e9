# Performance test configuration
spring:
  
  # Test database configuration - using H2 for performance tests to avoid lock contention
  datasource:
    url: jdbc:h2:mem:dms_test_performance;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password:

    # Enhanced connection pool for performance tests
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  # JPA configuration for performance tests
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true

  # Liquibase configuration for performance tests
  liquibase:
    enabled: false
    
  # Enhanced async configuration for performance tests
  task:
    execution:
      pool:
        core-size: 20
        max-size: 50
        queue-capacity: 1000
        keep-alive: 60s
        thread-name-prefix: perf-test-
        
    scheduling:
      pool:
        size: 10
        
# DMS specific performance test configuration
dms:
  audit:
    # Export configuration for performance tests
    export:
      base-path: ${java.io.tmpdir}/audit-exports-perf-test
      max-file-size: 100MB
      retention-days: 1
      
    # Verification configuration for performance tests
    verification:
      batch-size: 100
      scheduled-interval: 300000 # 5 minutes for performance tests
      
    # Chain configuration for performance tests
    chain:
      hash-algorithm: SHA-256
      signature-algorithm: RSA-2048
      
# Security configuration for performance tests
security:
  jwt:
    secret: performance-test-secret-key-for-testing-only
    expiration: 3600000 # 1 hour
    
# Logging configuration for performance tests
logging:
  level:
    com.ascentbusiness.dms_svc: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: ERROR
    org.hibernate.type.descriptor.sql.BasicBinder: ERROR
    org.springframework.transaction: ERROR
    
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    
# Performance test specific properties
test:
  performance:
    # Thread pool configuration
    max-concurrent-threads: 50
    
    # Database operation thresholds
    min-operations-per-second: 100
    max-operation-timeout: 30000
    
    # Progress update thresholds
    min-progress-updates-per-second: 50
    max-progress-update-timeout: 30000
    
    # Memory thresholds
    max-memory-increase-mb: 200
    
  audit:
    # Performance test thresholds
    performance:
      min-logs-per-second: 100
      min-verifications-per-second: 50
      min-export-records-per-second: 100
      max-memory-increase-mb: 200
      max-concurrent-threads: 50
      
    # Security test configuration
    security:
      rate-limit:
        requests-per-minute: 100
        
    # Integration test configuration
    integration:
      async-wait-timeout: 30000 # 30 seconds
      chain-verification-timeout: 60000 # 60 seconds
      
# Management endpoints for performance test monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,threaddump,heapdump
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
        
# Test data configuration for performance tests
testdata:
  performance:
    # Large dataset configuration
    documents:
      count: 1000
      size-range: 1KB-10MB
      
    jobs:
      concurrent-limit: 50
      batch-size: 100
      
    progress-updates:
      frequency: 100ms
      batch-size: 50
