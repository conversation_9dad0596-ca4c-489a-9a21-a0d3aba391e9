# Prometheus Configuration for Notification Service Only
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # Notification Service Metrics
  - job_name: 'notification-svc'
    static_configs:
      - targets: ['notification-svc:9091']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # MySQL Metrics
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql:3306']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    scrape_timeout: 10s

  # RabbitMQ Metrics
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
