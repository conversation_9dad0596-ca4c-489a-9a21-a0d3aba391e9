#!/bin/bash

# DMS Service Only - Docker Management Script
# This script helps manage the DMS Service standalone deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

COMPOSE_FILE="docker-compose.dms-only.yml"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
}

# Function to start all services
start_all() {
    print_header "Starting DMS Service - Standalone Deployment"
    check_docker
    check_docker_compose
    
    print_status "Starting infrastructure services first..."
    docker-compose -f $COMPOSE_FILE up -d mysql redis elasticsearch
    
    print_status "Waiting for infrastructure services to be ready..."
    sleep 30
    
    print_status "Starting DMS service..."
    docker-compose -f $COMPOSE_FILE up -d dms-svc
    
    print_status "Starting monitoring services (optional)..."
    docker-compose -f $COMPOSE_FILE up -d prometheus grafana zipkin
    
    print_status "DMS service started successfully!"
    show_status
}

# Function to start only infrastructure services
start_infra() {
    print_header "Starting Infrastructure Services Only"
    check_docker
    check_docker_compose
    
    docker-compose -f $COMPOSE_FILE up -d mysql redis elasticsearch
    print_status "Infrastructure services started!"
    show_status
}

# Function to show service status
show_status() {
    print_header "DMS Service Status"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    print_header "Service URLs"
    echo "Application Service:"
    echo "  DMS Service GraphQL:        http://localhost:9093/dms/graphql"
    echo "  DMS Service GraphiQL:       http://localhost:9093/graphiql"
    echo ""
    echo "Infrastructure Services:"
    echo "  MySQL:                      localhost:3306 (root/root_password)"
    echo "  Redis:                      localhost:6379 (shared_redis_password)"
    echo "  Elasticsearch:              http://localhost:9200"
    echo ""
    echo "Monitoring Services:"
    echo "  Grafana:                    http://localhost:3000 (admin/admin)"
    echo "  Prometheus:                 http://localhost:9090"
    echo "  Zipkin:                     http://localhost:9411"
}

# Function to show logs
show_logs() {
    if [ -z "$2" ]; then
        print_header "Showing All Service Logs"
        docker-compose -f $COMPOSE_FILE logs -f
    else
        print_header "Showing Logs for: $2"
        docker-compose -f $COMPOSE_FILE logs -f "$2"
    fi
}

# Function to stop all services
stop_all() {
    print_header "Stopping All Services"
    docker-compose -f $COMPOSE_FILE down
    print_status "All services stopped!"
}

# Function to restart services
restart_service() {
    if [ -z "$2" ]; then
        print_header "Restarting All Services"
        docker-compose -f $COMPOSE_FILE restart
    else
        print_header "Restarting Service: $2"
        docker-compose -f $COMPOSE_FILE restart "$2"
    fi
    print_status "Restart completed!"
}

# Function to clean up everything
cleanup() {
    print_warning "This will stop all services and remove all data!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_header "Cleaning Up Everything"
        docker-compose -f $COMPOSE_FILE down -v --rmi all
        docker system prune -f
        print_status "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show health status
health_check() {
    print_header "Health Check"
    
    echo "Checking DMS Service..."
    if curl -f http://localhost:9093/actuator/health > /dev/null 2>&1; then
        print_status "DMS Service: HEALTHY"
    else
        print_error "DMS Service: UNHEALTHY"
    fi
    
    echo "Checking MySQL..."
    if docker-compose -f $COMPOSE_FILE exec -T mysql mysqladmin ping -h localhost -u root -proot_password > /dev/null 2>&1; then
        print_status "MySQL: HEALTHY"
    else
        print_error "MySQL: UNHEALTHY"
    fi
    
    echo "Checking Redis..."
    if docker-compose -f $COMPOSE_FILE exec -T redis redis-cli -a shared_redis_password ping > /dev/null 2>&1; then
        print_status "Redis: HEALTHY"
    else
        print_error "Redis: UNHEALTHY"
    fi
    
    echo "Checking Elasticsearch..."
    if curl -f http://localhost:9200/_cluster/health > /dev/null 2>&1; then
        print_status "Elasticsearch: HEALTHY"
    else
        print_error "Elasticsearch: UNHEALTHY"
    fi
}

# Function to show help
show_help() {
    echo "DMS Service Only - Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start-all          Start all services (infrastructure + dms + monitoring)"
    echo "  start-infra        Start only infrastructure services (mysql, redis, elasticsearch)"
    echo "  status             Show service status and URLs"
    echo "  logs [SERVICE]     Show logs for all services or specific service"
    echo "  stop               Stop all services"
    echo "  restart [SERVICE]  Restart all services or specific service"
    echo "  health             Check health status of all services"
    echo "  cleanup            Stop all services and remove all data (WARNING: destructive)"
    echo "  help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start-all                 # Start everything"
    echo "  $0 logs dms-svc              # Show DMS service logs"
    echo "  $0 restart elasticsearch     # Restart Elasticsearch"
    echo "  $0 health                    # Check service health"
}

# Main script logic
case "$1" in
    "start-all")
        start_all
        ;;
    "start-infra")
        start_infra
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs "$@"
        ;;
    "stop")
        stop_all
        ;;
    "restart")
        restart_service "$@"
        ;;
    "health")
        health_check
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        print_error "No command specified. Use '$0 help' for usage information."
        exit 1
        ;;
    *)
        print_error "Unknown command: $1. Use '$0 help' for usage information."
        exit 1
        ;;
esac
