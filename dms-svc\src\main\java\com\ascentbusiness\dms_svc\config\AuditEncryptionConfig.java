package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.service.AuditEncryptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * Configuration for audit log encryption
 */
@Configuration
public class AuditEncryptionConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(AuditEncryptionConfig.class);
    
    @Autowired
    private AuditEncryptionService auditEncryptionService;
    
    /**
     * Initialize audit encryption service when application is ready
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializeAuditEncryption() {
        try {
            auditEncryptionService.initialize();
            logger.info("Audit encryption service initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize audit encryption service", e);
        }
    }
    
    /**
     * Scheduled task to clean up old encryption keys
     * Runs daily at 2 AM
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldEncryptionKeys() {
        try {
            // Keep keys for 30 days for decryption purposes
            long thirtyDaysInMillis = 30L * 24L * 60L * 60L * 1000L;
            auditEncryptionService.clearOldKeys(thirtyDaysInMillis);
            
            logger.info("Completed cleanup of old audit encryption keys");
        } catch (Exception e) {
            logger.error("Failed to cleanup old audit encryption keys", e);
        }
    }
}
