# Ingress for DMS Service
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dms-ingress
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: ingress
    app.kubernetes.io/part-of: dms-system
  annotations:
    # Nginx Ingress Controller annotations
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "DENY" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # CORS configuration
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    
    # Connection and timeout settings
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    
    # Load balancing
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    
    # Certificate management (cert-manager)
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
spec:
  tls:
  - hosts:
    - dms.company.com
    - api.dms.company.com
    secretName: dms-tls-secret
  rules:
  - host: dms.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: dms-service
            port:
              number: 8080
  - host: api.dms.company.com
    http:
      paths:
      - path: /dms
        pathType: Prefix
        backend:
          service:
            name: dms-service
            port:
              number: 8080

---
# Ingress for Grafana Monitoring Dashboard
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: grafana-ingress
  namespace: dms-system
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: ingress
    app.kubernetes.io/part-of: dms-system
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # Authentication (basic auth or OAuth)
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: grafana-auth
    nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Grafana"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Certificate management
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
spec:
  tls:
  - hosts:
    - monitoring.dms.company.com
    secretName: grafana-tls-secret
  rules:
  - host: monitoring.dms.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grafana-service
            port:
              number: 3000

---
# Ingress for Prometheus (restricted access)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: prometheus-ingress
  namespace: dms-system
  labels:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: ingress
    app.kubernetes.io/part-of: dms-system
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # IP whitelist for security
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    
    # Authentication
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: prometheus-auth
    nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Prometheus"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "DENY" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Certificate management
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
spec:
  tls:
  - hosts:
    - prometheus.dms.company.com
    secretName: prometheus-tls-secret
  rules:
  - host: prometheus.dms.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: prometheus-service
            port:
              number: 9090

---
# Network Policy for Ingress Traffic
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: dms-ingress-netpol
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: network-policy
    app.kubernetes.io/part-of: dms-system
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: dms
      app.kubernetes.io/component: application
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: mysql
    ports:
    - protocol: TCP
      port: 3306
  - to:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: elasticsearch
    ports:
    - protocol: TCP
      port: 9200
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
