-- MySQL Initialization Script for Local Development
-- This script creates databases and users for both DMS and Notification services in local development

-- Create databases
CREATE DATABASE IF NOT EXISTS `dms_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `notification_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create users and grant permissions
-- DMS Service User
CREATE USER IF NOT EXISTS 'dms_user'@'%' IDENTIFIED BY 'dms_password';
GRANT ALL PRIVILEGES ON `dms_db`.* TO 'dms_user'@'%';

-- Notification Service User
CREATE USER IF NOT EXISTS 'notification_user'@'%' IDENTIFIED BY 'notification_password';
GRANT ALL PRIVILEGES ON `notification_db`.* TO 'notification_user'@'%';

-- Grant cross-service access for local development and testing
-- <PERSON><PERSON> can read from notification tables
GRANT SELECT ON `notification_db`.* TO 'dms_user'@'%';

-- Notification service can read from DMS
GRANT SELECT ON `dms_db`.* TO 'notification_user'@'%';

-- Create a development user with full access to both databases
CREATE USER IF NOT EXISTS 'dev_user'@'%' IDENTIFIED BY 'dev_password';
GRANT ALL PRIVILEGES ON `dms_db`.* TO 'dev_user'@'%';
GRANT ALL PRIVILEGES ON `notification_db`.* TO 'dev_user'@'%';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Display created databases
SHOW DATABASES;

-- Display users
SELECT User, Host FROM mysql.user WHERE User IN ('dms_user', 'notification_user', 'dev_user');

-- Insert some sample data for local development (optional)
USE `dms_db`;
-- Sample data will be inserted by application on startup

USE `notification_db`;
-- Sample data will be inserted by application on startup
