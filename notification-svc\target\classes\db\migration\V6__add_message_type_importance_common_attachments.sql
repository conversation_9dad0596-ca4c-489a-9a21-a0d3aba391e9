-- Add message type, importance, and common attachments columns to notifications table
ALTER TABLE notifications 
ADD COLUMN message_type VARCHAR(50),
ADD COLUMN importance VARCHAR(20),
ADD COLUMN common_attachments TEXT;

-- Add comments for clarity
COMMENT ON COLUMN notifications.message_type IS 'Type of message: INFOR<PERSON><PERSON><PERSON><PERSON>, ACT<PERSON><PERSON><PERSON>, <PERSON>ERT, REMINDE<PERSON>, PR<PERSON><PERSON>IONAL';
COMMENT ON COLUMN notifications.importance IS 'Importance level: HIGH, MEDIUM, LOW';
COMMENT ON COLUMN notifications.common_attachments IS 'JSON string of common attachment filenames sent to all recipients';
