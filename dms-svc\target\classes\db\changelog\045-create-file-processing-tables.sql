--liquibase formatted sql

--changeset dms:045-create-file-processing-tables-001
-- Create async_processing_jobs table
CREATE TABLE IF NOT EXISTS async_processing_jobs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    job_id VARCHAR(255) NOT NULL UNIQUE,
    file_name VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    processing_strategy VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'QUEUED',
    progress DECIMAL(5,2) DEFAULT 0.00,
    estimated_time_remaining INT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    document_id BIGINT,
    correlation_id VARCHAR(255),
    created_by VA<PERSON>HA<PERSON>(255) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by <PERSON><PERSON><PERSON><PERSON>(255),
    last_modified_date TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign key constraints
    CONSTRAINT fk_async_jobs_document FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL,

    -- Indexes for performance
    INDEX idx_async_jobs_job_id (job_id),
    INDEX idx_async_jobs_status (status),
    INDEX idx_async_jobs_created_by (created_by),
    INDEX idx_async_jobs_correlation_id (correlation_id),
    INDEX idx_async_jobs_created_date (created_date),
    INDEX idx_async_jobs_status_created (status, created_date)
);

--changeset dms:045-create-file-processing-tables-002
-- Create chunked_upload_sessions table
CREATE TABLE IF NOT EXISTS chunked_upload_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    file_name VARCHAR(500) NOT NULL,
    total_size BIGINT NOT NULL,
    chunk_size INT NOT NULL,
    total_chunks INT NOT NULL,
    received_chunks INT DEFAULT 0,
    received_bytes BIGINT DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    progress DECIMAL(5,2) DEFAULT 0.00,
    temp_directory VARCHAR(1000),
    document_id BIGINT,
    correlation_id VARCHAR(255),
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    error_message TEXT,

    -- Foreign key constraints
    CONSTRAINT fk_chunk_sessions_document FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL,

    -- Indexes for performance
    INDEX idx_chunk_sessions_session_id (session_id),
    INDEX idx_chunk_sessions_status (status),
    INDEX idx_chunk_sessions_created_by (created_by),
    INDEX idx_chunk_sessions_correlation_id (correlation_id),
    INDEX idx_chunk_sessions_expires_at (expires_at),
    INDEX idx_chunk_sessions_status_expires (status, expires_at)
);

--changeset dms:045-create-file-processing-tables-003
-- Create chunked_upload_chunks table for tracking individual chunks
CREATE TABLE IF NOT EXISTS chunked_upload_chunks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    chunk_number INT NOT NULL,
    chunk_size BIGINT NOT NULL,
    checksum VARCHAR(255),
    temp_file_path VARCHAR(1000),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Foreign key constraints
    CONSTRAINT fk_chunks_session FOREIGN KEY (session_id) REFERENCES chunked_upload_sessions(session_id) ON DELETE CASCADE,

    -- Unique constraint to prevent duplicate chunks
    UNIQUE KEY uk_session_chunk (session_id, chunk_number),

    -- Indexes for performance
    INDEX idx_chunks_session_id (session_id),
    INDEX idx_chunks_session_chunk (session_id, chunk_number),
    INDEX idx_chunks_uploaded_at (uploaded_at)
);

--changeset dms:045-create-file-processing-tables-004
-- Create file_processing_temp_files table for cleanup tracking
CREATE TABLE IF NOT EXISTS file_processing_temp_files (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT,
    associated_job_id VARCHAR(255),
    associated_session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    cleanup_attempted BOOLEAN DEFAULT FALSE,
    cleanup_successful BOOLEAN DEFAULT FALSE,
    cleanup_error TEXT,

    -- Indexes for cleanup operations
    INDEX idx_temp_files_expires_at (expires_at),
    INDEX idx_temp_files_cleanup (cleanup_attempted, expires_at),
    INDEX idx_temp_files_job_id (associated_job_id),
    INDEX idx_temp_files_session_id (associated_session_id)
);

--changeset dms:045-create-file-processing-tables-005
-- Add comments for documentation
ALTER TABLE async_processing_jobs COMMENT = 'Tracks asynchronous document processing jobs with status and progress';

--changeset dms:045-create-file-processing-tables-006
ALTER TABLE chunked_upload_sessions COMMENT = 'Manages chunked upload sessions for large files';

--changeset dms:045-create-file-processing-tables-007
ALTER TABLE chunked_upload_chunks COMMENT = 'Tracks individual chunks within upload sessions';

--changeset dms:045-create-file-processing-tables-008
ALTER TABLE file_processing_temp_files COMMENT = 'Tracks temporary files for cleanup operations';
