package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.DocumentStatus;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Input DTO for advanced Elasticsearch-powered document search.
 * 
 * Supports complex search queries with multiple filters, faceted search,
 * and security-aware filtering based on user permissions.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdvancedSearchInput {

    // Text search parameters
    private String query;
    @Builder.Default
    private SearchType searchType = SearchType.MULTI_FIELD;
    @Builder.Default
    private Boolean fuzzySearch = false;
    @Builder.Default
    private Boolean phraseSearch = false;

    // Content search
    @Builder.Default
    private Boolean includeContent = true;
    @Builder.Default
    private Boolean contentOnly = false;

    // Basic filters
    private String name;
    private String description;
    private String creator;
    private StorageProvider storageProvider;
    private DocumentStatus status;
    private List<String> mimeTypes;

    // Date range filters
    private LocalDateTime createdDateFrom;
    private LocalDateTime createdDateTo;
    private LocalDateTime modifiedDateFrom;
    private LocalDateTime modifiedDateTo;
    private LocalDateTime expiryDateFrom;
    private LocalDateTime expiryDateTo;

    // Metadata filters
    private String module;
    private String subModule;
    private String businessUnit;
    private String regionLocation;
    private String documentType;
    private String language;
    private String confidentialityLevel;

    // Ownership filters
    private String owner;
    private String approver;
    private String ownershipStatus;

    // Compliance filters
    private String complianceStandard;
    private String auditRelevance;
    private String controlId;
    private String thirdPartyId;
    private String policyId;

    // Tags and keywords
    private List<String> tags;
    private List<String> keywords;

    // Version filters
    @Builder.Default
    private Boolean includeHistoricalVersions = false;
    @Builder.Default
    private Boolean currentVersionOnly = true;

    // File size filters
    private Long minFileSize;
    private Long maxFileSize;

    // Security filters
    @Builder.Default
    private Boolean includeConfidential = false;

    /**
     * Check if this is a simple text search
     */
    public boolean isSimpleTextSearch() {
        return query != null && !query.trim().isEmpty() && 
               searchType == SearchType.MULTI_FIELD &&
               !hasAdvancedFilters();
    }

    /**
     * Check if advanced filters are applied
     */
    public boolean hasAdvancedFilters() {
        return name != null || description != null || creator != null ||
               storageProvider != null || status != null ||
               (mimeTypes != null && !mimeTypes.isEmpty()) ||
               createdDateFrom != null || createdDateTo != null ||
               modifiedDateFrom != null || modifiedDateTo != null ||
               module != null || businessUnit != null ||
               documentType != null || confidentialityLevel != null ||
               owner != null || approver != null ||
               complianceStandard != null || controlId != null ||
               (tags != null && !tags.isEmpty()) ||
               (keywords != null && !keywords.isEmpty()) ||
               minFileSize != null || maxFileSize != null;
    }

    /**
     * Check if content search is enabled
     */
    public boolean shouldSearchContent() {
        return includeContent != null && includeContent;
    }

    /**
     * Check if only content should be searched
     */
    public boolean isContentOnlySearch() {
        return contentOnly != null && contentOnly;
    }

    /**
     * Check if fuzzy search is enabled
     */
    public boolean isFuzzySearchEnabled() {
        return fuzzySearch != null && fuzzySearch;
    }

    /**
     * Check if phrase search is enabled
     */
    public boolean isPhraseSearchEnabled() {
        return phraseSearch != null && phraseSearch;
    }

    /**
     * Check if historical versions should be included
     */
    public boolean shouldIncludeHistoricalVersions() {
        return includeHistoricalVersions != null && includeHistoricalVersions;
    }

    /**
     * Check if only current versions should be returned
     */
    public boolean isCurrentVersionOnly() {
        return currentVersionOnly == null || currentVersionOnly;
    }

    /**
     * Check if confidential documents should be included
     */
    public boolean shouldIncludeConfidential() {
        return includeConfidential != null && includeConfidential;
    }

    /**
     * Get effective search type
     */
    public SearchType getEffectiveSearchType() {
        if (searchType == null) {
            return SearchType.MULTI_FIELD;
        }
        
        // Override search type based on other parameters
        if (isPhraseSearchEnabled()) {
            return SearchType.PHRASE;
        }
        
        if (isFuzzySearchEnabled()) {
            return SearchType.FUZZY;
        }
        
        if (isContentOnlySearch()) {
            return SearchType.CONTENT_ONLY;
        }
        
        return searchType;
    }
}
