package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for audit statistics
 * Corresponds to AuditStatistics type in audit-schema.graphqls
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditStatistics {
    
    private Long totalRecords;
    private Long recordsToday;
    private Long recordsThisWeek;
    private Long recordsThisMonth;
    private List<ActionCount> actionStatistics;
    private List<UserActivityCount> userStatistics;
    private List<HourlyActivityCount> hourlyActivity;
    private List<DocumentActivityCount> topDocuments;
    private Long securityEvents;
    private Long complianceEvents;
}
