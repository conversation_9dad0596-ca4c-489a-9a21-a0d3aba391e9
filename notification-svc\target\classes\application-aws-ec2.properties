# AWS EC2 Production Configuration for Notification Service
# This file contains AWS EC2-specific settings for production deployment

# Server Configuration
server.port=9091

# Database Configuration - AWS EC2
spring.datasource.url=*****************************************************************************************************************************************************************************************************************
spring.datasource.username=notification_user
spring.datasource.password=notification_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration for Production
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA Configuration - Production
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

# Performance Settings
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Flyway Configuration
spring.flyway.enabled=false
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration

# GraphQL Configuration - AWS EC2 Production
spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:false}
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.printer.enabled=false
spring.graphql.path=/graphql
spring.graphql.graphiql.path=/graphiql

# CORS Configuration - AWS EC2 Production
# CRITICAL: Specify exact origins for production - DO NOT use wildcards with credentials
spring.graphql.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID,Accept
spring.graphql.cors.allow-credentials=true
spring.graphql.cors.max-age=3600

# RabbitMQ Configuration - AWS EC2
spring.rabbitmq.host=rabbitmq
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin123
spring.rabbitmq.virtual-host=/

# RabbitMQ Connection Pool Configuration
spring.rabbitmq.connection-timeout=30000
spring.rabbitmq.requested-heartbeat=60
spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true

# Redis Configuration - AWS EC2
spring.redis.host=redis
spring.redis.port=6379
spring.redis.password=shared_redis_password
spring.redis.database=0
spring.redis.timeout=5000ms
spring.redis.connect-timeout=3000ms

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=3600
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=notification:

# Redis Connection Pool Configuration (Lettuce)
spring.data.redis.lettuce.pool.max-active=20
spring.data.redis.lettuce.pool.max-idle=10
spring.data.redis.lettuce.pool.min-idle=2
spring.data.redis.lettuce.pool.max-wait=2000ms
spring.data.redis.lettuce.pool.time-between-eviction-runs=30s

# Email Configuration - AWS EC2 Production
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=*

# Notification Configuration
notification.email.from=${NOTIFICATION_FROM_EMAIL:<EMAIL>}
notification.email.enabled=${EMAIL_ENABLED:true}
notification.email.mock=${EMAIL_MOCK:false}

# Security Configuration - AWS EC2 Production
security.jwt.enabled=true
security.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}
security.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
security.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID,Accept
security.cors.allow-credentials=true
security.cors.max-age=3600

# JWT Configuration
jwt.issuer=notification-service
jwt.audience=notification-clients
jwt.secret=${JWT_SECRET}
jwt.expiration=3600000

# Actuator Configuration - Production
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized
management.endpoint.health.show-components=when-authorized
management.health.defaults.enabled=true

# Health Indicators
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=true
management.health.rabbitmq.enabled=true
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true

# Logging Configuration - Production
logging.level.com.ascentbusiness.notification_svc=INFO
logging.level.org.springframework.web=WARN
logging.level.org.hibernate=WARN
logging.level.org.springframework.security=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Application Configuration
spring.application.name=notification-svc

# Production specific settings
spring.main.allow-bean-definition-overriding=false

# Server Configuration - Production
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024

# Connection timeout settings
server.tomcat.connection-timeout=20000
server.tomcat.max-connections=8192
server.tomcat.threads.max=200
server.tomcat.threads.min-spare=10

# Async Configuration
spring.task.execution.pool.core-size=10
spring.task.execution.pool.max-size=50
spring.task.execution.pool.queue-capacity=100
spring.task.execution.thread-name-prefix=notification-async-

# Notification Processing Configuration
notification.processing.batch-size=100
notification.processing.retry-attempts=3
notification.processing.retry-delay=5000

# Template Configuration
notification.template.cache-enabled=true
notification.template.cache-ttl=3600

# Webhook Configuration
notification.webhook.enabled=true
notification.webhook.timeout=30000
notification.webhook.retry-attempts=3
