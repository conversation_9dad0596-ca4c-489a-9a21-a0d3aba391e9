package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for document activity count statistics
 * Corresponds to DocumentActivityCount type in audit-schema.graphqls
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentActivityCount {
    
    private Long documentId;
    private String documentName;
    private Long activityCount;
    private LocalDateTime lastActivity;
}
