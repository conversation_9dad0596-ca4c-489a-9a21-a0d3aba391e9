package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Entity representing an individual chunk within a chunked upload session.
 * 
 * <p>This entity tracks the details of each uploaded chunk including
 * its position in the sequence, size, checksum for integrity verification,
 * and temporary storage location.
 */
@Entity
@Table(name = "chunked_upload_chunks")
@Data
@EqualsAndHashCode(callSuper = true)
public class ChunkedUploadChunk extends BaseEntity {
    
    /**
     * Reference to the upload session this chunk belongs to.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "session_id", referencedColumnName = "session_id", nullable = false)
    private ChunkedUploadSession session;
    
    /**
     * Session ID for direct reference (denormalized for performance).
     */
    @Column(name = "session_id", insertable = false, updatable = false)
    private String sessionId;
    
    /**
     * Sequential number of this chunk within the upload session.
     * Chunks are numbered starting from 1.
     */
    @Column(name = "chunk_number", nullable = false)
    private Integer chunkNumber;
    
    /**
     * Size of this chunk in bytes.
     */
    @Column(name = "chunk_size", nullable = false)
    private Long chunkSize;
    
    /**
     * Checksum of the chunk content for integrity verification.
     */
    @Column(name = "checksum")
    private String checksum;
    
    /**
     * Path to the temporary file where this chunk is stored.
     */
    @Column(name = "temp_file_path", length = 1000)
    private String tempFilePath;
    
    /**
     * Timestamp when this chunk was uploaded.
     */
    @Column(name = "uploaded_at")
    private LocalDateTime uploadedAt;
    
    /**
     * Set the session and update the session ID.
     * 
     * @param session the upload session
     */
    public void setSession(ChunkedUploadSession session) {
        this.session = session;
        this.sessionId = session != null ? session.getSessionId() : null;
    }
    
    /**
     * Check if this chunk has a valid checksum.
     * 
     * @return true if checksum is present
     */
    @Transient
    public boolean hasChecksum() {
        return checksum != null && !checksum.trim().isEmpty();
    }
    
    /**
     * Check if this chunk has been stored to a temporary file.
     * 
     * @return true if temporary file path is present
     */
    @Transient
    public boolean hasTemporaryFile() {
        return tempFilePath != null && !tempFilePath.trim().isEmpty();
    }
    
    /**
     * Get a display name for this chunk.
     * 
     * @return formatted chunk identifier
     */
    @Transient
    public String getDisplayName() {
        return String.format("Chunk %d/%d", chunkNumber, 
                session != null ? session.getTotalChunks() : "?");
    }
    
    /**
     * Calculate the upload duration for this chunk.
     * 
     * @return duration in milliseconds, or null if not available
     */
    @Transient
    public Long getUploadDurationMillis() {
        if (uploadedAt == null || getCreatedDate() == null) {
            return null;
        }
        
        return java.time.Duration.between(getCreatedDate(), uploadedAt).toMillis();
    }
    
    /**
     * Check if this is the first chunk in the sequence.
     * 
     * @return true if this is chunk number 1
     */
    @Transient
    public boolean isFirstChunk() {
        return chunkNumber != null && chunkNumber == 1;
    }
    
    /**
     * Check if this is the last chunk in the sequence.
     * 
     * @return true if this is the final chunk
     */
    @Transient
    public boolean isLastChunk() {
        return session != null && chunkNumber != null && 
               chunkNumber.equals(session.getTotalChunks());
    }
    
    @PrePersist
    protected void onChunkCreate() {
        if (uploadedAt == null) {
            uploadedAt = LocalDateTime.now();
        }
    }
}
