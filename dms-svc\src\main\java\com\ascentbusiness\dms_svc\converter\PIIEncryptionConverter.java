package com.ascentbusiness.dms_svc.converter;

import com.ascentbusiness.dms_svc.service.PIIEncryptionService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.HashMap;
import java.util.Map;

/**
 * JPA converter for automatic PII encryption/decryption
 * Stores encrypted data as JSON with metadata
 */
@Converter
@Component
public class PIIEncryptionConverter implements AttributeConverter<String, String> {
    
    private static final Logger logger = LoggerFactory.getLogger(PIIEncryptionConverter.class);
    
    @Autowired
    private PIIEncryptionService piiEncryptionService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private String currentFieldName = "unknown"; // Will be set by annotation processor
    
    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (attribute == null || attribute.trim().isEmpty()) {
            return attribute;
        }
        
        try {
            PIIEncryptionService.EncryptedPIIData encryptedData = 
                piiEncryptionService.encryptIfPII(attribute, currentFieldName);
            
            if (!encryptedData.isEncrypted()) {
                return attribute; // Return as-is if not encrypted
            }
            
            // Store as JSON with encryption metadata
            Map<String, Object> encryptionWrapper = new HashMap<>();
            encryptionWrapper.put("encrypted", true);
            encryptionWrapper.put("data", encryptedData.getData());
            encryptionWrapper.put("iv", encryptedData.getIv());
            encryptionWrapper.put("keyId", encryptedData.getKeyId());
            encryptionWrapper.put("fieldName", encryptedData.getFieldName());
            encryptionWrapper.put("version", "1.0");
            
            return objectMapper.writeValueAsString(encryptionWrapper);
            
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize encrypted PII data for field: {}", currentFieldName, e);
            return attribute; // Return original data as fallback
        } catch (Exception e) {
            logger.error("Failed to encrypt PII data for field: {}", currentFieldName, e);
            return attribute; // Return original data as fallback
        }
    }
    
    @Override
    public String convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return dbData;
        }
        
        // Check if data is encrypted (JSON format)
        if (!dbData.trim().startsWith("{")) {
            return dbData; // Return as-is if not JSON (unencrypted)
        }
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> encryptionWrapper = objectMapper.readValue(dbData, Map.class);
            
            Boolean encrypted = (Boolean) encryptionWrapper.get("encrypted");
            if (encrypted == null || !encrypted) {
                return dbData; // Return as-is if not marked as encrypted
            }
            
            String encryptedDataStr = (String) encryptionWrapper.get("data");
            String iv = (String) encryptionWrapper.get("iv");
            String keyId = (String) encryptionWrapper.get("keyId");
            String fieldName = (String) encryptionWrapper.get("fieldName");
            
            PIIEncryptionService.EncryptedPIIData encryptedData = 
                new PIIEncryptionService.EncryptedPIIData(encryptedDataStr, iv, keyId, true, fieldName);
            
            return piiEncryptionService.decrypt(encryptedData);
            
        } catch (JsonProcessingException e) {
            logger.error("Failed to deserialize encrypted PII data: {}", e.getMessage());
            return "[ENCRYPTED_PII_PARSE_ERROR]";
        } catch (Exception e) {
            logger.error("Failed to decrypt PII data: {}", e.getMessage());
            return "[ENCRYPTED_PII_DECRYPT_ERROR]";
        }
    }
    
    /**
     * Set the current field name for context-aware encryption
     */
    public void setCurrentFieldName(String fieldName) {
        this.currentFieldName = fieldName;
    }
}


