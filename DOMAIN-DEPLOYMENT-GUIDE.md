# Domain-Based Deployment Guide for AutoResilience.com

## 🎯 **Your Domain Configuration**

You're deploying with custom domains:
- **DMS Service**: `https://dms-service.autoresilience.com/`
- **Notification Service**: `https://notify-service.autoresilience.com/`

This is **excellent** for production! Here are the required configuration changes:

## ✅ **Configuration Changes Made**

### **1. Updated Environment Configuration**
- Created `.env.autoresilience.com` with your domain-specific settings
- Updated CORS origins for your domain
- Set proper base URLs for both services
- Configured email from address as `<EMAIL>`

### **2. Updated Docker Compose**
- Added `DMS_BASE_URL` environment variable
- Added `NOTIFICATION_SERVICE_URL` environment variable
- Updated CORS configuration for production

### **3. Updated Application Properties**
- Added base URL configuration for DMS service
- Configured proper HTTPS URLs

## 🔧 **Required Infrastructure Setup**

Since you're using custom domains with HTTPS, you'll need:

### **Option 1: AWS Application Load Balancer (Recommended)**

```bash
# 1. Create Application Load Balancer
# 2. Configure Target Groups:
#    - dms-service-tg: Port 9093
#    - notification-service-tg: Port 9091
# 3. Configure Listeners:
#    - HTTPS:443 with SSL certificate
#    - HTTP:80 redirect to HTTPS
# 4. Configure Rules:
#    - dms-service.autoresilience.com → dms-service-tg
#    - notify-service.autoresilience.com → notification-service-tg
```

### **Option 2: Nginx Reverse Proxy**

Create `nginx.conf`:

```nginx
# /etc/nginx/sites-available/autoresilience-services
server {
    listen 80;
    server_name dms-service.autoresilience.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name dms-service.autoresilience.com;
    
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    
    location / {
        proxy_pass http://localhost:9093;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers (if needed)
        add_header Access-Control-Allow-Origin "https://app.autoresilience.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Correlation-ID" always;
        add_header Access-Control-Allow-Credentials "true" always;
    }
}

server {
    listen 80;
    server_name notify-service.autoresilience.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name notify-service.autoresilience.com;
    
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    
    location / {
        proxy_pass http://localhost:9091;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers (if needed)
        add_header Access-Control-Allow-Origin "https://app.autoresilience.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Correlation-ID" always;
        add_header Access-Control-Allow-Credentials "true" always;
    }
}
```

## 🚀 **Deployment Steps**

### **Step 1: Configure Environment**

```bash
# Use the domain-specific environment file
cp .env.autoresilience.com .env

# Edit with your specific settings
nano .env
```

**Critical Settings to Update:**
```bash
# Your frontend domains (replace with actual)
CORS_ALLOWED_ORIGINS=https://app.autoresilience.com,https://admin.autoresilience.com,https://uat.autoresillence.com

# Your EC2 public IP
AWS_EC2_PUBLIC_IP=your-actual-ec2-ip

# Strong passwords
MYSQL_ROOT_PASSWORD=your-strong-password
JWT_SECRET=your-256-bit-jwt-secret
```

### **Step 2: Deploy Services**

```bash
# Deploy with domain configuration
docker-compose -f docker-compose.aws-ec2.yml --env-file .env up -d

# Check status
docker-compose -f docker-compose.aws-ec2.yml ps

# Check logs
docker-compose -f docker-compose.aws-ec2.yml logs -f
```

### **Step 3: Configure DNS**

```bash
# Add DNS records in your domain registrar:
# A Record: dms-service.autoresilience.com → your-ec2-public-ip
# A Record: notify-service.autoresilience.com → your-ec2-public-ip

# Or if using ALB:
# CNAME: dms-service.autoresilience.com → your-alb-dns-name
# CNAME: notify-service.autoresilience.com → your-alb-dns-name
```

### **Step 4: Configure SSL Certificates**

```bash
# Option 1: AWS Certificate Manager (if using ALB)
# - Request certificates for both domains
# - Attach to ALB listeners

# Option 2: Let's Encrypt (if using Nginx)
sudo certbot --nginx -d dms-service.autoresilience.com -d notify-service.autoresilience.com
```

## 🌐 **Frontend Integration**

### **Service URLs**
```javascript
// Your frontend will call these URLs:
const API_CONFIG = {
  DMS_SERVICE: 'https://dms-service.autoresilience.com/dms/graphql',
  NOTIFICATION_SERVICE: 'https://notify-service.autoresilience.com/graphql'
};
```

### **GraphQL Endpoints**
```javascript
// DMS Service
const DMS_GRAPHQL_URL = 'https://dms-service.autoresilience.com/dms/graphql';
const DMS_GRAPHIQL_URL = 'https://dms-service.autoresilience.com/graphiql';

// Notification Service  
const NOTIFICATION_GRAPHQL_URL = 'https://notify-service.autoresilience.com/graphql';
const NOTIFICATION_GRAPHIQL_URL = 'https://notify-service.autoresilience.com/graphiql';
```

### **CORS Configuration**
Your services are configured to accept requests from:
```bash
# Update this in .env file with your actual frontend domains
CORS_ALLOWED_ORIGINS=https://app.autoresilience.com,https://admin.autoresilience.com,https://uat.autoresillence.com
```

## 🔒 **Security Considerations**

### **1. SSL/TLS Configuration**
- ✅ Both services use HTTPS
- ✅ Proper SSL certificates required
- ✅ HTTP redirects to HTTPS

### **2. CORS Security**
- ✅ Specific origins (no wildcards)
- ✅ Credentials enabled
- ✅ Proper headers configured

### **3. Domain Security**
- ✅ Subdomain isolation
- ✅ Proper DNS configuration
- ✅ SSL certificate validation

## 🧪 **Testing Your Configuration**

### **Test Service Availability**
```bash
# Test DMS Service
curl -k https://dms-service.autoresilience.com/actuator/health

# Test Notification Service
curl -k https://notify-service.autoresilience.com/actuator/health
```

### **Test GraphQL Endpoints**
```bash
# Test DMS GraphQL
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query":"{ __schema { types { name } } }"}' \
  https://dms-service.autoresilience.com/dms/graphql

# Test Notification GraphQL
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query":"{ __schema { types { name } } }"}' \
  https://notify-service.autoresilience.com/graphql
```

### **Test CORS**
```javascript
// Test from browser console (on your frontend domain)
fetch('https://dms-service.autoresilience.com/dms/graphql', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({ query: '{ __schema { types { name } } }' })
})
.then(response => response.json())
.then(data => console.log('CORS working:', data))
.catch(error => console.error('CORS error:', error));
```

## ✅ **Configuration Summary**

**Your setup is excellent for production:**

1. ✅ **Custom domains** with HTTPS
2. ✅ **Proper CORS configuration** for your domains
3. ✅ **Base URL configuration** updated
4. ✅ **Email from address** using your domain
5. ✅ **SSL/TLS security** enabled
6. ✅ **Service isolation** with subdomains

**No additional changes needed** in the application code - just ensure:
- DNS records point to your EC2 instance or load balancer
- SSL certificates are properly configured
- CORS origins match your frontend domains

Your domain-based deployment is production-ready! 🚀
