--liquibase formatted sql

--changeset dms:003-add-user-audit-columns

-- Add user audit columns to documents table
ALTER TABLE documents 
ADD COLUMN created_by VARCHAR(100),
ADD COLUMN last_modified_by VARCHAR(100);

-- Add user audit columns to audit_logs table (if it extends BaseEntity in future)
-- Note: audit_logs doesn't extend BaseEntity currently, but adding for consistency
-- ALTER TABLE audit_logs 
-- ADD COLUMN created_by VARCHAR(100),
-- ADD COLUMN last_modified_by VARCHAR(100);

-- Create indexes for the new audit columns
CREATE INDEX idx_documents_created_by ON documents(created_by);
CREATE INDEX idx_documents_last_modified_by ON documents(last_modified_by);

-- Add any other tables that extend BaseEntity
-- (Add similar ALTER statements for other entities that extend BaseEntity)

-- Note: For existing records, these will be NULL initially
-- The application will populate them going forward through JPA auditing
