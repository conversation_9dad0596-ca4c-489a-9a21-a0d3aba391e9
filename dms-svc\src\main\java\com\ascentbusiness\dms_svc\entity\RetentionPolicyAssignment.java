package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.*;

/**
 * Entity representing assignment of retention policies to document types/categories
 */
@Entity
@Table(name = "retention_policy_assignments", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"retention_policy_id", "assignment_type", "assignment_value"}))
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RetentionPolicyAssignment extends BaseEntity {
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "retention_policy_id", nullable = false)
    private RetentionPolicy retentionPolicy;
    
    @Column(name = "assignment_type", nullable = false, length = 50)
    private String assignmentType; // e.g., "DOCUMENT_TYPE", "CATEGORY", "DEPARTMENT", "TAG"
    
    @Column(name = "assignment_value", nullable = false, length = 255)
    private String assignmentValue; // The actual value to match against
    
    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;
    
    @Column(name = "priority", nullable = false)
    @Builder.Default
    private Integer priority = 0; // Higher number = higher priority when multiple policies match
    
    @Column(name = "conditions", columnDefinition = "JSON")
    private String conditions; // Additional JSON conditions for complex matching
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * Check if this assignment matches the given document
     */
    @Transient
    public boolean matches(Document document) {
        if (!isActive) {
            return false;
        }
        
        switch (assignmentType.toUpperCase()) {
            case "DOCUMENT_TYPE":
                return matchesDocumentType(document);
            case "CATEGORY":
                return matchesCategory(document);
            case "DEPARTMENT":
                return matchesDepartment(document);
            case "TAG":
                return matchesTag(document);
            case "MIME_TYPE":
                return matchesMimeType(document);
            case "CREATOR":
                return matchesCreator(document);
            default:
                return false;
        }
    }
    
    private boolean matchesDocumentType(Document document) {
        if (document.getClassificationMetadata() != null) {
            return assignmentValue.equalsIgnoreCase(document.getClassificationMetadata().getDocumentType());
        }
        return false;
    }
    
    private boolean matchesCategory(Document document) {
        if (document.getClassificationMetadata() != null) {
            return assignmentValue.equalsIgnoreCase(document.getClassificationMetadata().getModule()) ||
                   assignmentValue.equalsIgnoreCase(document.getClassificationMetadata().getSubModule());
        }
        return false;
    }
    
    private boolean matchesDepartment(Document document) {
        if (document.getClassificationMetadata() != null) {
            return assignmentValue.equalsIgnoreCase(document.getClassificationMetadata().getBusinessUnit());
        }
        return false;
    }
    
    private boolean matchesTag(Document document) {
        if (document.getTags() != null) {
            return document.getTags().toLowerCase().contains(assignmentValue.toLowerCase());
        }
        return false;
    }
    
    private boolean matchesMimeType(Document document) {
        return assignmentValue.equalsIgnoreCase(document.getMimeType());
    }
    
    private boolean matchesCreator(Document document) {
        return assignmentValue.equalsIgnoreCase(document.getCreatorUserId());
    }
}
