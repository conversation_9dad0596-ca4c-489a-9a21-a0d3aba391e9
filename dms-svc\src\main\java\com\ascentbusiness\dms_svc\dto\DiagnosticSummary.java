package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.HealthStatus;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * Diagnostic summary DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class DiagnosticSummary {
    private HealthStatus overallHealth;
    private Integer criticalIssues;
    private Integer warnings;
    private List<String> recommendations;
    private OffsetDateTime nextCheckRecommended;
}
