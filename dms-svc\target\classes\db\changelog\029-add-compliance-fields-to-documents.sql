-- Liquibase formatted SQL
-- changeset dms:029-add-compliance-fields-to-documents

-- Add compliance-related columns to documents table (only columns not already added by migration 025)
ALTER TABLE documents
ADD COLUMN compliance_classification_level VARCHAR(50),
ADD COLUMN storage_region VARCHAR(50),
ADD COLUMN requires_consent BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN consent_reference VARCHAR(200),
ADD COLUMN is_anonymized BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN is_pseudonymized BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN encryption_status VARCHAR(50),
ADD COLUMN compliance_notes TEXT;

-- Create collection table for document data subject categories
CREATE TABLE document_data_subject_categories (
    document_id BIGINT NOT NULL,
    data_subject_category VARCHAR(50) NOT NULL,
    PRIMARY KEY (document_id, data_subject_category),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- Create collection table for document geographic restrictions
CREATE TABLE document_geographic_restrictions (
    document_id BIGINT NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (document_id, region),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- Add indexes for better performance (only for new columns)
CREATE INDEX idx_documents_compliance_classification ON documents(compliance_classification_level);
CREATE INDEX idx_documents_storage_region ON documents(storage_region);
CREATE INDEX idx_documents_requires_consent ON documents(requires_consent);
CREATE INDEX idx_documents_is_anonymized ON documents(is_anonymized);
CREATE INDEX idx_documents_is_pseudonymized ON documents(is_pseudonymized);
CREATE INDEX idx_documents_encryption_status ON documents(encryption_status);

-- Foreign key constraint for retention policy already exists from migration 025

-- Add comments for documentation
ALTER TABLE documents COMMENT = 'Enhanced documents table with compliance, retention, and legal hold capabilities';
ALTER TABLE document_data_subject_categories COMMENT = 'Maps documents to data subject categories for compliance tracking';
ALTER TABLE document_geographic_restrictions COMMENT = 'Maps documents to geographic access restrictions for compliance';
