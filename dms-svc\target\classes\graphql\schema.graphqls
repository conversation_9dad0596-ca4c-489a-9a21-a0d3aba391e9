scalar DateTime
scalar Long
scalar Upload
scalar JSON
enum StorageProvider {
  LOCAL
  S3
  SHAREPOINT
}

enum VirusScannerType {
  CLAMAV
  SOPHOS
  WINDOWS_DEFENDER
  VIRUS_TOTAL
  MOCK
}

enum DocumentStatus {
  ACTIVE
  HISTORICAL
  DELETED
}

enum Permission {
  READ
  WRITE
  DELETE
  ADMIN
}

enum DispositionAction {
  ARCHIVE
  DELETE
  REVIEW
  EXTEND
  TRANSFER
}

enum LegalHoldStatus {
  NONE
  ACTIVE
  RELEASED
  PENDING_REVIEW
}

enum DispositionStatus {
  ACTIVE
  ELIGIBLE
  PENDING
  DISPOSED
  ON_HOLD
  REVIEW_REQUIRED
  SUSPENDED
}

enum RetentionPeriodUnit {
  DAYS
  MONTHS
  YEARS
}

type Document {
  id: ID!
  name: String!
  version: Int!
  status: DocumentStatus!
  storageProvider: StorageProvider!
  filePath: String!             # Stored physical path on disk or external system
  fileContent: String           # Base64 or similar encoding of the BLOB (optional)
  mimeType: String              # MIME type of the document
  fileSize: Long                # File size in bytes
  templateId: ID                # ID of template used to create this document (if any)
  templateName: String          # Name of template used to create this document (if any)
  createdBy: String!
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  keywords: [String!]
  classificationMetadata: DocumentClassificationMetadata
  ownershipMetadata: DocumentOwnershipMetadata
  complianceMetadata: DocumentComplianceMetadata
  # Retention fields
  retentionPolicy: RetentionPolicy
  retentionExpiryDate: DateTime
  legalHoldStatus: LegalHoldStatus!
  dispositionStatus: DispositionStatus!
  legalHoldReason: String
  legalHoldAppliedDate: DateTime
  legalHoldAppliedBy: String
  dispositionReviewDate: DateTime
  dispositionNotes: String
  isEligibleForDisposition: Boolean!
  isUnderLegalHold: Boolean!
}

type DocumentVersion {
  id: ID!
  version: Int!
  status: DocumentStatus!
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  filePath: String!
}

# Document Metadata Types
type DocumentClassificationMetadata {
  id: ID!
  documentId: ID!
  module: String
  subModule: String
  businessUnit: String
  regionLocation: String
  confidentialityLevel: String
  tagsKeywords: [String!]
  language: String
  documentType: String
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  createdBy: String
  lastModifiedBy: String
}

type DocumentOwnershipMetadata {
  id: ID!
  documentId: ID!
  owner: String
  approver: String
  status: String
  approvalDate: DateTime
  expiryDate: DateTime
  renewalReminder: DateTime
  retentionPeriod: String
  archived: Boolean
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  createdBy: String
  lastModifiedBy: String
}

type DocumentComplianceMetadata {
  id: ID!
  documentId: ID!
  complianceStandard: String
  auditRelevance: String
  linkedRisksControls: String
  controlId: String
  thirdPartyId: String
  policyId: String
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  createdBy: String
  lastModifiedBy: String
}

# Retention Policy Types
type RetentionPolicy {
  id: ID!
  name: String!
  description: String
  scope: String
  retentionPeriod: Int!
  retentionPeriodUnit: RetentionPeriodUnit!
  dispositionAction: DispositionAction!
  isActive: Boolean!
  allowLegalHold: Boolean!
  autoApply: Boolean!
  priority: Int!
  triggerEvent: String
  businessJustification: String
  legalBasis: String
  reviewFrequencyMonths: Int
  notificationBeforeDays: Int
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  createdBy: String
  lastModifiedBy: String
  assignments: [RetentionPolicyAssignment!]
  documentCount: Long
  retentionPeriodDescription: String!
}

type RetentionPolicyAssignment {
  id: ID!
  assignmentType: String!
  assignmentValue: String!
  isActive: Boolean!
  priority: Int!
  conditions: String
  description: String
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  retentionPolicy: RetentionPolicy!
}

type RetentionPolicyPage {
  content: [RetentionPolicy!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}


input UploadDocumentInput {
  name: String!
  description: String
  storageProvider: StorageProvider
  file: Upload!
  keywords: [String!]
  overrideFile: Boolean = false
  scannerType: VirusScannerType
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}

input UploadDocumentFromPathInput {
  name: String!
  description: String
  storageProvider: StorageProvider
  sourceFilePath: String!
  keywords: [String!]
  overrideFile: Boolean = false
  scannerType: VirusScannerType
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}

input UploadNewVersionInput {
  documentId: ID!                                # Required - ID of the existing document to create a new version for
  file: Upload!                                  # Required - the new version file
  name: String                                   # Optional - can inherit from original if not provided
  description: String                            # Optional - can inherit from original if not provided
  storageProvider: StorageProvider               # OPTIONAL - defaults to application.properties setting
  keywords: [String!]                            # OPTIONAL - keywords can be provided or omitted
  overrideFile: Boolean = false                  # NEW - duplicate control (default: false)
  comment: String                                # NEW - optional comment for audit trail
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}

input UploadNewVersionFromPathInput {
  documentId: ID!                                # Required - ID of the existing document to create a new version for
  sourceFilePath: String!                        # Required - Full path on server to be read
  name: String                                   # Optional - can inherit from original if not provided
  description: String                            # Optional - can inherit from original if not provided
  storageProvider: StorageProvider               # OPTIONAL - defaults to application.properties setting
  keywords: [String!]                            # OPTIONAL - keywords can be provided or omitted
  overrideFile: Boolean = false                  # NEW - duplicate control (default: false)
  comment: String                                # NEW - optional comment for audit trail
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}

# Document Metadata Input Types
input DocumentClassificationMetadataInput {
  module: String
  subModule: String
  businessUnit: String
  regionLocation: String
  confidentialityLevel: String
  tagsKeywords: [String!]
  language: String
  documentType: String
}

input DocumentOwnershipMetadataInput {
  owner: String
  approver: String
  status: String
  approvalDate: DateTime
  expiryDate: DateTime
  renewalReminder: DateTime
  retentionPeriod: String
  archived: Boolean
}

input DocumentComplianceMetadataInput {
  complianceStandard: String
  auditRelevance: String
  linkedRisksControls: String
  controlId: String
  thirdPartyId: String
  policyId: String
}

# Retention Policy Input Types
input RetentionPolicyInput {
  name: String!
  description: String
  scope: String
  retentionPeriod: Int!
  retentionPeriodUnit: RetentionPeriodUnit!
  dispositionAction: DispositionAction!
  isActive: Boolean = true
  allowLegalHold: Boolean = true
  autoApply: Boolean = false
  priority: Int = 0
  triggerEvent: String
  businessJustification: String
  legalBasis: String
  reviewFrequencyMonths: Int
  notificationBeforeDays: Int
  assignments: [RetentionPolicyAssignmentInput!]
}

input RetentionPolicyAssignmentInput {
  assignmentType: String!
  assignmentValue: String!
  isActive: Boolean = true
  priority: Int = 0
  conditions: String
  description: String
}

input LegalHoldInput {
  documentIds: [ID!]!
  reason: String!
}

input DispositionReviewInput {
  documentId: ID!
  approved: Boolean!
  notes: String
}

# Document Permission Input Types
input DocumentPermissionInput {
  documentId: ID!
  userId: String
  roleName: String
  permissionType: Permission!
  expiresAt: DateTime
  notes: String
}

input BulkDocumentPermissionInput {
  documentId: ID!
  permissions: [SinglePermissionInput!]!
}

input SinglePermissionInput {
  userId: String
  roleName: String
  permissionType: Permission!
  expiresAt: DateTime
  notes: String
}

input RevokePermissionInput {
  documentId: ID!
  userId: String
  roleName: String
}

input DocumentSearchInput {
  name: String
  creator: String
  creationDate: DateTime
  storageProvider: StorageProvider
  status: DocumentStatus
  keywords: [String!]
}

# Advanced search input for Elasticsearch-powered search
input AdvancedSearchInput {
  # Text search parameters
  query: String                           # Main search query
  searchType: SearchType = MULTI_FIELD    # Type of search to perform
  fuzzySearch: Boolean = false            # Enable fuzzy matching
  phraseSearch: Boolean = false           # Enable phrase matching

  # Content search
  includeContent: Boolean = true          # Search within document content
  contentOnly: Boolean = false            # Search only in content, not metadata

  # Filters
  name: String
  description: String
  creator: String
  storageProvider: StorageProvider
  status: DocumentStatus
  mimeTypes: [String!]

  # Date range filters
  createdDateFrom: DateTime
  createdDateTo: DateTime
  modifiedDateFrom: DateTime
  modifiedDateTo: DateTime
  expiryDateFrom: DateTime
  expiryDateTo: DateTime

  # Metadata filters
  module: String
  subModule: String
  businessUnit: String
  regionLocation: String
  documentType: String
  language: String
  confidentialityLevel: String

  # Ownership filters
  owner: String
  approver: String
  ownershipStatus: String

  # Compliance filters
  complianceStandard: String
  auditRelevance: String
  controlId: String
  thirdPartyId: String
  policyId: String

  # Tags and keywords
  tags: [String!]
  keywords: [String!]

  # Version filters
  includeHistoricalVersions: Boolean = false
  currentVersionOnly: Boolean = true

  # File size filters
  minFileSize: Long
  maxFileSize: Long

  # Security filters (applied automatically based on user context)
  includeConfidential: Boolean = false
}

enum SearchType {
  MULTI_FIELD      # Search across multiple fields with boosting
  FUZZY           # Fuzzy matching for typos and variations
  PHRASE          # Exact phrase matching
  WILDCARD        # Wildcard pattern matching
  SEMANTIC        # Semantic/natural language search
  CONTENT_ONLY    # Search only in document content
  METADATA_ONLY   # Search only in metadata fields
}

# Search result with highlighting and facets
type AdvancedSearchResult {
  documents: SearchDocumentPage!
  facets: SearchFacets!
  suggestions: [String!]!
  totalTime: Int!                        # Search time in milliseconds
  maxScore: Float                        # Maximum relevance score
}

# Faceted search results for filtering
type SearchFacets {
  documentTypes: [FacetCount!]!
  storageProviders: [FacetCount!]!
  creators: [FacetCount!]!
  modules: [FacetCount!]!
  businessUnits: [FacetCount!]!
  confidentialityLevels: [FacetCount!]!
  mimeTypes: [FacetCount!]!
  tags: [FacetCount!]!
  languages: [FacetCount!]!
  years: [FacetCount!]!
}

# FacetCount type moved to template-management-schema.graphqls to avoid duplication

# Enhanced document type with search highlighting
type SearchDocument {
  id: ID!
  name: String!
  description: String
  version: Int!
  status: DocumentStatus!
  storageProvider: StorageProvider!
  filePath: String!
  createdBy: String!
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  keywords: [String!]
  tags: [String!]

  # Search-specific fields
  relevanceScore: Float
  highlights: DocumentHighlights

  # Metadata
  classificationMetadata: DocumentClassificationMetadata
  ownershipMetadata: DocumentOwnershipMetadata
  complianceMetadata: DocumentComplianceMetadata
}

type DocumentHighlights {
  name: [String!]
  description: [String!]
  content: [String!]
  keywords: [String!]
}

input PaginationInput {
  page: Int! = 0
  size: Int! = 10
  sortBy: String! = "createdDate"
  sortDirection: String! = "DESC"
}

type DocumentPage {
  content: [Document!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}

type SearchDocumentPage {
  content: [SearchDocument!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}


input JwtTokenRequest {
  username: String!
  roles: [String!]
  permissions: [String!]
}

type JwtTokenResponse {
  token: String!
  tokenType: String!
  expiresAt: DateTime!
}

# Document Permission Types
type DocumentPermission {
  id: ID!
  documentId: ID!
  userId: String
  roleName: String
  permissionType: Permission!
  grantedBy: String!
  isActive: Boolean!
  expiresAt: DateTime
  notes: String
  createdDate: DateTime!
  lastModifiedDate: DateTime
}

type DocumentPermissionResponse {
  success: Boolean!
  message: String!
  permission: DocumentPermission
}

type DocumentPermissionListResponse {
  success: Boolean!
  message: String!
  permissions: [DocumentPermission!]!
}

# Security Violation Types
type SecurityViolation {
  id: ID!
  userId: String!
  violationType: SecurityViolationType!
  document: Document
  attemptedAction: String
  ipAddress: String
  userAgent: String
  violationDetails: String
  correlationId: String
  severity: ViolationSeverity!
  isResolved: Boolean!
  resolvedBy: String
  resolvedDate: DateTime
  createdDate: DateTime!
  lastModifiedDate: DateTime
}

type SecurityViolationStats {
  correlationId: String!
  totalCount: Int!
  unresolvedCount: Int!
  criticalCount: Int!
  highCount: Int!
}

type CleanupResult {
  success: Boolean!
  deletedCount: Int!
  message: String!
}

enum SecurityViolationType {
  PERMISSION_DENIED
  TOKEN_EXPIRED
  RATE_LIMIT_EXCEEDED
  INVALID_ACCESS
  PRIVILEGE_ESCALATION
  # Migration-specific violations
  MIGRATION_UNAUTHORIZED_ACCESS
  MIGRATION_PATH_TRAVERSAL
  MIGRATION_INVALID_PARAMETERS
  MIGRATION_FILE_ACCESS_DENIED
  MIGRATION_RATE_LIMIT_EXCEEDED
}

enum ViolationSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

# Storage Configuration Types
type StorageConfiguration {
  id: ID!
  providerType: StorageProvider!
  isActive: Boolean!
  isDefault: Boolean!
  configurationJson: String!
  description: String
  priority: Int!
  healthCheckEnabled: Boolean!
  lastHealthCheck: DateTime
  healthStatus: String
  createdDate: DateTime!
  lastModifiedDate: DateTime
  createdBy: String
  lastModifiedBy: String
}

input StorageConfigurationInput {
  id: ID
  providerType: StorageProvider!
  isActive: Boolean!
  isDefault: Boolean!
  configurationJson: String!
  description: String
  priority: Int!
  healthCheckEnabled: Boolean!
}

type Query {
  getDocumentById(id: ID!): Document
  getDocument(id: ID!): Document
  listDocumentVersions(documentId: ID!): [DocumentVersion!]!
  searchDocuments(filter: DocumentSearchInput, pagination: PaginationInput): DocumentPage!
  getAuditLogsByFilter(filter: DocumentSearchInput): [AuditLog!]!
  downloadDocument(id: ID!): Document

  # Advanced Elasticsearch-powered search
  advancedSearch(input: AdvancedSearchInput!, pagination: PaginationInput): AdvancedSearchResult!
  searchSuggestions(query: String!, limit: Int = 10): [String!]!
  getSearchFacets(input: AdvancedSearchInput!): SearchFacets!

  # Security violation queries with correlation ID support
  getSecurityViolationsByCorrelationId(correlationId: String!): [SecurityViolation!]!
  getUnresolvedSecurityViolationsByCorrelationId(correlationId: String!): [SecurityViolation!]!
  getSecurityViolationsByUserId(userId: String!, isResolved: Boolean): [SecurityViolation!]!
  getSecurityViolationsByType(violationType: SecurityViolationType!, afterDate: DateTime): [SecurityViolation!]!
  getSecurityViolationsBySeverity(severity: ViolationSeverity!, isResolved: Boolean): [SecurityViolation!]!
  getRecentUnresolvedSecurityViolations(hours: Int): [SecurityViolation!]!
  getSecurityViolationStatsByCorrelationId(correlationId: String!): SecurityViolationStats!

  # Document permission queries
  getDocumentPermissions(documentId: ID!): [DocumentPermission!]!
  getDocumentPermissionsByUser(userId: String!): [DocumentPermission!]!
  getDocumentPermissionsByRole(roleName: String!): [DocumentPermission!]!
  getUserDocumentPermission(documentId: ID!, userId: String!): DocumentPermission
  getRoleDocumentPermission(documentId: ID!, roleName: String!): DocumentPermission

  # Document metadata queries
  getDocumentClassificationMetadata(documentId: ID!): DocumentClassificationMetadata
  getDocumentOwnershipMetadata(documentId: ID!): DocumentOwnershipMetadata
  getDocumentComplianceMetadata(documentId: ID!): DocumentComplianceMetadata
  searchDocumentsByClassification(module: String, subModule: String, businessUnit: String, confidentialityLevel: String): [Document!]!
  searchDocumentsByOwnership(owner: String, approver: String, status: String, archived: Boolean): [Document!]!
  searchDocumentsByCompliance(complianceStandard: String, controlId: String, thirdPartyId: String, policyId: String): [Document!]!
  getDocumentsExpiringBefore(date: DateTime!): [Document!]!
  getDocumentsForRenewalReminder(date: DateTime!): [Document!]!

  # Storage configuration queries (Admin only)
  getAllStorageConfigurations: [StorageConfiguration!]!
  getActiveStorageConfigurations: [StorageConfiguration!]!
  getStorageConfigurationByProvider(providerType: StorageProvider!): StorageConfiguration
  getDefaultStorageConfiguration: StorageConfiguration
  getAvailableStorageProviders: [String!]!
  isStorageProviderAvailable(providerType: StorageProvider!): Boolean!

  # Retention policy queries
  getRetentionPolicy(id: ID!): RetentionPolicy
  getAllRetentionPolicies(pagination: PaginationInput): RetentionPolicyPage!
  getActiveRetentionPolicies: [RetentionPolicy!]!
  getRetentionPolicyByName(name: String!): RetentionPolicy
  getRetentionPoliciesByScope(scope: String!): [RetentionPolicy!]!
  getDocumentsByRetentionPolicy(policyId: ID!, pagination: PaginationInput): DocumentPage!

  # Document retention queries
  getDocumentsWithoutRetentionPolicy(pagination: PaginationInput): DocumentPage!
  getDocumentsUnderLegalHold(pagination: PaginationInput): DocumentPage!
  getDocumentsRequiringReview(pagination: PaginationInput): DocumentPage!
  getDocumentsExpiringWithin(days: Int!): [Document!]!
  getExpiredDocuments(pagination: PaginationInput): DocumentPage!
  isDocumentUnderLegalHold(documentId: ID!): Boolean!
  getDocumentLegalHoldStatus(documentId: ID!): LegalHoldStatus!

  # ===== WORKFLOW QUERIES =====
  getWorkflowDefinition(id: ID!): WorkflowDefinition
  getWorkflowDefinitions(pagination: WorkflowPaginationInput): WorkflowDefinitionPage!
  getWorkflowDefinitionsByType(workflowType: WorkflowType!): [WorkflowDefinition!]!
  getActiveWorkflowDefinitions: [WorkflowDefinition!]!
  getDefaultWorkflowDefinition(workflowType: WorkflowType!): WorkflowDefinition
  searchWorkflowDefinitions(name: String!): [WorkflowDefinition!]!

  getWorkflowInstance(id: ID!): WorkflowInstance
  getWorkflowInstancesForDocument(documentId: ID!): [WorkflowInstance!]!
  getActiveWorkflowForDocument(documentId: ID!): WorkflowInstance
  getWorkflowInstancesByStatus(status: WorkflowStatus!): [WorkflowInstance!]!
  getWorkflowInstancesByInitiator(initiatorUserId: String!, pagination: WorkflowPaginationInput): WorkflowInstancePage!
  getOverdueWorkflowInstances: [WorkflowInstance!]!
  getWorkflowInstancesDueSoon(hoursAhead: Int = 24): [WorkflowInstance!]!

  getWorkflowTask(id: ID!): WorkflowTask
  getTasksAssignedToUser(userId: String!, pagination: WorkflowPaginationInput): WorkflowTaskPage!
  getActiveTasksForUser(userId: String!): [WorkflowTask!]!
  getTasksByRole(role: String!): [WorkflowTask!]!
  getTasksByDepartment(department: String!): [WorkflowTask!]!
  getOverdueTasks: [WorkflowTask!]!
  getTasksDueSoon(hoursAhead: Int = 4): [WorkflowTask!]!

  getWorkflowStatistics: WorkflowStatistics!

  # ===== TEMPLATE QUERIES ===== (moved to template-management-schema.graphqls)
  # getDocumentTemplate(id: ID!): DocumentTemplate
  # getDocumentTemplates(pagination: TemplatePaginationInput): DocumentTemplatePage!
  # getAccessibleTemplates(userId: String!, department: String): [DocumentTemplate!]!
  # getTemplatesByCategory(category: String!): [DocumentTemplate!]!
  # getTemplatesByType(templateType: TemplateType!): [DocumentTemplate!]!
  # searchTemplates(name: String!): [DocumentTemplate!]!
  # getTemplatesByOwner(ownerUserId: String!, pagination: TemplatePaginationInput): DocumentTemplatePage!
  # getPopularTemplates(minUsageCount: Int = 5): [DocumentTemplate!]!
  # getRecentlyUsedTemplates(daysBack: Int = 30): [DocumentTemplate!]!
  # getPublishedTemplates: [DocumentTemplate!]!
  # getTemplatesPendingApproval: [DocumentTemplate!]!

  # Template queries moved to template-management-schema.graphqls to avoid duplication
  # getTemplateUsageHistory(templateId: ID!, pagination: TemplatePaginationInput): [TemplateUsageHistory!]!
  # getTemplateStatistics: TemplateStatistics!

  # ===== WEBHOOK AND EVENT QUERIES =====
  getSystemEvent(id: ID!): SystemEvent
  getSystemEvents(pagination: EventPaginationInput): SystemEventPage!
  getEventsByType(eventType: EventType!): [SystemEvent!]!
  getEventsByCategory(eventCategory: EventCategory!): [SystemEvent!]!
  getEventsByActor(actorUserId: String!, pagination: EventPaginationInput): SystemEventPage!
  getEventsByCorrelationId(correlationId: String!): [SystemEvent!]!
  getRecentEvents(hoursBack: Int = 24): [SystemEvent!]!
  getPendingEvents: [SystemEvent!]!
  getFailedEvents: [SystemEvent!]!

  getWebhookEndpoint(id: ID!): WebhookEndpoint
  getWebhookEndpoints(pagination: EventPaginationInput): WebhookEndpointPage!
  getActiveWebhookEndpoints: [WebhookEndpoint!]!
  getWebhookEndpointsByCreator(createdBy: String!, pagination: EventPaginationInput): WebhookEndpointPage!
  getWebhookEndpointsForEvent(eventType: EventType!): [WebhookEndpoint!]!

  getWebhookDelivery(id: ID!): WebhookDelivery
  getWebhookDeliveriesForEndpoint(webhookEndpointId: ID!): [WebhookDelivery!]!
  getWebhookDeliveriesForEvent(systemEventId: ID!): [WebhookDelivery!]!
  getFailedWebhookDeliveries: [WebhookDelivery!]!

  getEventStatistics: EventStatistics!
  getWebhookStatistics: WebhookStatistics!
  getWebhookEndpointStatistics: WebhookEndpointStatistics!
  getHourlyEventCounts(hoursBack: Int = 24): [EventTypeCount!]!
  getDailyEventCounts(daysBack: Int = 30): [EventTypeCount!]!

  # ===== EXTENDED FILE PROCESSING QUERIES =====
  documentProcessingStatus(jobId: String!): AsyncJobStatus
  chunkedUploadStatus(sessionId: String!): ChunkUploadStatus
}

type Mutation {
    uploadDocument(input: UploadDocumentInput!): Document!
    uploadDocumentFromPath(input: UploadDocumentFromPathInput!): Document!
    uploadDocumentNewVersion(input: UploadNewVersionInput!): Document!
    uploadDocumentNewVersionFromPath(input: UploadNewVersionFromPathInput!): Document!
    # bulkUploadDocuments moved to document-upload-schema.graphqls to avoid type duplication
    deleteDocument(id: ID!): Boolean!
    generateTestToken(input: JwtTokenRequest!): JwtTokenResponse!

    # Document metadata mutations
    saveDocumentClassificationMetadata(documentId: ID!, input: DocumentClassificationMetadataInput!): DocumentClassificationMetadata!
    updateClassificationMetadata(documentId: ID!, input: DocumentClassificationMetadataInput!): DocumentClassificationMetadata!
    saveDocumentOwnershipMetadata(documentId: ID!, input: DocumentOwnershipMetadataInput!): DocumentOwnershipMetadata!
    saveDocumentComplianceMetadata(documentId: ID!, input: DocumentComplianceMetadataInput!): DocumentComplianceMetadata!
    deleteDocumentMetadata(documentId: ID!): Boolean!

    # Document permission mutations (Admin and Creator access)
    grantDocumentPermission(input: DocumentPermissionInput!): DocumentPermissionResponse!
    grantBulkDocumentPermissions(input: BulkDocumentPermissionInput!): DocumentPermissionListResponse!
    revokeDocumentPermission(input: RevokePermissionInput!): DocumentPermissionResponse!
    updateDocumentPermission(permissionId: ID!, input: DocumentPermissionInput!): DocumentPermissionResponse!
    expireDocumentPermission(permissionId: ID!): DocumentPermissionResponse!

    # Security violation mutations
    resolveSecurityViolation(violationId: ID!, resolvedBy: String!): Boolean!

    # Storage configuration mutations (Admin only)
    saveStorageConfiguration(input: StorageConfigurationInput!): StorageConfiguration!
    setStorageConfigurationAsDefault(configurationId: ID!): Boolean!
    toggleStorageConfiguration(configurationId: ID!, active: Boolean!): Boolean!
    deleteStorageConfiguration(configurationId: ID!): Boolean!
    testStorageConfiguration(configurationId: ID!): Boolean!

    # Retention policy mutations
    createRetentionPolicy(input: RetentionPolicyInput!): RetentionPolicy!
    updateRetentionPolicy(id: ID!, input: RetentionPolicyInput!): RetentionPolicy!
    deleteRetentionPolicy(id: ID!): Boolean!
    assignRetentionPolicyToDocument(documentId: ID!, policyId: ID!): Document!
    removeRetentionPolicyFromDocument(documentId: ID!): Document!

    # Legal hold mutations
    applyLegalHold(input: LegalHoldInput!): [Document!]!
    releaseLegalHold(documentIds: [ID!]!): [Document!]!
    updateLegalHoldReason(documentId: ID!, reason: String!): Document!

    # Disposition mutations
    approveDisposition(input: DispositionReviewInput!): Document!
    rejectDisposition(input: DispositionReviewInput!): Document!
    processRetentionForDocument(documentId: ID!): Document!

    # ===== WORKFLOW MUTATIONS =====
    createWorkflowDefinition(input: WorkflowDefinitionInput!): WorkflowDefinition!
    updateWorkflowDefinition(id: ID!, input: WorkflowDefinitionInput!): WorkflowDefinition!
    activateWorkflowDefinition(id: ID!): WorkflowDefinition!
    deactivateWorkflowDefinition(id: ID!): WorkflowDefinition!
    setWorkflowDefinitionAsDefault(id: ID!): WorkflowDefinition!
    deleteWorkflowDefinition(id: ID!): Boolean!

    startWorkflow(input: StartWorkflowInput!): WorkflowInstance!
    completeWorkflow(instanceId: ID!, completionReason: String!): WorkflowInstance!
    cancelWorkflow(instanceId: ID!, cancellationReason: String!): WorkflowInstance!
    advanceWorkflowToNextStage(instanceId: ID!): WorkflowInstance!

    completeTask(input: CompleteTaskInput!): WorkflowTask!
    delegateTask(taskId: ID!, delegateToUserId: String!, comments: String): WorkflowTask!
    escalateTask(taskId: ID!, escalateToUserId: String!, reason: String!): WorkflowTask!
    cancelTask(taskId: ID!, reason: String!): WorkflowTask!

    # ===== TEMPLATE MUTATIONS ===== (moved to template-management-schema.graphqls)
    # createDocumentTemplate(input: DocumentTemplateInput!): DocumentTemplate!
    # updateDocumentTemplate(id: ID!, input: DocumentTemplateInput!): DocumentTemplate!
    # publishTemplate(id: ID!): DocumentTemplate!
    # approveTemplate(id: ID!): DocumentTemplate!
    # rejectTemplate(id: ID!): DocumentTemplate!
    # deactivateTemplate(id: ID!): DocumentTemplate!
    # deleteTemplate(id: ID!): Boolean!

    # Template mutations moved to template-management-schema.graphqls to avoid duplication
    # createDocumentFromTemplate(input: CreateDocumentFromTemplateInput!): Document!
    # previewTemplate(templateId: ID!, fieldValues: [FieldValueInput!]!): String!  # Returns Base64 content

    # ===== WEBHOOK AND EVENT MUTATIONS =====
    createWebhookEndpoint(input: WebhookEndpointInput!): WebhookEndpoint!
    updateWebhookEndpoint(id: ID!, input: WebhookEndpointInput!): WebhookEndpoint!
    verifyWebhookEndpoint(verificationToken: String!): WebhookEndpoint!
    activateWebhookEndpoint(id: ID!): WebhookEndpoint!
    deactivateWebhookEndpoint(id: ID!): WebhookEndpoint!
    deleteWebhookEndpoint(id: ID!): Boolean!

    publishSystemEvent(eventType: EventType!, eventCategory: EventCategory!, eventName: String!,
                      sourceEntityType: String, sourceEntityId: ID, eventData: String): SystemEvent!  # eventData as JSON string
    markEventAsCompleted(eventId: ID!): SystemEvent!
    markEventAsFailed(eventId: ID!): SystemEvent!

    retryWebhookDelivery(deliveryId: ID!): WebhookDelivery!
    cancelWebhookDelivery(deliveryId: ID!): WebhookDelivery!

    # ===== EXTENDED FILE PROCESSING MUTATIONS =====
    # Extended document upload mutations
    uploadDocumentEx(input: UploadDocumentExInput!): DocumentEx
    uploadDocumentFromPathEx(input: UploadDocumentFromPathExInput!): DocumentEx

    # Chunked upload operations (types defined in document-upload-schema.graphqls)
    # uploadChunk(input: ChunkUploadInput!): ChunkUploadStatus
    # completeChunkedUpload(input: CompleteChunkedUploadInput!): DocumentEx

    # ===== PDF CONVERSION MUTATIONS =====
    # Convert PDF to Word document from multipart file
    convertPdfToWordMultipart(input: PdfConversionMultipartInput!): PdfConversionResult!

    # Convert PDF to Word document from file path
    convertPdfToWordFromPath(input: PdfConversionPathInput!): PdfConversionResult!

    # ===== WORD CONVERSION MUTATIONS =====
    # Convert Word document to PDF from multipart file
    convertWordToPdfMultipart(input: WordConversionMultipartInput!): WordConversionResult!

    # Convert Word document to PDF from file path
    convertWordToPdfFromPath(input: WordConversionPathInput!): WordConversionResult!

    # ===== MARKDOWN CONVERSION MUTATIONS =====
    # Convert Markdown file to Word document from multipart file
    convertMarkdownToWordMultipart(input: MarkdownConversionMultipartInput!): MarkdownConversionResult!

    # Convert Markdown file to Word document from file path
    convertMarkdownToWordFromPath(input: MarkdownConversionPathInput!): MarkdownConversionResult!

    # System maintenance mutations
    cleanupOldEvents(olderThanDays: Int!): CleanupResult!
}

# ===== EXTENDED FILE PROCESSING TYPES =====

# Processing strategy enum
enum ProcessingStrategy {
  DIRECT
  ASYNC
  CHUNKED
}

# Processing status enum
enum ProcessingStatus {
  QUEUED
  PROCESSING
  COMPLETED
  FAILED
}

# Extended Document type with processing fields
type DocumentEx {
  # Base document fields
  id: ID!
  name: String!
  description: String
  version: Int!
  status: DocumentStatus!
  storageProvider: StorageProvider
  storagePath: String
  originalFileName: String
  fileSize: Long
  mimeType: String
  createdDate: DateTime
  lastModifiedDate: DateTime
  creatorUserId: String
  tags: [String!]

  # Processing fields
  processingStrategy: ProcessingStrategy!
  processingStatus: ProcessingStatus!
  processingJobId: String
  processingProgress: Float
  statusCheckUrl: String

  # Metadata fields (same as Document)
  permissions: [DocumentPermission!]
  classificationMetadata: DocumentClassificationMetadata
  ownershipMetadata: DocumentOwnershipMetadata
  complianceMetadata: DocumentComplianceMetadata
}

# Async job status type
type AsyncJobStatus {
  jobId: ID!
  status: ProcessingStatus!
  fileName: String!
  fileSize: Long!
  progress: Float!
  estimatedTimeRemaining: Int
  startedAt: DateTime
  completedAt: DateTime
  errorMessage: String
  document: DocumentEx
}

# Chunked upload status type
type ChunkUploadStatus {
  sessionId: ID!
  fileName: String!
  totalSize: Long!
  status: String!
  receivedChunks: Int!
  totalChunks: Int!
  receivedBytes: Long!
  progress: Float!
  createdAt: DateTime!
  lastActivityAt: DateTime
  completedAt: DateTime
  errorMessage: String
  document: DocumentEx
}

# Chunked upload recommendation type
type ChunkedUploadRecommendation {
  sessionId: ID!
  fileName: String!
  totalSize: Long!
  recommendedChunkSize: Int!
  maxChunkSize: Int!
  totalChunks: Int!
  expiresAt: DateTime!
}

# ===== EXTENDED INPUT TYPES =====

# New input types for extended operations
input UploadDocumentExInput {
  # Same fields as UploadDocumentInput
  name: String!
  description: String
  file: Upload!
  keywords: [String!]
  storageProvider: StorageProvider
  overrideFile: Boolean = false
  scannerType: VirusScannerType

  # Metadata fields
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput

  # Processing options
  forceProcessingStrategy: ProcessingStrategy
  chunkSize: Int
}

input UploadDocumentFromPathExInput {
  # Same fields as UploadDocumentFromPathInput
  name: String!
  description: String
  sourceFilePath: String!
  keywords: [String!]
  storageProvider: StorageProvider
  overrideFile: Boolean = false
  scannerType: VirusScannerType

  # Metadata fields
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput

  # Processing options
  forceProcessingStrategy: ProcessingStrategy
  chunkSize: Int
}

# ChunkUploadInput type moved to document-upload-schema.graphqls to avoid duplication

# CompleteChunkedUploadInput type moved to document-upload-schema.graphqls to avoid duplication

input BulkUploadInput {
  files: [Upload!]!
  namePrefix: String
  description: String
  keywords: [String!]
  storageProvider: StorageProvider
  overrideFile: Boolean = false
  scannerType: VirusScannerType
  continueOnVirusDetection: Boolean = true
  continueOnError: Boolean = false
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}

# BulkUploadResult and BulkUploadItemResult types moved to document-upload-schema.graphqls to avoid duplication

# ===== PDF CONVERSION TYPES =====

# Input type for PDF conversion from multipart file
input PdfConversionMultipartInput {
  # The PDF file to convert
  file: Upload!

  # Optional virus scanner type (defaults to system default)
  scannerType: VirusScannerType
}

# Input type for PDF conversion from file path
input PdfConversionPathInput {
  # Path to the PDF file to convert
  filePath: String!

  # Optional virus scanner type (defaults to system default)
  scannerType: VirusScannerType
}

# Result of PDF to Word conversion operation
type PdfConversionResult {
  # Unique session identifier for the conversion operation
  sessionId: String!

  # Original PDF file name
  originalFileName: String!

  # Converted Word document file name
  convertedFileName: String!

  # Full path to the converted file in the download directory
  downloadPath: String!

  # Size of the converted file in bytes
  fileSize: Long!

  # Result of virus scanning performed on the original PDF
  virusScanResponse: PdfVirusScanResponse!

  # Whether the conversion was successful
  success: Boolean!

  # Success or error message
  message: String!

  # Error details if conversion failed
  errorDetails: String

  # Timestamp when conversion was completed
  completedAt: DateTime

  # Duration of the conversion process in milliseconds
  processingTimeMs: Long
}

# Virus scan response type for PDF conversion
type PdfVirusScanResponse {
  # Scan result status
  result: String!

  # Scanner type used
  scannerType: VirusScannerType!

  # File name that was scanned
  fileName: String!

  # File size in bytes
  fileSize: Long

  # Any detected threats
  detectedThreats: [String!]

  # Scan duration in milliseconds
  scanDurationMs: Long

  # Scanner message
  scannerMessage: String

  # Error message if scan failed
  errorMessage: String

  # Unique scan identifier
  scanId: String
}

# ===== WORD CONVERSION TYPES =====

# Input type for Word conversion from multipart file
input WordConversionMultipartInput {
  # The Word document file to convert
  file: Upload!

  # Optional virus scanner type (defaults to system default)
  scannerType: VirusScannerType
}

# Input type for Word conversion from file path
input WordConversionPathInput {
  # Path to the Word document file to convert
  filePath: String!

  # Optional virus scanner type (defaults to system default)
  scannerType: VirusScannerType
}

# Result of Word to PDF conversion operation
type WordConversionResult {
  # Unique session identifier for the conversion operation
  sessionId: String!

  # Original Word document file name
  originalFileName: String!

  # Converted PDF file name
  convertedFileName: String!

  # Full path to the converted file in the download directory
  downloadPath: String!

  # Size of the converted file in bytes
  fileSize: Long!

  # Result of virus scanning performed on the original Word document
  virusScanResponse: WordVirusScanResponse!

  # Whether the conversion was successful
  success: Boolean!

  # Success or error message
  message: String!

  # Error details if conversion failed
  errorDetails: String

  # Timestamp when conversion was completed
  completedAt: DateTime

  # Duration of the conversion process in milliseconds
  processingTimeMs: Long
}

# Virus scan response type for Word conversion
type WordVirusScanResponse {
  # Scan result status
  result: String!

  # Scanner type used
  scannerType: VirusScannerType!

  # File name that was scanned
  fileName: String!

  # File size in bytes
  fileSize: Long

  # Any detected threats
  detectedThreats: [String!]

  # Scan duration in milliseconds
  scanDurationMs: Long

  # Scanner message
  scannerMessage: String

  # Error message if scan failed
  errorMessage: String

  # Unique scan identifier
  scanId: String
}

# ===== MARKDOWN CONVERSION TYPES =====

# Input type for Markdown conversion from multipart file
input MarkdownConversionMultipartInput {
  # The Markdown file to convert
  file: Upload!

  # Optional virus scanner type (defaults to system default)
  scannerType: VirusScannerType
}

# Input type for Markdown conversion from file path
input MarkdownConversionPathInput {
  # Path to the Markdown file to convert
  filePath: String!

  # Optional virus scanner type (defaults to system default)
  scannerType: VirusScannerType
}

# Result of Markdown to Word conversion operation
type MarkdownConversionResult {
  # Unique session identifier for the conversion operation
  sessionId: String!

  # Original Markdown file name
  originalFileName: String!

  # Converted Word document file name
  convertedFileName: String!

  # Full path to the converted file in the download directory
  downloadPath: String!

  # Size of the converted file in bytes
  fileSize: Long!

  # Result of virus scanning performed on the original Markdown file
  virusScanResponse: MarkdownVirusScanResponse!

  # Whether the conversion was successful
  success: Boolean!

  # Success or error message
  message: String!

  # Error details if conversion failed
  errorDetails: String

  # Timestamp when conversion was completed
  completedAt: DateTime

  # Duration of the conversion process in milliseconds
  processingTimeMs: Long

  # Whether Pandoc was used for the conversion
  usedPandoc: Boolean!

  # Conversion method used (e.g., "pandoc", "fallback")
  conversionMethod: String!
}

# Virus scan response type for Markdown conversion
type MarkdownVirusScanResponse {
  # Scan result status
  result: String!

  # Scanner type used
  scannerType: VirusScannerType!

  # List of detected threats (if any)
  detectedThreats: [String!]!

  # Scan duration in milliseconds
  scanDurationMs: Long!

  # Timestamp when scan was performed
  scanTimestamp: DateTime!

  # Error message if scan failed
  errorMessage: String

  # Additional scan metadata
  metadata: String

  # Unique scan identifier
  scanId: String
}
