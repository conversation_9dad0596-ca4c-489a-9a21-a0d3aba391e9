# DMS Service - Consolidated Test Runner Guide

## Overview

The `run-all-tests.bat` script is a comprehensive test execution tool that consolidates all testing activities for the DMS Service. It runs multiple test categories and generates detailed reports.

## Features

### Test Categories Supported

1. **Unit Tests** - Basic functionality tests for individual components
2. **Integration Tests** - Tests for component interactions
3. **End-to-End (E2E) Tests** - Full workflow tests
4. **Security Tests** - Security validation and vulnerability tests
5. **Performance Tests** - Load and performance benchmarks
6. **Contract Tests** - API contract validation
7. **Configuration Tests** - Configuration and setup validation
8. **Infrastructure Tests** - Docker, Kubernetes, and deployment tests
9. **API Tests** - REST API functionality tests
10. **GraphQL Tests** - GraphQL schema and resolver tests
11. **Compliance Tests** - Regulatory compliance validation
12. **Retention Policy Tests** - Document retention policy tests

### Key Features

- **Selective Test Execution** - Run specific test categories only
- **Fail-Fast Mode** - Stop on first test failure
- **Comprehensive Reporting** - Generate coverage and test reports
- **Timestamped Results** - Organized output with timestamps
- **PowerShell Integration** - Includes infrastructure and API tests
- **Detailed Logging** - Complete execution logs

## Usage

### Basic Usage

```batch
# Run all tests
scripts\run-all-tests.bat

# Run with fail-fast mode
scripts\run-all-tests.bat --fail-fast

# Skip report generation
scripts\run-all-tests.bat --no-reports
```

### Selective Test Execution

```batch
# Run only unit tests
scripts\run-all-tests.bat --unit-only

# Run only integration tests
scripts\run-all-tests.bat --integration-only

# Run only security tests
scripts\run-all-tests.bat --security-only
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `--unit-only` | Run only unit tests |
| `--integration-only` | Run only integration tests |
| `--security-only` | Run only security tests |
| `--fail-fast` | Stop execution on first test failure |
| `--no-reports` | Skip test report generation |
| `--help` | Show help message |

## Prerequisites

### Required Software

- **Java 21+** - For running Maven and tests
- **Maven 3.9+** - For build and test execution
- **PowerShell 5.1+** - For infrastructure and API tests
- **Docker** (optional) - For infrastructure tests
- **kubectl** (optional) - For Kubernetes tests

### Environment Setup

1. Ensure Java and Maven are in your PATH
2. Verify Docker is running (if infrastructure tests enabled)
3. Configure kubectl (if Kubernetes tests enabled)

## Output and Reports

### Report Structure

```
target/test-reports/YYYYMMDD_HHMMSS/
├── surefire-reports/          # Maven Surefire test reports
├── failsafe-reports/          # Maven Failsafe integration test reports
├── coverage/                  # JaCoCo code coverage reports
│   └── index.html            # Main coverage report
├── custom-reports/           # Custom test reports
├── infrastructure-reports/   # Infrastructure test results
├── powershell-logs/         # PowerShell test execution logs
└── test-execution.log       # Complete execution log
```

### Key Report Files

- **Coverage Report**: `coverage/index.html` - Code coverage analysis
- **Test Results**: `surefire-reports/` - Detailed test results
- **Execution Log**: `test-execution.log` - Complete execution trace

## Test Categories Details

### Unit Tests
- **Pattern**: `**/*Test.java`
- **Excludes**: Integration, E2E, Security, Performance, Contract tests
- **Purpose**: Test individual components in isolation

### Integration Tests
- **Pattern**: `**/*IntegrationTest.java`
- **Purpose**: Test component interactions and database operations

### Security Tests
- **Pattern**: `**/*SecurityTest.java`, `**/*VulnerabilityScannerTest.java`
- **Purpose**: Validate security controls and scan for vulnerabilities

### Performance Tests
- **Pattern**: `**/*PerformanceTest.java`, `**/*BenchmarkTest.java`
- **Purpose**: Measure system performance and identify bottlenecks

### Infrastructure Tests
- **Script**: `run-infrastructure-tests.ps1`
- **Purpose**: Validate Docker containers, Kubernetes deployments, CI/CD pipelines

### API Tests
- **Script**: `test-api-functionality.ps1`
- **Purpose**: Test REST API endpoints and functionality

### GraphQL Tests
- **Script**: `test-graphql.ps1`
- **Purpose**: Test GraphQL schema, resolvers, and queries

## Troubleshooting

### Common Issues

1. **Maven Not Found**
   - Ensure Maven is installed and in PATH
   - Verify with: `mvn --version`

2. **Java Version Issues**
   - Ensure Java 21+ is installed
   - Verify with: `java -version`

3. **PowerShell Execution Policy**
   - May need to run: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

4. **Docker Not Available**
   - Infrastructure tests will be skipped if Docker is not running
   - Start Docker Desktop or Docker service

### Log Analysis

- Check `test-execution.log` for detailed execution information
- Review individual test reports in `surefire-reports/`
- Examine PowerShell logs for infrastructure test issues

## Integration with CI/CD

The test runner is designed to work in CI/CD environments:

```yaml
# Example GitHub Actions usage
- name: Run All Tests
  run: scripts\run-all-tests.bat --fail-fast
  
# Example with selective testing
- name: Run Security Tests Only
  run: scripts\run-all-tests.bat --security-only
```

## Customization

### Adding New Test Categories

1. Add new failure counter variable
2. Add new command line option parsing
3. Add new test execution section
4. Update summary reporting
5. Update help documentation

### Modifying Test Patterns

Edit the Maven test patterns in the respective test sections to include or exclude specific test files.

## Support

For issues or questions about the test runner:

1. Check the execution log for detailed error information
2. Verify prerequisites are met
3. Review individual test reports for specific failures
4. Consult the project documentation for test-specific guidance
