-- liquibase formatted sql

-- changeset dms:016-create-user-tables
-- Create user management tables to support User entity

-- Create users table
CREATE TABLE users (
    id VARCHAR(100) PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_date TIMESTAMP NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    last_modified_by <PERSON><PERSON><PERSON><PERSON>(100),
    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    INDEX idx_users_is_active (is_active)
);

-- Create user_roles table for @ElementCollection mapping
CREATE TABLE user_roles (
    user_id VARCHAR(100) NOT NULL,
    role VARCHAR(100) NOT NULL,
    <PERSON><PERSON><PERSON><PERSON> (user_id, role),
    <PERSON>OR<PERSON><PERSON><PERSON>EY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_roles_user_id (user_id),
    INDEX idx_user_roles_role (role)
);

-- Create user_permissions table for @ElementCollection mapping
CREATE TABLE user_permissions (
    user_id VARCHAR(100) NOT NULL,
    permission VARCHAR(100) NOT NULL,
    PRIMARY KEY (user_id, permission),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_permissions_user_id (user_id),
    INDEX idx_user_permissions_permission (permission)
);

-- Insert sample admin user for testing
INSERT INTO users (id, username, email, first_name, last_name, is_active, created_by) 
VALUES ('admin', 'admin', '<EMAIL>', 'System', 'Administrator', TRUE, 'system');

-- Insert admin roles
INSERT INTO user_roles (user_id, role) VALUES 
('admin', 'ADMIN'),
('admin', 'USER');

-- Insert admin permissions
INSERT INTO user_permissions (user_id, permission) VALUES 
('admin', 'READ'),
('admin', 'WRITE'),
('admin', 'DELETE'),
('admin', 'ADMIN');
