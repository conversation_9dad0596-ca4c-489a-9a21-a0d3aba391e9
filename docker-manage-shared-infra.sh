#!/bin/bash

# GRC Platform v4 - Shared Infrastructure Management Script
# This script manages shared infrastructure and individual service deployments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
}

# Function to start shared infrastructure
start_infrastructure() {
    print_header "Starting Shared Infrastructure"
    check_docker
    check_docker_compose
    
    print_status "Starting shared infrastructure services..."
    docker-compose -f docker-compose.infrastructure.yml up -d
    
    print_status "Waiting for infrastructure services to be ready..."
    sleep 30
    
    print_status "Shared infrastructure started successfully!"
    show_infrastructure_status
}

# Function to start notification service (connects to shared infra)
start_notification() {
    print_header "Starting Notification Service (using shared infrastructure)"
    check_docker
    check_docker_compose
    
    # Check if shared infrastructure is running
    if ! docker ps | grep -q "grc-mysql-shared"; then
        print_error "Shared infrastructure is not running. Please start it first with: $0 start-infra"
        exit 1
    fi
    
    print_status "Starting notification service..."
    docker-compose -f docker-compose.notification-app-only.yml up -d
    
    print_status "Notification service started successfully!"
    show_notification_status
}

# Function to start DMS service (connects to shared infra)
start_dms() {
    print_header "Starting DMS Service (using shared infrastructure)"
    check_docker
    check_docker_compose
    
    # Check if shared infrastructure is running
    if ! docker ps | grep -q "grc-mysql-shared"; then
        print_error "Shared infrastructure is not running. Please start it first with: $0 start-infra"
        exit 1
    fi
    
    print_status "Starting DMS service..."
    docker-compose -f docker-compose.dms-app-only.yml up -d
    
    print_status "DMS service started successfully!"
    show_dms_status
}

# Function to start everything
start_all() {
    print_header "Starting Complete GRC Platform (Infrastructure + Both Services)"
    start_infrastructure
    sleep 10
    start_notification
    sleep 5
    start_dms
    show_complete_status
}

# Function to show infrastructure status
show_infrastructure_status() {
    print_header "Shared Infrastructure Status"
    docker-compose -f docker-compose.infrastructure.yml ps
    
    echo ""
    print_header "Infrastructure URLs"
    echo "  MySQL:                      localhost:3306 (root/root_password)"
    echo "  Redis:                      localhost:6379 (shared_redis_password)"
    echo "  RabbitMQ Management:        http://localhost:15672 (admin/admin123)"
    echo "  Elasticsearch:              http://localhost:9200"
    echo "  Prometheus:                 http://localhost:9090"
    echo "  Grafana:                    http://localhost:3000 (admin/admin)"
    echo "  Zipkin:                     http://localhost:9411"
}

# Function to show notification service status
show_notification_status() {
    print_header "Notification Service Status"
    docker-compose -f docker-compose.notification-app-only.yml ps
    
    echo ""
    print_header "Notification Service URLs"
    echo "  GraphQL API:                http://localhost:9091/graphql"
    echo "  GraphiQL UI:                http://localhost:9091/graphiql"
    echo "  Health Check:               http://localhost:9091/actuator/health"
}

# Function to show DMS service status
show_dms_status() {
    print_header "DMS Service Status"
    docker-compose -f docker-compose.dms-app-only.yml ps
    
    echo ""
    print_header "DMS Service URLs"
    echo "  GraphQL API:                http://localhost:9093/dms/graphql"
    echo "  GraphiQL UI:                http://localhost:9093/graphiql"
    echo "  Health Check:               http://localhost:9093/actuator/health"
    echo "  Metrics:                    http://localhost:9464/actuator/prometheus"
}

# Function to show complete status
show_complete_status() {
    show_infrastructure_status
    echo ""
    show_notification_status
    echo ""
    show_dms_status
}

# Function to stop services
stop_notification() {
    print_header "Stopping Notification Service"
    docker-compose -f docker-compose.notification-app-only.yml down
    print_status "Notification service stopped!"
}

stop_dms() {
    print_header "Stopping DMS Service"
    docker-compose -f docker-compose.dms-app-only.yml down
    print_status "DMS service stopped!"
}

stop_infrastructure() {
    print_header "Stopping Shared Infrastructure"
    docker-compose -f docker-compose.infrastructure.yml down
    print_status "Shared infrastructure stopped!"
}

stop_all() {
    print_header "Stopping All Services"
    stop_notification
    stop_dms
    stop_infrastructure
    print_status "All services stopped!"
}

# Function to show logs
show_logs() {
    case "$2" in
        "infra"|"infrastructure")
            docker-compose -f docker-compose.infrastructure.yml logs -f
            ;;
        "notification")
            docker-compose -f docker-compose.notification-app-only.yml logs -f
            ;;
        "dms")
            docker-compose -f docker-compose.dms-app-only.yml logs -f
            ;;
        "")
            print_header "Showing All Service Logs"
            docker-compose -f docker-compose.infrastructure.yml logs --tail=50 &
            docker-compose -f docker-compose.notification-app-only.yml logs --tail=50 &
            docker-compose -f docker-compose.dms-app-only.yml logs --tail=50 &
            wait
            ;;
        *)
            print_error "Unknown service: $2. Use 'infra', 'notification', 'dms', or leave empty for all."
            ;;
    esac
}

# Function to show health status
health_check() {
    print_header "Health Check"
    
    echo "Checking Shared Infrastructure..."
    if docker ps | grep -q "grc-mysql-shared"; then
        print_status "MySQL: RUNNING"
    else
        print_error "MySQL: NOT RUNNING"
    fi
    
    if docker ps | grep -q "grc-redis-shared"; then
        print_status "Redis: RUNNING"
    else
        print_error "Redis: NOT RUNNING"
    fi
    
    echo ""
    echo "Checking Application Services..."
    if curl -f http://localhost:9091/actuator/health > /dev/null 2>&1; then
        print_status "Notification Service: HEALTHY"
    else
        print_warning "Notification Service: NOT RUNNING or UNHEALTHY"
    fi
    
    if curl -f http://localhost:9093/actuator/health > /dev/null 2>&1; then
        print_status "DMS Service: HEALTHY"
    else
        print_warning "DMS Service: NOT RUNNING or UNHEALTHY"
    fi
}

# Function to show help
show_help() {
    echo "GRC Platform v4 - Shared Infrastructure Management Script"
    echo ""
    echo "This script allows you to:"
    echo "1. Deploy shared infrastructure once"
    echo "2. Deploy services independently that connect to shared infrastructure"
    echo "3. Add/remove services without affecting others"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Infrastructure Commands:"
    echo "  start-infra        Start shared infrastructure (MySQL, Redis, RabbitMQ, etc.)"
    echo "  stop-infra         Stop shared infrastructure"
    echo ""
    echo "Service Commands:"
    echo "  start-notification Start notification service (requires shared infra)"
    echo "  start-dms          Start DMS service (requires shared infra)"
    echo "  stop-notification  Stop notification service"
    echo "  stop-dms           Stop DMS service"
    echo ""
    echo "Combined Commands:"
    echo "  start-all          Start infrastructure + both services"
    echo "  stop-all           Stop all services and infrastructure"
    echo ""
    echo "Monitoring Commands:"
    echo "  status             Show status of all components"
    echo "  logs [SERVICE]     Show logs (infra, notification, dms, or all)"
    echo "  health             Check health status of all services"
    echo ""
    echo "Examples:"
    echo "  # Scenario 1: Deploy notification service first"
    echo "  $0 start-infra"
    echo "  $0 start-notification"
    echo ""
    echo "  # Scenario 2: Later add DMS service (uses same infrastructure)"
    echo "  $0 start-dms"
    echo ""
    echo "  # Scenario 3: Deploy everything at once"
    echo "  $0 start-all"
}

# Main script logic
case "$1" in
    "start-infra"|"start-infrastructure")
        start_infrastructure
        ;;
    "start-notification")
        start_notification
        ;;
    "start-dms")
        start_dms
        ;;
    "start-all")
        start_all
        ;;
    "stop-infra"|"stop-infrastructure")
        stop_infrastructure
        ;;
    "stop-notification")
        stop_notification
        ;;
    "stop-dms")
        stop_dms
        ;;
    "stop-all")
        stop_all
        ;;
    "status")
        show_complete_status
        ;;
    "logs")
        show_logs "$@"
        ;;
    "health")
        health_check
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        print_error "No command specified. Use '$0 help' for usage information."
        exit 1
        ;;
    *)
        print_error "Unknown command: $1. Use '$0 help' for usage information."
        exit 1
        ;;
esac
