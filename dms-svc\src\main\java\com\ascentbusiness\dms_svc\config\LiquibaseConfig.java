package com.ascentbusiness.dms_svc.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;
import java.util.logging.Logger;

/**
 * Liquibase Configuration to handle Java 21 compatibility issues
 * Specifically addresses JsonChangeLogParser ServiceConfigurationError
 */
@Configuration
@ConditionalOnProperty(name = "spring.liquibase.enabled", havingValue = "true")
public class LiquibaseConfig {

    private static final Logger logger = Logger.getLogger(LiquibaseConfig.class.getName());

    @PostConstruct
    public void configureLiquibase() {
        logger.info("Configuring Liquibase with Java 21 compatibility fixes");
        
        // Suppress service locator errors to prevent JsonChangeLogParser issues
        System.setProperty("liquibase.servicelocator.supress.errors", "true");
        System.setProperty("liquibase.parser.json.enabled", "false");
        
        // Additional properties to handle Java 21 module system
        System.setProperty("liquibase.servicelocator.class.name", "liquibase.servicelocator.StandardServiceLocator");
        
        logger.info("Liquibase Java 21 compatibility properties set successfully");
    }
}