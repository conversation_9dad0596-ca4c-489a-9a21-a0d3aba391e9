#!/bin/bash
# Comprehensive Infrastructure Test Runner for DMS Service
# This script runs all infrastructure-related tests including unit tests, integration tests, and validation tests

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/tmp/dms-infrastructure-tests-$(date +%Y%m%d-%H%M%S).log"

# Test configuration
RUN_UNIT_TESTS=${RUN_UNIT_TESTS:-true}
RUN_INTEGRATION_TESTS=${RUN_INTEGRATION_TESTS:-true}
RUN_INFRASTRUCTURE_TESTS=${RUN_INFRASTRUCTURE_TESTS:-true}
RUN_DOCKER_TESTS=${RUN_DOCKER_TESTS:-true}
RUN_KUBERNETES_TESTS=${RUN_KUBERNETES_TESTS:-false}
RUN_CICD_TESTS=${RUN_CICD_TESTS:-true}
GENERATE_REPORTS=${GENERATE_REPORTS:-true}
FAIL_FAST=${FAIL_FAST:-false}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Test result tracking
TOTAL_TEST_SUITES=0
PASSED_TEST_SUITES=0
FAILED_TEST_SUITES=0
SKIPPED_TEST_SUITES=0

# Function to run a test suite and track results
run_test_suite() {
    local suite_name="$1"
    local test_command="$2"
    local required="${3:-true}"
    
    TOTAL_TEST_SUITES=$((TOTAL_TEST_SUITES + 1))
    
    log_info "Running test suite: $suite_name"
    
    if eval "$test_command" >> "$LOG_FILE" 2>&1; then
        log_success "✓ $suite_name completed successfully"
        PASSED_TEST_SUITES=$((PASSED_TEST_SUITES + 1))
        return 0
    else
        if [[ "$required" == "true" ]]; then
            log_error "✗ $suite_name failed"
            FAILED_TEST_SUITES=$((FAILED_TEST_SUITES + 1))
            if [[ "$FAIL_FAST" == "true" ]]; then
                log_error "Failing fast due to test failure"
                exit 1
            fi
            return 1
        else
            log_warning "⚠ $suite_name skipped (optional)"
            SKIPPED_TEST_SUITES=$((SKIPPED_TEST_SUITES + 1))
            return 0
        fi
    fi
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Run comprehensive infrastructure tests for DMS Service

Options:
    --unit-only         Run only unit tests
    --integration-only  Run only integration tests
    --infrastructure-only Run only infrastructure validation tests
    --docker-only       Run only Docker-related tests
    --kubernetes-only   Run only Kubernetes-related tests
    --cicd-only        Run only CI/CD validation tests
    --no-reports       Skip test report generation
    --fail-fast        Stop on first test failure
    --help, -h         Show this help message

Environment Variables:
    RUN_UNIT_TESTS=true|false           Enable/disable unit tests
    RUN_INTEGRATION_TESTS=true|false    Enable/disable integration tests
    RUN_INFRASTRUCTURE_TESTS=true|false Enable/disable infrastructure tests
    RUN_DOCKER_TESTS=true|false         Enable/disable Docker tests
    RUN_KUBERNETES_TESTS=true|false     Enable/disable Kubernetes tests
    RUN_CICD_TESTS=true|false          Enable/disable CI/CD tests
    GENERATE_REPORTS=true|false         Enable/disable report generation
    FAIL_FAST=true|false               Stop on first failure

Examples:
    $0                                  # Run all tests
    $0 --unit-only                      # Run only unit tests
    $0 --infrastructure-only            # Run only infrastructure tests
    RUN_KUBERNETES_TESTS=true $0        # Enable Kubernetes tests
    $0 --fail-fast                      # Stop on first failure

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --unit-only)
                RUN_UNIT_TESTS=true
                RUN_INTEGRATION_TESTS=false
                RUN_INFRASTRUCTURE_TESTS=false
                RUN_DOCKER_TESTS=false
                RUN_KUBERNETES_TESTS=false
                RUN_CICD_TESTS=false
                shift
                ;;
            --integration-only)
                RUN_UNIT_TESTS=false
                RUN_INTEGRATION_TESTS=true
                RUN_INFRASTRUCTURE_TESTS=false
                RUN_DOCKER_TESTS=false
                RUN_KUBERNETES_TESTS=false
                RUN_CICD_TESTS=false
                shift
                ;;
            --infrastructure-only)
                RUN_UNIT_TESTS=false
                RUN_INTEGRATION_TESTS=false
                RUN_INFRASTRUCTURE_TESTS=true
                RUN_DOCKER_TESTS=false
                RUN_KUBERNETES_TESTS=false
                RUN_CICD_TESTS=false
                shift
                ;;
            --docker-only)
                RUN_UNIT_TESTS=false
                RUN_INTEGRATION_TESTS=false
                RUN_INFRASTRUCTURE_TESTS=false
                RUN_DOCKER_TESTS=true
                RUN_KUBERNETES_TESTS=false
                RUN_CICD_TESTS=false
                shift
                ;;
            --kubernetes-only)
                RUN_UNIT_TESTS=false
                RUN_INTEGRATION_TESTS=false
                RUN_INFRASTRUCTURE_TESTS=false
                RUN_DOCKER_TESTS=false
                RUN_KUBERNETES_TESTS=true
                RUN_CICD_TESTS=false
                shift
                ;;
            --cicd-only)
                RUN_UNIT_TESTS=false
                RUN_INTEGRATION_TESTS=false
                RUN_INFRASTRUCTURE_TESTS=false
                RUN_DOCKER_TESTS=false
                RUN_KUBERNETES_TESTS=false
                RUN_CICD_TESTS=true
                shift
                ;;
            --no-reports)
                GENERATE_REPORTS=false
                shift
                ;;
            --fail-fast)
                FAIL_FAST=true
                shift
                ;;
            --help|-h)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Maven is installed
    if ! command -v mvn &> /dev/null; then
        log_error "Maven is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Java is installed
    if ! command -v java &> /dev/null; then
        log_error "Java is not installed or not in PATH"
        exit 1
    fi
    
    # Check if we're in the correct directory
    if [[ ! -f "$PROJECT_ROOT/pom.xml" ]]; then
        log_error "Not in a Maven project directory"
        exit 1
    fi
    
    # Check Docker if Docker tests are enabled
    if [[ "$RUN_DOCKER_TESTS" == "true" ]]; then
        if ! command -v docker &> /dev/null; then
            log_warning "Docker not found, disabling Docker tests"
            RUN_DOCKER_TESTS=false
        elif ! docker info &> /dev/null; then
            log_warning "Docker daemon not running, disabling Docker tests"
            RUN_DOCKER_TESTS=false
        fi
    fi
    
    # Check kubectl if Kubernetes tests are enabled
    if [[ "$RUN_KUBERNETES_TESTS" == "true" ]]; then
        if ! command -v kubectl &> /dev/null; then
            log_warning "kubectl not found, disabling Kubernetes tests"
            RUN_KUBERNETES_TESTS=false
        fi
    fi
    
    log_success "Prerequisites check completed"
}

# Run unit tests
run_unit_tests() {
    if [[ "$RUN_UNIT_TESTS" != "true" ]]; then
        log_info "Skipping unit tests"
        return 0
    fi
    
    log_info "=== Running Unit Tests ==="
    
    run_test_suite "Unit Tests" "cd '$PROJECT_ROOT' && mvn test -Dgroups='!integration,!infrastructure'"
}

# Run integration tests
run_integration_tests() {
    if [[ "$RUN_INTEGRATION_TESTS" != "true" ]]; then
        log_info "Skipping integration tests"
        return 0
    fi
    
    log_info "=== Running Integration Tests ==="
    
    run_test_suite "Integration Tests" "cd '$PROJECT_ROOT' && mvn test -Dgroups=integration"
}

# Run infrastructure validation tests
run_infrastructure_tests() {
    if [[ "$RUN_INFRASTRUCTURE_TESTS" != "true" ]]; then
        log_info "Skipping infrastructure tests"
        return 0
    fi
    
    log_info "=== Running Infrastructure Tests ==="
    
    # Set environment variables for infrastructure tests
    export DOCKER_TEST_ENABLED="$RUN_DOCKER_TESTS"
    export KUBERNETES_TEST_ENABLED="$RUN_KUBERNETES_TESTS"
    export CICD_TEST_ENABLED="$RUN_CICD_TESTS"
    
    run_test_suite "Infrastructure Configuration Tests" "cd '$PROJECT_ROOT' && mvn test -Dtest=ConfigurationValidationTest"
    run_test_suite "Infrastructure Test Suite" "cd '$PROJECT_ROOT' && mvn test -Dtest=InfrastructureTestSuite"
}

# Run Docker tests
run_docker_tests() {
    if [[ "$RUN_DOCKER_TESTS" != "true" ]]; then
        log_info "Skipping Docker tests"
        return 0
    fi
    
    log_info "=== Running Docker Tests ==="
    
    export DOCKER_TEST_ENABLED=true
    run_test_suite "Docker Container Tests" "cd '$PROJECT_ROOT' && mvn test -Dtest=DockerContainerTest"
    
    # Run shell-based Docker tests
    run_test_suite "Docker Infrastructure Validation" "$SCRIPT_DIR/test-infrastructure.sh --docker-only" false
}

# Run Kubernetes tests
run_kubernetes_tests() {
    if [[ "$RUN_KUBERNETES_TESTS" != "true" ]]; then
        log_info "Skipping Kubernetes tests"
        return 0
    fi
    
    log_info "=== Running Kubernetes Tests ==="
    
    export KUBERNETES_TEST_ENABLED=true
    run_test_suite "Kubernetes Deployment Tests" "cd '$PROJECT_ROOT' && mvn test -Dtest=KubernetesDeploymentTest" false
    
    # Run shell-based Kubernetes tests
    run_test_suite "Kubernetes Infrastructure Validation" "$SCRIPT_DIR/test-infrastructure.sh --k8s-only" false
}

# Run CI/CD tests
run_cicd_tests() {
    if [[ "$RUN_CICD_TESTS" != "true" ]]; then
        log_info "Skipping CI/CD tests"
        return 0
    fi
    
    log_info "=== Running CI/CD Tests ==="
    
    export CICD_TEST_ENABLED=true
    run_test_suite "CI/CD Pipeline Tests" "cd '$PROJECT_ROOT' && mvn test -Dtest=CiCdPipelineTest"
    
    # Run shell-based CI/CD tests
    run_test_suite "CI/CD Infrastructure Validation" "$SCRIPT_DIR/test-infrastructure.sh --cicd-only"
}

# Generate test reports
generate_reports() {
    if [[ "$GENERATE_REPORTS" != "true" ]]; then
        log_info "Skipping report generation"
        return 0
    fi
    
    log_info "=== Generating Test Reports ==="
    
    run_test_suite "Surefire Reports" "cd '$PROJECT_ROOT' && mvn surefire-report:report" false
    run_test_suite "JaCoCo Coverage Report" "cd '$PROJECT_ROOT' && mvn jacoco:report" false
    
    # Copy reports to a consolidated location
    local reports_dir="$PROJECT_ROOT/target/infrastructure-test-reports"
    mkdir -p "$reports_dir"
    
    if [[ -d "$PROJECT_ROOT/target/site" ]]; then
        cp -r "$PROJECT_ROOT/target/site"/* "$reports_dir/" 2>/dev/null || true
    fi
    
    if [[ -d "$PROJECT_ROOT/target/surefire-reports" ]]; then
        cp -r "$PROJECT_ROOT/target/surefire-reports" "$reports_dir/" 2>/dev/null || true
    fi
    
    log_info "Test reports available in: $reports_dir"
}

# Main execution function
main() {
    log_info "Starting DMS Infrastructure Test Runner..."
    log_info "Log file: $LOG_FILE"
    log_info "Project root: $PROJECT_ROOT"
    
    parse_args "$@"
    
    log_info "Test Configuration:"
    log_info "  Unit Tests: $RUN_UNIT_TESTS"
    log_info "  Integration Tests: $RUN_INTEGRATION_TESTS"
    log_info "  Infrastructure Tests: $RUN_INFRASTRUCTURE_TESTS"
    log_info "  Docker Tests: $RUN_DOCKER_TESTS"
    log_info "  Kubernetes Tests: $RUN_KUBERNETES_TESTS"
    log_info "  CI/CD Tests: $RUN_CICD_TESTS"
    log_info "  Generate Reports: $GENERATE_REPORTS"
    log_info "  Fail Fast: $FAIL_FAST"
    
    check_prerequisites
    
    # Run test suites
    run_unit_tests
    run_integration_tests
    run_infrastructure_tests
    run_docker_tests
    run_kubernetes_tests
    run_cicd_tests
    
    # Generate reports
    generate_reports
    
    # Print summary
    log_info "=== Test Execution Summary ==="
    log_info "Total Test Suites: $TOTAL_TEST_SUITES"
    log_success "Passed: $PASSED_TEST_SUITES"
    log_error "Failed: $FAILED_TEST_SUITES"
    log_warning "Skipped: $SKIPPED_TEST_SUITES"
    
    # Calculate success rate
    if [[ $TOTAL_TEST_SUITES -gt 0 ]]; then
        SUCCESS_RATE=$(( (PASSED_TEST_SUITES * 100) / TOTAL_TEST_SUITES ))
        log_info "Success Rate: ${SUCCESS_RATE}%"
    fi
    
    # Exit with appropriate code
    if [[ $FAILED_TEST_SUITES -eq 0 ]]; then
        log_success "All infrastructure tests completed successfully!"
        exit 0
    else
        log_error "Some infrastructure tests failed. Check the log file for details: $LOG_FILE"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
