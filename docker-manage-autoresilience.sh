#!/bin/bash

# AutoResilience.com Domain Deployment - Docker Management Script
# This script manages the domain-based deployment for autoresilience.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

COMPOSE_FILE="docker-compose.aws-ec2.yml"
ENV_FILE=".env"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
}

# Function to check environment file
check_env_file() {
    if [ ! -f "$ENV_FILE" ]; then
        print_error "Environment file $ENV_FILE not found!"
        print_status "Please copy .env.autoresilience.com to $ENV_FILE and configure it:"
        print_status "cp .env.autoresilience.com $ENV_FILE"
        print_status "nano $ENV_FILE"
        exit 1
    fi
}

# Function to validate domain configuration
validate_domain_config() {
    print_header "Validating Domain Configuration"
    
    # Check if using autoresilience.com domains
    if ! grep -q "autoresilience.com" "$ENV_FILE"; then
        print_warning "Domain configuration doesn't contain autoresilience.com"
        print_warning "Please ensure your domains are properly configured"
    fi
    
    # Check CORS configuration
    if grep -q "CORS_ALLOWED_ORIGINS=.*app.autoresilience.com" "$ENV_FILE"; then
        print_status "CORS configuration looks good for autoresilience.com domain!"
    else
        print_warning "CORS configuration may need updating for your frontend domains"
    fi
    
    # Check base URLs
    if grep -q "DMS_BASE_URL=https://dms-service.autoresilience.com" "$ENV_FILE"; then
        print_status "DMS base URL configured correctly!"
    else
        print_warning "DMS base URL may need updating"
    fi
    
    if grep -q "NOTIFICATION_SERVICE_URL=https://notify-service.autoresilience.com" "$ENV_FILE"; then
        print_status "Notification service URL configured correctly!"
    else
        print_warning "Notification service URL may need updating"
    fi
}

# Function to start all services
start_all() {
    print_header "Starting AutoResilience.com Services - Domain Deployment"
    check_docker
    check_docker_compose
    check_env_file
    validate_domain_config
    
    print_status "Starting infrastructure services first..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d mysql redis rabbitmq elasticsearch
    
    print_status "Waiting for infrastructure services to be ready..."
    sleep 30
    
    print_status "Starting application services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d notification-svc dms-svc
    
    print_status "Starting monitoring services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d zipkin
    
    print_status "All services started successfully!"
    show_status
}

# Function to show service status
show_status() {
    print_header "AutoResilience.com Services Status"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    print_header "Service URLs"
    
    echo "Production Service URLs (HTTPS):"
    echo "  DMS Service GraphQL:        https://dms-service.autoresilience.com/dms/graphql"
    echo "  DMS Service GraphiQL:       https://dms-service.autoresilience.com/graphiql"
    echo "  Notification Service GraphQL: https://notify-service.autoresilience.com/graphql"
    echo "  Notification Service GraphiQL: https://notify-service.autoresilience.com/graphiql"
    echo ""
    
    # Get EC2 IP from environment file if available
    if [ -f "$ENV_FILE" ]; then
        EC2_IP=$(grep "AWS_EC2_PUBLIC_IP=" "$ENV_FILE" | cut -d'=' -f2)
        if [ -n "$EC2_IP" ] && [ "$EC2_IP" != "your-ec2-public-ip" ]; then
            echo "Direct Access URLs (HTTP - for testing only):"
            echo "  DMS Service:                http://$EC2_IP:9093/dms/graphql"
            echo "  Notification Service:       http://$EC2_IP:9091/graphql"
            echo ""
        fi
    fi
    
    echo "Infrastructure Services:"
    echo "  MySQL:                      localhost:3306"
    echo "  Redis:                      localhost:6379"
    echo "  RabbitMQ Management:        http://localhost:15672"
    echo "  Elasticsearch:              http://localhost:9200"
    echo ""
    echo "Monitoring Services:"
    echo "  Zipkin:                     http://localhost:9411"
}

# Function to test domain connectivity
test_domains() {
    print_header "Testing Domain Connectivity"
    
    echo "Testing DMS Service domain..."
    if curl -k -f https://dms-service.autoresilience.com/actuator/health > /dev/null 2>&1; then
        print_status "DMS Service domain: ACCESSIBLE"
    else
        print_error "DMS Service domain: NOT ACCESSIBLE"
        print_error "Check DNS configuration and SSL certificates"
    fi
    
    echo "Testing Notification Service domain..."
    if curl -k -f https://notify-service.autoresilience.com/actuator/health > /dev/null 2>&1; then
        print_status "Notification Service domain: ACCESSIBLE"
    else
        print_error "Notification Service domain: NOT ACCESSIBLE"
        print_error "Check DNS configuration and SSL certificates"
    fi
}

# Function to test GraphQL endpoints
test_graphql() {
    print_header "Testing GraphQL Endpoints"
    
    echo "Testing DMS GraphQL endpoint..."
    if curl -k -X POST \
        -H "Content-Type: application/json" \
        -d '{"query":"{ __schema { types { name } } }"}' \
        https://dms-service.autoresilience.com/dms/graphql > /dev/null 2>&1; then
        print_status "DMS GraphQL: WORKING"
    else
        print_error "DMS GraphQL: NOT WORKING"
    fi
    
    echo "Testing Notification GraphQL endpoint..."
    if curl -k -X POST \
        -H "Content-Type: application/json" \
        -d '{"query":"{ __schema { types { name } } }"}' \
        https://notify-service.autoresilience.com/graphql > /dev/null 2>&1; then
        print_status "Notification GraphQL: WORKING"
    else
        print_error "Notification GraphQL: NOT WORKING"
    fi
}

# Function to show logs
show_logs() {
    if [ -z "$2" ]; then
        print_header "Showing All Service Logs"
        docker-compose -f $COMPOSE_FILE logs -f
    else
        print_header "Showing Logs for: $2"
        docker-compose -f $COMPOSE_FILE logs -f "$2"
    fi
}

# Function to stop all services
stop_all() {
    print_header "Stopping All Services"
    docker-compose -f $COMPOSE_FILE down
    print_status "All services stopped!"
}

# Function to restart services
restart_service() {
    if [ -z "$2" ]; then
        print_header "Restarting All Services"
        docker-compose -f $COMPOSE_FILE restart
    else
        print_header "Restarting Service: $2"
        docker-compose -f $COMPOSE_FILE restart "$2"
    fi
    print_status "Restart completed!"
}

# Function to show help
show_help() {
    echo "AutoResilience.com Domain Deployment - Docker Management Script"
    echo ""
    echo "This script manages the domain-based deployment for:"
    echo "  - https://dms-service.autoresilience.com/"
    echo "  - https://notify-service.autoresilience.com/"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start-all          Start all services with domain configuration"
    echo "  status             Show service status and URLs"
    echo "  test-domains       Test domain connectivity and SSL"
    echo "  test-graphql       Test GraphQL endpoints"
    echo "  logs [SERVICE]     Show logs for all services or specific service"
    echo "  stop               Stop all services"
    echo "  restart [SERVICE]  Restart all services or specific service"
    echo "  help               Show this help message"
    echo ""
    echo "Prerequisites:"
    echo "  1. Copy .env.autoresilience.com to .env"
    echo "  2. Configure your specific settings in .env"
    echo "  3. Ensure DNS records point to your EC2 instance"
    echo "  4. Configure SSL certificates (ALB or Nginx)"
    echo ""
    echo "Examples:"
    echo "  $0 start-all                 # Start everything"
    echo "  $0 test-domains              # Test domain connectivity"
    echo "  $0 test-graphql              # Test GraphQL endpoints"
    echo "  $0 logs notification-svc     # Show notification service logs"
    echo "  $0 restart dms-svc           # Restart DMS service"
}

# Main script logic
case "$1" in
    "start-all")
        start_all
        ;;
    "status")
        show_status
        ;;
    "test-domains")
        test_domains
        ;;
    "test-graphql")
        test_graphql
        ;;
    "logs")
        show_logs "$@"
        ;;
    "stop")
        stop_all
        ;;
    "restart")
        restart_service "$@"
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        print_error "No command specified. Use '$0 help' for usage information."
        exit 1
        ;;
    *)
        print_error "Unknown command: $1. Use '$0 help' for usage information."
        exit 1
        ;;
esac
