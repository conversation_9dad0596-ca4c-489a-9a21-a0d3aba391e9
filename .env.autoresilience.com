# AutoResilience.com Domain Configuration
# Production environment configuration for autoresilience.com domain

# =============================================================================
# AWS EC2 CONFIGURATION
# =============================================================================
AWS_EC2_PUBLIC_IP=your-ec2-public-ip
AWS_EC2_DOMAIN=autoresilience.com
AWS_EC2_REGION=us-east-1

# =============================================================================
# CORS CONFIGURATION - CRITICAL FOR FRONTEND ACCESS
# =============================================================================
# Configure your frontend domains here
CORS_ALLOWED_ORIGINS=https://app.autoresilience.com,https://www.autoresilience.com,https://admin.autoresilience.com

# Alternative: If you need subdomain support
# CORS_ALLOWED_ORIGIN_PATTERNS=https://*.autoresilience.com,https://autoresilience.com

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Service URLs - Using custom domains with HTTPS
DMS_BASE_URL=https://dms-service.autoresilience.com
NOTIFICATION_SERVICE_URL=https://notify-service.autoresilience.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MYSQL_ROOT_PASSWORD=your-secure-root-password-here
MYSQL_DMS_DATABASE=dms_db
MYSQL_DMS_USER=dms_user
MYSQL_DMS_PASSWORD=your-secure-dms-password-here
MYSQL_NOTIFICATION_DATABASE=notification_db
MYSQL_NOTIFICATION_USER=notification_user
MYSQL_NOTIFICATION_PASSWORD=your-secure-notification-password-here

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=your-secure-redis-password-here

# =============================================================================
# RABBITMQ CONFIGURATION
# =============================================================================
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=your-secure-rabbitmq-password-here

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
# Generate a secure JWT secret: openssl rand -base64 64
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-256-bits-long-for-production-use

# =============================================================================
# EMAIL CONFIGURATION (for Notification Service)
# =============================================================================
# Gmail Configuration (requires App Password)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-gmail-app-password

# Email Settings - Using autoresilience.com domain
NOTIFICATION_FROM_EMAIL=<EMAIL>
EMAIL_ENABLED=true
EMAIL_MOCK=false

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
DMS_STORAGE_PROVIDER=LOCAL
DMS_STORAGE_LOCAL_BASE_PATH=/app/storage

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Grafana
GF_SECURITY_ADMIN_PASSWORD=your-secure-grafana-password

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Disable GraphiQL in production
GRAPHIQL_ENABLED=false

# Logging Levels
DMS_LOG_LEVEL=INFO
NOTIFICATION_LOG_LEVEL=INFO

# JVM Options for production
DMS_JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
NOTIFICATION_JAVA_OPTS=-Xmx1g -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Database Connection Pool
DB_CONNECTION_POOL_SIZE=20
DB_CONNECTION_TIMEOUT=30000

# Redis Connection Pool
REDIS_POOL_MAX_ACTIVE=20
REDIS_POOL_MAX_IDLE=10
REDIS_POOL_MIN_IDLE=2

# Cache TTL (seconds)
CACHE_TTL=3600

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# DMS Features
DMS_VIRUS_SCANNING_ENABLED=true
DMS_ELASTICSEARCH_ENABLED=false

# Notification Features
NOTIFICATION_WEBHOOK_ENABLED=true
NOTIFICATION_BATCH_PROCESSING_ENABLED=true
