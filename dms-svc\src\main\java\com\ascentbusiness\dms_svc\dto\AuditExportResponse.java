package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ExportFormat;
import com.ascentbusiness.dms_svc.enums.ExportStatus;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * Response DTO for audit export operations
 * Used in GraphQL audit export mutations and REST controller
 */
@Data
public class AuditExportResponse {
    private String exportId;
    private ExportStatus status;
    private String fileName;
    private String downloadUrl;
    private Long fileSize;
    private Integer recordCount;
    private String requestedBy;
    private OffsetDateTime requestedAt;
    private OffsetDateTime completedAt;
    private OffsetDateTime expiresAt;
    private ExportFormat format;
    private String message;
    private String errorDetails;
}
