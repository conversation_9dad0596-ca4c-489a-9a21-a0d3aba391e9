package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for file validation error.
 * Corresponds to FileValidationError GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileValidationError {

    /**
     * Error code for categorization.
     */
    private String code;

    /**
     * Human-readable error message.
     */
    private String message;

    /**
     * Severity of the validation error.
     */
    private ValidationSeverity severity;

    /**
     * The field that caused the error (optional).
     */
    private String field;
}
