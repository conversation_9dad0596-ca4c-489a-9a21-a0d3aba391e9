--liquibase formatted sql

--changeset system:064-fix-template-tables-audit-columns
--comment: Fix audit columns for template tables that extend BaseEntity

-- Fix template_fields table - add missing BaseEntity audit columns
ALTER TABLE template_fields 
ADD COLUMN created_by VA<PERSON><PERSON><PERSON>(100) NOT NULL DEFAULT 'system' AFTER last_modified_date,
ADD COLUMN last_modified_by VARCHAR(100) DEFAULT NULL AFTER created_by;

-- Fix template_versions table - add missing BaseEntity audit columns
ALTER TABLE template_versions 
ADD COLUMN last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_by,
ADD COLUMN last_modified_by VA<PERSON><PERSON><PERSON>(100) DEFAULT NULL AFTER last_modified_date;

-- Fix template_usage_history table - add missing BaseEntity audit columns
ALTER TABLE template_usage_history 
ADD COLUMN created_by <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL DEFAULT 'system' AFTER usage_date,
ADD COLUMN created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER created_by,
ADD COLUMN last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_date,
ADD COLUMN last_modified_by VARCHAR(100) DEFAULT NULL AFTER last_modified_date;

-- Fix template_permissions table - add missing BaseEntity audit columns  
ALTER TABLE template_permissions 
ADD COLUMN created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER is_active,
ADD COLUMN last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_date,
ADD COLUMN last_modified_by VARCHAR(100) DEFAULT NULL AFTER last_modified_date;

-- Fix template_categories table - update column lengths to match BaseEntity expectations
ALTER TABLE template_categories 
MODIFY COLUMN created_by VARCHAR(100) NOT NULL,
MODIFY COLUMN last_modified_by VARCHAR(100) DEFAULT NULL;

-- Update existing records to have proper audit values
UPDATE template_fields SET created_by = 'system' WHERE created_by IS NULL OR created_by = '';
UPDATE template_versions SET last_modified_date = created_date WHERE last_modified_date IS NULL;
UPDATE template_usage_history SET created_by = 'system', created_date = usage_date WHERE created_by IS NULL OR created_by = '';
UPDATE template_permissions SET created_date = granted_date WHERE created_date IS NULL;