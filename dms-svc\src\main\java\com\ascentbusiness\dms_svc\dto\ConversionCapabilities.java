package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * DTO for conversion capabilities information from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionCapabilities {
    private Boolean pandocAvailable;
    private String pandocVersion;
    private List<String> supportedInputFormats;
    private List<String> supportedOutputFormats;
    private Long maxFileSize;
    private Integer maxBatchSize;
    private Integer concurrentConversions;
    private Integer queueCapacity;
}
