-- H2 Test Database Schema
-- This file creates the necessary tables for H2 in-memory database used in tests

-- Create documents table
CREATE TABLE IF NOT EXISTS documents (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    original_file_name VA<PERSON>HAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    storage_provider VARCHAR(20) NOT NULL,
    storage_path VARCHAR(500) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    version INT NOT NULL DEFAULT 1,
    tags JSO<PERSON>,
    file_content LONGBLOB,
    creator_user_id VARCHAR(100) NOT NULL,
    creator_roles JSON,
    creator_permissions JSON,
    last_modifier_roles JSON,
    last_modifier_permissions JSON,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    last_modified_by VARCHAR(100),
    parent_document_id BIGINT,
    -- Compliance fields
    compliance_classification_level VARCHAR(50),
    storage_region VARCHAR(50),
    requires_consent BOOLEAN NOT NULL DEFAULT FALSE,
    consent_reference VARCHAR(200),
    is_anonymized BOOLEAN NOT NULL DEFAULT FALSE,
    is_pseudonymized BOOLEAN NOT NULL DEFAULT FALSE,
    encryption_status VARCHAR(50),
    compliance_notes TEXT,
    -- Retention policy fields
    retention_policy_id BIGINT,
    retention_expiry_date TIMESTAMP,
    -- Legal hold fields
    legal_hold_status VARCHAR(20),
    legal_hold_reason TEXT,
    legal_hold_applied_by VARCHAR(100),
    legal_hold_applied_date TIMESTAMP,
    -- Disposition fields
    disposition_status VARCHAR(20),
    disposition_notes TEXT,
    disposition_review_date TIMESTAMP,
    -- Workflow fields
    current_workflow_instance_id BIGINT,
    workflow_status VARCHAR(20),
    workflow_completion_date TIMESTAMP,
    -- Template fields
    source_template_id BIGINT,
    template_field_values JSON,
    FOREIGN KEY (parent_document_id) REFERENCES documents(id)
);

-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT,
    user_id VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    correlation_id VARCHAR(100),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Enhanced GRC fields
    event_type VARCHAR(50) DEFAULT 'DOCUMENT_OPERATION',
    event_category VARCHAR(50) DEFAULT 'GENERAL',
    compliance_framework_id BIGINT,
    regulation_reference VARCHAR(255),
    data_subject_category VARCHAR(50),
    geographic_region VARCHAR(50),
    risk_level VARCHAR(20) DEFAULT 'LOW',
    business_impact VARCHAR(20) DEFAULT 'LOW',
    technical_details JSON,
    before_state JSON,
    after_state JSON,
    session_id VARCHAR(100),
    request_id VARCHAR(100),
    client_info JSON,
    -- Tamper-proof fields
    hash_value VARCHAR(128),
    previous_hash VARCHAR(128),
    chain_sequence BIGINT,
    digital_signature TEXT,
    signature_algorithm VARCHAR(50),
    signature_timestamp TIMESTAMP,
    verification_status VARCHAR(20) DEFAULT 'PENDING',
    is_tampered BOOLEAN DEFAULT FALSE,
    tamper_detection_date TIMESTAMP,
    -- Retention and archival fields
    retention_period_days INT,
    archive_date TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE,
    export_count INT DEFAULT 0,
    last_export_date TIMESTAMP,
    -- Encryption fields
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key_id VARCHAR(100),
    encryption_iv VARCHAR(200),
    encrypted_details TEXT,
    encrypted_technical_details TEXT,
    encrypted_before_state TEXT,
    encrypted_after_state TEXT,
    encrypted_client_info TEXT,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create document_permissions table
CREATE TABLE IF NOT EXISTS document_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    user_id VARCHAR(100),
    role_name VARCHAR(100),
    permission_type VARCHAR(20) NOT NULL,
    granted_by VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP,
    notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    last_modified_by VARCHAR(100),
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create document_versions table
CREATE TABLE IF NOT EXISTS document_versions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    version_number INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    storage_path VARCHAR(500) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    file_content LONGBLOB,
    checksum VARCHAR(128),
    version_notes TEXT,
    is_current BOOLEAN NOT NULL DEFAULT FALSE,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create document_classification_metadata table
CREATE TABLE IF NOT EXISTS document_classification_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    classification_level VARCHAR(50) NOT NULL,
    security_tags JSON,
    access_restrictions TEXT,
    retention_period_days INT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create document_ownership_metadata table
CREATE TABLE IF NOT EXISTS document_ownership_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    owner_user_id VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    business_unit VARCHAR(100),
    cost_center VARCHAR(50),
    project_code VARCHAR(50),
    lifecycle_stage VARCHAR(50),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create document_compliance_metadata table
CREATE TABLE IF NOT EXISTS document_compliance_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    compliance_framework VARCHAR(100),
    regulatory_requirements JSON,
    audit_trail_required BOOLEAN DEFAULT FALSE,
    data_residency_requirements VARCHAR(100),
    encryption_required BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create storage_configurations table
CREATE TABLE IF NOT EXISTS storage_configurations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    provider_type VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    configuration_json TEXT NOT NULL,
    description TEXT,
    priority INT DEFAULT 0,
    health_check_enabled BOOLEAN DEFAULT TRUE,
    health_status VARCHAR(20),
    last_health_check TIMESTAMP,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create security_config table
CREATE TABLE IF NOT EXISTS security_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create security_violations table
CREATE TABLE IF NOT EXISTS security_violations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    violation_type VARCHAR(50) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    document_id BIGINT,
    severity VARCHAR(20) DEFAULT 'MEDIUM',
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by VARCHAR(100),
    resolved_date TIMESTAMP,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create document_data_subject_categories table for ElementCollection
CREATE TABLE IF NOT EXISTS document_data_subject_categories (
    document_id BIGINT NOT NULL,
    data_subject_category VARCHAR(50) NOT NULL,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create retention_policies table
CREATE TABLE IF NOT EXISTS retention_policies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    retention_period_days INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create compliance_frameworks table
CREATE TABLE IF NOT EXISTS compliance_frameworks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create regulation_mappings table
CREATE TABLE IF NOT EXISTS regulation_mappings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    regulation_name VARCHAR(200) NOT NULL,
    compliance_requirement TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create document_compliance_mappings table
CREATE TABLE IF NOT EXISTS document_compliance_mappings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    compliance_framework_id BIGINT NOT NULL,
    classification_level VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id),
    FOREIGN KEY (compliance_framework_id) REFERENCES compliance_frameworks(id)
);

-- Create audit_verification_log table
CREATE TABLE IF NOT EXISTS audit_verification_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    verification_id VARCHAR(100) NOT NULL UNIQUE,
    audit_log_id BIGINT,
    chain_id VARCHAR(100),
    verification_type VARCHAR(50) NOT NULL,
    verification_date TIMESTAMP,
    verified_by VARCHAR(100),
    verification_method VARCHAR(50),
    expected_hash VARCHAR(128),
    actual_hash VARCHAR(128),
    hash_match BOOLEAN,
    signature_valid BOOLEAN,
    chain_integrity_valid BOOLEAN,
    overall_status VARCHAR(20),
    anomalies_detected JSON,
    verification_details JSON,
    correlation_id VARCHAR(100),
    created_date TIMESTAMP,
    FOREIGN KEY (audit_log_id) REFERENCES audit_logs(id)
);

-- Create audit_chain_metadata table
CREATE TABLE IF NOT EXISTS audit_chain_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chain_id VARCHAR(100) NOT NULL UNIQUE,
    chain_name VARCHAR(255) NOT NULL,
    description TEXT,
    hash_algorithm VARCHAR(50) NOT NULL DEFAULT 'SHA-256',
    signature_algorithm VARCHAR(50) NOT NULL DEFAULT 'RSA-2048',
    current_sequence BIGINT NOT NULL DEFAULT 0,
    genesis_hash VARCHAR(128) NOT NULL,
    last_block_hash VARCHAR(128),
    last_block_timestamp TIMESTAMP,
    total_entries BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    verification_key TEXT,
    signing_key_fingerprint VARCHAR(64),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_documents_creator ON documents(creator_user_id);
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_documents_storage_provider ON documents(storage_provider);
CREATE INDEX IF NOT EXISTS idx_documents_created_date ON documents(created_date);

CREATE INDEX IF NOT EXISTS idx_audit_logs_document_id ON audit_logs(document_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_correlation_id ON audit_logs(correlation_id);

CREATE INDEX IF NOT EXISTS idx_document_permissions_document_id ON document_permissions(document_id);
CREATE INDEX IF NOT EXISTS idx_document_permissions_user_id ON document_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_document_permissions_role_name ON document_permissions(role_name);
CREATE INDEX IF NOT EXISTS idx_document_permissions_is_active ON document_permissions(is_active);

CREATE INDEX IF NOT EXISTS idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX IF NOT EXISTS idx_document_versions_version_number ON document_versions(version_number);
CREATE INDEX IF NOT EXISTS idx_document_versions_is_current ON document_versions(is_current);

CREATE INDEX IF NOT EXISTS idx_classification_metadata_document_id ON document_classification_metadata(document_id);
CREATE INDEX IF NOT EXISTS idx_ownership_metadata_document_id ON document_ownership_metadata(document_id);
CREATE INDEX IF NOT EXISTS idx_compliance_metadata_document_id ON document_compliance_metadata(document_id);

CREATE INDEX IF NOT EXISTS idx_security_config_key ON security_config(config_key);
CREATE INDEX IF NOT EXISTS idx_security_config_active ON security_config(is_active);

CREATE INDEX IF NOT EXISTS idx_security_violations_user_id ON security_violations(user_id);
CREATE INDEX IF NOT EXISTS idx_security_violations_type ON security_violations(violation_type);
CREATE INDEX IF NOT EXISTS idx_security_violations_document_id ON security_violations(document_id);
CREATE INDEX IF NOT EXISTS idx_security_violations_resolved ON security_violations(is_resolved);
