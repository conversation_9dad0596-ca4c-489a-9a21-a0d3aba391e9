spring.application.name=notification-svc
server.port=9091

# Database Configuration
spring.datasource.url=*******************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Enable JPA Auditing
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

# Flyway Configuration
spring.flyway.enabled=false
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration

# GraphQL Configuration
spring.graphql.graphiql.enabled=true
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.printer.enabled=true

# Logging Configuration
logging.level.com.ascentbusiness.notification_svc=DEBUG
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=INFO
logging.level.org.springframework.security=DEBUG

# RabbitMQ Configuration
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.cache.type=redis
spring.cache.redis.time-to-live=3600

# Email Configuration Options:

# Option 1: Gmail (Requires App Password)
# To use Gmail, you need to:
# 1. Enable 2-Factor Authentication
# 2. Generate an App Password at: https://myaccount.google.com/apppasswords
# 3. Use the App Password instead of your regular password
#spring.mail.host=smtp.gmail.com
#spring.mail.port=587
#spring.mail.username=<EMAIL>
#spring.mail.password=your-app-password-here
#spring.mail.properties.mail.smtp.auth=true
#spring.mail.properties.mail.smtp.starttls.enable=true

# Option 2: Mailtrap (For Testing - emails won't be delivered to real addresses)
# Sign up at https://mailtrap.io for free testing account
#spring.mail.host=sandbox.smtp.mailtrap.io
#spring.mail.port=2525
#spring.mail.username=your-mailtrap-username
#spring.mail.password=your-mailtrap-password
#spring.mail.properties.mail.smtp.auth=true
#spring.mail.properties.mail.smtp.starttls.enable=true

# Option 3: Outlook/Hotmail
#spring.mail.host=smtp-mail.outlook.com
#spring.mail.port=587
#spring.mail.username=<EMAIL>
#spring.mail.password=your-password
#spring.mail.properties.mail.smtp.auth=true
#spring.mail.properties.mail.smtp.starttls.enable=true

# Email Configuration (ascentbusiness.com) - Disabled for now
spring.mail.host=email-smtp.ap-south-1.amazonaws.com
spring.mail.port=587
spring.mail.username=AKIA2KPAUIIKU4IGE2PD
spring.mail.password=BFHjDlCJ0dDtC7JaVd2HDY9hY5d8jY0eLTToxP/OWrl5
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Email sender configuration
#notification.email.from=<EMAIL>

# Java 21 Performance Optimizations
# Enable virtual threads for better concurrency (requires Java 21+)
spring.threads.virtual.enabled=true

# Notification Configuration
notification.email.templates.path=classpath:templates/email/

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.health.diskspace.enabled=true
management.health.db.enabled=true
management.health.redis.enabled=true
management.metrics.export.prometheus.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true

# JWT Security Configuration
jwt.public-key.path=classpath:keys/public_key.pem
jwt.issuer=notification-service
jwt.audience=notification-clients
jwt.token.expiration=3600

# Security Configuration
security.jwt.enabled=true
#security.cors.allowed-origins=http://localhost:4200,http://localhost:3000
security.cors.allowed-origins=*
security.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
security.cors.allowed-headers=*
security.cors.allow-credentials=true

spring.main.allow-bean-definition-overriding=true

