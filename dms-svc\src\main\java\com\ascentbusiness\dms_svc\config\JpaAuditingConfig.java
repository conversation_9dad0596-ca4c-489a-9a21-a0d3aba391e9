package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.security.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.lang.NonNull;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class JpaAuditingConfig {

    @Bean
    public AuditorAware<String> auditorProvider() {
        return new SpringSecurityAuditorAware();
    }

    public static class SpringSecurityAuditorAware implements AuditorAware<String> {
        
        private static final Logger logger = LoggerFactory.getLogger(SpringSecurityAuditorAware.class);
        @Override
        @NonNull
        public Optional<String> getCurrentAuditor() {
            logger.debug("JPA Auditing: getCurrentAuditor() called");
            
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            logger.debug("JPA Auditing: Authentication object: {}", authentication);
            
            if (authentication == null) {
                logger.warn("JPA Auditing: Authentication is null, returning 'system'");
                return Optional.of("system");
            }
            
            if (!authentication.isAuthenticated()) {
                logger.warn("JPA Auditing: Authentication not authenticated, returning 'system'");
                return Optional.of("system");
            }
            
            logger.debug("JPA Auditing: Authentication principal type: {}", 
                    authentication.getPrincipal() != null ? authentication.getPrincipal().getClass().getName() : "null");
            
            if (authentication.getPrincipal() instanceof UserPrincipal) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                String username = userPrincipal.getUsername();
                logger.info("JPA Auditing: Found UserPrincipal, username: {}", username);
                return Optional.of(username);
            }
            
            if (authentication.getPrincipal() instanceof String) {
                String username = (String) authentication.getPrincipal();
                logger.info("JPA Auditing: Found String principal, username: {}", username);
                return Optional.of(username);
            }
            
            logger.warn("JPA Auditing: Unknown principal type, returning 'system'. Principal: {}", 
                    authentication.getPrincipal());
            return Optional.of("system");
        }
    }
}
