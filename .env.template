# GRC Platform v4 - Environment Configuration Template
# Copy this file to .env and customize the values for your environment

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DMS_DATABASE=dms_db
MYSQL_DMS_USER=dms_user
MYSQL_DMS_PASSWORD=dms_password
MYSQL_NOTIFICATION_DATABASE=notification_db
MYSQL_NOTIFICATION_USER=notification_user
MYSQL_NOTIFICATION_PASSWORD=notification_password

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=shared_redis_password

# =============================================================================
# RABBITMQ CONFIGURATION
# =============================================================================
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin123

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ

# =============================================================================
# EMAIL CONFIGURATION (for Notification Service)
# =============================================================================
# Gmail Configuration (requires App Password)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Outlook/Hotmail Configuration
# MAIL_HOST=smtp-mail.outlook.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-password

# Custom SMTP Configuration
# MAIL_HOST=your-smtp-server.com
# MAIL_PORT=587
# MAIL_USERNAME=your-username
# MAIL_PASSWORD=your-password

# Email Settings
NOTIFICATION_FROM_EMAIL=<EMAIL>
EMAIL_ENABLED=true
EMAIL_MOCK=false

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# DMS Service
DMS_BASE_URL=http://localhost:9093
DMS_STORAGE_PROVIDER=LOCAL
DMS_STORAGE_LOCAL_BASE_PATH=/app/storage

# Notification Service
NOTIFICATION_SERVICE_URL=http://localhost:9091

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Grafana
GF_SECURITY_ADMIN_PASSWORD=admin

# OpenTelemetry
OTEL_SERVICE_NAME=grc-platform
OTEL_SERVICE_VERSION=1.0.0
OTEL_RESOURCE_ATTRIBUTES_DEPLOYMENT_ENVIRONMENT=docker

# =============================================================================
# DEVELOPMENT/DEBUG CONFIGURATION
# =============================================================================
# Enable/Disable GraphiQL
GRAPHIQL_ENABLED=true

# Logging Levels
DMS_LOG_LEVEL=INFO
NOTIFICATION_LOG_LEVEL=INFO

# JVM Options
DMS_JAVA_OPTS=-Xmx2g -Xms1g
NOTIFICATION_JAVA_OPTS=-Xmx1g -Xms512m

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS Configuration
CORS_ALLOWED_ORIGIN_PATTERNS=*

# Security Features
SECURITY_JWT_ENABLED=true

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Database Connection Pool
DB_CONNECTION_POOL_SIZE=20
DB_CONNECTION_TIMEOUT=30000

# Redis Connection Pool
REDIS_POOL_MAX_ACTIVE=20
REDIS_POOL_MAX_IDLE=10
REDIS_POOL_MIN_IDLE=2

# Cache TTL (seconds)
CACHE_TTL=3600

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# DMS Features
DMS_VIRUS_SCANNING_ENABLED=true
DMS_ELASTICSEARCH_ENABLED=false

# Notification Features
NOTIFICATION_WEBHOOK_ENABLED=true
NOTIFICATION_BATCH_PROCESSING_ENABLED=true
