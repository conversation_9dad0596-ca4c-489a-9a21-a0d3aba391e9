# Tracing Operations Schema
# Defines GraphQL operations for tracing and span management

# Tracing Types
type TracingResult {
  message: String!
  traceId: String!
  spanId: String!
  timestamp: String!
  status: String
  tags: [TracingTag!]
  duration: Long
}

type TracingTag {
  key: String!
  value: String!
}

type NestedSpanResult {
  message: String!
  traceId: String!
  parentSpanId: String
  childSpans: [SpanInfo!]!
  totalSpans: Int!
  executionTime: Long
}

type SpanInfo {
  spanId: String!
  operationName: String!
  startTime: String
  endTime: String
  duration: Long
  tags: [TracingTag!]
}

type CustomTraceResult {
  traceId: String!
  spanId: String!
  startTime: String!
  operationName: String!
}

type TraceEndResult {
  success: Boolean!
  duration: Long!
  endTime: String!
  summary: String
}

# Input Types
input TracingTestInput {
  operationName: String!
  tags: [TracingTagInput!]
  timeout: Int
}

input TracingTagInput {
  key: String!
  value: String!
}

input NestedSpanInput {
  parentOperationName: String!
  childOperations: [ChildOperationInput!]!
}

input ChildOperationInput {
  operationName: String!
  step: String!
  delay: Int
}

# Extend Query type to add tracing operations
extend type Query {
  # Test tracing functionality
  testTracing(input: TracingTestInput!): TracingResult!
  
  # Test nested spans
  testNestedSpans(input: NestedSpanInput!): NestedSpanResult!
  
  # Test tracing with error handling
  testTracingWithError(input: TracingTestInput!): TracingResult!
}

# Extend Mutation type to add tracing operations
extend type Mutation {
  # Start custom trace
  startCustomTrace(input: TracingTestInput!): CustomTraceResult!
  
  # End custom trace
  endCustomTrace(traceId: String!, spanId: String!): TraceEndResult!
}
