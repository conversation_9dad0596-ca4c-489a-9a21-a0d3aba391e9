package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.interceptor.CorrelationIdInterceptor;
import com.ascentbusiness.dms_svc.resolver.*;
import com.ascentbusiness.dms_svc.service.*;
import graphql.ExecutionResult;
import graphql.execution.instrumentation.ChainedInstrumentation;
import graphql.execution.instrumentation.Instrumentation;
import graphql.execution.instrumentation.InstrumentationContext;
import graphql.execution.instrumentation.parameters.InstrumentationExecutionParameters;
import graphql.execution.instrumentation.InstrumentationState;
// import graphql.execution.instrumentation.dataloader.DataLoaderDispatcherInstrumentation; // Not available in current GraphQL version
import graphql.scalars.ExtendedScalars;
import graphql.schema.GraphQLScalarType;
import graphql.schema.Coercing;
import graphql.schema.CoercingParseLiteralException;
import graphql.schema.CoercingParseValueException;
import graphql.schema.CoercingSerializeException;
import lombok.extern.slf4j.Slf4j;
// import org.dataloader.DataLoader; // DataLoader dependency not available
// import org.dataloader.DataLoaderRegistry; // DataLoader dependency not available
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.graphql.execution.RuntimeWiringConfigurer;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

/**
 * GraphQL Configuration for DMS Service.
 *
 * This configuration class sets up:
 * - Custom scalar types (Upload, DateTime, Long)
 * - Performance monitoring instrumentation
 * - Security instrumentation
 * - Correlation ID tracking
 *
 * New GraphQL Resolvers (automatically registered by Spring Boot):
 * - DocumentUploadGraphQLResolver: Replaces DocumentRestController
 * - ConversionGraphQLResolver: Replaces MarkdownConversionController
 * - DiagnosticsGraphQLResolver: Replaces SharePointTestController
 * - TestCaseGraphQLResolver: Replaces TestCaseController
 * - TracingGraphQLResolver: Replaces TracingTestController
 *
 * Note: DataLoader patterns are planned for future implementation to prevent N+1 queries.
 */
@Configuration
@Slf4j
public class GraphQlConfig {

    @Autowired
    private CorrelationIdInterceptor correlationIdInterceptor;

    // @Autowired
    // private GraphQLRateLimitInterceptor rateLimitInterceptor;

    // @Autowired
    // private GraphQLVersioningInterceptor versioningInterceptor;

    @Bean
    public GraphQLScalarType uploadScalar() {
        return GraphQLScalarType.newScalar()
                .name("Upload")
                .description("A file upload scalar")
                .coercing(new Coercing<MultipartFile, String>() {
                    @Override
                    public String serialize(Object dataFetcherResult) throws CoercingSerializeException {
                        if (dataFetcherResult instanceof MultipartFile) {
                            return ((MultipartFile) dataFetcherResult).getOriginalFilename();
                        }
                        throw new CoercingSerializeException("Unable to serialize " + dataFetcherResult + " as an Upload");
                    }

                    @Override
                    public MultipartFile parseValue(Object input) throws CoercingParseValueException {
                        if (input instanceof MultipartFile) {
                            return (MultipartFile) input;
                        }
                        throw new CoercingParseValueException("Unable to parse value " + input + " as an Upload");
                    }

                    @Override
                    public MultipartFile parseLiteral(Object input) throws CoercingParseLiteralException {
                        throw new CoercingParseLiteralException("Upload scalar cannot be parsed from literal");
                    }
                })
                .build();
    }

    @Bean
    public RuntimeWiringConfigurer runtimeWiringConfigurer() {
        return wiringBuilder -> {
            try {
                wiringBuilder
                    .scalar(ExtendedScalars.GraphQLLong)
                    .scalar(ExtendedScalars.DateTime)
                    .scalar(ExtendedScalars.Json)
                    .scalar(uploadScalar());
            } catch (Exception e) {
                // Fallback: try to register scalars individually
                try {
                    wiringBuilder.scalar(ExtendedScalars.GraphQLLong);
                } catch (Exception ignored) {}

                try {
                    wiringBuilder.scalar(ExtendedScalars.DateTime);
                } catch (Exception ignored) {}

                try {
                    wiringBuilder.scalar(ExtendedScalars.Json);
                } catch (Exception ignored) {}

                try {
                    wiringBuilder.scalar(uploadScalar());
                } catch (Exception ignored) {
                    // Upload scalar registration failed
                }
            }
        };
    }





    // ===== RESOLVER REGISTRATION AND PERFORMANCE MONITORING =====

    /**
     * Register all GraphQL resolvers and configure performance monitoring.
     * This ensures all new resolvers are properly registered with the GraphQL engine.
     *
     * Note: Resolvers are automatically discovered by Spring Boot's @Controller annotation,
     * but we can add explicit registration here if needed for custom configuration.
     */

    /**
     * Performance monitoring instrumentation for GraphQL operations.
     * Integrates with GraphQLMonitoringConfig to collect metrics automatically.
     */
    @Bean
    public Instrumentation performanceMonitoringInstrumentation(GraphQLMonitoringConfig monitoringConfig) {
        log.info("Configuring GraphQL performance monitoring instrumentation with automatic metrics collection");

        return new Instrumentation() {
            @Override
            public InstrumentationContext<ExecutionResult> beginExecution(
                    InstrumentationExecutionParameters parameters,
                    InstrumentationState state) {

                long startTime = System.currentTimeMillis();
                String operationName = extractOperationName(parameters.getQuery());

                // Record query start
                monitoringConfig.recordQueryStart();

                return new InstrumentationContext<ExecutionResult>() {
                    @Override
                    public void onCompleted(ExecutionResult result, Throwable t) {
                        long executionTime = System.currentTimeMillis() - startTime;
                        boolean hasErrors = result.getErrors() != null && !result.getErrors().isEmpty();

                        // Record query completion and execution time
                        monitoringConfig.recordQueryEnd(hasErrors);
                        monitoringConfig.recordExecutionTime(operationName, executionTime, hasErrors);

                        log.debug("GraphQL monitoring recorded - Operation: {}, Duration: {}ms, HasErrors: {}",
                                operationName, executionTime, hasErrors);
                    }

                    @Override
                    public void onDispatched() {
                        // Called when execution is dispatched to another thread
                    }
                };
            }

            private String extractOperationName(String query) {
                if (query == null || query.trim().isEmpty()) {
                    return "anonymous";
                }

                // Simple operation name extraction
                String trimmed = query.trim();
                if (trimmed.startsWith("query")) {
                    // Extract operation name from query
                    String[] parts = trimmed.split("\\s+");
                    if (parts.length > 1 && !parts[1].equals("{")) {
                        return parts[1].replaceAll("[{}]", "");
                    }
                    return "query";
                } else if (trimmed.startsWith("mutation")) {
                    // Extract operation name from mutation
                    String[] parts = trimmed.split("\\s+");
                    if (parts.length > 1 && !parts[1].equals("{")) {
                        return parts[1].replaceAll("[{}]", "");
                    }
                    return "mutation";
                } else if (trimmed.startsWith("subscription")) {
                    return "subscription";
                } else {
                    return "anonymous";
                }
            }
        };
    }

    /**
     * Security and validation instrumentation for GraphQL operations.
     * Can be extended to add resolver-level security annotations.
     */
    @Bean
    public Instrumentation securityInstrumentation() {
        log.info("Configuring GraphQL security instrumentation");

        return new Instrumentation() {
            // Security instrumentation can be added here
            // Example: JWT validation, role-based access control, etc.
        };
    }

    @Bean
    public Instrumentation correlationIdInstrumentation() {
        return correlationIdInterceptor;
    }

    // @Bean
    // public Instrumentation rateLimitInstrumentation() {
    //     return rateLimitInterceptor;
    // }

    // @Bean
    // public Instrumentation versioningInstrumentation() {
    //     return versioningInterceptor;
    // }

    @Bean
    public Instrumentation chainedInstrumentation(GraphQLMonitoringConfig monitoringConfig) {
        log.info("Configuring chained GraphQL instrumentation with {} instrumentations", 3);

        return new ChainedInstrumentation(Arrays.asList(
            correlationIdInterceptor,
            performanceMonitoringInstrumentation(monitoringConfig),
            securityInstrumentation()
            // rateLimitInterceptor - can be added when implemented
            // versioningInterceptor - can be added when implemented
        ));
    }


}
