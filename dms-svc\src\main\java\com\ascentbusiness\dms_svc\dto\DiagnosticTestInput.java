package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.DiagnosticTestType;
import lombok.Data;

import java.util.List;

/**
 * Diagnostic test input DTO from diagnostics-schema.graphqls.
 */
@Data
public class DiagnosticTestInput {
    private String testSuite;
    private List<DiagnosticTestType> testTypes;
    private Boolean includePerformanceTests = false;
    private Boolean includeConnectivityTests = true;
    private Boolean includeSecurityTests = false;
    private Integer timeout = 30; // seconds
}
