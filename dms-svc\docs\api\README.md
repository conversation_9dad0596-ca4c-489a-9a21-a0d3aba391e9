# DMS API Documentation

Welcome to the Document Management Service (DMS) API documentation. This directory contains comprehensive documentation for all API endpoints, schemas, and integration guides.

## 📚 Documentation Structure

### Core API Documentation
- **[Complete API Documentation](DMS_Complete_API_Documentation.md)** - Comprehensive GraphQL API reference with examples and business features
- **[GraphQL API Reference](GraphQL_API_Reference.md)** - Focused GraphQL operations reference
- **[GraphQL Implementation Guide](GraphQL_Implementation_Guide.md)** - Implementation guidance and architecture
- **[GraphQL Testing Guide](GraphQL_Testing_Guide.md)** - Comprehensive testing procedures and examples
- **[OpenAPI Specification](openapi-specification.yaml)** - Machine-readable API specification

### Interactive Documentation
- **GraphiQL Interface**: `http://localhost:9092/graphiql` - Interactive GraphQL explorer
- **Swagger UI**: Available when OpenAPI specification is loaded
- **Postman Collection**: Import OpenAPI spec into Postman for testing

## 🚀 Quick Start

### 1. Authentication
All API endpoints require JWT authentication except public endpoints:

```bash
# Generate a test token
curl -X POST http://localhost:9092/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation { generateTestToken(input: { userId: \"test-user\", roles: [\"USER\"], permissions: [\"READ\", \"WRITE\"], expirationMinutes: 60 }) { token expiresAt } }"
  }'
```

### 2. Upload a Document
```bash
# GraphQL mutation for document upload
curl -X POST http://localhost:9092/graphql \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F 'operations={"query":"mutation UploadDocument($input: UploadDocumentInput!) { uploadDocument(input: $input) { id name version status } }","variables":{"input":{"name":"Test Document","description":"Test upload","keywords":["test"],"storageProvider":"LOCAL"}}}' \
  -F 'map={"0":["variables.input.file"]}' \
  -F '0=@/path/to/your/file.pdf'
```

### 3. Search Documents
```bash
# GraphQL query for document search
curl -X POST http://localhost:9092/graphql \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query SearchDocuments($filter: DocumentSearchInput, $pagination: PaginationInput) { searchDocuments(filter: $filter, pagination: $pagination) { content { id name version status createdDate } totalElements } }",
    "variables": {
      "filter": { "name": "test", "status": "ACTIVE" },
      "pagination": { "page": 0, "size": 10 }
    }
  }'
```

### 4. Download a Document
```bash
# REST endpoint for document download
curl -X GET http://localhost:9092/api/v1/documents/123/download \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o downloaded-file.pdf
```

## 📋 API Categories

### Core Document Operations
- **Upload**: Single file, from path, new version
- **Download**: Direct download, metadata retrieval
- **Search**: Full-text search, filtering, faceted search
- **Versioning**: Version management, restoration
- **Deletion**: Soft delete with audit trail

### Metadata Management
- **Classification**: Module, confidentiality, security level
- **Ownership**: Owner, department, expiry dates
- **Compliance**: Standards, audit relevance, controls

### Security & Audit
- **Audit Logs**: Comprehensive audit trail with tamper-proof chains
- **Security Violations**: Real-time violation tracking
- **Permissions**: Role-based access control
- **Export**: Audit log export in multiple formats

### Business Features
- **Workflows**: Definition, execution, monitoring
- **Templates**: Document templates, approval workflows
- **Webhooks**: Event-driven integrations
- **Events**: System event publishing and consumption

## 🔧 Development Tools

### GraphiQL Interface
Access the interactive GraphQL explorer at `http://localhost:9092/graphiql`:

1. **Schema Exploration**: Browse the complete GraphQL schema
2. **Query Building**: Interactive query and mutation builder
3. **Documentation**: Inline documentation for all fields
4. **Testing**: Execute queries and mutations directly

### Postman Integration
1. Import the OpenAPI specification into Postman
2. Configure environment variables for base URL and JWT token
3. Use pre-configured requests for common operations
4. Set up automated testing workflows

### cURL Examples
Each API endpoint includes comprehensive cURL examples for:
- Authentication setup
- Request formatting
- Response handling
- Error scenarios

## 📊 API Characteristics

### Performance
- **Rate Limiting**: Configurable per endpoint and user role
- **Caching**: Redis-based caching for frequently accessed data
- **Pagination**: Efficient pagination for large result sets
- **Compression**: GZIP compression for large responses

### Security
- **Authentication**: JWT Bearer token authentication
- **Authorization**: Role-based access control (RBAC)
- **Audit Trail**: Comprehensive logging with correlation IDs
- **Encryption**: Field-level PII encryption
- **Rate Limiting**: Protection against abuse

### Reliability
- **Error Handling**: Comprehensive error responses with correlation IDs
- **Validation**: Input validation with detailed error messages
- **Monitoring**: Health checks and metrics endpoints
- **Graceful Degradation**: Fallback mechanisms for service dependencies

## 🔍 Error Handling

### Standard Error Format
```json
{
  "error": "ERROR_CODE",
  "message": "Human-readable error message",
  "timestamp": "2024-12-01T10:30:00Z",
  "path": "/api/v1/documents/123",
  "correlationId": "req-123e4567-e89b-12d3-a456-426614174000"
}
```

### Common Error Codes
- **UNAUTHORIZED**: Missing or invalid JWT token
- **FORBIDDEN**: Insufficient permissions
- **DOCUMENT_NOT_FOUND**: Requested document does not exist
- **INVALID_INPUT**: Invalid request parameters
- **RATE_LIMIT_EXCEEDED**: Too many requests
- **INTERNAL_SERVER_ERROR**: Unexpected server error

### Error Troubleshooting
1. Check the correlation ID in logs for detailed error context
2. Verify JWT token validity and permissions
3. Validate request parameters against schema
4. Check service health endpoints for system status

## 📈 Monitoring & Observability

### Health Endpoints
- **System Health**: `/actuator/health`
- **Database Health**: `/actuator/health/db`
- **Redis Health**: `/actuator/health/redis`
- **Elasticsearch Health**: `/actuator/health/elasticsearch`

### Metrics
- **Prometheus Metrics**: `/actuator/prometheus`
- **Custom Business Metrics**: Document operations, user activity
- **Performance Metrics**: Response times, throughput
- **Error Metrics**: Error rates, failure patterns

### Logging
- **Structured Logging**: JSON format with correlation IDs
- **Audit Logging**: Tamper-proof audit chains
- **Security Logging**: Violation tracking and monitoring
- **Performance Logging**: Request/response timing

## 🤝 Support & Feedback

### Getting Help
- **Documentation Issues**: Create GitHub issues for documentation improvements
- **API Questions**: Contact the development team
- **Bug Reports**: Include correlation IDs and detailed reproduction steps
- **Feature Requests**: Submit enhancement requests with use cases

### Contributing
- **API Changes**: Follow semantic versioning guidelines
- **Documentation Updates**: Keep documentation in sync with code changes
- **Testing**: Include comprehensive test cases for new endpoints
- **Examples**: Provide working examples for new features

---

**Last Updated**: December 2024  
**API Version**: 1.0.0  
**Service Version**: Latest
