package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;

import java.util.List;

/**
 * Input DTO for completing a chunked upload session and creating the document.
 */
@Data
public class CompleteChunkedUploadInput {
    
    /**
     * The session ID for the chunked upload.
     */
    private String sessionId;
    
    /**
     * Name of the document to create.
     */
    private String name;
    
    /**
     * Optional description of the document.
     */
    private String description;
    
    /**
     * Storage provider for the document.
     */
    private StorageProvider storageProvider;
    
    /**
     * Optional keywords/tags for the document.
     */
    private List<String> keywords;
    
    /**
     * Whether to override existing files with the same name.
     */
    private Boolean overrideFile;
    
    /**
     * Optional classification metadata for the document.
     */
    private DocumentClassificationMetadataInput classificationMetadata;
    
    /**
     * Optional ownership metadata for the document.
     */
    private DocumentOwnershipMetadataInput ownershipMetadata;
    
    /**
     * Optional compliance metadata for the document.
     */
    private DocumentComplianceMetadataInput complianceMetadata;
    
    /**
     * Validate the input parameters.
     * 
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            throw new IllegalArgumentException("Session ID is required");
        }
        
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Document name is required");
        }
        
        if (storageProvider == null) {
            throw new IllegalArgumentException("Storage provider is required");
        }
    }
    
    /**
     * Check if the upload has metadata.
     * 
     * @return true if any metadata is present
     */
    public boolean hasMetadata() {
        return classificationMetadata != null || 
               ownershipMetadata != null || 
               complianceMetadata != null;
    }
}
