package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.BulkShareOperation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response DTO for bulk document sharing operations.
 * 
 * This DTO provides a comprehensive response format for bulk sharing operations
 * including operation status, summary statistics, and detailed item results.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkShareResponse {
    
    /** Whether the bulk operation was successful */
    private Boolean success;
    
    /** Descriptive message about the operation result */
    private String message;
    
    /** The bulk share operation data */
    private BulkShareOperation operation;
    
    /** Detailed results for individual share items */
    private List<BulkShareItemResult> items;
    
    /**
     * Create a successful response with operation data
     */
    public static BulkShareResponse success(String message, BulkShareOperation operation, List<BulkShareItemResult> items) {
        return BulkShareResponse.builder()
                .success(true)
                .message(message)
                .operation(operation)
                .items(items)
                .build();
    }
    
    /**
     * Create a successful response without detailed items
     */
    public static BulkShareResponse success(String message, BulkShareOperation operation) {
        return BulkShareResponse.builder()
                .success(true)
                .message(message)
                .operation(operation)
                .build();
    }
    
    /**
     * Create an error response
     */
    public static BulkShareResponse error(String message) {
        return BulkShareResponse.builder()
                .success(false)
                .message(message)
                .build();
    }
    
    /**
     * Create a successful response for completed bulk operation
     */
    public static BulkShareResponse completed(BulkShareOperation operation, List<BulkShareItemResult> items) {
        String message = String.format("Bulk share operation completed: %d successful, %d failed", 
                                      operation.getSuccessCount(), operation.getFailureCount());
        return success(message, operation, items);
    }
    
    /**
     * Create an error response for validation failures
     */
    public static BulkShareResponse validationError(String message) {
        return error("Validation error: " + message);
    }
    
    /**
     * Create an error response for unauthorized access
     */
    public static BulkShareResponse unauthorized(String message) {
        return error("Unauthorized: " + message);
    }
    
    /**
     * DTO for individual bulk share item results
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BulkShareItemResult {
        
        /** Document ID that was shared */
        private Long documentId;
        
        /** Recipient ID (user or role) */
        private String recipientId;
        
        /** Whether the recipient is a role */
        private Boolean isRole;
        
        /** Whether this share was successful */
        private Boolean isSuccessful;
        
        /** Error message if the share failed */
        private String errorMessage;
        
        /** Share link ID if successful */
        private String shareLinkId;
        
        /** Full share URL if successful */
        private String shareUrl;
        
        /**
         * Create a successful item result
         */
        public static BulkShareItemResult success(Long documentId, String recipientId, boolean isRole, 
                                                 String shareLinkId, String shareUrl) {
            return BulkShareItemResult.builder()
                    .documentId(documentId)
                    .recipientId(recipientId)
                    .isRole(isRole)
                    .isSuccessful(true)
                    .shareLinkId(shareLinkId)
                    .shareUrl(shareUrl)
                    .build();
        }
        
        /**
         * Create a failed item result
         */
        public static BulkShareItemResult failure(Long documentId, String recipientId, boolean isRole, 
                                                 String errorMessage) {
            return BulkShareItemResult.builder()
                    .documentId(documentId)
                    .recipientId(recipientId)
                    .isRole(isRole)
                    .isSuccessful(false)
                    .errorMessage(errorMessage)
                    .build();
        }
    }
}
