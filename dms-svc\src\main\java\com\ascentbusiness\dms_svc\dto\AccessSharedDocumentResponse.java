package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.enums.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for accessing shared documents via share links.
 * 
 * This DTO provides the result of attempting to access a document through
 * a shareable link, including the document data, permission level, and
 * any access requirements or restrictions.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccessSharedDocumentResponse {
    
    /** Whether the access attempt was successful */
    private Boolean success;
    
    /** Descriptive message about the access result */
    private String message;
    
    /** The document data (if access was successful) */
    private Document document;
    
    /** Whether the link requires a password */
    private Boolean requiresPassword;
    
    /** The permission level granted through this link */
    private Permission permission;
    
    /**
     * Create a successful response with document access
     */
    public static AccessSharedDocumentResponse success(Document document, Permission permission) {
        return AccessSharedDocumentResponse.builder()
                .success(true)
                .message("Document accessed successfully")
                .document(document)
                .requiresPassword(false)
                .permission(permission)
                .build();
    }
    
    /**
     * Create a response indicating password is required
     */
    public static AccessSharedDocumentResponse passwordRequired() {
        return AccessSharedDocumentResponse.builder()
                .success(false)
                .message("Password required to access this document")
                .requiresPassword(true)
                .build();
    }
    
    /**
     * Create an error response for invalid password
     */
    public static AccessSharedDocumentResponse invalidPassword() {
        return AccessSharedDocumentResponse.builder()
                .success(false)
                .message("Invalid password provided")
                .requiresPassword(true)
                .build();
    }
    
    /**
     * Create an error response for expired link
     */
    public static AccessSharedDocumentResponse expired() {
        return AccessSharedDocumentResponse.builder()
                .success(false)
                .message("This share link has expired")
                .requiresPassword(false)
                .build();
    }
    
    /**
     * Create an error response for inactive link
     */
    public static AccessSharedDocumentResponse inactive() {
        return AccessSharedDocumentResponse.builder()
                .success(false)
                .message("This share link is no longer active")
                .requiresPassword(false)
                .build();
    }
    
    /**
     * Create an error response for max uses reached
     */
    public static AccessSharedDocumentResponse maxUsesReached() {
        return AccessSharedDocumentResponse.builder()
                .success(false)
                .message("This share link has reached its maximum number of uses")
                .requiresPassword(false)
                .build();
    }
    
    /**
     * Create an error response for not found
     */
    public static AccessSharedDocumentResponse notFound() {
        return AccessSharedDocumentResponse.builder()
                .success(false)
                .message("Share link not found")
                .requiresPassword(false)
                .build();
    }
    
    /**
     * Create a generic error response
     */
    public static AccessSharedDocumentResponse error(String message) {
        return AccessSharedDocumentResponse.builder()
                .success(false)
                .message(message)
                .requiresPassword(false)
                .build();
    }
}
