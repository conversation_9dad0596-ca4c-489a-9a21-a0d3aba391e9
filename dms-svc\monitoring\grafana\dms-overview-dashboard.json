{"dashboard": {"id": null, "title": "DMS Service Overview", "tags": ["dms", "monitoring", "overview"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Service Status", "type": "stat", "targets": [{"expr": "up{job=\"dms-service\"}", "legendFormat": "Service Up"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "DOWN"}}, "type": "value"}, {"options": {"1": {"text": "UP"}}, "type": "value"}]}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate", "type": "stat", "targets": [{"expr": "rate(http_server_requests_seconds_count{job=\"dms-service\"}[5m])", "legendFormat": "Requests/sec"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Error Rate", "type": "stat", "targets": [{"expr": "rate(http_server_requests_seconds_count{job=\"dms-service\",status=~\"5..\"}[5m]) / rate(http_server_requests_seconds_count{job=\"dms-service\"}[5m]) * 100", "legendFormat": "Error Rate %"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Response Time (95th percentile)", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(http_server_requests_seconds_bucket{job=\"dms-service\"}[5m]))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 2}]}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "HTTP Request Rate by Status", "type": "timeseries", "targets": [{"expr": "rate(http_server_requests_seconds_count{job=\"dms-service\"}[5m])", "legendFormat": "{{status}} - {{method}}"}], "fieldConfig": {"defaults": {"unit": "reqps", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}}, {"id": 6, "title": "Response Time Distribution", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(http_server_requests_seconds_bucket{job=\"dms-service\"}[5m]))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, rate(http_server_requests_seconds_bucket{job=\"dms-service\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, rate(http_server_requests_seconds_bucket{job=\"dms-service\"}[5m]))", "legendFormat": "99th percentile"}], "fieldConfig": {"defaults": {"unit": "s", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 0, "gradientMode": "none", "spanNulls": false, "pointSize": 5, "stacking": {"mode": "none", "group": "A"}}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}}, {"id": 7, "title": "JVM Memory Usage", "type": "timeseries", "targets": [{"expr": "jvm_memory_used_bytes{job=\"dms-service\",area=\"heap\"}", "legendFormat": "Heap Used"}, {"expr": "jvm_memory_max_bytes{job=\"dms-service\",area=\"heap\"}", "legendFormat": "Heap Max"}, {"expr": "jvm_memory_used_bytes{job=\"dms-service\",area=\"nonheap\"}", "legendFormat": "Non-Heap Used"}], "fieldConfig": {"defaults": {"unit": "bytes", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "gradientMode": "none"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}}, {"id": 8, "title": "Database Connection Pool", "type": "timeseries", "targets": [{"expr": "hikaricp_connections_active{job=\"dms-service\"}", "legendFormat": "Active Connections"}, {"expr": "hikaricp_connections_idle{job=\"dms-service\"}", "legendFormat": "Idle Connections"}, {"expr": "hikaricp_connections_max{job=\"dms-service\"}", "legendFormat": "Max Connections"}], "fieldConfig": {"defaults": {"unit": "short", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}}, {"id": 9, "title": "Document Operations", "type": "timeseries", "targets": [{"expr": "rate(dms_business_documents_created_total[5m])", "legendFormat": "Documents Created/sec"}, {"expr": "rate(dms_business_documents_updated_total[5m])", "legendFormat": "Documents Updated/sec"}, {"expr": "rate(dms_business_documents_deleted_total[5m])", "legendFormat": "Documents Deleted/sec"}], "fieldConfig": {"defaults": {"unit": "ops", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}}, {"id": 10, "title": "<PERSON><PERSON>", "type": "timeseries", "targets": [{"expr": "rate(dms_cache_hit_count_total[5m])", "legendFormat": "Cache Hits/sec"}, {"expr": "rate(dms_cache_miss_count_total[5m])", "legendFormat": "<PERSON><PERSON>/sec"}, {"expr": "dms_cache_hit_count_total / (dms_cache_hit_count_total + dms_cache_miss_count_total) * 100", "legendFormat": "Hit Ratio %"}], "fieldConfig": {"defaults": {"unit": "short", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}}, {"id": 11, "title": "Storage Utilization", "type": "gauge", "targets": [{"expr": "dms_business_storage_quota_utilization", "legendFormat": "Storage Utilization %"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "orange", "value": 85}, {"color": "red", "value": 95}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 28}}, {"id": 12, "title": "Active Users", "type": "stat", "targets": [{"expr": "dms_business_user_active", "legendFormat": "Active Users"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 28}}, {"id": 13, "title": "Security Events", "type": "timeseries", "targets": [{"expr": "rate(dms_business_user_login_failed_total[5m])", "legendFormat": "Failed Logins/sec"}, {"expr": "rate(dms_business_compliance_violations_total[5m])", "legendFormat": "Compliance Violations/sec"}], "fieldConfig": {"defaults": {"unit": "ops", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}, "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 28}}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up{job=\"dms-service\"}, instance)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(process_start_time_seconds{job=\"dms-service\"}[1h])", "iconColor": "blue", "textFormat": "Deployment"}]}}}