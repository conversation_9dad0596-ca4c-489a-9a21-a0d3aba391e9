-- H2 Test Data
-- This file inserts test data for H2 in-memory database used in tests

-- Insert sample documents for testing
INSERT INTO documents (
    name, description, original_file_name, file_size, mime_type,
    storage_provider, storage_path, status, version, tags,
    creator_user_id, creator_roles, creator_permissions,
    created_date, last_modified_date, created_by, last_modified_by
) VALUES
('Sample Document 1', 'This is a test document', 'test-document.pdf', 1024576, 'application/pdf',
 'LOCAL', '/storage/documents/test-document.pdf', 'ACTIVE', 1, '["document", "test", "sample"]',
 'user123', '["USER"]', '["READ", "WRITE"]', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'user123', 'user123'),
('Sample Document 2', 'Another test document', 'test-image.jpg', 2048000, 'image/jpeg',
 'LOCAL', '/storage/documents/test-image.jpg', 'ACTIVE', 1, '["image", "test"]',
 'user456', '["USER"]', '["READ", "WRITE"]', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'user456', 'user456'),
('Sample Document 3', 'Spreadsheet for testing', 'test-spreadsheet.xlsx', 512000, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
 'S3', '/s3/bucket/test-spreadsheet.xlsx', 'ACTIVE', 1, '["spreadsheet", "test"]',
 'user789', '["USER"]', '["READ", "WRITE"]', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'user789', 'user789');

-- Insert sample audit logs
INSERT INTO audit_logs (
    document_id, user_id, action, details, ip_address, user_agent, correlation_id, timestamp
) VALUES 
(1, 'user123', 'UPLOAD', 'Document uploaded successfully', '*************', 'Mozilla/5.0', 'corr-001', CURRENT_TIMESTAMP),
(2, 'user456', 'UPLOAD', 'Image document uploaded', '*************', 'Mozilla/5.0', 'corr-002', CURRENT_TIMESTAMP),
(3, 'user789', 'UPLOAD', 'Spreadsheet uploaded to S3', '*************', 'Mozilla/5.0', 'corr-003', CURRENT_TIMESTAMP);

-- Insert sample document permissions
INSERT INTO document_permissions (
    document_id, user_id, role_name, permission_type, granted_by, is_active, notes,
    created_date, last_modified_date, created_by, last_modified_by
) VALUES
(1, 'user123', NULL, 'ADMIN', 'system', TRUE, 'Creator permissions',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
(1, 'user456', NULL, 'READ', 'user123', TRUE, 'Read access granted',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'user123', 'user123'),
(2, 'user456', NULL, 'ADMIN', 'system', TRUE, 'Creator permissions',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
(2, NULL, 'MANAGER', 'WRITE', 'user456', TRUE, 'Manager role permissions',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'user456', 'user456'),
(3, 'user789', NULL, 'ADMIN', 'system', TRUE, 'Creator permissions',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');

-- Insert sample document versions
INSERT INTO document_versions (
    document_id, version_number, file_name, file_size, mime_type, storage_path, status, is_current, created_by, created_date, version_notes
) VALUES
(1, 1, 'test-document.pdf', 1024576, 'application/pdf', '/storage/documents/test-document.pdf', 'ACTIVE', TRUE, 'user123', CURRENT_TIMESTAMP, 'Initial version'),
(2, 1, 'test-image.jpg', 2048000, 'image/jpeg', '/storage/documents/test-image.jpg', 'ACTIVE', TRUE, 'user456', CURRENT_TIMESTAMP, 'Initial version'),
(3, 1, 'test-spreadsheet.xlsx', 512000, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '/s3/bucket/test-spreadsheet.xlsx', 'ACTIVE', TRUE, 'user789', CURRENT_TIMESTAMP, 'Initial version');

-- Insert sample classification metadata
INSERT INTO document_classification_metadata (
    document_id, classification_level, security_tags, access_restrictions, retention_period_days
) VALUES 
(1, 'PUBLIC', '["public", "general"]', 'No restrictions', 365),
(2, 'INTERNAL', '["internal", "company"]', 'Company employees only', 1095),
(3, 'CONFIDENTIAL', '["confidential", "sensitive"]', 'Authorized personnel only', 2555);

-- Insert sample ownership metadata
INSERT INTO document_ownership_metadata (
    document_id, owner_user_id, department, business_unit, cost_center, project_code, lifecycle_stage
) VALUES 
(1, 'user123', 'IT', 'Technology', 'IT001', 'PROJ001', 'ACTIVE'),
(2, 'user456', 'Marketing', 'Sales & Marketing', 'MKT001', 'PROJ002', 'ACTIVE'),
(3, 'user789', 'Finance', 'Finance & Accounting', 'FIN001', 'PROJ003', 'ACTIVE');

-- Insert sample compliance metadata
INSERT INTO document_compliance_metadata (
    document_id, compliance_framework, regulatory_requirements, audit_trail_required, data_residency_requirements, encryption_required
) VALUES 
(1, 'ISO27001', '["data_protection", "access_control"]', FALSE, 'US', FALSE),
(2, 'GDPR', '["privacy", "consent"]', TRUE, 'EU', TRUE),
(3, 'SOX', '["financial_reporting", "audit"]', TRUE, 'US', TRUE);

-- Insert sample storage configurations
INSERT INTO storage_configurations (
    provider_type, is_active, is_default, configuration_json, description, priority, health_check_enabled, health_status, created_by, last_modified_by
) VALUES
('LOCAL', TRUE, TRUE, '{"basePath": "/tmp/test-storage", "createDirectories": true}', 'Local file system storage for tests', 1, TRUE, 'HEALTHY', 'system', 'system'),
('S3', TRUE, FALSE, '{"bucketName": "test-bucket", "region": "us-east-1", "accessKey": "test", "secretKey": "test"}', 'AWS S3 storage for tests', 2, FALSE, 'UNKNOWN', 'system', 'system'),
('SHAREPOINT', FALSE, FALSE, '{"siteUrl": "https://test.sharepoint.com", "documentLibrary": "Documents"}', 'SharePoint storage for tests', 3, FALSE, 'DISABLED', 'system', 'system');

-- Insert additional test-specific security configurations (avoiding duplicates from changelog)
-- Skip security_config inserts to avoid conflicts with Liquibase changelog data
-- The security_config table will be populated by Liquibase migrations

-- Insert sample security violations
INSERT INTO security_violations (
    user_id, violation_type, description, ip_address, user_agent, document_id, severity, is_resolved, created_by
) VALUES
('user123', 'UNAUTHORIZED_ACCESS', 'Attempted to access document without permission', '*************', 'Mozilla/5.0', 1, 'HIGH', FALSE, 'system'),
('user456', 'RATE_LIMIT_EXCEEDED', 'Exceeded maximum permission operations per hour', '*************', 'Mozilla/5.0', NULL, 'MEDIUM', TRUE, 'system');
