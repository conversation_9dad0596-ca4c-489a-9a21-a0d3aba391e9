package com.ascentbusiness.dms_svc.dto;

import lombok.Data;
import java.util.List;

@Data
public class DocumentClassificationMetadataInput {
    
    private String module; // GRC module name (Governance, Risk, Compliance, etc.)
    private String subModule; // e.g., within Risk: Operational, Financial, IT, etc.
    private String businessUnit; // Department or business function
    private String regionLocation; // Relevant geography or business location
    private String confidentialityLevel; // Public, Internal, Confidential, Restricted
    private List<String> tagsKeywords; // For categorization/search, e.g., "vendor", "ISO27001", "audit"
    private String language; // Document language if multilingual support is needed
    private String documentType; // Type of document
}
