package com.ascentbusiness.dms_svc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for dynamic file processing strategies.
 * 
 * <p>This configuration class defines thresholds and settings that determine
 * how files are processed based on their size and other characteristics:
 * <ul>
 *   <li>DIRECT: Small files processed immediately and synchronously</li>
 *   <li>ASYNC: Medium files processed asynchronously in background</li>
 *   <li>CHUNKED: Large files processed in chunks with session management</li>
 * </ul>
 */
@Data
@Component
@ConfigurationProperties(prefix = "dms.file-processing")
public class FileProcessingConfig {
    
    /**
     * Maximum file size (in bytes) for direct processing.
     * Files smaller than this threshold will be processed immediately.
     * Default: 10MB
     */
    private long directProcessingThreshold = 10 * 1024 * 1024; // 10MB
    
    /**
     * Maximum file size (in bytes) for async processing.
     * Files between directProcessingThreshold and this threshold will be processed asynchronously.
     * Default: 100MB
     */
    private long asyncProcessingThreshold = 100 * 1024 * 1024; // 100MB
    
    /**
     * Files larger than asyncProcessingThreshold will require chunked upload.
     * Maximum file size (in bytes) for chunked processing.
     * Default: 1GB
     */
    private long maxFileSize = 1024 * 1024 * 1024; // 1GB
    
    /**
     * Default chunk size (in bytes) for chunked uploads.
     * Default: 5MB
     */
    private int defaultChunkSize = 5 * 1024 * 1024; // 5MB
    
    /**
     * Maximum chunk size (in bytes) allowed for chunked uploads.
     * Default: 50MB
     */
    private int maxChunkSize = 50 * 1024 * 1024; // 50MB
    
    /**
     * Minimum chunk size (in bytes) allowed for chunked uploads.
     * Default: 1MB
     */
    private int minChunkSize = 1024 * 1024; // 1MB
    
    /**
     * Maximum number of concurrent async processing jobs.
     * Default: 10
     */
    private int maxConcurrentAsyncJobs = 10;
    
    /**
     * Timeout (in seconds) for async job processing.
     * Default: 3600 seconds (1 hour)
     */
    private int asyncJobTimeoutSeconds = 3600;
    
    /**
     * Maximum duration (in seconds) for chunked upload sessions.
     * Sessions older than this will be cleaned up.
     * Default: 86400 seconds (24 hours)
     */
    private int chunkSessionTimeoutSeconds = 86400;
    
    /**
     * Interval (in seconds) for cleanup of expired sessions and temporary files.
     * Default: 3600 seconds (1 hour)
     */
    private int cleanupIntervalSeconds = 3600;
    
    /**
     * Directory for temporary files during processing.
     * Default: ./temp/processing
     */
    private String tempDirectory = "./temp/processing";
    
    /**
     * Whether to enable automatic cleanup of temporary files.
     * Default: true
     */
    private boolean enableAutoCleanup = true;
    
    /**
     * Whether to enable progress tracking for async jobs.
     * Default: true
     */
    private boolean enableProgressTracking = true;
    
    /**
     * Whether to enable detailed logging for file processing operations.
     * Default: false
     */
    private boolean enableDetailedLogging = false;
    
    /**
     * Calculate optimal chunk size based on file size.
     *
     * @param fileSize the size of the file in bytes
     * @return optimal chunk size in bytes
     */
    public int getOptimalChunkSize(long fileSize) {
        if (fileSize <= 50 * 1024 * 1024) { // <= 50MB
            return minChunkSize; // Use minimum chunk size for small files
        } else if (fileSize <= 500 * 1024 * 1024) { // <= 500MB
            return defaultChunkSize; // 5MB
        } else if (fileSize <= 2L * 1024 * 1024 * 1024) { // <= 2GB
            return Math.min(maxChunkSize, defaultChunkSize * 2); // 10MB
        } else {
            return maxChunkSize; // 50MB
        }
    }
    
    /**
     * Get optimal chunk size with default file size assumption.
     * 
     * @return default chunk size in bytes
     */
    public int getOptimalChunkSize() {
        return defaultChunkSize;
    }
    
    /**
     * Calculate estimated number of chunks for a given file size.
     * 
     * @param fileSize the size of the file in bytes
     * @param chunkSize the size of each chunk in bytes
     * @return estimated number of chunks
     */
    public int calculateChunkCount(long fileSize, int chunkSize) {
        return (int) Math.ceil((double) fileSize / chunkSize);
    }
    
    /**
     * Validate if the given chunk size is within acceptable limits.
     * 
     * @param chunkSize the chunk size to validate
     * @return true if valid, false otherwise
     */
    public boolean isValidChunkSize(int chunkSize) {
        return chunkSize >= minChunkSize && chunkSize <= maxChunkSize;
    }
    
    /**
     * Validate if the given file size is within processing limits.
     * 
     * @param fileSize the file size to validate
     * @return true if valid, false otherwise
     */
    public boolean isValidFileSize(long fileSize) {
        return fileSize > 0 && fileSize <= maxFileSize;
    }
}
