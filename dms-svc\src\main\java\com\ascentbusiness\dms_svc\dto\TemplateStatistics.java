package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.DocumentTemplate;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * DTO for template statistics that matches GraphQL schema
 */
@Data
@Builder
public class TemplateStatistics {
    private Long totalTemplates;
    private Long totalActive;
    private Long totalDraft;
    private Long totalPending;
    private Long totalPublished;
    private Long totalArchived;
    
    private List<TemplateCategoryStats> categoryStatistics;
    private List<TemplateTypeStats> typeStatistics;
    private List<TemplateApprovalStats> approvalStatistics;
    private List<TemplateUsageStats> usageStatistics;
    
    private List<TemplateCategoryStats> topCategories;
    private List<DocumentTemplate> mostUsedTemplates;
    private List<DocumentTemplate> recentlyCreated;
    private List<TemplateApprovalStats> pendingApproval;
}
