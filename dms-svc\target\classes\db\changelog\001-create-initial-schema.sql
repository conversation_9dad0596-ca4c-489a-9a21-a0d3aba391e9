--liquibase formatted sql

--changeset dms:001-create-initial-schema

-- Create documents table
CREATE TABLE documents (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    original_file_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    storage_provider ENUM('LOCAL', 'S3', 'SHAREPOINT') NOT NULL,
    storage_path VARCHAR(500) NOT NULL,
    status ENUM('ACTIVE', 'HISTORICAL', 'DELETED') NOT NULL DEFAULT 'ACTIVE',
    version INT NOT NULL DEFAULT 1,
    tags JSON, -- MySQL JSON for tags
    file_content LONGBLOB, -- Store file as binary data
    creator_user_id VARCHAR(100) NOT NULL, -- Simple user ID
    parent_document_id BIGINT, -- For versioning
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (parent_document_id) REFERENCES documents(id)
);

-- Create audit_logs table
CREATE TABLE audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT,
    user_id VARCHAR(100) NOT NULL, -- Simple user ID
    action ENUM('UPLOAD', 'DOWNLOAD', 'DELETE', 'UPDATE', 'VERSION_CREATE') NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    correlation_id VARCHAR(100),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Create indexes for better performance
CREATE INDEX idx_documents_creator_user_id ON documents(creator_user_id);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_storage_provider ON documents(storage_provider);
CREATE INDEX idx_documents_created_date ON documents(created_date);
CREATE INDEX idx_documents_parent_document_id ON documents(parent_document_id);
CREATE INDEX idx_documents_name ON documents(name);

CREATE INDEX idx_audit_logs_document_id ON audit_logs(document_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_correlation_id ON audit_logs(correlation_id);
