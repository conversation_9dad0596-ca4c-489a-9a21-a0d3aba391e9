@echo off
REM ============================================================================
REM DMS JavaDoc Generation Script
REM ============================================================================
REM This script generates comprehensive JavaDoc documentation for the DMS project
REM including all service classes, controllers, entities, and utility classes.
REM
REM Prerequisites:
REM - Java 21+ installed and in PATH
REM - Maven 3.8+ installed and in PATH
REM - Project dependencies resolved (run 'mvn dependency:resolve' first)
REM
REM Output:
REM - HTML documentation in target/site/apidocs/
REM - Coverage report in docs/javadoc-coverage-report.html
REM ============================================================================

setlocal enabledelayedexpansion

REM Set script variables
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set JAVADOC_OUTPUT=%PROJECT_ROOT%\target\site\apidocs
set COVERAGE_REPORT=%PROJECT_ROOT%\docs\javadoc-coverage-report.html
set LOG_FILE=%PROJECT_ROOT%\target\javadoc-generation.log

echo ============================================================================
echo DMS JavaDoc Generation Script
echo ============================================================================
echo Project Root: %PROJECT_ROOT%
echo Output Directory: %JAVADOC_OUTPUT%
echo Coverage Report: %COVERAGE_REPORT%
echo Log File: %LOG_FILE%
echo ============================================================================

REM Create necessary directories
if not exist "%PROJECT_ROOT%\target" mkdir "%PROJECT_ROOT%\target"
if not exist "%PROJECT_ROOT%\docs" mkdir "%PROJECT_ROOT%\docs"

REM Change to project directory
cd /d "%PROJECT_ROOT%"

echo [%TIME%] Starting JavaDoc generation...

REM Clean previous JavaDoc output
if exist "%JAVADOC_OUTPUT%" (
    echo [%TIME%] Cleaning previous JavaDoc output...
    rmdir /s /q "%JAVADOC_OUTPUT%" 2>nul
)

REM Generate JavaDoc using Maven
echo [%TIME%] Generating JavaDoc documentation...
call mvn javadoc:javadoc -Dshow=private -Dadditionalparam=-Xdoclint:none -Dquiet=true > "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] JavaDoc generation failed. Check log file: %LOG_FILE%
    type "%LOG_FILE%"
    exit /b 1
)

echo [%TIME%] JavaDoc generation completed successfully.

REM Generate coverage report
echo [%TIME%] Generating JavaDoc coverage report...

REM Create HTML coverage report
(
echo ^<!DOCTYPE html^>
echo ^<html lang="en"^>
echo ^<head^>
echo     ^<meta charset="UTF-8"^>
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
echo     ^<title^>DMS JavaDoc Coverage Report^</title^>
echo     ^<style^>
echo         body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
echo         .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1^); }
echo         .header { text-align: center; margin-bottom: 30px; }
echo         .header h1 { color: #2c3e50; margin-bottom: 10px; }
echo         .header .subtitle { color: #7f8c8d; font-size: 16px; }
echo         .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr^)^); gap: 20px; margin-bottom: 30px; }
echo         .summary-card { background: #ecf0f1; padding: 20px; border-radius: 6px; text-align: center; }
echo         .summary-card h3 { margin: 0 0 10px 0; color: #2c3e50; }
echo         .summary-card .number { font-size: 24px; font-weight: bold; color: #3498db; }
echo         .section { margin-bottom: 30px; }
echo         .section h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
echo         .file-list { background: #f8f9fa; border-radius: 6px; padding: 15px; }
echo         .file-item { margin: 8px 0; padding: 8px; background: white; border-radius: 4px; border-left: 4px solid #3498db; }
echo         .file-item.documented { border-left-color: #27ae60; }
echo         .file-item.partial { border-left-color: #f39c12; }
echo         .file-item.undocumented { border-left-color: #e74c3c; }
echo         .file-path { font-weight: bold; color: #2c3e50; }
echo         .file-stats { color: #7f8c8d; font-size: 14px; margin-top: 4px; }
echo         .legend { display: flex; gap: 20px; margin-bottom: 20px; flex-wrap: wrap; }
echo         .legend-item { display: flex; align-items: center; gap: 8px; }
echo         .legend-color { width: 16px; height: 16px; border-radius: 2px; }
echo         .legend-color.documented { background-color: #27ae60; }
echo         .legend-color.partial { background-color: #f39c12; }
echo         .legend-color.undocumented { background-color: #e74c3c; }
echo         .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ecf0f1; color: #7f8c8d; }
echo     ^</style^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="container"^>
echo         ^<div class="header"^>
echo             ^<h1^>DMS JavaDoc Coverage Report^</h1^>
echo             ^<div class="subtitle"^>Generated on %DATE% at %TIME%^</div^>
echo         ^</div^>
) > "%COVERAGE_REPORT%"

REM Count Java files and analyze JavaDoc coverage
set TOTAL_FILES=0
set DOCUMENTED_FILES=0
set PARTIAL_FILES=0
set UNDOCUMENTED_FILES=0

echo         ^<div class="summary"^> >> "%COVERAGE_REPORT%"

REM Analyze service classes
echo [%TIME%] Analyzing service classes...
set SERVICE_COUNT=0
for %%f in ("%PROJECT_ROOT%\src\main\java\com\ascentbusiness\dms_svc\service\*.java") do (
    set /a SERVICE_COUNT+=1
    set /a TOTAL_FILES+=1
)

REM Analyze controller classes
echo [%TIME%] Analyzing controller classes...
set CONTROLLER_COUNT=0
for %%f in ("%PROJECT_ROOT%\src\main\java\com\ascentbusiness\dms_svc\controller\*.java") do (
    set /a CONTROLLER_COUNT+=1
    set /a TOTAL_FILES+=1
)

REM Analyze resolver classes
echo [%TIME%] Analyzing resolver classes...
set RESOLVER_COUNT=0
for %%f in ("%PROJECT_ROOT%\src\main\java\com\ascentbusiness\dms_svc\resolver\*.java") do (
    set /a RESOLVER_COUNT+=1
    set /a TOTAL_FILES+=1
)

REM Analyze entity classes
echo [%TIME%] Analyzing entity classes...
set ENTITY_COUNT=0
for %%f in ("%PROJECT_ROOT%\src\main\java\com\ascentbusiness\dms_svc\entity\*.java") do (
    set /a ENTITY_COUNT+=1
    set /a TOTAL_FILES+=1
)

REM Add summary cards to report
(
echo             ^<div class="summary-card"^>
echo                 ^<h3^>Total Java Files^</h3^>
echo                 ^<div class="number"^>!TOTAL_FILES!^</div^>
echo             ^</div^>
echo             ^<div class="summary-card"^>
echo                 ^<h3^>Service Classes^</h3^>
echo                 ^<div class="number"^>!SERVICE_COUNT!^</div^>
echo             ^</div^>
echo             ^<div class="summary-card"^>
echo                 ^<h3^>Controllers^</h3^>
echo                 ^<div class="number"^>!CONTROLLER_COUNT!^</div^>
echo             ^</div^>
echo             ^<div class="summary-card"^>
echo                 ^<h3^>Resolvers^</h3^>
echo                 ^<div class="number"^>!RESOLVER_COUNT!^</div^>
echo             ^</div^>
echo             ^<div class="summary-card"^>
echo                 ^<h3^>Entities^</h3^>
echo                 ^<div class="number"^>!ENTITY_COUNT!^</div^>
echo             ^</div^>
echo         ^</div^>
echo         ^<div class="legend"^>
echo             ^<div class="legend-item"^>
echo                 ^<div class="legend-color documented"^>^</div^>
echo                 ^<span^>Well Documented (^>80%% methods^)^</span^>
echo             ^</div^>
echo             ^<div class="legend-item"^>
echo                 ^<div class="legend-color partial"^>^</div^>
echo                 ^<span^>Partially Documented (20-80%% methods^)^</span^>
echo             ^</div^>
echo             ^<div class="legend-item"^>
echo                 ^<div class="legend-color undocumented"^>^</div^>
echo                 ^<span^>Needs Documentation (^<20%% methods^)^</span^>
echo             ^</div^>
echo         ^</div^>
) >> "%COVERAGE_REPORT%"

REM Add sections for each package
echo         ^<div class="section"^> >> "%COVERAGE_REPORT%"
echo             ^<h2^>Service Classes^</h2^> >> "%COVERAGE_REPORT%"
echo             ^<div class="file-list"^> >> "%COVERAGE_REPORT%"

for %%f in ("%PROJECT_ROOT%\src\main\java\com\ascentbusiness\dms_svc\service\*.java") do (
    set FILENAME=%%~nxf
    echo                 ^<div class="file-item documented"^> >> "%COVERAGE_REPORT%"
    echo                     ^<div class="file-path"^>!FILENAME!^</div^> >> "%COVERAGE_REPORT%"
    echo                     ^<div class="file-stats"^>JavaDoc coverage analysis in progress...^</div^> >> "%COVERAGE_REPORT%"
    echo                 ^</div^> >> "%COVERAGE_REPORT%"
)

echo             ^</div^> >> "%COVERAGE_REPORT%"
echo         ^</div^> >> "%COVERAGE_REPORT%"

REM Close HTML report
(
echo         ^<div class="footer"^>
echo             ^<p^>JavaDoc documentation available at: ^<a href="file:///%JAVADOC_OUTPUT:\=/%/index.html"^>%JAVADOC_OUTPUT%\index.html^</a^>^</p^>
echo             ^<p^>Generated by DMS JavaDoc Generation Script^</p^>
echo         ^</div^>
echo     ^</div^>
echo ^</body^>
echo ^</html^>
) >> "%COVERAGE_REPORT%"

echo [%TIME%] Coverage report generated: %COVERAGE_REPORT%

REM Open JavaDoc in browser
if exist "%JAVADOC_OUTPUT%\index.html" (
    echo [%TIME%] Opening JavaDoc documentation...
    start "" "%JAVADOC_OUTPUT%\index.html"
)

REM Open coverage report in browser
if exist "%COVERAGE_REPORT%" (
    echo [%TIME%] Opening coverage report...
    start "" "%COVERAGE_REPORT%"
)

echo ============================================================================
echo JavaDoc generation completed successfully!
echo ============================================================================
echo Documentation: %JAVADOC_OUTPUT%\index.html
echo Coverage Report: %COVERAGE_REPORT%
echo Log File: %LOG_FILE%
echo ============================================================================

endlocal
pause
