# Shared Infrastructure - Docker Compose Configuration
# Deploy shared infrastructure services that can be used by both DMS and Notification services
version: '3.8'

services:
  # =============================================================================
  # SHARED INFRASTRUCTURE SERVICES
  # =============================================================================

  # Shared MySQL Database
  mysql:
    image: mysql:8.0
    container_name: grc-mysql-shared
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      # Create multiple databases via init script
      - MYSQL_DATABASE=${MYSQL_DMS_DATABASE:-dms_db}
      - MYSQL_USER=${MYSQL_DMS_USER:-dms_user}
      - MYSQL_PASSWORD=${MYSQL_DMS_PASSWORD:-dms_password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_shared_data:/var/lib/mysql
      - ./docker/mysql/shared-init:/docker-entrypoint-initdb.d:ro
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-root_password}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: --default-authentication-plugin=mysql_native_password

  # Shared Redis Cache
  redis:
    image: redis:7-alpine
    container_name: grc-redis-shared
    command: redis-server --requirepass ${REDIS_PASSWORD:-shared_redis_password} --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_shared_data:/data
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-shared_redis_password}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker (for Notification Service)
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: grc-rabbitmq-shared
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS:-admin123}
      - RABBITMQ_DEFAULT_VHOST=/
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_shared_data:/var/lib/rabbitmq
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Elasticsearch for Search (for DMS Service)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: grc-elasticsearch-shared
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_shared_data:/usr/share/elasticsearch/data
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # SHARED MONITORING SERVICES
  # =============================================================================

  # Zipkin for Distributed Tracing
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: grc-zipkin-shared
    ports:
      - "9411:9411"
    networks:
      - grc-shared-network
    restart: unless-stopped

  # Prometheus for Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: grc-prometheus-shared
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/shared-prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./docker/prometheus/alerts.yml:/etc/prometheus/alerts.yml:ro
      - prometheus_shared_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - grc-shared-network
    restart: unless-stopped

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: grc-grafana-shared
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD:-admin}
    volumes:
      - grafana_shared_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - grc-shared-network
    restart: unless-stopped

volumes:
  mysql_shared_data:
    driver: local
  redis_shared_data:
    driver: local
  rabbitmq_shared_data:
    driver: local
  elasticsearch_shared_data:
    driver: local
  prometheus_shared_data:
    driver: local
  grafana_shared_data:
    driver: local

networks:
  grc-shared-network:
    name: grc-shared-network
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
