@echo off
REM GRC Platform v4 - Docker Management Script (Windows)
REM This script helps manage the shared Docker Compose setup

setlocal enabledelayedexpansion

REM Function to print colored output (Windows doesn't support colors easily, so we'll use plain text)
:print_status
echo [INFO] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

:print_header
echo === %~1 ===
goto :eof

REM Function to check if Dock<PERSON> is running
:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not running. Please start Docker and try again."
    exit /b 1
)
goto :eof

REM Function to check if docker-compose is available
:check_docker_compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "docker-compose is not installed. Please install docker-compose and try again."
    exit /b 1
)
goto :eof

REM Function to start all services
:start_all
call :print_header "Starting GRC Platform v4 - All Services"
call :check_docker
call :check_docker_compose

call :print_status "Starting infrastructure services first..."
docker-compose up -d mysql redis rabbitmq elasticsearch

call :print_status "Waiting for infrastructure services to be ready..."
timeout /t 30 /nobreak >nul

call :print_status "Starting application services..."
docker-compose up -d dms-service notification-service

call :print_status "Starting monitoring services..."
docker-compose up -d prometheus grafana zipkin

call :print_status "All services started successfully!"
call :show_status
goto :eof

REM Function to start only application services
:start_apps
call :print_header "Starting Application Services Only"
call :check_docker
call :check_docker_compose

docker-compose up -d dms-service notification-service
call :print_status "Application services started!"
call :show_status
goto :eof

REM Function to start only infrastructure services
:start_infra
call :print_header "Starting Infrastructure Services Only"
call :check_docker
call :check_docker_compose

docker-compose up -d mysql redis rabbitmq elasticsearch
call :print_status "Infrastructure services started!"
call :show_status
goto :eof

REM Function to show service status
:show_status
call :print_header "Service Status"
docker-compose ps

echo.
call :print_header "Service URLs"
echo Application Services:
echo   DMS Service GraphQL:        http://localhost:9093/dms/graphql
echo   DMS Service GraphiQL:       http://localhost:9093/graphiql
echo   Notification Service GraphQL: http://localhost:9091/graphql
echo   Notification Service GraphiQL: http://localhost:9091/graphiql
echo.
echo Infrastructure Services:
echo   MySQL:                      localhost:3306 (root/root_password)
echo   Redis:                      localhost:6379 (shared_redis_password)
echo   RabbitMQ Management:        http://localhost:15672 (admin/admin123)
echo   Elasticsearch:              http://localhost:9200
echo.
echo Monitoring Services:
echo   Grafana:                    http://localhost:3000 (admin/admin)
echo   Prometheus:                 http://localhost:9090
echo   Zipkin:                     http://localhost:9411
goto :eof

REM Function to show logs
:show_logs
if "%~2"=="" (
    call :print_header "Showing All Service Logs"
    docker-compose logs -f
) else (
    call :print_header "Showing Logs for: %~2"
    docker-compose logs -f %~2
)
goto :eof

REM Function to stop all services
:stop_all
call :print_header "Stopping All Services"
docker-compose down
call :print_status "All services stopped!"
goto :eof

REM Function to restart services
:restart_service
if "%~2"=="" (
    call :print_header "Restarting All Services"
    docker-compose restart
) else (
    call :print_header "Restarting Service: %~2"
    docker-compose restart %~2
)
call :print_status "Restart completed!"
goto :eof

REM Function to clean up everything
:cleanup
call :print_warning "This will stop all services and remove all data!"
set /p confirm="Are you sure? (y/N): "
if /i "!confirm!"=="y" (
    call :print_header "Cleaning Up Everything"
    docker-compose down -v --rmi all
    docker system prune -f
    call :print_status "Cleanup completed!"
) else (
    call :print_status "Cleanup cancelled."
)
goto :eof

REM Function to show health status
:health_check
call :print_header "Health Check"

echo Checking DMS Service...
curl -f http://localhost:9093/actuator/health >nul 2>&1
if errorlevel 1 (
    call :print_error "DMS Service: UNHEALTHY"
) else (
    call :print_status "DMS Service: HEALTHY"
)

echo Checking Notification Service...
curl -f http://localhost:9091/actuator/health >nul 2>&1
if errorlevel 1 (
    call :print_error "Notification Service: UNHEALTHY"
) else (
    call :print_status "Notification Service: HEALTHY"
)

echo Checking MySQL...
docker-compose exec -T mysql mysqladmin ping -h localhost -u root -proot_password >nul 2>&1
if errorlevel 1 (
    call :print_error "MySQL: UNHEALTHY"
) else (
    call :print_status "MySQL: HEALTHY"
)

echo Checking Redis...
docker-compose exec -T redis redis-cli -a shared_redis_password ping >nul 2>&1
if errorlevel 1 (
    call :print_error "Redis: UNHEALTHY"
) else (
    call :print_status "Redis: HEALTHY"
)
goto :eof

REM Function to show help
:show_help
echo GRC Platform v4 - Docker Management Script (Windows)
echo.
echo Usage: %0 [COMMAND] [OPTIONS]
echo.
echo Commands:
echo   start-all          Start all services (infrastructure + applications + monitoring)
echo   start-apps         Start only application services (dms-service, notification-service)
echo   start-infra        Start only infrastructure services (mysql, redis, rabbitmq, elasticsearch)
echo   status             Show service status and URLs
echo   logs [SERVICE]     Show logs for all services or specific service
echo   stop               Stop all services
echo   restart [SERVICE]  Restart all services or specific service
echo   health             Check health status of all services
echo   cleanup            Stop all services and remove all data (WARNING: destructive)
echo   help               Show this help message
echo.
echo Examples:
echo   %0 start-all                 # Start everything
echo   %0 logs dms-service          # Show DMS service logs
echo   %0 restart notification-service  # Restart notification service
echo   %0 health                    # Check service health
goto :eof

REM Main script logic
if "%1"=="start-all" (
    call :start_all
) else if "%1"=="start-apps" (
    call :start_apps
) else if "%1"=="start-infra" (
    call :start_infra
) else if "%1"=="status" (
    call :show_status
) else if "%1"=="logs" (
    call :show_logs %*
) else if "%1"=="stop" (
    call :stop_all
) else if "%1"=="restart" (
    call :restart_service %*
) else if "%1"=="health" (
    call :health_check
) else if "%1"=="cleanup" (
    call :cleanup
) else if "%1"=="help" (
    call :show_help
) else if "%1"=="--help" (
    call :show_help
) else if "%1"=="-h" (
    call :show_help
) else if "%1"=="" (
    call :print_error "No command specified. Use '%0 help' for usage information."
    exit /b 1
) else (
    call :print_error "Unknown command: %1. Use '%0 help' for usage information."
    exit /b 1
)
