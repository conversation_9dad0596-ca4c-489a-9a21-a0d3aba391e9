# ===== DIAGNOSTICS SCHEMA =====
# GraphQL schema for system diagnostics operations (replacing SharePointTestController and other diagnostic REST endpoints)
# Note: StorageProvider enum is defined in main schema.graphqls

# System Health Types
type SystemHealth {
  status: HealthStatus!
  timestamp: DateTime!
  version: String!
  uptime: Long! # milliseconds
  components: [ComponentHealth!]!
  overallScore: Float! # 0.0 to 1.0
  issues: [HealthIssue!]!
  recommendations: [String!]!
}

enum HealthStatus {
  UP
  DOWN
  DEGRADED
  UNKNOWN
  MAINTENANCE
}

type ComponentHealth {
  name: String!
  status: HealthStatus!
  details: ComponentHealthDetails!
  lastChecked: DateTime!
  responseTime: Long! # milliseconds
  errorCount: Int!
  warningCount: Int!
}

type ComponentHealthDetails {
  description: String!
  version: String
  connected: Boolean!
  lastError: String
  metrics: [HealthMetric!]!
  configuration: String # JSON string
}

type HealthMetric {
  name: String!
  value: String!
  unit: String
  threshold: String
  status: MetricStatus!
}

enum MetricStatus {
  NORMAL
  WARNING
  CRITICAL
  UNKNOWN
}

type HealthIssue {
  severity: IssueSeverity!
  component: String!
  message: String!
  details: String
  timestamp: DateTime!
  resolution: String
  recommendation: String
}

enum IssueSeverity {
  INFO
  WARNING
  ERROR
  CRITICAL
}

# Connection Test Types
type ConnectionTestResult {
  testId: String!
  testType: ConnectionTestType!
  target: String!
  status: TestStatus!
  success: Boolean!
  responseTime: Long! # milliseconds
  message: String!
  details: ConnectionTestDetails
  timestamp: DateTime!
  retryCount: Int!
}

enum ConnectionTestType {
  SHAREPOINT
  DATABASE
  ELASTICSEARCH
  STORAGE_PROVIDER
  STORAGE
  EXTERNAL_API
  NETWORK
  AUTHENTICATION
}

enum TestStatus {
  PASSED
  FAILED
  TIMEOUT
  SKIPPED
  RUNNING
}

type ConnectionTestDetails {
  endpoint: String!
  method: String
  statusCode: Int
  headers: String # JSON string
  responseSize: Long
  sslInfo: SSLInfo
  networkInfo: NetworkInfo
  errorDetails: String

  # Fields expected by tests
  version: String
  connectionPool: ConnectionPool

  # Storage provider specific fields
  region: String
  bucket: String
  lastError: String
}

type SSLInfo {
  isSecure: Boolean!
  protocol: String
  certificateValid: Boolean!
  certificateExpiry: DateTime
  issuer: String
}

type NetworkInfo {
  ipAddress: String
  port: Int
  latency: Long # milliseconds
  packetLoss: Float # percentage
  bandwidth: Long # bytes per second
}

# Diagnostic Result Types
type DiagnosticResult {
  # Fields expected by tests
  testId: String!
  testSuite: String!
  status: TestStatus!
  overallResult: TestStatus!
  startTime: DateTime!
  endTime: DateTime
  duration: Long! # milliseconds
  testsRun: Int!
  testsPassed: Int!
  testsFailed: Int!
  testsSkipped: Int!
  summary: DiagnosticSummary!
  recommendations: [String!]!

  # Schema fields (for GraphQL compatibility)
  diagnosticId: String!
  startedAt: DateTime!
  completedAt: DateTime
  totalTests: Int!
  passedTests: Int!
  failedTests: Int!
  skippedTests: Int!
  results: [DiagnosticTestResult!]!
}

type DiagnosticTestResult {
  testName: String!
  category: String!
  status: TestStatus!
  success: Boolean!
  message: String!
  details: String
  duration: Long! # milliseconds
  expectedResult: String
  actualResult: String
  errorMessage: String
  stackTrace: String
}

type DiagnosticSummary {
  overallHealth: HealthStatus!
  criticalIssues: Int!
  warnings: Int!
  recommendations: [String!]!
  nextCheckRecommended: DateTime!
}

# System Performance Types
type SystemPerformance {
  timestamp: DateTime!

  # Fields expected by tests
  cpuUsage: Float!
  memoryUsage: Float!
  diskUsage: Float!
  networkIO: NetworkIO!
  jvmMetrics: JvmMetrics!
  databaseMetrics: DatabaseMetrics!
  applicationMetrics: ApplicationMetrics!

  # Schema fields (for GraphQL compatibility)
  cpu: PerformanceMetric!
  memory: PerformanceMetric!
  disk: PerformanceMetric!
  network: NetworkPerformance!
  jvm: JVMPerformance!
  database: DatabasePerformance!
  application: ApplicationPerformance!
}

type PerformanceMetric {
  current: Float!
  average: Float!
  peak: Float!
  unit: String!
  status: MetricStatus!
  threshold: Float
}

type NetworkPerformance {
  inbound: PerformanceMetric!
  outbound: PerformanceMetric!
  connections: Int!
  errors: Int!
}

type JVMPerformance {
  heapUsed: PerformanceMetric!
  heapMax: PerformanceMetric!
  nonHeapUsed: PerformanceMetric!
  gcCount: Int!
  gcTime: Long! # milliseconds
  threadCount: Int!
  classesLoaded: Int!
}

type DatabasePerformance {
  connectionPool: PerformanceMetric!
  activeConnections: Int!
  queryTime: PerformanceMetric!
  slowQueries: Int!
  errors: Int!
}

type ApplicationPerformance {
  requestsPerSecond: PerformanceMetric!
  averageResponseTime: PerformanceMetric!
  errorRate: PerformanceMetric!
  activeUsers: Int!
  cacheHitRate: PerformanceMetric!
}

# Storage Provider Diagnostics
type StorageProviderDiagnostic {
  provider: StorageProvider!
  status: HealthStatus!
  connectionTest: ConnectionTestResult!
  performance: StoragePerformance!
  capacity: StorageCapacity!
  configuration: StorageConfigurationInfo!
  lastTested: DateTime!
}

type StoragePerformance {
  readSpeed: PerformanceMetric!
  writeSpeed: PerformanceMetric!
  latency: PerformanceMetric!
  throughput: PerformanceMetric!
  errorRate: PerformanceMetric!
}

type StorageCapacity {
  totalSpace: Long! # bytes
  usedSpace: Long! # bytes
  freeSpace: Long! # bytes
  usagePercentage: Float!
  quotaLimit: Long # bytes
  quotaUsed: Long # bytes
}

type StorageConfigurationInfo {
  endpoint: String
  region: String
  bucket: String
  path: String
  encryption: Boolean!
  compression: Boolean!
  backupEnabled: Boolean!
  retentionDays: Int
}

# Input Types
input DiagnosticTestInput {
  testSuite: String = "FULL"
  includePerformance: Boolean = true
  includeConnectivity: Boolean = true
  includeStorage: Boolean = true
  includeDatabase: Boolean = true
  includeElasticsearch: Boolean = true
  timeout: Int = 30 # seconds
}

input ConnectionTestInput {
  testType: ConnectionTestType!
  target: String
  timeout: Int = 10 # seconds
  retryCount: Int = 3
  parameters: String # JSON string for test-specific parameters
}

# Query Extensions
extend type Query {
  # System health and diagnostics (replacing REST endpoints)
  getSystemHealth: SystemHealth!
  getComponentHealth(componentName: String!): ComponentHealth
  
  # Connection tests
  testSharePointConnection: ConnectionTestResult!
  testDatabaseConnection: ConnectionTestResult!
  testElasticsearchConnection: ConnectionTestResult!
  testStorageProviderConnection(provider: StorageProvider!): ConnectionTestResult!
  testConnection(input: ConnectionTestInput!): ConnectionTestResult!
  
  # Comprehensive diagnostics
  runDiagnostics(input: DiagnosticTestInput!): DiagnosticResult!
  getDiagnosticHistory(
    limit: Int = 10
    dateFrom: DateTime
    dateTo: DateTime
  ): [DiagnosticResult!]!
  
  # Performance monitoring
  getSystemPerformance: SystemPerformance!
  getPerformanceHistory(
    component: String
    hours: Int = 24
  ): [SystemPerformance!]!
  
  # Storage diagnostics
  getStorageProviderDiagnostics: [StorageProviderDiagnostic!]!
  getStorageProviderHealth(provider: StorageProvider!): StorageProviderDiagnostic!
  
  # Application metrics
  getApplicationMetrics: ApplicationMetrics!
  getHealthSummary: HealthSummary!
}

# Additional Types
type ApplicationMetrics {
  uptime: Long! # milliseconds
  totalRequests: Long!
  totalErrors: Long!
  averageResponseTime: Float! # milliseconds
  peakResponseTime: Float! # milliseconds
  activeUsers: Int!
  documentsProcessed: Long!
  storageUsed: Long! # bytes
  cacheStatistics: CacheStatistics!

  # Fields expected by tests
  requestCount: Long!
  errorCount: Long!
}

type CacheStatistics {
  hitCount: Long!
  missCount: Long!
  hitRate: Float! # percentage
  missRate: Float! # percentage
  evictionCount: Long!
  size: Long!
  maxSize: Int!
}

type HealthSummary {
  overallStatus: HealthStatus!
  components: [ComponentHealth!]!
  lastUpdated: DateTime!
  # Additional fields for test compatibility
  componentsUp: Int!
  componentsDown: Int!
  componentsDegraded: Int!
  totalComponents: Int!
  criticalIssues: Int!
  warnings: Int!
  systemLoad: Float!
  availabilityPercentage: Float!
  # Legacy fields
  score: Float! # 0.0 to 1.0
  lastChecked: DateTime!
  nextCheckDue: DateTime!
  trends: [HealthTrend!]!
}

type HealthTrend {
  component: String!
  trend: TrendDirection!
  changePercentage: Float!
  period: String!
}

enum TrendDirection {
  IMPROVING
  STABLE
  DEGRADING
  UNKNOWN
}

# Types expected by tests
type NetworkIO {
  bytesIn: Long!
  bytesOut: Long!
  packetsIn: Long!
  packetsOut: Long!
}

type JvmMetrics {
  heapUsed: Long!
  heapMax: Long!
  nonHeapUsed: Long!
  gcCount: Int!
  gcTime: Long!
  threadCount: Int!
}

type DatabaseMetrics {
  activeConnections: Int!
  idleConnections: Int!
  totalConnections: Int!
  queryCount: Long!
  averageQueryTime: Float!
}

type ConnectionPool {
  active: Int!
  idle: Int!
  max: Int!
}

# Mutation Extensions
extend type Mutation {
  # Diagnostic operations
  runSystemDiagnostics(input: DiagnosticTestInput!): DiagnosticResult!
  runConnectionTest(input: ConnectionTestInput!): ConnectionTestResult!
  
  # Health check operations
  refreshSystemHealth: SystemHealth!
  refreshComponentHealth(componentName: String!): ComponentHealth!
  
  # Maintenance operations
  clearDiagnosticHistory(olderThanDays: Int = 30): Int!
  resetHealthMetrics: Boolean!
  
  # Performance operations
  clearPerformanceHistory(olderThanDays: Int = 7): Int!
  optimizeSystemPerformance: Boolean!
}
