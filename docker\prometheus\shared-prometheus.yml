# Prometheus Configuration for Shared Infrastructure
# Monitors both DMS and Notification services when available
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alerts.yml"

scrape_configs:
  # DMS Service Metrics (when available)
  - job_name: 'dms-service'
    static_configs:
      - targets: ['dms-service-app:9464']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Notification Service Metrics (when available)
  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification-service-app:9091']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Shared Infrastructure Metrics
  - job_name: 'mysql'
    static_configs:
      - targets: ['grc-mysql-shared:3306']
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'redis'
    static_configs:
      - targets: ['grc-redis-shared:6379']
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['grc-rabbitmq-shared:15692']
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['grc-elasticsearch-shared:9200']
    metrics_path: '/_prometheus/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
