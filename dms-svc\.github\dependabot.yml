# Dependabot configuration for automated dependency updates
# This file configures <PERSON><PERSON><PERSON><PERSON> to automatically create pull requests for dependency updates
# across different package ecosystems used in the DMS project.

version: 2
updates:
  # Maven dependencies (Java)
  - package-ecosystem: "maven"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10
    reviewers:
      - "dms-team"
    assignees:
      - "dms-maintainer"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "automated"
    milestone: "dependency-updates"
    
    # Group related updates together
    groups:
      spring-boot:
        patterns:
          - "org.springframework.boot:*"
          - "org.springframework:*"
          - "org.springframework.security:*"
          - "org.springframework.data:*"
          - "org.springframework.graphql:*"
        update-types:
          - "minor"
          - "patch"
      
      aws-sdk:
        patterns:
          - "software.amazon.awssdk:*"
          - "com.amazonaws:*"
        update-types:
          - "minor"
          - "patch"
      
      azure-sdk:
        patterns:
          - "com.azure:*"
          - "com.microsoft.graph:*"
        update-types:
          - "minor"
          - "patch"
      
      testing-frameworks:
        patterns:
          - "org.junit.*:*"
          - "org.mockito:*"
          - "org.testcontainers:*"
          - "org.awaitility:*"
          - "com.github.tomakehurst:*"
        update-types:
          - "minor"
          - "patch"
      
      monitoring-observability:
        patterns:
          - "io.micrometer:*"
          - "io.opentelemetry.*:*"
          - "io.zipkin.*:*"
          - "net.logstash.logback:*"
        update-types:
          - "minor"
          - "patch"
      
      document-processing:
        patterns:
          - "org.apache.tika:*"
          - "org.apache.pdfbox:*"
          - "org.apache.poi:*"
        update-types:
          - "minor"
          - "patch"
      
      security-libraries:
        patterns:
          - "io.jsonwebtoken:*"
          - "org.springframework.security:*"
        update-types:
          - "patch"  # Only patch updates for security libraries
    
    # Ignore specific dependencies that require manual updates
    ignore:
      # Major version updates that need careful testing
      - dependency-name: "org.springframework.boot:*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "com.mysql:mysql-connector-j"
        update-types: ["version-update:semver-major"]
      - dependency-name: "org.elasticsearch:*"
        update-types: ["version-update:semver-major"]
      
      # Dependencies with known compatibility issues
      - dependency-name: "com.graphql-java:graphql-java-extended-scalars"
        versions: [">= 23.0"]  # Stick to v22.x for compatibility
      
      # Test dependencies that are stable
      - dependency-name: "com.h2database:h2"
        update-types: ["version-update:semver-major"]

  # GitHub Actions dependencies
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "dms-team"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "ci/cd"
      - "dependencies"
      - "automated"

  # Docker dependencies (if using Dockerfile)
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 3
    reviewers:
      - "dms-team"
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "docker"
      - "dependencies"
      - "automated"
    
    # Only patch and minor updates for base images
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]

# Additional configuration notes:
# 1. Security updates are automatically prioritized by Dependabot
# 2. Pull requests include detailed changelogs and compatibility information
# 3. Automated tests must pass before merge is allowed
# 4. Critical security updates bypass the normal schedule and are created immediately
# 5. Dependencies are grouped to reduce PR noise and improve review efficiency
