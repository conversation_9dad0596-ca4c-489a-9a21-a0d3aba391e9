package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Paginated history DTO for conversion operations from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionHistoryPage {
    private List<ConversionResult> content;
    private Integer totalElements;
    private Integer totalPages;
    private Integer size;
    private Integer number;
    private Boolean first;
    private Boolean last;
}
