# ===== CONVERSION SCHEMA =====
# GraphQL schema for file conversion operations (replacing MarkdownConversionController and other conversion REST endpoints)
# Note: VirusScannerType is defined in main schema.graphqls

# Conversion Status and Progress Types
enum ConversionStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  PAUSED
}

enum ConversionMethod {
  PANDOC
  LEGACY
  FALLBACK
  NATIVE
  EXTERNAL_API
}

enum ConversionType {
  MARKDOWN_TO_WORD
  PDF_TO_WORD
  WORD_TO_PDF
  HTML_TO_PDF
  EXCEL_TO_PDF
  POWERPOINT_TO_PDF
  IMAGE_TO_PDF
  TEXT_TO_PDF
}

# Conversion Result Types
type ConversionResult {
  sessionId: String!
  conversionType: ConversionType!
  originalFileName: String!
  convertedFileName: String!
  downloadPath: String!
  downloadUrl: String
  fileSize: Long!
  status: ConversionStatus!
  success: Boolean!
  message: String!
  errorDetails: String
  startedAt: DateTime
  completedAt: DateTime
  processingTimeMs: Long!
  conversionMethod: ConversionMethod!
  usedPandoc: Boolean!
  virusScanResponse: VirusScanResponse
  metadata: ConversionMetadata
}

type VirusScanResponse {
  scanId: String!
  scanResult: String!
  scannerType: VirusScannerType!
  detectedThreats: [String!]!
  scanDurationMs: Long!
  scanTimestamp: DateTime!
  isClean: Boolean!
}

type ConversionMetadata {
  originalMimeType: String!
  convertedMimeType: String!
  originalSize: Long!
  convertedSize: Long!
  compressionRatio: Float
  qualitySettings: String
  conversionOptions: String # JSON string
  engineVersion: String
  processingNode: String
}

# Conversion Progress Tracking
type ConversionProgress {
  sessionId: String!
  conversionId: String! # Added for test compatibility
  conversionType: ConversionType!
  status: ConversionStatus!
  progress: Float! # 0.0 to 1.0
  progressPercentage: Float! # Added for test compatibility (0-100)
  currentStep: String!
  totalSteps: Int!
  completedSteps: Int!
  startedAt: DateTime!
  lastUpdatedAt: DateTime!
  estimatedTimeRemaining: Long # milliseconds
  processingRate: Float # pages/files per second
  errorMessage: String
  message: String # Added for test compatibility
}

# Batch Conversion Types
type BatchConversionResult {
  batchId: String!
  totalFiles: Int!
  completedFiles: Int!
  failedFiles: Int!
  status: ConversionStatus!
  results: [ConversionResult!]!
  startedAt: DateTime!
  completedAt: DateTime
  totalProcessingTime: Long!
  progress: Float!
  estimatedCompletionTime: DateTime
  errors: [ConversionError!]!
}

type ConversionError {
  fileName: String!
  errorCode: String!
  errorMessage: String!
  details: String
  timestamp: DateTime!
}

# Conversion Statistics
type ConversionStatistics {
  totalConversions: Long!
  successfulConversions: Long!
  failedConversions: Long!
  averageProcessingTime: Float!
  totalProcessingTime: Long!
  conversionsByDate: [ConversionTrendData!]!
  topErrorReasons: [String!]!
  conversionsByType: [ConversionTypeStats!]!
  conversionsByMethod: [ConversionMethodStats!]!
  conversionTrends: [ConversionTrendData!]!
  popularFormats: [FormatPopularity!]!
}

type ConversionTypeStats {
  conversionType: ConversionType!
  count: Long!
  successRate: Float!
  averageProcessingTime: Float!
}

type ConversionMethodStats {
  method: ConversionMethod!
  count: Long!
  successRate: Float!
  averageProcessingTime: Float!
}

type ConversionTrendData {
  date: DateTime!
  conversionCount: Long!
  successRate: Float!
  averageProcessingTime: Float!
}

type FormatPopularity {
  fromFormat: String!
  toFormat: String!
  count: Long!
  percentage: Float!
}

# Input Types for Conversions
input ConversionOptionsInput {
  quality: ConversionQuality = STANDARD
  compressionLevel: Int # 1-9 for applicable formats
  preserveFormatting: Boolean = true
  includeImages: Boolean = true
  includeMetadata: Boolean = true
  pageSize: String # A4, Letter, etc.
  orientation: PageOrientation = PORTRAIT
  margins: MarginsInput
  customOptions: String # JSON string for format-specific options
}

enum ConversionQuality {
  LOW
  STANDARD
  HIGH
  MAXIMUM
}

enum PageOrientation {
  PORTRAIT
  LANDSCAPE
}

input MarginsInput {
  top: Float
  bottom: Float
  left: Float
  right: Float
  unit: String = "cm" # cm, in, pt
}

# Specific Conversion Input Types
input MarkdownConversionInput {
  file: Upload
  filePath: String
  scannerType: VirusScannerType
  options: ConversionOptionsInput
  outputFormat: String = "docx"
}

input PdfConversionInput {
  file: Upload
  filePath: String
  scannerType: VirusScannerType
  options: ConversionOptionsInput
  outputFormat: String = "docx"
}

input WordConversionInput {
  file: Upload
  filePath: String
  scannerType: VirusScannerType
  options: ConversionOptionsInput
  outputFormat: String = "pdf"
}

input BatchConversionInput {
  files: [Upload!]
  filePaths: [String!]
  conversionType: ConversionType!
  scannerType: VirusScannerType
  options: ConversionOptionsInput
  outputFormat: String
  maxConcurrentConversions: Int = 3
  continueOnError: Boolean = true
  notifyOnCompletion: Boolean = false
  notificationEmail: String
}

# Conversion Queue Management
type ConversionQueue {
  queueId: String!
  totalJobs: Int!
  pendingJobs: Int!
  processingJobs: Int!
  completedJobs: Int!
  failedJobs: Int!
  estimatedWaitTime: Long # milliseconds
  jobs: [ConversionJob!]!
}

type ConversionJob {
  jobId: String!
  sessionId: String!
  conversionType: ConversionType!
  fileName: String!
  status: ConversionStatus!
  priority: ConversionPriority!
  submittedAt: DateTime!
  startedAt: DateTime
  estimatedCompletion: DateTime
  userId: String!
  progress: Float!
}

enum ConversionPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

# Query Extensions
extend type Query {
  # Conversion status and progress
  getConversionStatus(sessionId: String!): ConversionResult
  getConversionProgress(sessionId: String, conversionId: String): ConversionProgress
  getBatchConversionStatus(batchId: String!): BatchConversionResult
  
  # Conversion history and statistics
  getConversionHistory(
    userId: String
    conversionType: ConversionType
    dateFrom: DateTime
    dateTo: DateTime
    pagination: PaginationInput
  ): ConversionHistoryPage!
  
  getConversionStatistics(
    dateFrom: DateTime
    dateTo: DateTime
    conversionTypes: [ConversionType!]
  ): ConversionStatistics!
  
  # Queue management
  getConversionQueue: ConversionQueue!
  getUserConversions(
    userId: String!
    status: ConversionStatus
    pagination: PaginationInput
  ): ConversionHistoryPage!
  
  # System capabilities
  getSupportedConversions: [SupportedConversion!]!
  getConversionCapabilities: ConversionCapabilities!
}

# Additional Types
type ConversionHistoryPage {
  content: [ConversionResult!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}

type SupportedConversion {
  fromFormat: String!
  toFormat: String!
  conversionType: ConversionType!
  supportedMethods: [ConversionMethod!]!
  maxFileSize: Long!
  description: String!
}

type ConversionCapabilities {
  pandocAvailable: Boolean!
  pandocVersion: String
  supportedInputFormats: [String!]!
  supportedOutputFormats: [String!]!
  maxFileSize: Long!
  maxBatchSize: Int!
  concurrentConversions: Int!
  queueCapacity: Int!
}

# Mutation Extensions
extend type Mutation {
  # Single file conversions (replacing REST endpoints)
  convertMarkdownToWord(input: MarkdownConversionInput!): ConversionResult!
  convertPdfToWord(input: PdfConversionInput!): ConversionResult!
  convertWordToPdf(input: WordConversionInput!): ConversionResult!
  
  # Generic conversion mutation
  convertFile(
    input: ConversionInput!
  ): ConversionResult!
  
  # Batch conversions
  batchConvertFiles(input: BatchConversionInput!): BatchConversionResult!
  
  # Conversion management
  cancelConversion(sessionId: String!): Boolean!
  pauseConversion(sessionId: String!): Boolean!
  resumeConversion(sessionId: String!): Boolean!
  retryConversion(sessionId: String!): ConversionResult!
  
  # Queue management
  prioritizeConversion(sessionId: String!, priority: ConversionPriority!): Boolean!
  cancelBatchConversion(batchId: String!): Boolean!
  
  # Cleanup operations
  cleanupCompletedConversions(olderThanDays: Int = 7): Int!
  cleanupFailedConversions(olderThanDays: Int = 3): Int!
}

# Generic Conversion Input
input ConversionInput {
  file: Upload
  filePath: String
  fromFormat: String!
  toFormat: String!
  scannerType: VirusScannerType
  options: ConversionOptionsInput
  priority: ConversionPriority = NORMAL
}
