package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

/**
 * Base entity class providing common auditing fields and functionality for all domain entities.
 *
 * This abstract class provides:
 * - Automatic ID generation using IDENTITY strategy
 * - Audit fields for creation and modification tracking
 * - JPA lifecycle callbacks for timestamp management
 * - GraphQL-compatible DateTime conversion methods
 *
 * All domain entities should extend this class to inherit standard auditing capabilities.
 *
 * <AUTHOR> Development Team
 * @version 1.1.0
 * @since 1.0.0
 */
@MappedSuperclass
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private LocalDateTime createdDate;
    
    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private LocalDateTime lastModifiedDate;
    
    @CreatedBy
    @Column(name = "created_by", length = 100, updatable = false)
    private String createdBy;
    
    @LastModifiedBy
    @Column(name = "last_modified_by", length = 100)
    private String lastModifiedBy;
    
    @PrePersist
    protected void onCreate() {
        if (createdDate == null) {
            createdDate = LocalDateTime.now();
        }
        if (lastModifiedDate == null) {
            lastModifiedDate = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        lastModifiedDate = LocalDateTime.now();
    }
    
    // GraphQL compatibility methods for DateTime serialization
    @Transient
    public OffsetDateTime getCreatedDateTime() {
        return createdDate != null ? createdDate.atOffset(ZoneOffset.UTC) : null;
    }
    
    @Transient
    public OffsetDateTime getLastModifiedDateTime() {
        return lastModifiedDate != null ? lastModifiedDate.atOffset(ZoneOffset.UTC) : null;
    }
}
