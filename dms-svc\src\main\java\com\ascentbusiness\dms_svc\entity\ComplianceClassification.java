package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.ComplianceClassificationLevel;
import com.ascentbusiness.dms_svc.enums.DataSubjectCategory;
import com.ascentbusiness.dms_svc.enums.GeographicRegion;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Entity representing a compliance classification for documents
 */
@Entity
@Table(name = "compliance_classifications")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ComplianceClassification extends BaseEntity {

    @Column(name = "name", nullable = false, length = 200, unique = true)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "classification_level", nullable = false, length = 50)
    private ComplianceClassificationLevel classificationLevel;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "color_code", length = 7) // Hex color code
    private String colorCode;

    @Column(name = "icon", length = 50)
    private String icon;

    @Builder.Default
    @Column(name = "requires_approval", nullable = false)
    private Boolean requiresApproval = false;

    @Column(name = "approval_role", length = 100)
    private String approvalRole; // Role required for approval

    @Builder.Default
    @Column(name = "requires_encryption", nullable = false)
    private Boolean requiresEncryption = false;

    @Builder.Default
    @Column(name = "requires_audit_logging", nullable = false)
    private Boolean requiresAuditLogging = true;

    @Column(name = "max_retention_days")
    private Integer maxRetentionDays;

    @ElementCollection(targetClass = DataSubjectCategory.class)
    @Enumerated(EnumType.STRING)
    @CollectionTable(
        name = "classification_data_subjects",
        joinColumns = @JoinColumn(name = "classification_id")
    )
    @Column(name = "data_subject_category")
    private Set<DataSubjectCategory> applicableDataSubjects;

    @ElementCollection(targetClass = GeographicRegion.class)
    @Enumerated(EnumType.STRING)
    @CollectionTable(
        name = "classification_regions",
        joinColumns = @JoinColumn(name = "classification_id")
    )
    @Column(name = "region")
    private Set<GeographicRegion> allowedRegions;

    @Column(name = "access_restrictions", columnDefinition = "TEXT")
    private String accessRestrictions; // JSON or text describing access rules

    @Column(name = "handling_instructions", columnDefinition = "TEXT")
    private String handlingInstructions;

    @Column(name = "disposal_instructions", columnDefinition = "TEXT")
    private String disposalInstructions;

    @Builder.Default
    @Column(name = "priority", nullable = false)
    private Integer priority = 0; // Higher number = higher priority

    @Column(name = "effective_date")
    private LocalDateTime effectiveDate;

    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;

    // Relationships
    @OneToMany(mappedBy = "complianceClassification", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<DocumentComplianceMapping> documentMappings;

    /**
     * Check if this classification is currently effective
     */
    @Transient
    public boolean isCurrentlyEffective() {
        LocalDateTime now = LocalDateTime.now();
        
        if (!isActive) {
            return false;
        }
        
        if (effectiveDate != null && now.isBefore(effectiveDate)) {
            return false;
        }
        
        if (expiryDate != null && now.isAfter(expiryDate)) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if this classification applies to a specific data subject category
     */
    @Transient
    public boolean appliesTo(DataSubjectCategory dataSubject) {
        return applicableDataSubjects == null || 
               applicableDataSubjects.isEmpty() || 
               applicableDataSubjects.contains(dataSubject);
    }

    /**
     * Check if this classification allows storage in a specific region
     */
    @Transient
    public boolean allowsStorageIn(GeographicRegion region) {
        return allowedRegions == null || 
               allowedRegions.isEmpty() || 
               allowedRegions.contains(region) ||
               allowedRegions.contains(GeographicRegion.GLOBAL);
    }

    /**
     * Get the effective retention period considering both classification and data subject requirements
     */
    @Transient
    public Integer getEffectiveRetentionDays() {
        Integer classificationRetention = maxRetentionDays;
        Integer levelRetention = classificationLevel.getMaxRetentionDays();
        
        // Use the most restrictive (shortest) retention period
        if (classificationRetention == null) {
            return levelRetention;
        }
        if (levelRetention == null) {
            return classificationRetention;
        }
        
        return Math.min(classificationRetention, levelRetention);
    }

    /**
     * Check if user with given roles can access documents with this classification
     */
    @Transient
    public boolean canAccess(Set<String> userRoles) {
        if (userRoles == null || userRoles.isEmpty()) {
            return classificationLevel == ComplianceClassificationLevel.PUBLIC;
        }
        
        // Admin can access everything
        if (userRoles.contains("ADMIN") || userRoles.contains("SYSTEM_ADMIN")) {
            return true;
        }
        
        // Check classification level requirements
        switch (classificationLevel) {
            case PUBLIC:
                return true;
            case INTERNAL:
                return userRoles.contains("EMPLOYEE") || userRoles.contains("INTERNAL_ACCESS");
            case CONFIDENTIAL:
                return userRoles.contains("CONFIDENTIAL_ACCESS") || userRoles.contains("MANAGER");
            case RESTRICTED:
                return userRoles.contains("RESTRICTED_ACCESS") || userRoles.contains("SENIOR_MANAGER");
            case TOP_SECRET:
                return userRoles.contains("TOP_SECRET_ACCESS") || userRoles.contains("EXECUTIVE");
            default:
                return false;
        }
    }

    /**
     * Get compliance score based on security requirements
     */
    @Transient
    public int getComplianceScore() {
        int score = classificationLevel.getLevel() * 20; // Base score from level
        
        if (requiresApproval) score += 10;
        if (requiresEncryption) score += 15;
        if (requiresAuditLogging) score += 5;
        if (maxRetentionDays != null && maxRetentionDays < 1825) score += 10; // Less than 5 years
        
        return score;
    }

    @Override
    public String toString() {
        return String.format("ComplianceClassification{id=%d, name='%s', level=%s, active=%s}", 
                           getId(), name, classificationLevel, isActive);
    }
}
