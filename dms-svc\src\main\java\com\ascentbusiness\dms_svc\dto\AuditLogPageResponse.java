package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.AuditLog;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * Response DTO for paginated audit log results
 * Used in GraphQL audit operations
 */
@Data
@Builder
public class AuditLogPageResponse {
    private List<AuditLog> content;
    private Integer totalElements;
    private Integer totalPages;
    private Integer size;
    private Integer number;
    private Boolean first;
    private Boolean last;
    private Integer numberOfElements;
    private Boolean empty;

    /**
     * Create AuditLogPageResponse from Spring Data Page
     * Maintains compatibility with existing REST controller
     */
    public static AuditLogPageResponse fromPage(Page<AuditLog> page) {
        return AuditLogPageResponse.builder()
                .content(page.getContent())
                .totalElements((int) page.getTotalElements())
                .totalPages(page.getTotalPages())
                .size(page.getSize())
                .number(page.getNumber())
                .first(page.isFirst())
                .last(page.isLast())
                .numberOfElements(page.getNumberOfElements())
                .empty(page.isEmpty())
                .build();
    }
}
