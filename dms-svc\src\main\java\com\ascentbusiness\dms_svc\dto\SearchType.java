package com.ascentbusiness.dms_svc.dto;

/**
 * Enumeration of search types supported by the advanced search functionality.
 * 
 * Each search type uses different Elasticsearch query strategies optimized
 * for specific use cases and search patterns.
 */
public enum SearchType {
    
    /**
     * Multi-field search with field boosting.
     * Searches across name, description, content, and keywords with different weights.
     * Best for general-purpose search queries.
     */
    MULTI_FIELD,
    
    /**
     * Fuzzy matching for handling typos and variations.
     * Uses Elasticsearch fuzzy queries with automatic fuzziness.
     * Good for handling user input errors and similar terms.
     */
    FUZZY,
    
    /**
     * Exact phrase matching.
     * Searches for exact phrase matches in text fields.
     * Best for finding specific quotes or exact terminology.
     */
    PHRASE,
    
    /**
     * Wildcard pattern matching.
     * Supports * and ? wildcards in search terms.
     * Useful for partial matches and pattern-based searches.
     */
    WILDCARD,
    
    /**
     * Semantic/natural language search.
     * Uses advanced text analysis and semantic understanding.
     * Best for natural language queries and conceptual searches.
     */
    SEMANTIC,
    
    /**
     * Content-only search.
     * Searches only within document content, ignoring metadata.
     * Optimized for finding information within document bodies.
     */
    CONTENT_ONLY,
    
    /**
     * Metadata-only search.
     * Searches only in metadata fields (name, description, tags, etc.).
     * Fast search for document properties and attributes.
     */
    METADATA_ONLY
}
