import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { SharedArray } from 'k6/data';

// Custom metrics
const errorRate = new Rate('errors');
const uploadTrend = new Trend('upload_duration');
const downloadTrend = new Trend('download_duration');
const searchTrend = new Trend('search_duration');
const deleteTrend = new Trend('delete_duration');
const totalRequests = new Counter('total_requests');

// Test data
const testUsers = new SharedArray('users', function () {
    return [
        { username: 'testuser1', token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...' },
        { username: 'testuser2', token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...' },
        { username: 'testuser3', token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...' },
        { username: 'testuser4', token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...' },
        { username: 'testuser5', token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...' }
    ];
});

// Test configuration
export let options = {
    stages: [
        { duration: '2m', target: 10 },   // Ramp up to 10 users
        { duration: '5m', target: 10 },   // Stay at 10 users
        { duration: '2m', target: 20 },   // Ramp up to 20 users
        { duration: '5m', target: 20 },   // Stay at 20 users
        { duration: '2m', target: 50 },   // Ramp up to 50 users
        { duration: '5m', target: 50 },   // Stay at 50 users
        { duration: '2m', target: 0 },    // Ramp down to 0 users
    ],
    thresholds: {
        http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
        http_req_failed: ['rate<0.05'],    // Error rate must be below 5%
        upload_duration: ['p(95)<5000'],   // 95% of uploads must complete below 5s
        download_duration: ['p(95)<1000'], // 95% of downloads must complete below 1s
        search_duration: ['p(95)<500'],    // 95% of searches must complete below 500ms
    },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:8080';
const GRAPHQL_ENDPOINT = `${BASE_URL}/dms/graphql`;

// Generate test file content
function generateTestFileContent(sizeKB = 100) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    const targetLength = sizeKB * 1024;
    
    for (let i = 0; i < targetLength; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Get random user
function getRandomUser() {
    return testUsers[Math.floor(Math.random() * testUsers.length)];
}

// GraphQL request helper
function graphqlRequest(query, variables = {}, user = null) {
    const headers = {
        'Content-Type': 'application/json',
        'X-Correlation-ID': `k6-test-${__VU}-${__ITER}-${Date.now()}`,
    };
    
    if (user && user.token) {
        headers['Authorization'] = `Bearer ${user.token}`;
    }
    
    const payload = JSON.stringify({
        query: query,
        variables: variables
    });
    
    totalRequests.add(1);
    return http.post(GRAPHQL_ENDPOINT, payload, { headers: headers });
}

// Document upload test
function testDocumentUpload() {
    group('Document Upload', function () {
        const user = getRandomUser();
        const fileContent = generateTestFileContent(Math.floor(Math.random() * 500) + 100); // 100-600KB
        const fileName = `load-test-${__VU}-${__ITER}-${Date.now()}.txt`;
        
        const uploadMutation = `
            mutation UploadDocument($input: DocumentUploadInput!) {
                uploadDocument(input: $input) {
                    id
                    name
                    size
                    storageProvider
                    uploadedAt
                }
            }
        `;
        
        const variables = {
            input: {
                name: fileName,
                content: fileContent,
                mimeType: 'text/plain',
                storageProvider: 'LOCAL',
                metadata: {
                    description: 'K6 load test document',
                    tags: ['load-test', 'performance']
                }
            }
        };
        
        const startTime = Date.now();
        const response = graphqlRequest(uploadMutation, variables, user);
        const duration = Date.now() - startTime;
        
        uploadTrend.add(duration);
        
        const success = check(response, {
            'upload status is 200': (r) => r.status === 200,
            'upload has no errors': (r) => !JSON.parse(r.body).errors,
            'upload returns document ID': (r) => {
                const data = JSON.parse(r.body).data;
                return data && data.uploadDocument && data.uploadDocument.id;
            }
        });
        
        if (!success) {
            errorRate.add(1);
            console.error(`Upload failed for user ${user.username}: ${response.body}`);
        } else {
            const documentId = JSON.parse(response.body).data.uploadDocument.id;
            return documentId;
        }
    });
}

// Document search test
function testDocumentSearch() {
    group('Document Search', function () {
        const user = getRandomUser();
        const searchTerms = ['load-test', 'performance', 'document', 'test', 'k6'];
        const searchTerm = searchTerms[Math.floor(Math.random() * searchTerms.length)];
        
        const searchQuery = `
            query SearchDocuments($input: DocumentSearchInput!) {
                searchDocuments(input: $input) {
                    documents {
                        id
                        name
                        size
                        uploadedAt
                    }
                    totalCount
                    hasNextPage
                }
            }
        `;
        
        const variables = {
            input: {
                query: searchTerm,
                pageSize: 20,
                pageNumber: 0
            }
        };
        
        const startTime = Date.now();
        const response = graphqlRequest(searchQuery, variables, user);
        const duration = Date.now() - startTime;
        
        searchTrend.add(duration);
        
        const success = check(response, {
            'search status is 200': (r) => r.status === 200,
            'search has no errors': (r) => !JSON.parse(r.body).errors,
            'search returns results': (r) => {
                const data = JSON.parse(r.body).data;
                return data && data.searchDocuments;
            }
        });
        
        if (!success) {
            errorRate.add(1);
            console.error(`Search failed for user ${user.username}: ${response.body}`);
        }
    });
}

// Document download test
function testDocumentDownload(documentId) {
    if (!documentId) return;
    
    group('Document Download', function () {
        const user = getRandomUser();
        
        const downloadQuery = `
            query DownloadDocument($id: ID!) {
                downloadDocument(id: $id) {
                    content
                    mimeType
                    size
                }
            }
        `;
        
        const variables = { id: documentId };
        
        const startTime = Date.now();
        const response = graphqlRequest(downloadQuery, variables, user);
        const duration = Date.now() - startTime;
        
        downloadTrend.add(duration);
        
        const success = check(response, {
            'download status is 200': (r) => r.status === 200,
            'download has no errors': (r) => !JSON.parse(r.body).errors,
            'download returns content': (r) => {
                const data = JSON.parse(r.body).data;
                return data && data.downloadDocument && data.downloadDocument.content;
            }
        });
        
        if (!success) {
            errorRate.add(1);
            console.error(`Download failed for user ${user.username}: ${response.body}`);
        }
    });
}

// Document deletion test
function testDocumentDeletion(documentId) {
    if (!documentId) return;
    
    group('Document Deletion', function () {
        const user = getRandomUser();
        
        const deleteMutation = `
            mutation DeleteDocument($id: ID!) {
                deleteDocument(id: $id) {
                    success
                    message
                }
            }
        `;
        
        const variables = { id: documentId };
        
        const startTime = Date.now();
        const response = graphqlRequest(deleteMutation, variables, user);
        const duration = Date.now() - startTime;
        
        deleteTrend.add(duration);
        
        const success = check(response, {
            'delete status is 200': (r) => r.status === 200,
            'delete has no errors': (r) => !JSON.parse(r.body).errors,
            'delete returns success': (r) => {
                const data = JSON.parse(r.body).data;
                return data && data.deleteDocument && data.deleteDocument.success;
            }
        });
        
        if (!success) {
            errorRate.add(1);
            console.error(`Delete failed for user ${user.username}: ${response.body}`);
        }
    });
}

// Main test function
export default function () {
    // Simulate realistic user behavior with think time
    const thinkTime = Math.random() * 2 + 1; // 1-3 seconds
    
    // Test document operations in sequence
    const documentId = testDocumentUpload();
    sleep(thinkTime);
    
    testDocumentSearch();
    sleep(thinkTime);
    
    if (documentId && Math.random() > 0.7) { // 30% chance to download
        testDocumentDownload(documentId);
        sleep(thinkTime);
    }
    
    if (documentId && Math.random() > 0.8) { // 20% chance to delete
        testDocumentDeletion(documentId);
    }
    
    sleep(thinkTime);
}

// Setup function
export function setup() {
    console.log('Starting DMS Load Test');
    console.log(`Base URL: ${BASE_URL}`);
    console.log(`GraphQL Endpoint: ${GRAPHQL_ENDPOINT}`);
    
    // Health check
    const healthResponse = http.get(`${BASE_URL}/dms/actuator/health`);
    if (healthResponse.status !== 200) {
        throw new Error(`Health check failed: ${healthResponse.status}`);
    }
    
    console.log('Health check passed - starting load test');
    return { startTime: Date.now() };
}

// Teardown function
export function teardown(data) {
    const duration = (Date.now() - data.startTime) / 1000;
    console.log(`Load test completed in ${duration} seconds`);
}
