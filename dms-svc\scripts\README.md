# DMS Service Scripts

This directory contains operational and utility scripts for the DMS service.

## 📁 Directory Contents

### Health Check Scripts
- `check-app-status.ps1` - Check if the DMS application is running and ready
- `simple-health-check.ps1` - Simple comprehensive health check for DMS Service
- `comprehensive-health-check.ps1` - Detailed health check with multiple components

### Test Execution Scripts
- `run-all-tests.bat` - **Consolidated test runner** - Windows batch script to run all test categories
- `run-all-tests.sh` - Unix shell script to run all tests
- `run-infrastructure-tests.ps1` - PowerShell script for infrastructure and deployment tests
- `run-tests-with-coverage.sh` - Shell script for tests with coverage analysis
- `test-summary.bat` - Quick test inventory and status summary
- `generate-html-report.bat` - **HTML test report generator** - Creates user-friendly HTML reports from test results

### Environment Setup Scripts
- `setup-local-env.bat` - Windows batch script for local environment setup
- `setup-local-env.sh` - Unix shell script for local environment setup

## 🏥 Health Check Scripts

### check-app-status.ps1
**Purpose**: Check if the DMS application is running and ready to accept requests.

**Features**:
- Polls the health endpoint until the application is ready
- Configurable timeout and retry attempts
- Clear status reporting

**Usage**:
```powershell
.\check-app-status.ps1
```

**Use Cases**:
- CI/CD pipeline health checks
- Deployment verification
- Startup monitoring

### simple-health-check.ps1
**Purpose**: Perform a simple but comprehensive health check of the DMS service.

**Features Tested**:
- Application health endpoint
- Database connectivity (inferred)
- GraphQL endpoint accessibility
- Storage provider status
- Security configuration

**Usage**:
```powershell
.\simple-health-check.ps1
```

**Output**: Summary report with HEALTHY/UNHEALTHY status for each component.

### comprehensive-health-check.ps1
**Purpose**: Detailed health check with extensive component testing.

**Features Tested**:
- Application health and info endpoints
- Database connectivity
- GraphQL schema loading
- Storage provider configurations
- Security and authentication
- Performance metrics
- Detailed component analysis

**Usage**:
```powershell
.\comprehensive-health-check.ps1
```

**Output**: Detailed report with component-by-component analysis and recommendations.

## 🧪 Test Execution Scripts

### run-all-tests.bat (Consolidated Test Runner)
**Purpose**: Execute the complete test suite with all test categories in a single command.

**Test Categories Supported**:
- Unit Tests
- Integration Tests
- End-to-End (E2E) Tests
- Security Tests
- Performance Tests
- Contract Tests
- Configuration Tests
- Infrastructure Tests
- API Tests
- GraphQL Tests
- Compliance Tests
- Retention Policy Tests

**Features**:
- **Selective execution** - Run specific test categories only
- **Fail-fast mode** - Stop on first test failure
- **Comprehensive reporting** - Coverage and test reports
- **Timestamped results** - Organized output with timestamps
- **PowerShell integration** - Includes infrastructure and API tests
- **Detailed logging** - Complete execution logs

**Usage**:
```batch
# Run all tests
.\run-all-tests.bat

# Run specific test categories
.\run-all-tests.bat --unit-only
.\run-all-tests.bat --integration-only
.\run-all-tests.bat --security-only

# Run with options
.\run-all-tests.bat --fail-fast
.\run-all-tests.bat --no-reports

# Show help
.\run-all-tests.bat --help
```

**Output**: Timestamped reports in `target/test-reports/YYYYMMDD_HHMMSS/`

### run-infrastructure-tests.ps1
**Purpose**: Execute infrastructure-specific tests including Docker, Kubernetes, and CI/CD validation.

**Features**:
- Docker container testing
- Kubernetes deployment validation
- CI/CD pipeline testing
- Configuration validation
- Environment-specific testing

**Usage**:
```powershell
.\run-infrastructure-tests.ps1
.\run-infrastructure-tests.ps1 -UnitOnly
.\run-infrastructure-tests.ps1 -DockerOnly
.\run-infrastructure-tests.ps1 -FailFast
```

### run-tests-with-coverage.sh
**Purpose**: Execute tests with detailed coverage analysis and workflow-specific reporting.

**Features**:
- JaCoCo coverage reporting
- Workflow-specific test analysis
- Coverage summary extraction
- Test file inventory

**Usage**:
```bash
chmod +x run-tests-with-coverage.sh
./run-tests-with-coverage.sh
```

### test-summary.bat
**Purpose**: Quick overview of available tests and recent results without running tests.

**Features**:
- Test file inventory by category
- Available test scripts check
- Recent test results summary
- Environment prerequisites check

**Usage**:
```batch
.\test-summary.bat
```

### generate-html-report.bat (HTML Test Report Generator)
**Purpose**: Generate user-friendly HTML reports from existing XML test results.

**Features**:
- Converts XML test results to HTML format
- Automatically opens report in browser
- Shows test summary with pass/fail statistics
- Detailed results by test class
- Works with existing results (no re-running needed)

**Usage**:
```batch
.\generate-html-report.bat
```

**Requirements**:
- Existing XML test results in `target/surefire-reports/`
- PowerShell execution policy allowing script execution

**Output**:
- HTML report at `target/site/surefire-report.html`
- Automatic browser opening
- Test summary with visual indicators

**Use Cases**:
- Viewing test results in user-friendly format
- Sharing test results with stakeholders
- Quick test status overview
- Post-test analysis and reporting

## 🔧 Environment Setup Scripts

### setup-local-env.bat / setup-local-env.sh
**Purpose**: Set up the local development environment with required configurations.

**Features**:
- Environment variable configuration
- Local database setup
- Storage provider configuration
- Development-specific settings

**Usage**:
```bash
# Windows
.\setup-local-env.bat

# Unix/Linux/Mac
chmod +x setup-local-env.sh
./setup-local-env.sh
```

## 🚀 Quick Start

### For Developers
1. **Set up environment**: Run the appropriate setup script for your platform
2. **Start the application**: Use `mvn spring-boot:run` or your IDE
3. **Verify health**: Run `.\simple-health-check.ps1` to ensure everything is working
4. **Run tests**: Execute `.\run-all-tests.bat` or `./run-all-tests.sh`

### For CI/CD
1. **Application startup**: Use `.\check-app-status.ps1` to wait for application readiness
2. **Health verification**: Run `.\comprehensive-health-check.ps1` for detailed status
3. **Test execution**: Use the test runner scripts for automated testing

## 📋 Prerequisites

### PowerShell Scripts
- PowerShell 5.1 or later
- Network access to DMS service (default: http://localhost:9093)
- Appropriate permissions for script execution

### Shell Scripts
- Bash shell
- Maven installed and configured
- Java 11 or later
- Network access to required services

## 🔍 Troubleshooting

### Common Issues

**Script Execution Policy (Windows)**:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**Permission Denied (Unix/Linux)**:
```bash
chmod +x script-name.sh
```

**Service Not Responding**:
1. Check if the DMS service is running
2. Verify the correct port (default: 9093)
3. Check firewall settings
4. Review application logs

### Health Check Failures
1. **Database Issues**: Check database connectivity and configuration
2. **Storage Provider Issues**: Verify storage provider configuration
3. **Authentication Issues**: Check JWT configuration and security settings
4. **Network Issues**: Verify network connectivity and port availability

## 📊 Script Output

### Health Check Scripts
- **PASS/FAIL** status for each component
- **Detailed error messages** for failures
- **Performance metrics** where applicable
- **Recommendations** for issue resolution

### Test Scripts
- **Test execution summary**
- **Pass/fail counts**
- **Test report locations**
- **Coverage information**
- **HTML test reports** (automatically generated)
- **Browser-friendly result viewing**

## 🔗 Related Documentation

- `/tests/scripts/` - Test-specific scripts and documentation
- `/docs/testing/` - Comprehensive testing documentation
- `/docs/deployment/` - Deployment and configuration guides
- `/docs/troubleshooting/` - Detailed troubleshooting guides

## 📝 Adding New Scripts

When adding new operational scripts:

1. **Follow naming conventions**: Use descriptive names with appropriate extensions
2. **Include documentation**: Add comprehensive comments and usage instructions
3. **Error handling**: Implement proper error handling and meaningful messages
4. **Cross-platform**: Consider compatibility across different operating systems
5. **Update this README**: Document the new script's purpose and usage
