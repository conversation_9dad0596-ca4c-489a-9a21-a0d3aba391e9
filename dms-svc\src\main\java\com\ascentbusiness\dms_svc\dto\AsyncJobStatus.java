package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.AsyncProcessingJob;
import com.ascentbusiness.dms_svc.enums.ProcessingStatus;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * DTO for async job status information.
 */
@Data
public class AsyncJobStatus {
    
    private String jobId;
    private ProcessingStatus status;
    private String fileName;
    private Long fileSize;
    private Float progress;
    private Integer estimatedTimeRemaining;
    private OffsetDateTime startedAt;
    private OffsetDateTime completedAt;
    private String errorMessage;
    private DocumentEx document;
    
    /**
     * Create AsyncJobStatus from AsyncProcessingJob entity.
     * 
     * @param job the processing job
     * @return AsyncJobStatus DTO
     */
    public static AsyncJobStatus fromEntity(AsyncProcessingJob job) {
        AsyncJobStatus status = new AsyncJobStatus();
        
        status.setJobId(job.getJobId());
        status.setStatus(job.getStatus());
        status.setFileName(job.getFileName());
        status.setFileSize(job.getFileSize());
        status.setProgress(job.getProgressAsFloat());
        status.setEstimatedTimeRemaining(job.getEstimatedTimeRemaining());
        status.setErrorMessage(job.getErrorMessage());
        
        // Convert timestamps
        if (job.getStartedAt() != null) {
            status.setStartedAt(job.getStartedAt().atOffset(java.time.ZoneOffset.UTC));
        }
        if (job.getCompletedAt() != null) {
            status.setCompletedAt(job.getCompletedAt().atOffset(java.time.ZoneOffset.UTC));
        }
        
        // Convert document if available
        if (job.getDocument() != null) {
            status.setDocument(DocumentEx.fromDocument(job.getDocument(), 
                    job.getProcessingStrategy(), job.getStatus()));
        }
        
        return status;
    }
    
    /**
     * Check if the job is complete.
     * 
     * @return true if job is complete
     */
    public boolean isComplete() {
        return status == ProcessingStatus.COMPLETED;
    }
    
    /**
     * Check if the job has failed.
     * 
     * @return true if job has failed
     */
    public boolean isFailed() {
        return status == ProcessingStatus.FAILED;
    }
    
    /**
     * Check if the job is still active.
     * 
     * @return true if job is active
     */
    public boolean isActive() {
        return status == ProcessingStatus.QUEUED || status == ProcessingStatus.PROCESSING;
    }
}
