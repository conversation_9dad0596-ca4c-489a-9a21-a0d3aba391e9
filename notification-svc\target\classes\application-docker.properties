# Docker Environment Configuration for Notification Service
# This file contains Docker-specific settings that override the main application.properties

# Server Configuration
server.port=9091

# Database Configuration - Docker
spring.datasource.url=*****************************************************************************************************************************************************************************************************************
spring.datasource.username=notification_user
spring.datasource.password=notification_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Enable JPA Auditing
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

# Flyway Configuration
spring.flyway.enabled=false
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration

# GraphQL Configuration - Docker
spring.graphql.graphiql.enabled=true
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.printer.enabled=true
spring.graphql.path=/graphql
spring.graphql.graphiql.path=/graphiql
# Use allowedOriginPatterns instead of allowedOrigins when allowCredentials is true
spring.graphql.cors.allowed-origin-patterns=*
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID,Accept
spring.graphql.cors.allow-credentials=true

# RabbitMQ Configuration - Docker
spring.rabbitmq.host=rabbitmq
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin123

# Redis Configuration - Docker
spring.redis.host=redis
spring.redis.port=6379
spring.redis.password=shared_redis_password
spring.cache.type=redis
spring.cache.redis.time-to-live=3600
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=notification:

# Redis Connection Pool Configuration (Lettuce)
spring.data.redis.lettuce.pool.max-active=20
spring.data.redis.lettuce.pool.max-idle=10
spring.data.redis.lettuce.pool.min-idle=2
spring.data.redis.lettuce.pool.max-wait=2000ms
spring.data.redis.lettuce.pool.time-between-eviction-runs=30s

# Email Configuration - Use environment variables or default to mock
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=*

# Notification Configuration
notification.email.from=${NOTIFICATION_FROM_EMAIL:<EMAIL>}
notification.email.enabled=${EMAIL_ENABLED:true}
notification.email.mock=${EMAIL_MOCK:false}

# Security Configuration
security.jwt.enabled=true
jwt.issuer=notification-service
jwt.audience=notification-clients
jwt.secret=${JWT_SECRET:sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always
management.health.defaults.enabled=true

# Health Indicators
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=true
management.health.rabbitmq.enabled=true
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true

# Logging Configuration - Docker
logging.level.com.ascentbusiness.notification_svc=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN
logging.level.org.springframework.security=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Application Configuration
spring.application.name=notification-svc
