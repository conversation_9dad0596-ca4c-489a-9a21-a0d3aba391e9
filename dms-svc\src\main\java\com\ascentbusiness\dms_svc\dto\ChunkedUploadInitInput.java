package com.ascentbusiness.dms_svc.dto;

import lombok.Data;

/**
 * Input DTO for initializing a chunked upload session.
 * Corresponds to ChunkedUploadInitInput GraphQL input type.
 */
@Data
public class ChunkedUploadInitInput {
    
    /**
     * Name of the file being uploaded.
     */
    private String fileName;
    
    /**
     * Total size of the file in bytes.
     */
    private Long totalSize;
    
    /**
     * Size of each chunk in bytes (optional, will use default if not provided).
     */
    private Integer chunkSize;
    
    /**
     * MIME type of the file (optional).
     */
    private String mimeType;
    
    /**
     * Additional metadata for the upload (optional).
     */
    private DocumentUploadMetadataInput metadata;
    
    /**
     * Validate the input parameters.
     * 
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("File name is required");
        }
        
        if (totalSize == null || totalSize <= 0) {
            throw new IllegalArgumentException("Total size must be positive");
        }
        
        if (chunkSize != null && chunkSize <= 0) {
            throw new IllegalArgumentException("Chunk size must be positive if provided");
        }
    }
}
