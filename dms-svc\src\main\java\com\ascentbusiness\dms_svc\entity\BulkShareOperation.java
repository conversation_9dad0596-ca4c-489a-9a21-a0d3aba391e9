package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.Permission;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Entity representing a bulk document sharing operation.
 * 
 * This entity tracks bulk sharing operations where multiple documents
 * are shared with multiple users/roles in a single transaction.
 * It provides:
 * - Operation tracking and status management
 * - Success/failure statistics
 * - Audit trail for bulk operations
 * - Relationship to individual share items
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Entity
@Table(name = "bulk_share_operations", indexes = {
    @Index(name = "idx_bulk_operation_id", columnList = "operation_id"),
    @Index(name = "idx_bulk_created_by", columnList = "created_by_user_id"),
    @Index(name = "idx_bulk_created_at", columnList = "created_at"),
    @Index(name = "idx_bulk_completed", columnList = "is_completed"),
    @Index(name = "idx_bulk_expires_at", columnList = "expires_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkShareOperation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /** Unique identifier for this bulk operation (UUID) */
    @Column(name = "operation_id", nullable = false, unique = true, length = 36)
    private String operationId;
    
    /** User who initiated this bulk share operation */
    @Column(name = "created_by_user_id", nullable = false, length = 100)
    private String createdByUserId;
    
    /** When this operation was created */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /** Permission level granted to all recipients */
    @Column(name = "permission", nullable = false)
    @Enumerated(EnumType.STRING)
    private Permission permission;
    
    /** When all share links from this operation expire */
    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;
    
    /** Optional: notes about this bulk share operation */
    @Column(name = "notes", length = 1000)
    private String notes;
    
    /** Whether this operation has completed processing */
    @Column(name = "is_completed", nullable = false)
    @Builder.Default
    private Boolean isCompleted = false;
    
    /** When this operation completed */
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    /** Total number of documents in this operation */
    @Column(name = "total_documents", nullable = false)
    private Integer totalDocuments;
    
    /** Total number of recipients in this operation */
    @Column(name = "total_recipients", nullable = false)
    private Integer totalRecipients;
    
    /** Number of successful share link creations */
    @Column(name = "success_count", nullable = false)
    @Builder.Default
    private Integer successCount = 0;
    
    /** Number of failed share link creations */
    @Column(name = "failure_count", nullable = false)
    @Builder.Default
    private Integer failureCount = 0;
    
    /** Individual items in this bulk operation */
    @OneToMany(mappedBy = "operation", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<BulkShareItem> items;
    
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (isCompleted == null) {
            isCompleted = false;
        }
        if (successCount == null) {
            successCount = 0;
        }
        if (failureCount == null) {
            failureCount = 0;
        }
    }
    
    /**
     * Mark this operation as completed
     */
    public void markCompleted() {
        this.isCompleted = true;
        this.completedAt = LocalDateTime.now();
    }
    
    /**
     * Get the total number of items processed (success + failure)
     */
    @Transient
    public int getTotalProcessed() {
        return (successCount != null ? successCount : 0) + (failureCount != null ? failureCount : 0);
    }
    
    /**
     * Get the expected total number of items (documents * recipients)
     */
    @Transient
    public int getExpectedTotal() {
        return (totalDocuments != null ? totalDocuments : 0) * (totalRecipients != null ? totalRecipients : 0);
    }
    
    /**
     * Check if this operation is fully processed
     */
    @Transient
    public boolean isFullyProcessed() {
        return getTotalProcessed() >= getExpectedTotal();
    }
    
    /**
     * Get success rate as a percentage
     */
    @Transient
    public double getSuccessRate() {
        int total = getTotalProcessed();
        if (total == 0) {
            return 0.0;
        }
        return ((double) (successCount != null ? successCount : 0) / total) * 100.0;
    }
}
