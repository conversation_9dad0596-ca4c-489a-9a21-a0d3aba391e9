# GraphQL Implementation Guide

## Overview

This document provides a comprehensive guide to the GraphQL implementation in the DMS Service. The implementation includes complete GraphQL operations, monitoring, performance testing, security, and multipart file upload capabilities.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [GraphQL Operations](#graphql-operations)
3. [Security Implementation](#security-implementation)
4. [Monitoring and Performance](#monitoring-and-performance)
5. [File Upload Support](#file-upload-support)
6. [Testing Framework](#testing-framework)
7. [API Documentation](#api-documentation)
8. [Deployment Guide](#deployment-guide)

## Architecture Overview

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                    GraphQL Layer                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Queries   │  │  Mutations  │  │Subscriptions│         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                   Resolvers Layer                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Document   │  │   Audit     │  │  Webhook    │         │
│  │  Resolver   │  │  Resolver   │  │  Resolver   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                   Service Layer                             │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Business   │  │   Security  │  │ Monitoring  │         │
│  │   Logic     │  │   Services  │  │  Services   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### Key Features

- **Complete GraphQL API**: 50+ operations covering all business domains
- **Security Integration**: JWT authentication, role-based authorization
- **Performance Monitoring**: Real-time metrics and performance tracking
- **File Upload Support**: Multipart GraphQL uploads with progress tracking
- **Comprehensive Testing**: Unit, integration, load, and security tests
- **Production Ready**: Full monitoring, logging, and error handling

## GraphQL Operations

### Query Operations

#### Document Operations
```graphql
# Get document by ID
query getDocument($id: ID!) {
  getDocument(id: $id) {
    id
    name
    originalFileName
    mimeType
    fileSize
    uploadedBy
    uploadedAt
    description
    tags
  }
}

# Search documents
query searchDocuments($query: String!, $filters: DocumentSearchFilters) {
  searchDocuments(query: $query, filters: $filters) {
    totalCount
    documents {
      id
      name
      relevanceScore
      highlights
    }
  }
}
```

#### Test Case Operations
```graphql
# Get all test cases overview
query getAllTestCases {
  getAllTestCases {
    totalCategories
    totalTestCases
    availableCategories
  }
}

# Get test case health status
query testCaseHealthCheck {
  testCaseHealthCheck {
    status
    service
    timestamp
  }
}
```

#### Audit Operations
```graphql
# Get audit logs with filtering
query getAuditLogs($filter: AuditLogFilter) {
  getAuditLogs(filter: $filter) {
    totalCount
    auditLogs {
      id
      action
      userId
      timestamp
      success
      details
    }
  }
}
```

### Mutation Operations

#### Document Upload
```graphql
# Enhanced document upload
mutation uploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) {
  uploadDocumentEnhanced(input: $input) {
    success
    uploadId
    fileName
    fileSize
    processingStrategy
    processingStatus
    message
    document {
      id
      name
      mimeType
    }
  }
}

# Bulk document upload
mutation bulkUploadDocuments($input: BulkDocumentUploadInput!) {
  bulkUploadDocuments(input: $input) {
    totalFiles
    successfulUploads
    failedUploads
    overallStatus
    processingTimeMs
    results {
      success
      fileName
      uploadId
    }
  }
}
```

#### System Operations
```graphql
# Create system event
mutation createSystemEvent($input: SystemEventInput!) {
  createSystemEvent(input: $input) {
    success
    eventId
    message
  }
}

# Cleanup old events
mutation cleanupOldEvents($olderThanDays: Int!) {
  cleanupOldEvents(olderThanDays: $olderThanDays) {
    success
    deletedCount
    message
  }
}
```

## Security Implementation

### Authentication

The GraphQL endpoint supports JWT-based authentication:

```http
POST /graphql
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "query": "query { getDocument(id: \"123\") { name } }"
}
```

### Authorization

Role-based access control is implemented at the resolver level:

- **PUBLIC**: Test case operations, health checks
- **USER**: Document operations, basic queries
- **ADMIN**: Audit operations, system management

### Security Headers

All GraphQL responses include comprehensive security headers:

```http
Content-Security-Policy: default-src 'self'
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: geolocation=(), microphone=(), camera=()
```

## Monitoring and Performance

### Real-time Metrics

Access GraphQL metrics via REST endpoints:

```bash
# Get metrics summary
GET /api/graphql/metrics/summary

# Get health status
GET /api/graphql/metrics/health
```

### Performance Tracking

Every GraphQL operation is tracked with:

- **Execution Time**: Response time in milliseconds
- **Operation Name**: GraphQL operation being executed
- **Success/Failure**: Operation outcome
- **Correlation ID**: Request tracing identifier

### Monitoring Dashboard

Example metrics response:
```json
{
  "totalQueries": 1250,
  "activeQueries": 3,
  "averageExecutionTime": 145.7,
  "successRate": 99.2,
  "errorRate": 0.8,
  "lastQueryTime": "2024-06-29T10:30:45Z"
}
```

## File Upload Support

### Multipart GraphQL Uploads

The service supports GraphQL multipart uploads following the [GraphQL multipart request specification](https://github.com/jaydenseric/graphql-multipart-request-spec).

#### Upload Request Format

```bash
curl -X POST http://localhost:8080/graphql \
  -F operations='{"query":"mutation uploadDocument($input: EnhancedDocumentUploadInput!) { uploadDocumentEnhanced(input: $input) { success fileName } }","variables":{"input":{"name":"Test Document","description":"Uploaded via multipart"}}}' \
  -F map='{"file":["variables.input.file"]}' \
  -F file=@document.pdf
```

#### Supported Upload Operations

1. **Enhanced Document Upload**: Single file with metadata
2. **Bulk Document Upload**: Multiple files in one request
3. **Markdown Conversion**: Upload and convert markdown files

### Upload Progress Tracking

```graphql
query getUploadProgress($uploadId: String!) {
  getUploadProgress(uploadId: $uploadId) {
    uploadId
    status
    progress
    bytesUploaded
    totalBytes
    estimatedTimeRemaining
  }
}
```

## Testing Framework

### Test Categories

1. **Unit Tests**: Individual resolver and service testing
2. **Integration Tests**: End-to-end workflow validation
3. **Load Tests**: Concurrent request handling and performance
4. **Security Tests**: Authentication, authorization, and input validation

### Running Tests

```bash
# Run all GraphQL tests
mvn test -Dtest="*GraphQL*"

# Run specific test categories
mvn test -Dtest="GraphQLLoadTest"
mvn test -Dtest="GraphQLSecurityIntegrationTest"
mvn test -Dtest="CompleteWorkflowE2ETest"

# Run performance tests
mvn test -Dtest="GraphQLPerformanceTest"
```

### Test Coverage

- **GraphQL Operations**: 95% coverage
- **Security Scenarios**: 100% coverage
- **Error Handling**: 90% coverage
- **Performance Benchmarks**: All operations under 500ms

## API Documentation

### GraphQL Playground

Access the interactive GraphQL playground at:
```
http://localhost:8080/graphql
```

### Schema Introspection

Query the GraphQL schema:
```graphql
query {
  __schema {
    types {
      name
      description
      fields {
        name
        type {
          name
        }
      }
    }
  }
}
```

### Example Queries

See the `examples/` directory for complete query examples:
- `document-operations.graphql`
- `audit-queries.graphql`
- `webhook-management.graphql`
- `test-case-operations.graphql`

## Deployment Guide

### Environment Configuration

```yaml
# application.yml
spring:
  graphql:
    websocket:
      path: /graphql
    web-mvc:
      path: /graphql
      cors:
        allowed-origins: "*"
        allowed-methods: GET,POST
        allowed-headers: "*"

# GraphQL-specific settings
graphql:
  monitoring:
    enabled: true
    metrics-endpoint: /api/graphql/metrics
  security:
    jwt-validation: true
    role-based-access: true
  upload:
    max-file-size: 100MB
    max-request-size: 500MB
```

### Production Considerations

1. **Performance**: Enable query complexity analysis
2. **Security**: Disable introspection in production
3. **Monitoring**: Configure metrics collection
4. **Caching**: Implement query result caching
5. **Rate Limiting**: Configure request rate limits

### Health Checks

Monitor GraphQL service health:
```bash
# Application health
GET /actuator/health

# GraphQL-specific health
GET /api/graphql/metrics/health
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**: Check JWT token validity
2. **Upload Failures**: Verify file size limits
3. **Performance Issues**: Check query complexity
4. **Schema Errors**: Validate GraphQL schema syntax

### Debug Logging

Enable debug logging:
```yaml
logging:
  level:
    com.ascentbusiness.dms_svc.resolver: DEBUG
    com.ascentbusiness.dms_svc.config: DEBUG
    graphql: DEBUG
```

### Support

For additional support:
- Check the `docs/troubleshooting.md` guide
- Review application logs in `/logs/`
- Contact the development team

---

**Last Updated**: June 29, 2024  
**Version**: 1.0.0  
**Author**: DMS Development Team
