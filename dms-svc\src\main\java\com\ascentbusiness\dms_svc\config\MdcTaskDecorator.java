package com.ascentbusiness.dms_svc.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;
import org.springframework.lang.NonNull;

import java.util.Map;

/**
 * TaskDecorator that propagates MDC (Mapped Diagnostic Context) to async threads.
 * 
 * This decorator ensures that correlation IDs and other MDC properties
 * are available in async operations by copying the MDC from the calling
 * thread to the async execution thread.
 */
public class MdcTaskDecorator implements TaskDecorator {

    private static final Logger logger = LoggerFactory.getLogger(MdcTaskDecorator.class);

    @Override
    @NonNull
    public Runnable decorate(@NonNull Runnable runnable) {
        // Capture the current MDC context from the calling thread
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        
        return () -> {
            try {
                // Set the captured MDC context in the async thread
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                    logger.debug("MDC context propagated to async thread: {}", contextMap);
                } else {
                    logger.debug("No MDC context to propagate to async thread");
                }
                
                // Execute the original runnable
                runnable.run();
                
            } finally {
                // Always clear MDC after execution to prevent memory leaks
                MDC.clear();
                logger.debug("MDC context cleared from async thread");
            }
        };
    }
}
