package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.ChunkedUploadSession;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * DTO for chunked upload status information.
 */
@Data
public class ChunkUploadStatus {
    
    private String sessionId;
    private String fileName;
    private Long totalSize;
    private String status;
    private Integer receivedChunks;
    private Integer totalChunks;
    private Long receivedBytes;
    private Float progress;
    private OffsetDateTime createdAt;
    private OffsetDateTime lastActivityAt;
    private OffsetDateTime completedAt;
    private String errorMessage;
    private DocumentEx document;
    
    /**
     * Create ChunkUploadStatus from ChunkedUploadSession entity.
     * 
     * @param session the upload session
     * @return ChunkUploadStatus DTO
     */
    public static ChunkUploadStatus fromEntity(ChunkedUploadSession session) {
        ChunkUploadStatus status = new ChunkUploadStatus();
        
        status.setSessionId(session.getSessionId());
        status.setFileName(session.getFileName());
        status.setTotalSize(session.getTotalSize());
        status.setStatus(session.getStatus());
        status.setReceivedChunks(session.getReceivedChunks());
        status.setTotalChunks(session.getTotalChunks());
        status.setReceivedBytes(session.getReceivedBytes());
        status.setProgress(session.getProgressAsFloat());
        status.setErrorMessage(session.getErrorMessage());
        
        // Convert timestamps
        if (session.getCreatedDate() != null) {
            status.setCreatedAt(session.getCreatedDate().atOffset(java.time.ZoneOffset.UTC));
        }
        if (session.getLastActivityAt() != null) {
            status.setLastActivityAt(session.getLastActivityAt().atOffset(java.time.ZoneOffset.UTC));
        }
        if (session.getCompletedAt() != null) {
            status.setCompletedAt(session.getCompletedAt().atOffset(java.time.ZoneOffset.UTC));
        }
        
        // Convert document if available
        if (session.getDocument() != null) {
            status.setDocument(DocumentEx.fromDocument(session.getDocument()));
        }
        
        return status;
    }
    
    /**
     * Check if the upload is complete.
     * 
     * @return true if upload is complete
     */
    public boolean isComplete() {
        return "COMPLETED".equals(status);
    }
    
    /**
     * Check if the upload has failed.
     * 
     * @return true if upload has failed
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }
    
    /**
     * Check if the upload is still active.
     * 
     * @return true if upload is active
     */
    public boolean isActive() {
        return "ACTIVE".equals(status);
    }
    
    /**
     * Get the remaining chunks to upload.
     * 
     * @return remaining chunks
     */
    public Integer getRemainingChunks() {
        if (totalChunks != null && receivedChunks != null) {
            return totalChunks - receivedChunks;
        }
        return null;
    }
    
    /**
     * Get the remaining bytes to upload.
     * 
     * @return remaining bytes
     */
    public Long getRemainingBytes() {
        if (totalSize != null && receivedBytes != null) {
            return totalSize - receivedBytes;
        }
        return null;
    }
}
