# GraphQL API Reference - Primary API

## Overview

This document provides a complete reference for all GraphQL operations in the DMS Service. GraphQL is the **primary and only API** for the DMS Service, providing comprehensive coverage of all business operations through a single, flexible endpoint.

## Base URL

```
POST http://localhost:8080/graphql
Content-Type: application/json
Authorization: Bearer <jwt-token>
```

## Query Operations

### Document Management

#### getDocument
Retrieve a document by its ID.

**Query:**
```graphql
query getDocument($id: ID!) {
  getDocument(id: $id) {
    id
    name
    originalFileName
    mimeType
    fileSize
    uploadedBy
    uploadedAt
    description
    tags
    status
    version
  }
}
```

**Variables:**
```json
{
  "id": "123"
}
```

**Response:**
```json
{
  "data": {
    "getDocument": {
      "id": "123",
      "name": "Sample Document",
      "originalFileName": "sample.pdf",
      "mimeType": "application/pdf",
      "fileSize": 1024000,
      "uploadedBy": "<EMAIL>",
      "uploadedAt": "2024-06-29T10:30:00Z",
      "description": "Sample document for testing",
      "tags": ["test", "sample"],
      "status": "ACTIVE",
      "version": 1
    }
  }
}
```

#### searchDocuments
Search documents with filters and pagination.

**Query:**
```graphql
query searchDocuments($query: String!, $filters: DocumentSearchFilters, $pagination: PaginationInput) {
  searchDocuments(query: $query, filters: $filters, pagination: $pagination) {
    totalCount
    documents {
      id
      name
      relevanceScore
      highlights
      snippet
    }
    facets {
      mimeTypes {
        value
        count
      }
      tags {
        value
        count
      }
    }
  }
}
```

### Test Case Management

#### getAllTestCases
Get overview of all test cases.

**Query:**
```graphql
query getAllTestCases {
  getAllTestCases {
    totalCategories
    totalTestCases
    availableCategories
  }
}
```

**Response:**
```json
{
  "data": {
    "getAllTestCases": {
      "totalCategories": 5,
      "totalTestCases": 150,
      "availableCategories": ["UNIT_TESTS", "INTEGRATION_TESTS", "PERFORMANCE_TESTS"]
    }
  }
}
```

#### getTestCase
Get specific test case details.

**Query:**
```graphql
query getTestCase($category: String!, $testId: String!) {
  getTestCase(category: $category, testId: $testId) {
    id
    name
    description
    testSteps
    expectedResults
    priority
    status
  }
}
```

#### testCaseHealthCheck
Check test case service health.

**Query:**
```graphql
query testCaseHealthCheck {
  testCaseHealthCheck {
    status
    service
    timestamp
    availableCategories
  }
}
```

### Audit Management

#### getAuditLogs
Retrieve audit logs with filtering.

**Query:**
```graphql
query getAuditLogs($filter: AuditLogFilter, $pagination: PaginationInput) {
  getAuditLogs(filter: $filter, pagination: $pagination) {
    totalCount
    auditLogs {
      id
      action
      userId
      timestamp
      success
      details
      correlationId
      ipAddress
    }
  }
}
```

**Variables:**
```json
{
  "filter": {
    "userId": "<EMAIL>",
    "actions": ["UPLOAD", "DELETE"],
    "dateFrom": "2024-06-01T00:00:00Z",
    "dateTo": "2024-06-29T23:59:59Z"
  },
  "pagination": {
    "page": 0,
    "size": 20
  }
}
```

### Webhook Management

#### getWebhookEndpointStatistics
Get webhook endpoint statistics.

**Query:**
```graphql
query getWebhookEndpointStatistics {
  getWebhookEndpointStatistics {
    totalDeliveries
    successfulDeliveries
    failedDeliveries
    averageResponseTime
    healthyEndpoints
    unhealthyEndpoints
  }
}
```

### System Events

#### getSystemEvents
Retrieve system events with filtering.

**Query:**
```graphql
query getSystemEvents($filter: SystemEventFilter, $pagination: PaginationInput) {
  getSystemEvents(filter: $filter, pagination: $pagination) {
    totalCount
    events {
      id
      eventType
      eventCategory
      eventName
      sourceEntityType
      sourceEntityId
      eventData
      timestamp
      actorUserId
    }
  }
}
```

## Mutation Operations

### Document Operations

#### uploadDocumentEnhanced
Upload a document with enhanced processing options.

**Mutation:**
```graphql
mutation uploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) {
  uploadDocumentEnhanced(input: $input) {
    success
    uploadId
    fileName
    fileSize
    processingStrategy
    processingStatus
    message
    document {
      id
      name
      mimeType
      uploadedAt
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "name": "Important Document",
    "description": "Critical business document",
    "file": "base64-encoded-file-content",
    "tags": ["important", "business"],
    "processingOptions": {
      "virusScan": true,
      "textExtraction": true,
      "thumbnailGeneration": true
    }
  }
}
```

#### bulkUploadDocuments
Upload multiple documents in a single operation.

**Mutation:**
```graphql
mutation bulkUploadDocuments($input: BulkDocumentUploadInput!) {
  bulkUploadDocuments(input: $input) {
    totalFiles
    successfulUploads
    failedUploads
    overallStatus
    processingTimeMs
    results {
      success
      fileName
      uploadId
      errorMessage
    }
  }
}
```

#### updateDocument
Update document metadata.

**Mutation:**
```graphql
mutation updateDocument($id: ID!, $input: DocumentUpdateInput!) {
  updateDocument(id: $id, input: $input) {
    success
    document {
      id
      name
      description
      tags
      lastModifiedAt
    }
    message
  }
}
```

#### deleteDocument
Delete a document.

**Mutation:**
```graphql
mutation deleteDocument($id: ID!) {
  deleteDocument(id: $id) {
    success
    message
  }
}
```

### System Operations

#### createSystemEvent
Create a new system event.

**Mutation:**
```graphql
mutation createSystemEvent($input: SystemEventInput!) {
  createSystemEvent(input: $input) {
    success
    eventId
    message
  }
}
```

**Variables:**
```json
{
  "input": {
    "eventType": "DOCUMENT_UPDATED",
    "eventCategory": "DOCUMENT",
    "eventName": "Document Modified",
    "sourceEntityType": "DOCUMENT",
    "sourceEntityId": 123,
    "eventData": {
      "documentId": 123,
      "changes": ["name", "description"]
    },
    "actorUserId": "<EMAIL>"
  }
}
```

#### cleanupOldEvents
Clean up old system events.

**Mutation:**
```graphql
mutation cleanupOldEvents($olderThanDays: Int!) {
  cleanupOldEvents(olderThanDays: $olderThanDays) {
    success
    deletedCount
    message
  }
}
```

### Audit Operations

#### verifyAuditIntegrity
Verify the integrity of audit logs.

**Mutation:**
```graphql
mutation verifyAuditIntegrity($logIds: [ID!]!) {
  verifyAuditIntegrity(logIds: $logIds) {
    success
    verifiedCount
    tamperingDetected
    message
    verificationDetails {
      logId
      isValid
      issues
    }
  }
}
```

#### purgeOldAuditLogs
Purge old audit logs (Admin only).

**Mutation:**
```graphql
mutation purgeOldAuditLogs($olderThanDays: Int!) {
  purgeOldAuditLogs(olderThanDays: $olderThanDays) {
    success
    deletedCount
    message
  }
}
```

## Input Types

### EnhancedDocumentUploadInput
```graphql
input EnhancedDocumentUploadInput {
  name: String!
  description: String
  file: Upload!
  tags: [String!]
  processingOptions: ProcessingOptionsInput
  metadata: DocumentMetadataInput
}
```

### ProcessingOptionsInput
```graphql
input ProcessingOptionsInput {
  virusScan: Boolean = true
  textExtraction: Boolean = false
  thumbnailGeneration: Boolean = false
  ocrProcessing: Boolean = false
  compressionLevel: CompressionLevel = MEDIUM
}
```

### DocumentSearchFilters
```graphql
input DocumentSearchFilters {
  mimeTypes: [String!]
  tags: [String!]
  uploadedBy: String
  dateFrom: DateTime
  dateTo: DateTime
  minFileSize: Long
  maxFileSize: Long
  status: DocumentStatus
}
```

### AuditLogFilter
```graphql
input AuditLogFilter {
  documentId: ID
  userId: String
  actions: [AuditAction!]
  eventTypes: [AuditEventType!]
  dateFrom: DateTime
  dateTo: DateTime
  correlationId: String
  success: Boolean
}
```

### SystemEventFilter
```graphql
input SystemEventFilter {
  eventTypes: [EventType!]
  eventCategories: [EventCategory!]
  sourceEntityType: String
  sourceEntityId: ID
  actorUserId: String
  dateFrom: DateTime
  dateTo: DateTime
}
```

## Enums

### DocumentStatus
```graphql
enum DocumentStatus {
  ACTIVE
  ARCHIVED
  DELETED
  PROCESSING
  FAILED
}
```

### AuditAction
```graphql
enum AuditAction {
  UPLOAD
  DOWNLOAD
  UPDATE
  DELETE
  VIEW
  PERMISSION_GRANT
  PERMISSION_REVOKE
  LOGIN
  LOGOUT
  SECURITY_VIOLATION
}
```

### EventType
```graphql
enum EventType {
  DOCUMENT_CREATED
  DOCUMENT_UPDATED
  DOCUMENT_DELETED
  USER_LOGIN
  USER_LOGOUT
  SYSTEM_ERROR
  WEBHOOK_DELIVERED
  AUDIT_LOG_CREATED
}
```

### EventCategory
```graphql
enum EventCategory {
  DOCUMENT
  USER
  SYSTEM
  SECURITY
  AUDIT
  WEBHOOK
}
```

## Error Handling

### Error Response Format
```json
{
  "errors": [
    {
      "message": "Document not found",
      "locations": [
        {
          "line": 2,
          "column": 3
        }
      ],
      "path": ["getDocument"],
      "extensions": {
        "code": "DOCUMENT_NOT_FOUND",
        "timestamp": "2024-06-29T10:30:00Z",
        "correlationId": "abc-123-def"
      }
    }
  ],
  "data": null
}
```

### Common Error Codes
- `AUTHENTICATION_REQUIRED`: JWT token missing or invalid
- `AUTHORIZATION_FAILED`: Insufficient permissions
- `DOCUMENT_NOT_FOUND`: Requested document doesn't exist
- `VALIDATION_ERROR`: Input validation failed
- `UPLOAD_FAILED`: File upload processing failed
- `SYSTEM_ERROR`: Internal server error

## Rate Limiting

The GraphQL endpoint implements rate limiting:
- **Authenticated Users**: 1000 requests per hour
- **Anonymous Users**: 100 requests per hour
- **Complex Queries**: Additional limits based on query complexity

## Pagination

Standard pagination format:
```graphql
input PaginationInput {
  page: Int = 0
  size: Int = 20
  sort: String = "id"
  direction: SortDirection = ASC
}

type PageInfo {
  totalElements: Long!
  totalPages: Int!
  number: Int!
  size: Int!
  first: Boolean!
  last: Boolean!
}
```

## Examples

### Complete Document Upload Workflow
```graphql
# 1. Upload document
mutation {
  uploadDocumentEnhanced(input: {
    name: "Contract.pdf"
    description: "Legal contract"
    file: "base64-content"
    tags: ["legal", "contract"]
  }) {
    success
    uploadId
    document {
      id
      name
    }
  }
}

# 2. Check upload progress
query {
  getUploadProgress(uploadId: "upload-123") {
    status
    progress
    estimatedTimeRemaining
  }
}

# 3. Retrieve uploaded document
query {
  getDocument(id: "doc-456") {
    id
    name
    status
    uploadedAt
  }
}
```

## Testing Examples

### Using cURL

```bash
# Simple query
curl -X POST http://localhost:8080/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "query": "query { getAllTestCases { totalCategories totalTestCases } }"
  }'

# Query with variables
curl -X POST http://localhost:8080/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "query": "query getDocument($id: ID!) { getDocument(id: $id) { name mimeType } }",
    "variables": { "id": "123" }
  }'
```

### Using JavaScript/Fetch

```javascript
const query = `
  query getAuditLogs($filter: AuditLogFilter) {
    getAuditLogs(filter: $filter) {
      totalCount
      auditLogs {
        id
        action
        userId
        timestamp
      }
    }
  }
`;

const variables = {
  filter: {
    userId: "<EMAIL>",
    actions: ["UPLOAD", "DELETE"]
  }
};

fetch('http://localhost:8080/graphql', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({ query, variables })
})
.then(response => response.json())
.then(data => console.log(data));
```

---

**Last Updated**: June 29, 2024
**Version**: 1.0.0
**GraphQL Schema Version**: 2.0
