package com.ascentbusiness.dms_svc.aspect;

import com.ascentbusiness.dms_svc.annotation.RateLimit;
import com.ascentbusiness.dms_svc.service.RedisRateLimitService;
import com.ascentbusiness.dms_svc.service.SecurityService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

/**
 * Enhanced aspect for handling distributed rate limiting with Redis
 */
@Aspect
@Component
public class RateLimitAspect {

    private static final Logger logger = LoggerFactory.getLogger(RateLimitAspect.class);

    @Autowired
    private RedisRateLimitService rateLimitService;

    @Autowired
    private SecurityService securityService;

    /**
     * Clear rate limit data (for testing purposes)
     */
    public void clearRateLimitData() {
        rateLimitService.clearRateLimitData();
    }

    @Around("@annotation(rateLimit)")
    public Object rateLimit(ProceedingJoinPoint joinPoint, RateLimit rateLimit) throws Throwable {
        RedisRateLimitService.RateLimitResult result = rateLimitService.checkRateLimit(rateLimit);

        if (!result.isAllowed()) {
            // Log security violation for rate limit exceeded
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String userId = auth != null && auth.isAuthenticated() ? auth.getName() : "anonymous";

            securityService.logSecurityViolation(
                userId,
                com.ascentbusiness.dms_svc.enums.SecurityViolationType.RATE_LIMIT_EXCEEDED,
                com.ascentbusiness.dms_svc.enums.ViolationSeverity.MEDIUM,
                null,
                rateLimit.type().name(),
                String.format("Rate limit exceeded: %d/%d requests in %d seconds",
                    result.getCurrentCount(), result.getLimit(), result.getWindowSeconds())
            );

            logger.warn("Rate limit exceeded for user: {}, type: {}, count: {}/{}, window: {}s",
                userId, rateLimit.type(), result.getCurrentCount(), result.getLimit(), result.getWindowSeconds());

            // Create detailed error response
            String errorMessage = String.format("%s (Limit: %d requests per %d seconds, Current: %d)",
                rateLimit.message(), result.getLimit(), result.getWindowSeconds(), result.getCurrentCount());

            ResponseStatusException exception = new ResponseStatusException(HttpStatus.TOO_MANY_REQUESTS, errorMessage);

            // Add rate limit headers for client information (handle test environment gracefully)
            try {
                exception.getHeaders().add("X-RateLimit-Limit", String.valueOf(result.getLimit()));
                exception.getHeaders().add("X-RateLimit-Remaining", String.valueOf(result.getRemainingRequests()));
                exception.getHeaders().add("X-RateLimit-Reset", String.valueOf(System.currentTimeMillis() + (result.getWindowSeconds() * 1000L)));
                exception.getHeaders().add("Retry-After", String.valueOf(result.getWindowSeconds()));
            } catch (UnsupportedOperationException e) {
                // In test environments, headers may be read-only - log and continue
                logger.debug("Unable to add rate limit headers in test environment: {}", e.getMessage());
            }

            throw exception;
        }

        logger.debug("Rate limit check passed for type: {}, count: {}/{}, window: {}s",
            rateLimit.type(), result.getCurrentCount(), result.getLimit(), result.getWindowSeconds());

        return joinPoint.proceed();
    }
}
