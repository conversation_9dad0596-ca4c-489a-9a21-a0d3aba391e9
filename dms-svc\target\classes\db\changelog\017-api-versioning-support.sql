--liquibase formatted sql

--changeset dms:017-api-versioning-support-001
--comment: Create API versioning support tables

-- Create table for API version tracking
CREATE TABLE api_versions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    version VARCHAR(20) NOT NULL UNIQUE,
    release_date DATE NOT NULL,
    is_current BOOLEAN DEFAULT FALSE,
    is_supported BOOLEAN DEFAULT TRUE,
    is_deprecated BOOLEAN DEFAULT FALSE,
    deprecation_date DATE,
    end_of_life_date DATE,
    description TEXT,
    breaking_changes TEXT,
    migration_notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_api_versions_version ON api_versions(version);
CREATE INDEX idx_api_versions_is_current ON api_versions(is_current);
CREATE INDEX idx_api_versions_is_supported ON api_versions(is_supported);
CREATE INDEX idx_api_versions_is_deprecated ON api_versions(is_deprecated);

--changeset dms:017-api-versioning-support-002
--comment: Create API usage tracking table

-- Create table for tracking API version usage
CREATE TABLE api_version_usage (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    user_id VARCHAR(100),
    client_ip VARCHAR(45),
    user_agent TEXT,
    operation_name VARCHAR(255),
    operation_type ENUM('QUERY', 'MUTATION', 'SUBSCRIPTION') NOT NULL,
    request_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    response_time_ms BIGINT,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    deprecation_warning BOOLEAN DEFAULT FALSE,
    correlation_id VARCHAR(100)
);

-- Add indexes for performance
CREATE INDEX idx_api_version_usage_version ON api_version_usage(version);
CREATE INDEX idx_api_version_usage_user_id ON api_version_usage(user_id);
CREATE INDEX idx_api_version_usage_timestamp ON api_version_usage(request_timestamp);
CREATE INDEX idx_api_version_usage_operation ON api_version_usage(operation_name, operation_type);
CREATE INDEX idx_api_version_usage_correlation_id ON api_version_usage(correlation_id);

--changeset dms:017-api-versioning-support-003
--comment: Create API deprecation warnings table

-- Create table for tracking deprecation warnings
CREATE TABLE api_deprecation_warnings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    element_type ENUM('FIELD', 'TYPE', 'OPERATION', 'ARGUMENT') NOT NULL,
    element_name VARCHAR(255) NOT NULL,
    deprecated_since VARCHAR(20),
    removed_in VARCHAR(20),
    replacement_element VARCHAR(255),
    warning_message TEXT,
    user_id VARCHAR(100),
    warning_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    warning_count BIGINT DEFAULT 1,
    last_warning_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_api_deprecation_warnings_version ON api_deprecation_warnings(version);
CREATE INDEX idx_api_deprecation_warnings_element ON api_deprecation_warnings(element_type, element_name);
CREATE INDEX idx_api_deprecation_warnings_user_id ON api_deprecation_warnings(user_id);
CREATE INDEX idx_api_deprecation_warnings_timestamp ON api_deprecation_warnings(warning_timestamp);

--changeset dms:017-api-versioning-support-004
--comment: Insert initial API version data

-- Insert initial API versions
INSERT INTO api_versions (version, release_date, is_current, is_supported, description) VALUES
('1.0', '2024-01-01', FALSE, TRUE, 'Initial API version with core document management functionality'),
('1.1', CURDATE(), TRUE, TRUE, 'Enhanced API with advanced search, analytics, and security features');

--changeset dms:017-api-versioning-support-005
--comment: Add API versioning configuration to security_config

-- Insert API versioning configuration
INSERT INTO security_config (config_key, config_value, description, created_by, is_active, created_date, last_modified_date) VALUES
('API_VERSION_CURRENT', '1.1', 'Current API version', 'SYSTEM', true, NOW(), NOW()),
('API_VERSION_SUPPORTED', '1.0,1.1', 'Comma-separated list of supported API versions', 'SYSTEM', true, NOW(), NOW()),
('API_VERSION_DEPRECATION_WARNING', 'true', 'Enable deprecation warnings for old API versions', 'SYSTEM', true, NOW(), NOW()),
('API_VERSION_STRICT_MODE', 'false', 'Enable strict version mode (only exact version matches allowed)', 'SYSTEM', true, NOW(), NOW()),
('API_VERSION_USAGE_TRACKING', 'true', 'Enable API version usage tracking', 'SYSTEM', true, NOW(), NOW()),
('API_VERSION_DEPRECATION_TRACKING', 'true', 'Enable deprecation warning tracking', 'SYSTEM', true, NOW(), NOW());

--changeset dms:017-api-versioning-support-006
--comment: Create stored procedures for API version management - DISABLED

-- Note: Stored procedure creation is disabled due to Liquibase compatibility issues with DELIMITER syntax
-- The cleanup functionality can be implemented in application code instead

-- -- Create stored procedure for API version cleanup
-- -- This is commented out due to Liquibase not supporting DELIMITER syntax properly
-- -- The cleanup can be done via application code instead

--changeset dms:017-api-versioning-support-006b
--comment: Create stored procedure for deprecation warning aggregation - DISABLED

-- Note: Stored procedure creation is disabled due to Liquibase compatibility issues with DELIMITER syntax
-- The aggregation functionality can be implemented in application code instead

-- -- Create stored procedure for deprecation warning aggregation
-- -- This is commented out due to Liquibase not supporting DELIMITER syntax properly
-- -- The aggregation can be done via application code instead

--changeset dms:017-api-versioning-support-007
--comment: Create views for API version monitoring

-- Create view for API version usage statistics
CREATE VIEW api_version_usage_stats AS
SELECT 
    version,
    DATE(request_timestamp) as usage_date,
    COUNT(*) as total_requests,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(response_time_ms) as avg_response_time,
    SUM(CASE WHEN success = TRUE THEN 1 ELSE 0 END) as successful_requests,
    SUM(CASE WHEN success = FALSE THEN 1 ELSE 0 END) as failed_requests,
    SUM(CASE WHEN deprecation_warning = TRUE THEN 1 ELSE 0 END) as deprecation_warnings,
    COUNT(DISTINCT operation_name) as unique_operations
FROM api_version_usage
WHERE request_timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY version, DATE(request_timestamp)
ORDER BY usage_date DESC, version;

-- Create view for deprecation warning summary
CREATE VIEW api_deprecation_summary AS
SELECT 
    adw.version,
    adw.element_type,
    adw.element_name,
    adw.deprecated_since,
    adw.removed_in,
    adw.replacement_element,
    COUNT(DISTINCT adw.user_id) as affected_users,
    SUM(adw.warning_count) as total_warnings,
    MAX(adw.last_warning_timestamp) as latest_warning,
    av.end_of_life_date
FROM api_deprecation_warnings adw
LEFT JOIN api_versions av ON adw.version = av.version
WHERE adw.warning_timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY adw.version, adw.element_type, adw.element_name
ORDER BY total_warnings DESC, latest_warning DESC;

--changeset dms:017-api-versioning-support-008
--comment: Add audit actions for API versioning events

-- Note: audit_actions table doesn't exist yet, so these INSERT statements are commented out
-- These will be added when the audit_actions table is created in a future changeset
-- INSERT INTO audit_actions (action_name, description, risk_level, retention_days) VALUES
-- ('API_VERSION_ACCESSED', 'API version was accessed', 'LOW', 365),
-- ('API_VERSION_DEPRECATED_ACCESSED', 'Deprecated API version was accessed', 'MEDIUM', 730),
-- ('API_VERSION_UNSUPPORTED_ACCESSED', 'Unsupported API version was accessed', 'HIGH', 1095),
-- ('API_VERSION_STRICT_MODE_VIOLATION', 'API version strict mode violation', 'HIGH', 1095),
-- ('API_DEPRECATION_WARNING_ISSUED', 'API deprecation warning was issued', 'LOW', 365),
-- ('API_VERSION_MIGRATION_STARTED', 'API version migration was started', 'LOW', 1095),
-- ('API_VERSION_MIGRATION_COMPLETED', 'API version migration was completed', 'LOW', 1095);

--changeset dms:017-api-versioning-support-009
--comment: Add table comments and documentation

-- Add table comments for documentation
ALTER TABLE api_versions COMMENT = 'API version registry with lifecycle information';
ALTER TABLE api_version_usage COMMENT = 'API version usage tracking for analytics and monitoring';
ALTER TABLE api_deprecation_warnings COMMENT = 'Deprecation warning tracking for API evolution management';

-- Add column comments
ALTER TABLE api_versions 
MODIFY COLUMN version VARCHAR(20) NOT NULL COMMENT 'Semantic version number (e.g., 1.0, 1.1)',
MODIFY COLUMN is_current BOOLEAN DEFAULT FALSE COMMENT 'Indicates if this is the current API version',
MODIFY COLUMN is_supported BOOLEAN DEFAULT TRUE COMMENT 'Indicates if this version is still supported',
MODIFY COLUMN is_deprecated BOOLEAN DEFAULT FALSE COMMENT 'Indicates if this version is deprecated',
MODIFY COLUMN breaking_changes TEXT COMMENT 'Description of breaking changes in this version',
MODIFY COLUMN migration_notes TEXT COMMENT 'Notes for migrating to/from this version';

ALTER TABLE api_version_usage 
MODIFY COLUMN version VARCHAR(20) NOT NULL COMMENT 'API version used for the request',
MODIFY COLUMN operation_type ENUM('QUERY', 'MUTATION', 'SUBSCRIPTION') NOT NULL COMMENT 'GraphQL operation type',
MODIFY COLUMN deprecation_warning BOOLEAN DEFAULT FALSE COMMENT 'Whether a deprecation warning was issued',
MODIFY COLUMN correlation_id VARCHAR(100) COMMENT 'Request correlation ID for tracing';
