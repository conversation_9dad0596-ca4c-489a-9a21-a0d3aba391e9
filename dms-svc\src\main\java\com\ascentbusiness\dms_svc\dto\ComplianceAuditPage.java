package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.AuditLog;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Response DTO for paginated compliance audit log results
 * Used in GraphQL compliance audit operations
 */
@Data
@Builder
public class ComplianceAuditPage {
    private List<ComplianceAuditLog> content;
    private Integer totalElements;
    private Integer totalPages;
    private Integer size;
    private Integer number;
    private Boolean first;
    private Boolean last;

    /**
     * Create ComplianceAuditPage from Spring Data Page of AuditLog
     * Converts AuditLog entities to ComplianceAuditLog DTOs
     */
    public static ComplianceAuditPage fromPage(Page<AuditLog> page) {
        List<ComplianceAuditLog> complianceAuditLogs = page.getContent()
                .stream()
                .map(ComplianceAuditLog::fromAuditLog)
                .collect(Collectors.toList());

        return ComplianceAuditPage.builder()
                .content(complianceAuditLogs)
                .totalElements((int) page.getTotalElements())
                .totalPages(page.getTotalPages())
                .size(page.getSize())
                .number(page.getNumber())
                .first(page.isFirst())
                .last(page.isLast())
                .build();
    }
}
