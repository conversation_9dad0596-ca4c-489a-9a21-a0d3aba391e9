package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Input DTO for bulk document sharing operations.
 * 
 * This DTO encapsulates all the parameters needed to share multiple documents
 * with multiple users and/or roles in a single operation, providing efficient
 * batch processing for large-scale sharing scenarios.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkShareInput {

    /** List of document IDs to share */
    private List<Long> documentIds;
    
    /** List of user IDs to share documents with */
    private List<String> recipientIds;
    
    /** List of role names to share documents with */
    private List<String> roleNames;
    
    /** Permission level to grant to all recipients */
    private Permission permission;
    
    /** Optional: when all share links should expire (defaults to 7 days) */
    private LocalDateTime expiresAt;
    
    /** Optional: notes about this bulk share operation */
    private String notes;

    /**
     * Validate the input parameters for bulk sharing
     */
    public void validate() {
        if (documentIds == null || documentIds.isEmpty()) {
            throw new IllegalArgumentException("At least one document ID is required");
        }

        if ((recipientIds == null || recipientIds.isEmpty()) && 
            (roleNames == null || roleNames.isEmpty())) {
            throw new IllegalArgumentException("At least one recipient (user or role) is required");
        }

        if (permission == null) {
            throw new IllegalArgumentException("Permission is required");
        }

        if (expiresAt != null && expiresAt.isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("expiresAt cannot be in the past");
        }

        if (notes != null && notes.length() > 1000) {
            throw new IllegalArgumentException("Notes cannot exceed 1000 characters");
        }

        // Validate document IDs
        for (Long documentId : documentIds) {
            if (documentId == null || documentId <= 0) {
                throw new IllegalArgumentException("All document IDs must be valid positive numbers");
            }
        }

        // Validate recipient IDs
        if (recipientIds != null) {
            for (String recipientId : recipientIds) {
                if (recipientId == null || recipientId.trim().isEmpty()) {
                    throw new IllegalArgumentException("All recipient IDs must be non-empty");
                }
            }
        }

        // Validate role names
        if (roleNames != null) {
            for (String roleName : roleNames) {
                if (roleName == null || roleName.trim().isEmpty()) {
                    throw new IllegalArgumentException("All role names must be non-empty");
                }
            }
        }
    }

    /**
     * Get the total number of recipients (users + roles)
     */
    public int getTotalRecipients() {
        int userCount = recipientIds != null ? recipientIds.size() : 0;
        int roleCount = roleNames != null ? roleNames.size() : 0;
        return userCount + roleCount;
    }

    /**
     * Get the total number of documents
     */
    public int getTotalDocuments() {
        return documentIds != null ? documentIds.size() : 0;
    }

    /**
     * Get the expected total number of share links to be created
     */
    public int getExpectedShareLinkCount() {
        return getTotalDocuments() * getTotalRecipients();
    }

    /**
     * Check if this operation includes user recipients
     */
    public boolean hasUserRecipients() {
        return recipientIds != null && !recipientIds.isEmpty();
    }

    /**
     * Check if this operation includes role recipients
     */
    public boolean hasRoleRecipients() {
        return roleNames != null && !roleNames.isEmpty();
    }

    /**
     * Get the effective expiration date (default to 7 days if not specified)
     */
    public LocalDateTime getEffectiveExpiresAt() {
        return expiresAt != null ? expiresAt : LocalDateTime.now().plusDays(7);
    }

    /**
     * Check if this is a large operation (for performance considerations)
     */
    public boolean isLargeOperation() {
        return getExpectedShareLinkCount() > 100;
    }
}
