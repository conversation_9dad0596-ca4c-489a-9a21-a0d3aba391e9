package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Input DTO for creating document share links.
 * 
 * This DTO encapsulates all the parameters needed to create a shareable link
 * for document access, including permission levels, expiration settings,
 * target recipients, and security options.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateShareLinkInput {

    /** Permission level to grant through this share link */
    private Permission permission;
    
    /** Optional: specific user this link is intended for */
    private String targetUserId;
    
    /** Optional: specific role this link is intended for */
    private String targetRoleName;
    
    /** Optional: when this share link should expire (defaults to 7 days) */
    private LocalDateTime expiresAt;
    
    /** Optional: password protection for the link */
    private String password;
    
    /** Optional: maximum number of times this link can be used */
    private Integer maxUses;
    
    /** Optional: notes about this share */
    private String notes;

    /**
     * Validate the input parameters for creating a share link
     */
    public void validate() {
        if (permission == null) {
            throw new IllegalArgumentException("Permission is required");
        }

        if (targetUserId != null && targetRoleName != null) {
            throw new IllegalArgumentException("Cannot specify both targetUserId and targetRoleName");
        }

        if (maxUses != null && maxUses <= 0) {
            throw new IllegalArgumentException("maxUses must be greater than 0");
        }

        if (expiresAt != null && expiresAt.isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("expiresAt cannot be in the past");
        }

        if (password != null && password.trim().isEmpty()) {
            throw new IllegalArgumentException("Password cannot be empty if provided");
        }

        if (notes != null && notes.length() > 1000) {
            throw new IllegalArgumentException("Notes cannot exceed 1000 characters");
        }
    }

    /**
     * Check if this share link is targeted to a specific user
     */
    public boolean isUserTargeted() {
        return targetUserId != null && !targetUserId.trim().isEmpty();
    }

    /**
     * Check if this share link is targeted to a specific role
     */
    public boolean isRoleTargeted() {
        return targetRoleName != null && !targetRoleName.trim().isEmpty();
    }

    /**
     * Check if this share link is publicly accessible (no specific target)
     */
    public boolean isPublicLink() {
        return !isUserTargeted() && !isRoleTargeted();
    }

    /**
     * Check if this share link has password protection
     */
    public boolean isPasswordProtected() {
        return password != null && !password.trim().isEmpty();
    }

    /**
     * Check if this share link has usage limits
     */
    public boolean hasUsageLimit() {
        return maxUses != null && maxUses > 0;
    }

    /**
     * Get the effective expiration date (default to 7 days if not specified)
     */
    public LocalDateTime getEffectiveExpiresAt() {
        return expiresAt != null ? expiresAt : LocalDateTime.now().plusDays(7);
    }
}
