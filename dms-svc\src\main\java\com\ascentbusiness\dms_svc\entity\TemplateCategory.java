package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.TemplateAccessLevel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Set;

/**
 * Entity representing a template category for organizing templates
 */
@Entity
@Table(name = "template_categories")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateCategory extends BaseEntity {

    @Column(name = "name", nullable = false, unique = true, length = 255)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_category_id")
    @JsonIgnore
    private TemplateCategory parentCategory;

    // Category configuration
    @Column(name = "icon", length = 100)
    private String icon;

    @Column(name = "color_code", length = 7)
    private String colorCode; // Hex color code

    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    // Access control
    @Enumerated(EnumType.STRING)
    @Column(name = "access_level", nullable = false, length = 50)
    @Builder.Default
    private TemplateAccessLevel accessLevel = TemplateAccessLevel.PUBLIC;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "department_restrictions", columnDefinition = "JSON")
    private JsonNode departmentRestrictions; // Array of departments

    @Column(name = "created_by", nullable = false, length = 255)
    private String createdBy;

    @Column(name = "last_modified_by", length = 255)
    private String lastModifiedBy;

    // Relationships
    @OneToMany(mappedBy = "parentCategory", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<TemplateCategory> subcategories;

    /**
     * Check if this category is currently active
     */
    @Transient
    public boolean isCurrentlyActive() {
        return isActive != null && isActive;
    }

    /**
     * Check if this category is a root category (no parent)
     */
    @Transient
    public boolean isRootCategory() {
        return parentCategory == null;
    }

    /**
     * Check if this category has subcategories
     */
    @Transient
    public boolean hasSubcategories() {
        return subcategories != null && !subcategories.isEmpty();
    }

    /**
     * Get the number of subcategories
     */
    @Transient
    public int getSubcategoryCount() {
        return subcategories != null ? subcategories.size() : 0;
    }

    /**
     * Check if this category has department restrictions
     */
    @Transient
    public boolean hasDepartmentRestrictions() {
        return departmentRestrictions != null && !departmentRestrictions.isNull() && 
               departmentRestrictions.size() > 0;
    }

    /**
     * Check if this category is publicly accessible
     */
    @Transient
    public boolean isPubliclyAccessible() {
        return TemplateAccessLevel.PUBLIC.equals(accessLevel) || 
               TemplateAccessLevel.SYSTEM.equals(accessLevel);
    }

    /**
     * Get the parent category name
     */
    @Transient
    public String getParentCategoryName() {
        return parentCategory != null ? parentCategory.getName() : null;
    }

    /**
     * Get the full category path (parent > child > grandchild)
     */
    @Transient
    public String getFullPath() {
        if (parentCategory == null) {
            return name;
        }
        return parentCategory.getFullPath() + " > " + name;
    }

    /**
     * Get the category depth level (0 for root, 1 for first level, etc.)
     */
    @Transient
    public int getDepthLevel() {
        if (parentCategory == null) {
            return 0;
        }
        return parentCategory.getDepthLevel() + 1;
    }

    /**
     * Get display name with icon
     */
    @Transient
    public String getDisplayName() {
        if (icon != null && !icon.trim().isEmpty()) {
            return icon + " " + name;
        }
        return name;
    }

    @Override
    public String toString() {
        return String.format("TemplateCategory{id=%d, name='%s', parent='%s', active=%s}", 
                           getId(), name, getParentCategoryName(), isActive);
    }
}
