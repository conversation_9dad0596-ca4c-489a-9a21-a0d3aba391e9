package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.Permission;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Entity representing a shareable link for document access.
 * 
 * This entity manages document sharing through secure, time-limited links that can be:
 * - Generated with specific permissions (READ, WRITE, DELETE, ADMIN)
 * - Targeted to specific users or roles
 * - Protected with passwords
 * - Limited by usage count and expiration dates
 * - Tracked for audit and security purposes
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Entity
@Table(name = "document_share_links", indexes = {
    @Index(name = "idx_share_link_id", columnList = "link_id"),
    @Index(name = "idx_share_document_id", columnList = "document_id"),
    @Index(name = "idx_share_created_by", columnList = "created_by_user_id"),
    @Index(name = "idx_share_expires_at", columnList = "expires_at"),
    @Index(name = "idx_share_active", columnList = "is_active"),
    @Index(name = "idx_share_target_user", columnList = "target_user_id"),
    @Index(name = "idx_share_target_role", columnList = "target_role_name")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentShareLink {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /** Unique identifier for the share link (UUID) */
    @Column(name = "link_id", nullable = false, unique = true, length = 36)
    private String linkId;
    
    /** Document being shared */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;
    
    /** User who created this share link */
    @Column(name = "created_by_user_id", nullable = false, length = 100)
    private String createdByUserId;
    
    /** Permission level granted through this link */
    @Column(name = "permission", nullable = false)
    @Enumerated(EnumType.STRING)
    private Permission permission;
    
    /** Optional: specific user this link is intended for */
    @Column(name = "target_user_id", length = 100)
    private String targetUserId;
    
    /** Optional: specific role this link is intended for */
    @Column(name = "target_role_name", length = 100)
    private String targetRoleName;
    
    /** When this share link was created */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /** When this share link expires */
    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;
    
    /** Whether this share link is currently active */
    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;
    
    /** Optional: password protection for the link */
    @Column(name = "password", length = 255)
    private String password;
    
    /** Optional: maximum number of times this link can be used */
    @Column(name = "max_uses")
    private Integer maxUses;
    
    /** Current number of times this link has been used */
    @Column(name = "use_count", nullable = false)
    @Builder.Default
    private Integer useCount = 0;
    
    /** Optional: notes about this share */
    @Column(name = "notes", length = 1000)
    private String notes;
    
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (isActive == null) {
            isActive = true;
        }
        if (useCount == null) {
            useCount = 0;
        }
    }
    
    /**
     * Check if this share link is currently valid for use
     */
    @Transient
    public boolean isValidForUse() {
        if (!isActive) {
            return false;
        }
        
        if (expiresAt != null && expiresAt.isBefore(LocalDateTime.now())) {
            return false;
        }
        
        if (maxUses != null && useCount >= maxUses) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if this share link requires a password
     */
    @Transient
    public boolean requiresPassword() {
        return password != null && !password.trim().isEmpty();
    }
    
    /**
     * Increment the use count for this share link
     */
    public void incrementUseCount() {
        this.useCount = (this.useCount == null ? 0 : this.useCount) + 1;
    }
}
