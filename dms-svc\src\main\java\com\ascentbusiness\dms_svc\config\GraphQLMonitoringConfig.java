/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Configuration for GraphQL monitoring and metrics collection.
 * Provides comprehensive monitoring of GraphQL operations including:
 * - Query execution time tracking
 * - Error rate monitoring
 * - Field-level performance metrics
 * - Query complexity analysis
 */
@Configuration
@Slf4j
public class GraphQLMonitoringConfig {

    private final MeterRegistry meterRegistry;
    private final AtomicLong activeQueries = new AtomicLong(0);
    private final AtomicLong totalQueries = new AtomicLong(0);
    private final AtomicLong totalErrors = new AtomicLong(0);

    public GraphQLMonitoringConfig(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        initializeMetrics();
    }

    /**
     * Initialize custom metrics for GraphQL monitoring
     */
    private void initializeMetrics() {
        // Register gauges for active queries
        meterRegistry.gauge("graphql.queries.active", activeQueries);
        meterRegistry.gauge("graphql.queries.total", totalQueries);
        meterRegistry.gauge("graphql.errors.total", totalErrors);
        
        log.info("GraphQL monitoring metrics initialized");
    }

    /**
     * Simple metrics tracking for GraphQL operations
     */
    public void recordQueryStart() {
        activeQueries.incrementAndGet();
        totalQueries.incrementAndGet();
    }

    public void recordQueryEnd(boolean hasErrors) {
        activeQueries.decrementAndGet();
        if (hasErrors) {
            totalErrors.incrementAndGet();
        }
    }

    public void recordExecutionTime(String operation, long durationMs, boolean hasErrors) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("graphql.execution.time")
                .description("GraphQL query execution time")
                .tag("operation", operation != null ? operation : "anonymous")
                .tag("status", hasErrors ? "error" : "success")
                .register(meterRegistry));

        if (hasErrors) {
            meterRegistry.counter("graphql.errors",
                    "operation", operation != null ? operation : "anonymous")
                    .increment();
        }

        log.debug("GraphQL execution completed - Operation: {}, Duration: {}ms, HasErrors: {}",
                operation, durationMs, hasErrors);
    }

    /**
     * Get current GraphQL metrics summary
     */
    public GraphQLMetricsSummary getMetricsSummary() {
        return GraphQLMetricsSummary.builder()
                .activeQueries(activeQueries.get())
                .totalQueries(totalQueries.get())
                .totalErrors(totalErrors.get())
                .errorRate(totalQueries.get() > 0 ?
                          (double) totalErrors.get() / totalQueries.get() * 100 : 0.0)
                .build();
    }

    /**
     * Reset all metrics (useful for testing)
     */
    public void resetMetrics() {
        activeQueries.set(0);
        totalQueries.set(0);
        totalErrors.set(0);
        log.info("GraphQL monitoring metrics reset");
    }

    /**
     * Data class for GraphQL metrics summary
     */
    public static class GraphQLMetricsSummary {
        private final long activeQueries;
        private final long totalQueries;
        private final long totalErrors;
        private final double errorRate;

        private GraphQLMetricsSummary(long activeQueries, long totalQueries, 
                                     long totalErrors, double errorRate) {
            this.activeQueries = activeQueries;
            this.totalQueries = totalQueries;
            this.totalErrors = totalErrors;
            this.errorRate = errorRate;
        }

        public static Builder builder() {
            return new Builder();
        }

        public long getActiveQueries() { return activeQueries; }
        public long getTotalQueries() { return totalQueries; }
        public long getTotalErrors() { return totalErrors; }
        public double getErrorRate() { return errorRate; }

        public static class Builder {
            private long activeQueries;
            private long totalQueries;
            private long totalErrors;
            private double errorRate;

            public Builder activeQueries(long activeQueries) {
                this.activeQueries = activeQueries;
                return this;
            }

            public Builder totalQueries(long totalQueries) {
                this.totalQueries = totalQueries;
                return this;
            }

            public Builder totalErrors(long totalErrors) {
                this.totalErrors = totalErrors;
                return this;
            }

            public Builder errorRate(double errorRate) {
                this.errorRate = errorRate;
                return this;
            }

            public GraphQLMetricsSummary build() {
                return new GraphQLMetricsSummary(activeQueries, totalQueries, totalErrors, errorRate);
            }
        }
    }
}
