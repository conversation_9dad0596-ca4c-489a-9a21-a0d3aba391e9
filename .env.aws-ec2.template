# AWS EC2 Production Environment Configuration Template
# Copy this file to .env.aws-ec2 and customize the values for your AWS EC2 deployment

# =============================================================================
# AWS EC2 CONFIGURATION
# =============================================================================
# Your AWS EC2 instance details
AWS_EC2_PUBLIC_IP=your-ec2-public-ip
AWS_EC2_DOMAIN=your-domain.com
AWS_EC2_REGION=us-east-1

# =============================================================================
# CORS CONFIGURATION - CRITICAL FOR FRONTEND ACCESS
# =============================================================================
# IMPORTANT: Replace with your actual frontend URLs
# DO NOT use wildcards (*) in production with credentials enabled

# Option 1: Specific Origins (Recommended for Production)
# Example for autoresilience.com domain:
CORS_ALLOWED_ORIGINS=https://app.autoresilience.com,https://www.autoresilience.com,https://admin.autoresilience.com,https://uat.autoresillence.com

# Option 2: If you need subdomain support, use origin patterns
# CORS_ALLOWED_ORIGIN_PATTERNS=https://*.autoresilience.com,https://autoresilience.com

# Examples of common frontend configurations:
# For Angular app on Netlify/Vercel:
# CORS_ALLOWED_ORIGINS=https://your-app.netlify.app,https://your-app.vercel.app

# For React app on different domains:
# CORS_ALLOWED_ORIGINS=https://app.yourcompany.com,https://admin.yourcompany.com

# For development + UAT + production:
# CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:3000,https://uat.autoresillence.com,https://app.autoresilience.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MYSQL_ROOT_PASSWORD=your-secure-root-password-here
MYSQL_DMS_DATABASE=dms_db
MYSQL_DMS_USER=dms_user
MYSQL_DMS_PASSWORD=your-secure-dms-password-here
MYSQL_NOTIFICATION_DATABASE=notification_db
MYSQL_NOTIFICATION_USER=notification_user
MYSQL_NOTIFICATION_PASSWORD=your-secure-notification-password-here

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=your-secure-redis-password-here

# =============================================================================
# RABBITMQ CONFIGURATION
# =============================================================================
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=your-secure-rabbitmq-password-here

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
# Generate a secure JWT secret: openssl rand -base64 64
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-256-bits-long-for-production-use

# =============================================================================
# EMAIL CONFIGURATION (for Notification Service)
# =============================================================================
# Gmail Configuration (requires App Password)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-gmail-app-password

# AWS SES Configuration (Alternative)
# MAIL_HOST=email-smtp.us-east-1.amazonaws.com
# MAIL_PORT=587
# MAIL_USERNAME=your-ses-smtp-username
# MAIL_PASSWORD=your-ses-smtp-password

# SendGrid Configuration (Alternative)
# MAIL_HOST=smtp.sendgrid.net
# MAIL_PORT=587
# MAIL_USERNAME=apikey
# MAIL_PASSWORD=your-sendgrid-api-key

# Email Settings
NOTIFICATION_FROM_EMAIL=<EMAIL>
EMAIL_ENABLED=true
EMAIL_MOCK=false

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Service URLs - Using custom domains with HTTPS
DMS_BASE_URL=https://dms-service.autoresilience.com
NOTIFICATION_SERVICE_URL=https://notify-service.autoresilience.com

# Alternative: If using EC2 IP directly (not recommended for production)
# DMS_BASE_URL=http://${AWS_EC2_PUBLIC_IP}:9093
# NOTIFICATION_SERVICE_URL=http://${AWS_EC2_PUBLIC_IP}:9091

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
DMS_STORAGE_PROVIDER=LOCAL
DMS_STORAGE_LOCAL_BASE_PATH=/app/storage

# AWS S3 Configuration (Alternative)
# DMS_STORAGE_PROVIDER=S3
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_S3_BUCKET_NAME=your-s3-bucket-name
# AWS_S3_REGION=us-east-1

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Grafana
GF_SECURITY_ADMIN_PASSWORD=your-secure-grafana-password

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Enable/Disable GraphiQL in production (set to false for production)
GRAPHIQL_ENABLED=false

# Logging Levels
DMS_LOG_LEVEL=INFO
NOTIFICATION_LOG_LEVEL=INFO

# JVM Options for production
DMS_JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
NOTIFICATION_JAVA_OPTS=-Xmx1g -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Database Connection Pool
DB_CONNECTION_POOL_SIZE=20
DB_CONNECTION_TIMEOUT=30000

# Redis Connection Pool
REDIS_POOL_MAX_ACTIVE=20
REDIS_POOL_MAX_IDLE=10
REDIS_POOL_MIN_IDLE=2

# Cache TTL (seconds)
CACHE_TTL=3600

# =============================================================================
# SSL/TLS CONFIGURATION (if using HTTPS)
# =============================================================================
# SSL_ENABLED=true
# SSL_CERTIFICATE_PATH=/path/to/certificate.crt
# SSL_PRIVATE_KEY_PATH=/path/to/private.key

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Database backup settings
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30
# BACKUP_S3_BUCKET=your-backup-bucket

# =============================================================================
# MONITORING AND ALERTING
# =============================================================================
# Slack webhook for alerts (optional)
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# Email for alerts (optional)
# ALERT_EMAIL=<EMAIL>

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# DMS Features
DMS_VIRUS_SCANNING_ENABLED=true
DMS_ELASTICSEARCH_ENABLED=false

# Notification Features
NOTIFICATION_WEBHOOK_ENABLED=true
NOTIFICATION_BATCH_PROCESSING_ENABLED=true

# =============================================================================
# RATE LIMITING (if implemented)
# =============================================================================
# RATE_LIMIT_ENABLED=true
# RATE_LIMIT_REQUESTS_PER_MINUTE=100
# RATE_LIMIT_BURST_CAPACITY=200
