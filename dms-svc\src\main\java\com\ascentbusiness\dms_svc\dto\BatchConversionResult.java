package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionStatus;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for batch conversion results from conversion-schema.graphqls.
 */
@Data
@Builder
public class BatchConversionResult {
    private String batchId;
    private Integer totalFiles;
    private Integer completedFiles;
    private Integer failedFiles;
    private ConversionStatus status;
    private List<ConversionResult> results;
    private LocalDateTime startedAt;
    private LocalDateTime completedAt;
    private Long totalProcessingTime;
    private List<ConversionError> errors;
    
    // Additional fields for progress tracking
    private Float progress;
    private LocalDateTime estimatedCompletionTime;
}
