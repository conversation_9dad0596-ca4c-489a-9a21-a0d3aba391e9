# DMS Service Testing Guide

## 🚀 Quick Start for VSCode Users

### For Development (Recommended for VSCode)
```cmd
scripts\run-vscode-tests.bat
```
This runs essential tests without overwhelming VSCode.

### For Comprehensive Testing
```cmd
scripts\run-all-tests.bat --unit-only
```
Start with unit tests only, then run full suite when needed.

## 📋 Available Test Scripts

### 1. `run-vscode-tests.bat` - VSCode Development Friendly ⭐
**Best for:** Daily development, VSCode users, quick feedback

**Features:**
- Lightweight resource usage
- Fast execution (2-5 minutes)
- Essential unit and basic integration tests
- Won't overwhelm VSCode or system resources
- No fail-fast to avoid interrupting development flow

**Usage:**
```cmd
scripts\run-vscode-tests.bat                # Quick essential tests
scripts\run-vscode-tests.bat --unit-only    # Unit tests only (fastest)
scripts\run-vscode-tests.bat --with-reports # Include test reports
```

### 2. `run-all-tests.bat` - Comprehensive Test Suite
**Best for:** CI/CD, pre-commit validation, comprehensive testing

**Features:**
- All 14 test categories
- Complete coverage analysis
- Detailed reporting
- Resource-intensive (10-30 minutes)

**Usage:**
```cmd
scripts\run-all-tests.bat                    # All tests
scripts\run-all-tests.bat --unit-only        # Unit tests only
scripts\run-all-tests.bat --fail-fast        # Stop on first failure
scripts\run-all-tests.bat --security-only    # Security tests only
```

### 3. `test-summary.bat` - Quick Test Overview
**Best for:** Getting test status without running tests

**Features:**
- Shows test file counts by category
- Lists available test scripts
- Shows recent test results
- Environment validation
- Generates HTML test report from existing results
- No test execution

**Usage:**
```cmd
scripts\test-summary.bat
```

### 4. `generate-html-report.bat` - HTML Test Report Generator ⭐
**Best for:** Viewing test results in a user-friendly HTML format

**Features:**
- Generates HTML report from XML test results
- Automatically opens report in browser
- Shows test summary with pass/fail counts
- Detailed test results by class
- Works with existing test results (no re-running needed)

**Usage:**
```cmd
scripts\generate-html-report.bat
```

**Note:** Requires existing XML test results. Run tests first if no results are available.

## 🔧 VSCode Integration Issues - SOLVED ✅

### Problem
The comprehensive test suite was causing VSCode to close due to:
- High memory usage from Maven processes
- File locking conflicts during clean operations
- Resource competition between VSCode and test processes

### Solution
1. **Created `run-quick-tests.bat`** - Lightweight alternative for development
2. **Enhanced `run-all-tests.bat`** with:
   - Reduced memory allocation (`-Xmx1g` instead of default)
   - Lower process priority
   - Batch mode execution (`--batch-mode -q`)
   - Skip clean operations to avoid file conflicts
   - Warning messages for VSCode users

### Recommendations

#### ✅ For Daily Development
- Use `run-vscode-tests.bat`
- Run in VSCode terminal
- Fast feedback loop

#### ✅ For Pre-Commit/CI
- Use `run-all-tests.bat`
- Run in separate terminal/command prompt
- Close VSCode temporarily for full test suite

#### ✅ For Specific Test Categories
```cmd
scripts\run-all-tests.bat --unit-only         # Fast unit tests
scripts\run-all-tests.bat --security-only     # Security validation
scripts\run-all-tests.bat --integration-only  # Integration tests
```

## 📊 Test Categories

| Category | Quick Tests | Full Tests | Description |
|----------|-------------|------------|-------------|
| Unit Tests | ✅ Essential | ✅ All | Service, util, config tests |
| Basic Integration | ✅ | ✅ | Basic connectivity tests |
| Full Integration | ❌ | ✅ | Database, external services |
| E2E Tests | ❌ | ✅ | End-to-end workflows |
| Security Tests | ❌ | ✅ | Security validation |
| Performance Tests | ❌ | ✅ | Load and performance |
| Contract Tests | ❌ | ✅ | API contract validation |
| Compliance Tests | ❌ | ✅ | Regulatory compliance |

## 🎯 Best Practices

### During Development
1. Run `run-vscode-tests.bat` frequently
2. Use `--unit-only` for fastest feedback
3. Run full suite before major commits

### Before Commits
1. Run `run-all-tests.bat --unit-only`
2. Fix any failures
3. Consider running security tests

### CI/CD Pipeline
1. Use `run-all-tests.bat` (full suite)
2. Generate all reports
3. Fail build on test failures

## 📊 HTML Test Reports

### Automatic Generation
- HTML reports are automatically generated when running `run-all-tests.bat`
- Reports are also generated by `test-summary.bat` if XML results exist
- Location: `target/site/surefire-report.html`

### Manual Generation
```cmd
scripts\generate-html-report.bat
```

### Report Features
- **Summary Section**: Total tests, pass/fail counts, success rate
- **Detailed Results**: Test results by class with timing information
- **Visual Indicators**: Color-coded success/failure status
- **Browser Integration**: Automatically opens in default browser

### Viewing Reports
- **Automatic**: Reports open automatically after generation
- **Manual**: Navigate to `target/site/surefire-report.html`
- **URL Format**: `file:///path/to/dms-svc/target/site/surefire-report.html`

### Report Contents
- Test execution summary
- Pass/fail statistics
- Test duration by class
- Error and failure details
- Generated timestamp

## 🔍 Troubleshooting

### VSCode Still Closes?
1. Use `run-vscode-tests.bat` instead
2. Close other resource-intensive applications
3. Run tests in external command prompt
4. Increase system memory if possible

### Tests Taking Too Long?
1. Use `--unit-only` flag
2. Use `run-vscode-tests.bat`
3. Run specific test categories only

### Memory Issues?
1. Close other applications
2. Use `run-vscode-tests.bat`
3. Increase JVM heap size if needed

## 📈 Performance Comparison

| Script | Duration | Memory | VSCode Impact | Use Case |
|--------|----------|--------|---------------|----------|
| `run-vscode-tests.bat` | 2-5 min | Low | Minimal | Development |
| `run-all-tests.bat --unit-only` | 5-10 min | Medium | Low | Pre-commit |
| `run-all-tests.bat` (full) | 15-30 min | High | High | CI/CD |

Choose the right tool for your needs! 🚀
