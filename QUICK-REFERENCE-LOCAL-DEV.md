# 🚀 Quick Reference - Local Development

## **Step-by-Step Setup (Demonstrates Shared Infrastructure)**

```bash
# Step 1: Start shared infrastructure
./docker-manage-local-dev.sh start-infra

# Step 2: Start DMS service (connects to shared infra)
./docker-manage-local-dev.sh start-dms

# Step 3: Start Notification service (connects to SAME shared infra)
./docker-manage-local-dev.sh start-notification

# Step 4: Start monitoring (optional)
./docker-manage-local-dev.sh start-monitoring
```

## **Quick Start (Everything at Once)**

```bash
# Start everything
./docker-manage-local-dev.sh start-all

# Check status
./docker-manage-local-dev.sh status

# Check health
./docker-manage-local-dev.sh health
```

## **Service URLs**

### **Applications**
- **DMS GraphQL**: http://localhost:9093/dms/graphql
- **DMS GraphiQL**: http://localhost:9093/graphiql
- **Notification GraphQL**: http://localhost:9091/graphql
- **Notification GraphiQL**: http://localhost:9091/graphiql

### **Infrastructure**
- **MySQL**: localhost:3306 (root/root_password)
- **Redis**: localhost:6379 (local_redis_password)
- **RabbitMQ UI**: http://localhost:15672 (admin/admin123)
- **Elasticsearch**: http://localhost:9200

### **Monitoring**
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Zipkin**: http://localhost:9411

## **Common Commands**

```bash
# Management
./docker-manage-local-dev.sh start-all     # Start everything
./docker-manage-local-dev.sh stop-all      # Stop everything
./docker-manage-local-dev.sh status        # Show status
./docker-manage-local-dev.sh health        # Health check
./docker-manage-local-dev.sh cleanup       # Clean up all data

# Logs
./docker-manage-local-dev.sh logs          # All logs
./docker-manage-local-dev.sh logs dms      # DMS logs only
./docker-manage-local-dev.sh logs notification  # Notification logs only

# Individual Services
./docker-manage-local-dev.sh start-dms     # Start DMS only
./docker-manage-local-dev.sh stop-dms      # Stop DMS only
```

## **Database Access**

```bash
# Connect to MySQL
docker exec -it mysql-shared-local mysql -u root -proot_password

# Connect as DMS user
docker exec -it mysql-shared-local mysql -u dms_user -pdms_password dms_db

# Connect as Notification user
docker exec -it mysql-shared-local mysql -u notification_user -pnotification_password notification_db

# Connect as Dev user (access to both databases)
docker exec -it mysql-shared-local mysql -u dev_user -pdev_password
```

## **Redis Access**

```bash
# Connect to Redis
docker exec -it redis-shared-local redis-cli -a local_redis_password

# Check DMS keys
KEYS dms:local:*

# Check Notification keys
KEYS notification:local:*
```

## **Testing**

```bash
# Test DMS Service
curl http://localhost:9093/actuator/health
curl -X POST -H "Content-Type: application/json" \
  -d '{"query":"{ __schema { types { name } } }"}' \
  http://localhost:9093/dms/graphql

# Test Notification Service
curl http://localhost:9091/actuator/health
curl -X POST -H "Content-Type: application/json" \
  -d '{"query":"{ __schema { types { name } } }"}' \
  http://localhost:9091/graphql
```

## **Troubleshooting**

```bash
# Check what's running
docker ps

# Check logs for issues
./docker-manage-local-dev.sh logs

# Restart everything
./docker-manage-local-dev.sh stop-all
./docker-manage-local-dev.sh start-all

# Complete reset
./docker-manage-local-dev.sh cleanup
./docker-manage-local-dev.sh start-all
```

## **File Structure**

```
grc-platform-v4/
├── docker-compose.local-dev.yml          # Main Docker Compose file
├── docker-manage-local-dev.sh            # Management script
├── .env.local-dev                        # Environment configuration
├── docker/
│   ├── mysql/local-dev-init/             # Database initialization
│   └── prometheus/local-dev-prometheus.yml # Prometheus config
├── dms-svc/src/main/resources/
│   └── application-local-dev.properties  # DMS local config
└── notification-svc/src/main/resources/
    └── application-local-dev.properties  # Notification local config
```

## **Key Features Demonstrated**

✅ **Shared MySQL** - Single instance, multiple databases  
✅ **Shared Redis** - Single instance, service-specific prefixes  
✅ **Shared RabbitMQ** - Single message broker  
✅ **Shared Elasticsearch** - Single search engine  
✅ **Independent Services** - Can start/stop individually  
✅ **Cross-service Access** - Services can read each other's data  
✅ **Monitoring** - Unified observability stack  
✅ **Development Features** - GraphiQL, debug logging, hot reload  

## **Resource Requirements**

- **RAM**: 4GB minimum, 6GB recommended
- **Disk**: 10GB free space
- **Ports**: 3000, 3306, 5672, 6379, 9090, 9091, 9093, 9200, 9411, 15672

## **Success Indicators**

✅ `./docker-manage-local-dev.sh health` shows all services HEALTHY  
✅ Both GraphiQL interfaces load successfully  
✅ MySQL shows both `dms_db` and `notification_db`  
✅ Redis shows keys with `dms:local:` and `notification:local:` prefixes  
✅ RabbitMQ management UI accessible  
✅ Elasticsearch cluster health is green  

---

**Need Help?** Check `LOCAL-DEVELOPMENT-SETUP-GUIDE.md` for detailed instructions!
