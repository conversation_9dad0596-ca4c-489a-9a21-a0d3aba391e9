<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.23.xsd">

    <!-- Include all existing migrations first -->
    <include file="db/changelog/001-create-initial-schema.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/002-insert-initial-data.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/003-add-user-audit-columns.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/004-add-document-permissions.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/005-add-audit-columns-to-document-permissions.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/006-insert-sample-data.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/007-create-document-versions-and-access-roles.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/008-add-user-context-metadata.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/009-consolidate-permissions-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/010-fix-document-permissions-issue.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/011-security-enhancements.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/012-fix-security-audit-columns.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/013-add-correlation-id-to-security-violations.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/014-create-storage-configurations-table.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/015-create-document-metadata-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/016-create-user-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/017-remove-user-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/018-fix-audit-action-enum.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/025-create-retention-policy-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/026-create-compliance-framework-tables.sql" relativeToChangelogFile="false"/>
    
    <!-- Include test-specific H2-compatible GRC enhancements -->
    <include file="db/changelog/test-enhance-audit-system-grc.sql" relativeToChangelogFile="true"/>

</databaseChangeLog>
