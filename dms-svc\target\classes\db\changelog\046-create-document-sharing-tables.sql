-- liquibase formatted sql

-- changeset dms:046-create-document-sharing-tables
-- comment: Create tables for document sharing functionality including share links and bulk operations

-- Create document_share_links table
CREATE TABLE document_share_links (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    link_id VARCHAR(36) NOT NULL UNIQUE,
    document_id BIGINT NOT NULL,
    created_by_user_id VARCHAR(100) NOT NULL,
    permission VARCHAR(20) NOT NULL,
    target_user_id VARCHAR(100),
    target_role_name VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    password VARCHAR(255),
    max_uses INT,
    use_count INT NOT NULL DEFAULT 0,
    notes VARCHAR(1000),
    
    CONSTRAINT fk_share_link_document 
        FOREIGN KEY (document_id) REFERENCES documents(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT chk_share_link_permission 
        CHECK (permission IN ('READ', 'WRITE', 'DELETE', 'ADMIN')),
    
    CONSTRAINT chk_share_link_target 
        CHECK (target_user_id IS NOT NULL OR target_role_name IS NOT NULL OR 
               (target_user_id IS NULL AND target_role_name IS NULL)),
    
    CONSTRAINT chk_share_link_max_uses 
        CHECK (max_uses IS NULL OR max_uses > 0),
    
    CONSTRAINT chk_share_link_use_count 
        CHECK (use_count >= 0),
    
    CONSTRAINT chk_share_link_expires_after_created 
        CHECK (expires_at > created_at)
);

-- Create indexes for document_share_links
CREATE INDEX idx_share_link_id ON document_share_links(link_id);
CREATE INDEX idx_share_document_id ON document_share_links(document_id);
CREATE INDEX idx_share_created_by ON document_share_links(created_by_user_id);
CREATE INDEX idx_share_expires_at ON document_share_links(expires_at);
CREATE INDEX idx_share_active ON document_share_links(is_active);
CREATE INDEX idx_share_target_user ON document_share_links(target_user_id);
CREATE INDEX idx_share_target_role ON document_share_links(target_role_name);
CREATE INDEX idx_share_created_at ON document_share_links(created_at);

-- Create bulk_share_operations table
CREATE TABLE bulk_share_operations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    operation_id VARCHAR(36) NOT NULL UNIQUE,
    created_by_user_id VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    permission VARCHAR(20) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    notes VARCHAR(1000),
    is_completed BOOLEAN NOT NULL DEFAULT FALSE,
    completed_at TIMESTAMP,
    total_documents INT NOT NULL,
    total_recipients INT NOT NULL,
    success_count INT NOT NULL DEFAULT 0,
    failure_count INT NOT NULL DEFAULT 0,
    
    CONSTRAINT chk_bulk_operation_permission 
        CHECK (permission IN ('READ', 'WRITE', 'DELETE', 'ADMIN')),
    
    CONSTRAINT chk_bulk_operation_totals 
        CHECK (total_documents > 0 AND total_recipients > 0),
    
    CONSTRAINT chk_bulk_operation_counts 
        CHECK (success_count >= 0 AND failure_count >= 0),
    
    CONSTRAINT chk_bulk_operation_completed 
        CHECK ((is_completed = FALSE AND completed_at IS NULL) OR 
               (is_completed = TRUE AND completed_at IS NOT NULL)),
    
    CONSTRAINT chk_bulk_operation_expires_after_created 
        CHECK (expires_at > created_at)
);

-- Create indexes for bulk_share_operations
CREATE INDEX idx_bulk_operation_id ON bulk_share_operations(operation_id);
CREATE INDEX idx_bulk_created_by ON bulk_share_operations(created_by_user_id);
CREATE INDEX idx_bulk_created_at ON bulk_share_operations(created_at);
CREATE INDEX idx_bulk_completed ON bulk_share_operations(is_completed);
CREATE INDEX idx_bulk_expires_at ON bulk_share_operations(expires_at);
CREATE INDEX idx_bulk_permission ON bulk_share_operations(permission);

-- Create bulk_share_items table
CREATE TABLE bulk_share_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    operation_id BIGINT NOT NULL,
    document_id BIGINT NOT NULL,
    recipient_id VARCHAR(100) NOT NULL,
    is_role BOOLEAN NOT NULL DEFAULT FALSE,
    is_successful BOOLEAN NOT NULL DEFAULT FALSE,
    error_message VARCHAR(1000),
    share_link_id VARCHAR(36),
    
    CONSTRAINT fk_bulk_item_operation 
        FOREIGN KEY (operation_id) REFERENCES bulk_share_operations(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT chk_bulk_item_success_consistency 
        CHECK ((is_successful = TRUE AND error_message IS NULL AND share_link_id IS NOT NULL) OR 
               (is_successful = FALSE AND error_message IS NOT NULL AND share_link_id IS NULL))
);

-- Create indexes for bulk_share_items
CREATE INDEX idx_bulk_item_operation ON bulk_share_items(operation_id);
CREATE INDEX idx_bulk_item_document ON bulk_share_items(document_id);
CREATE INDEX idx_bulk_item_recipient ON bulk_share_items(recipient_id);
CREATE INDEX idx_bulk_item_success ON bulk_share_items(is_successful);
CREATE INDEX idx_bulk_item_role ON bulk_share_items(is_role);
CREATE INDEX idx_bulk_item_share_link ON bulk_share_items(share_link_id);

-- Add comments for documentation
ALTER TABLE document_share_links COMMENT = 'Stores shareable links for document access with permissions and restrictions';
ALTER TABLE bulk_share_operations COMMENT = 'Tracks bulk document sharing operations with multiple documents and recipients';
ALTER TABLE bulk_share_items COMMENT = 'Individual items within bulk share operations tracking success/failure per document-recipient pair';

-- rollback DROP TABLE IF EXISTS bulk_share_items;
-- rollback DROP TABLE IF EXISTS bulk_share_operations;
-- rollback DROP TABLE IF EXISTS document_share_links;
