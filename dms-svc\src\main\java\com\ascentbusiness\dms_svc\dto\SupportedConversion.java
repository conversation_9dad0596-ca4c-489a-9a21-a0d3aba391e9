package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionMethod;
import com.ascentbusiness.dms_svc.enums.ConversionType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * DTO for supported conversion information from conversion-schema.graphqls.
 */
@Data
@Builder
public class SupportedConversion {
    private String fromFormat;
    private String toFormat;
    private ConversionType conversionType;
    private List<ConversionMethod> supportedMethods;
    private Long maxFileSize;
    private String description;
}
