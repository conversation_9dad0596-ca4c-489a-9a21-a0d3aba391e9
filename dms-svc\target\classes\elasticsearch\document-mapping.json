{"properties": {"id": {"type": "keyword"}, "name": {"type": "text", "analyzer": "content_analyzer", "fields": {"keyword": {"type": "keyword", "normalizer": "keyword_normalizer"}, "suggest": {"type": "text", "analyzer": "suggest_analyzer"}}}, "description": {"type": "text", "analyzer": "content_analyzer"}, "originalFileName": {"type": "keyword", "fields": {"text": {"type": "text", "analyzer": "content_analyzer"}}}, "fileSize": {"type": "long"}, "mimeType": {"type": "keyword"}, "storageProvider": {"type": "keyword"}, "storagePath": {"type": "keyword", "index": false}, "status": {"type": "keyword"}, "version": {"type": "integer"}, "content": {"type": "text", "analyzer": "content_analyzer", "fields": {"fuzzy": {"type": "text", "analyzer": "fuzzy_analyzer"}}}, "contentChunks": {"type": "text", "analyzer": "content_analyzer"}, "keywords": {"type": "text", "analyzer": "keyword_analyzer", "fields": {"raw": {"type": "keyword"}}}, "tags": {"type": "keyword"}, "creatorUserId": {"type": "keyword"}, "creatorRoles": {"type": "keyword"}, "authorizedUsers": {"type": "keyword"}, "authorizedRoles": {"type": "keyword"}, "confidentialityLevel": {"type": "keyword"}, "module": {"type": "keyword"}, "subModule": {"type": "keyword"}, "businessUnit": {"type": "keyword"}, "regionLocation": {"type": "keyword"}, "documentType": {"type": "keyword"}, "language": {"type": "keyword"}, "owner": {"type": "keyword"}, "approver": {"type": "keyword"}, "ownershipStatus": {"type": "keyword"}, "approvalDate": {"type": "date", "format": "yyyy-MM-dd'T'HH:mm:ss.SSS"}, "expiryDate": {"type": "date", "format": "yyyy-MM-dd'T'HH:mm:ss.SSS"}, "complianceStandard": {"type": "keyword"}, "auditRelevance": {"type": "keyword"}, "controlId": {"type": "keyword"}, "thirdPartyId": {"type": "keyword"}, "policyId": {"type": "keyword"}, "createdDate": {"type": "date", "format": "yyyy-MM-dd'T'HH:mm:ss.SSS"}, "lastModifiedDate": {"type": "date", "format": "yyyy-MM-dd'T'HH:mm:ss.SSS"}, "indexedDate": {"type": "date", "format": "yyyy-MM-dd'T'HH:mm:ss.SSS"}, "suggest": {"type": "text", "analyzer": "suggest_analyzer"}, "relevanceScore": {"type": "float"}, "parentDocumentId": {"type": "keyword"}, "isCurrentVersion": {"type": "boolean"}}}