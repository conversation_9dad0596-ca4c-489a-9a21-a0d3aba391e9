package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entity representing an individual item within a bulk share operation.
 * 
 * This entity tracks each document-recipient pair within a bulk sharing operation,
 * providing detailed status and error information for each share attempt.
 * It enables:
 * - Granular tracking of bulk operation progress
 * - Error reporting for failed share attempts
 * - Reference to created share links for successful operations
 * - Audit trail for individual share items
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Entity
@Table(name = "bulk_share_items", indexes = {
    @Index(name = "idx_bulk_item_operation", columnList = "operation_id"),
    @Index(name = "idx_bulk_item_document", columnList = "document_id"),
    @Index(name = "idx_bulk_item_recipient", columnList = "recipient_id"),
    @Index(name = "idx_bulk_item_success", columnList = "is_successful"),
    @Index(name = "idx_bulk_item_role", columnList = "is_role"),
    @Index(name = "idx_bulk_item_share_link", columnList = "share_link_id")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkShareItem {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /** The bulk operation this item belongs to */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "operation_id", nullable = false)
    private BulkShareOperation operation;
    
    /** ID of the document being shared */
    @Column(name = "document_id", nullable = false)
    private Long documentId;
    
    /** ID of the recipient (user ID or role name) */
    @Column(name = "recipient_id", nullable = false, length = 100)
    private String recipientId;
    
    /** Whether the recipient is a role (true) or user (false) */
    @Column(name = "is_role", nullable = false)
    @Builder.Default
    private Boolean isRole = false;
    
    /** Whether this share operation was successful */
    @Column(name = "is_successful", nullable = false)
    @Builder.Default
    private Boolean isSuccessful = false;
    
    /** Error message if the operation failed */
    @Column(name = "error_message", length = 1000)
    private String errorMessage;
    
    /** Reference to the created share link if successful */
    @Column(name = "share_link_id", length = 36)
    private String shareLinkId;
    
    /**
     * Get the recipient type as a string for display purposes
     */
    @Transient
    public String getRecipientType() {
        return isRole != null && isRole ? "ROLE" : "USER";
    }
    
    /**
     * Check if this item represents a successful share
     */
    @Transient
    public boolean isSuccess() {
        return isSuccessful != null && isSuccessful;
    }
    
    /**
     * Check if this item represents a failed share
     */
    @Transient
    public boolean isFailure() {
        return isSuccessful == null || !isSuccessful;
    }
    
    /**
     * Get a display-friendly description of this share item
     */
    @Transient
    public String getDescription() {
        String recipientType = getRecipientType();
        String status = isSuccess() ? "SUCCESS" : "FAILED";
        return String.format("Document %d -> %s %s [%s]", 
                           documentId, recipientType, recipientId, status);
    }
    
    /**
     * Mark this item as successful with the given share link ID
     */
    public void markSuccessful(String shareLinkId) {
        this.isSuccessful = true;
        this.shareLinkId = shareLinkId;
        this.errorMessage = null;
    }
    
    /**
     * Mark this item as failed with the given error message
     */
    public void markFailed(String errorMessage) {
        this.isSuccessful = false;
        this.errorMessage = errorMessage;
        this.shareLinkId = null;
    }
}
