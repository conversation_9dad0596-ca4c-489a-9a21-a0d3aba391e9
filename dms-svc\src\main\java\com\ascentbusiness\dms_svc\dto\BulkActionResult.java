package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * DTO for bulk action result
 */
@Data
@Builder
public class BulkActionResult {
    private Integer totalProcessed;
    private Integer successfulActions;
    private Integer failedActions;
    private List<TemplateActionResult> results;
    private Long processingTimeMs;
    private String overallStatus;
}
