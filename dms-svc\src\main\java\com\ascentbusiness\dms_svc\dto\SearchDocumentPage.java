package com.ascentbusiness.dms_svc.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * Paginated search results for SearchDocument objects
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchDocumentPage {
    
    /**
     * List of search documents in current page
     */
    private List<SearchDocument> content;
    
    /**
     * Total number of elements across all pages
     */
    private Long totalElements;
    
    /**
     * Total number of pages
     */
    private Integer totalPages;
    
    /**
     * Size of current page
     */
    private Integer size;
    
    /**
     * Current page number (0-based)
     */
    private Integer number;
    
    /**
     * Whether this is the first page
     */
    private Boolean first;
    
    /**
     * Whether this is the last page
     */
    private Boolean last;
    
    /**
     * Whether the page has content
     */
    private Boolean hasContent;
    
    /**
     * Whether the page is empty
     */
    private Boolean empty;
    
    /**
     * Number of elements in current page
     */
    private Integer numberOfElements;
}
