# DMS Service
apiVersion: v1
kind: Service
metadata:
  name: dms-service
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: application
    app.kubernetes.io/part-of: dms-system
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/dms/actuator/prometheus"
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: application

---
# DMS Service - LoadBalancer for external access
apiVersion: v1
kind: Service
metadata:
  name: dms-service-lb
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: loadbalancer
    app.kubernetes.io/part-of: dms-system
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 443
    targetPort: 8080
    protocol: TCP
    name: https
  selector:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: application
  loadBalancerSourceRanges:
  - 0.0.0.0/0  # Restrict this in production

---
# MySQL Service
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: dms-system
  labels:
    app.kubernetes.io/name: mysql
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: dms-system
spec:
  type: ClusterIP
  ports:
  - port: 3306
    targetPort: 3306
    protocol: TCP
    name: mysql
  selector:
    app.kubernetes.io/name: mysql
    app.kubernetes.io/component: database

---
# Redis Service
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: dms-system
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
    app.kubernetes.io/part-of: dms-system
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache

---
# Elasticsearch Service
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch-service
  namespace: dms-system
  labels:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: search
    app.kubernetes.io/part-of: dms-system
spec:
  type: ClusterIP
  ports:
  - port: 9200
    targetPort: 9200
    protocol: TCP
    name: http
  - port: 9300
    targetPort: 9300
    protocol: TCP
    name: transport
  selector:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: search

---
# Prometheus Service
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: dms-system
  labels:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: monitoring
    app.kubernetes.io/part-of: dms-system
spec:
  type: ClusterIP
  ports:
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: monitoring

---
# Grafana Service
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: dms-system
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: monitoring
    app.kubernetes.io/part-of: dms-system
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: monitoring

---
# Grafana Service - LoadBalancer for external access
apiVersion: v1
kind: Service
metadata:
  name: grafana-service-lb
  namespace: dms-system
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: monitoring-lb
    app.kubernetes.io/part-of: dms-system
spec:
  type: LoadBalancer
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: monitoring

---
# AlertManager Service
apiVersion: v1
kind: Service
metadata:
  name: alertmanager-service
  namespace: dms-system
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting
    app.kubernetes.io/part-of: dms-system
spec:
  type: ClusterIP
  ports:
  - port: 9093
    targetPort: 9093
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting

---
# Jaeger Service (for distributed tracing)
apiVersion: v1
kind: Service
metadata:
  name: jaeger-service
  namespace: dms-system
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: tracing
    app.kubernetes.io/part-of: dms-system
spec:
  type: ClusterIP
  ports:
  - port: 16686
    targetPort: 16686
    protocol: TCP
    name: ui
  - port: 14250
    targetPort: 14250
    protocol: TCP
    name: grpc
  - port: 14268
    targetPort: 14268
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: tracing

---
# Headless Service for StatefulSet components (if needed)
apiVersion: v1
kind: Service
metadata:
  name: dms-headless
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: headless
    app.kubernetes.io/part-of: dms-system
spec:
  clusterIP: None
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: application
