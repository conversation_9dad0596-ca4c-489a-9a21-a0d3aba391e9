package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a chunked upload session for large files.
 * 
 * <p>This entity manages the state of chunked file uploads, tracking
 * individual chunks, progress, and session metadata. It supports
 * resumable uploads and provides detailed progress information.
 */
@Entity
@Table(name = "chunked_upload_sessions")
@Data
@EqualsAndHashCode(callSuper = true)
public class ChunkedUploadSession extends BaseEntity {
    
    /**
     * Unique identifier for the upload session.
     * Used by clients to associate chunks with the session.
     */
    @Column(name = "session_id", nullable = false, unique = true)
    private String sessionId;
    
    /**
     * Original name of the file being uploaded.
     */
    @Column(name = "file_name", nullable = false, length = 500)
    private String fileName;
    
    /**
     * Total size of the file being uploaded in bytes.
     */
    @Column(name = "total_size", nullable = false)
    private Long totalSize;
    
    /**
     * Size of each chunk in bytes.
     */
    @Column(name = "chunk_size", nullable = false)
    private Integer chunkSize;
    
    /**
     * Total number of chunks expected for this upload.
     */
    @Column(name = "total_chunks", nullable = false)
    private Integer totalChunks;
    
    /**
     * Number of chunks successfully received.
     */
    @Column(name = "received_chunks", nullable = false)
    private Integer receivedChunks = 0;
    
    /**
     * Total bytes received so far.
     */
    @Column(name = "received_bytes", nullable = false)
    private Long receivedBytes = 0L;
    
    /**
     * Current status of the upload session.
     */
    @Column(name = "status", nullable = false, length = 50)
    private String status = "ACTIVE";
    
    /**
     * Upload progress as a percentage (0.00 to 100.00).
     */
    @Column(name = "progress", precision = 5, scale = 2)
    private BigDecimal progress = BigDecimal.ZERO;
    
    /**
     * Temporary directory where chunks are stored.
     */
    @Column(name = "temp_directory", length = 1000)
    private String tempDirectory;
    
    /**
     * Reference to the created document (if upload completed successfully).
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id")
    private Document document;
    
    /**
     * Correlation ID for request tracing and audit purposes.
     */
    @Column(name = "correlation_id")
    private String correlationId;
    
    /**
     * Timestamp of the last activity on this session.
     */
    @Column(name = "last_activity_at")
    private LocalDateTime lastActivityAt;
    
    /**
     * Timestamp when the upload completed.
     */
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    /**
     * Timestamp when the session expires.
     */
    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;
    
    /**
     * Error message if the upload failed.
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * List of individual chunks associated with this session.
     */
    @OneToMany(mappedBy = "session", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ChunkedUploadChunk> chunks = new ArrayList<>();
    
    /**
     * Check if the upload session has expired.
     * 
     * @return true if the session has expired
     */
    @Transient
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    
    /**
     * Check if the upload is complete.
     * 
     * @return true if all chunks have been received
     */
    @Transient
    public boolean isComplete() {
        return receivedChunks != null && totalChunks != null && 
               receivedChunks.equals(totalChunks);
    }
    
    /**
     * Check if the upload session is active.
     * 
     * @return true if the session is active and not expired
     */
    @Transient
    public boolean isActive() {
        return "ACTIVE".equals(status) && !isExpired();
    }
    
    /**
     * Get the progress as a float value (0.0 to 100.0).
     * 
     * @return progress percentage as float
     */
    @Transient
    public Float getProgressAsFloat() {
        return progress != null ? progress.floatValue() : 0.0f;
    }
    
    /**
     * Update progress based on received chunks.
     */
    @Transient
    public void updateProgress() {
        if (totalChunks != null && totalChunks > 0) {
            float progressValue = (receivedChunks.floatValue() / totalChunks.floatValue()) * 100.0f;
            this.progress = BigDecimal.valueOf(Math.min(100.0, progressValue));
        }
    }
    
    /**
     * Add a chunk to the session and update progress.
     * 
     * @param chunk the chunk to add
     */
    @Transient
    public void addChunk(ChunkedUploadChunk chunk) {
        if (chunks == null) {
            chunks = new ArrayList<>();
        }
        chunks.add(chunk);
        chunk.setSession(this);
        
        // Update counters
        receivedChunks = chunks.size();
        receivedBytes = chunks.stream()
                .mapToLong(ChunkedUploadChunk::getChunkSize)
                .sum();
        
        // Update progress
        updateProgress();
        
        // Update last activity
        lastActivityAt = LocalDateTime.now();
    }
    
    /**
     * Get the remaining bytes to upload.
     * 
     * @return remaining bytes
     */
    @Transient
    public Long getRemainingBytes() {
        return totalSize - receivedBytes;
    }
    
    /**
     * Get the remaining chunks to upload.
     * 
     * @return remaining chunks
     */
    @Transient
    public Integer getRemainingChunks() {
        return totalChunks - receivedChunks;
    }
}
