package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.DataSubjectCategory;
import com.ascentbusiness.dms_svc.enums.GeographicRegion;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Entity representing the compliance mapping for a specific document
 */
@Entity
@Table(name = "document_compliance_mappings")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentComplianceMapping extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "compliance_classification_id", nullable = false)
    private ComplianceClassification complianceClassification;

    @ElementCollection(targetClass = DataSubjectCategory.class)
    @Enumerated(EnumType.STRING)
    @CollectionTable(
        name = "document_data_subjects",
        joinColumns = @JoinColumn(name = "mapping_id")
    )
    @Column(name = "data_subject_category")
    private Set<DataSubjectCategory> dataSubjectCategories;

    @Enumerated(EnumType.STRING)
    @Column(name = "storage_region", length = 50)
    private GeographicRegion storageRegion;

    @ElementCollection(targetClass = GeographicRegion.class)
    @Enumerated(EnumType.STRING)
    @CollectionTable(
        name = "document_access_regions",
        joinColumns = @JoinColumn(name = "mapping_id")
    )
    @Column(name = "region")
    private Set<GeographicRegion> allowedAccessRegions;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "classification_reason", columnDefinition = "TEXT")
    private String classificationReason;

    @Column(name = "classified_by", length = 100)
    private String classifiedBy; // User who assigned the classification

    @Column(name = "classification_date")
    private LocalDateTime classificationDate;

    @Column(name = "review_date")
    private LocalDateTime reviewDate;

    @Column(name = "declassification_date")
    private LocalDateTime declassificationDate;

    @Builder.Default
    @Column(name = "requires_consent", nullable = false)
    private Boolean requiresConsent = false;

    @Column(name = "consent_reference", length = 200)
    private String consentReference; // Reference to consent record

    @Builder.Default
    @Column(name = "is_anonymized", nullable = false)
    private Boolean isAnonymized = false;

    @Builder.Default
    @Column(name = "is_pseudonymized", nullable = false)
    private Boolean isPseudonymized = false;

    @Column(name = "anonymization_method", length = 200)
    private String anonymizationMethod;

    @Column(name = "encryption_status", length = 50)
    private String encryptionStatus; // ENCRYPTED, NOT_ENCRYPTED, PARTIALLY_ENCRYPTED

    @Column(name = "encryption_algorithm", length = 100)
    private String encryptionAlgorithm;

    @Column(name = "access_log_retention_days")
    private Integer accessLogRetentionDays;

    @Column(name = "special_handling_instructions", columnDefinition = "TEXT")
    private String specialHandlingInstructions;

    @Column(name = "compliance_notes", columnDefinition = "TEXT")
    private String complianceNotes;

    @Column(name = "last_compliance_check")
    private LocalDateTime lastComplianceCheck;

    @Column(name = "compliance_status", length = 50)
    private String complianceStatus; // COMPLIANT, NON_COMPLIANT, UNDER_REVIEW

    @Column(name = "violation_count", nullable = false)
    @Builder.Default
    private Integer violationCount = 0;

    @Column(name = "last_violation_date")
    private LocalDateTime lastViolationDate;

    // Relationships
    @OneToMany(mappedBy = "documentComplianceMapping", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<ComplianceViolation> complianceViolations;

    /**
     * Check if this mapping is currently active and effective
     */
    @Transient
    public boolean isCurrentlyEffective() {
        return isActive && 
               complianceClassification != null && 
               complianceClassification.isCurrentlyEffective();
    }

    /**
     * Check if review is due
     */
    @Transient
    public boolean isReviewDue() {
        if (reviewDate == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(reviewDate);
    }

    /**
     * Check if declassification is due
     */
    @Transient
    public boolean isDeclassificationDue() {
        if (declassificationDate == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(declassificationDate);
    }

    /**
     * Check if document contains PII based on data subject categories
     */
    @Transient
    public boolean containsPII() {
        if (dataSubjectCategories == null || dataSubjectCategories.isEmpty()) {
            return false;
        }
        
        return dataSubjectCategories.stream()
                .anyMatch(category -> category != DataSubjectCategory.ANONYMOUS && 
                                    category != DataSubjectCategory.PSEUDONYMIZED);
    }

    /**
     * Check if document requires special protection for minors
     */
    @Transient
    public boolean requiresMinorProtection() {
        return dataSubjectCategories != null && 
               dataSubjectCategories.contains(DataSubjectCategory.MINOR);
    }

    /**
     * Get the most restrictive data subject category
     */
    @Transient
    public DataSubjectCategory getMostRestrictiveDataSubject() {
        if (dataSubjectCategories == null || dataSubjectCategories.isEmpty()) {
            return null;
        }
        
        return dataSubjectCategories.stream()
                .max((a, b) -> a.getMinimumClassificationLevel().getLevel() - 
                              b.getMinimumClassificationLevel().getLevel())
                .orElse(null);
    }

    /**
     * Check if storage region is compliant with data subject requirements
     */
    @Transient
    public boolean isStorageRegionCompliant() {
        if (storageRegion == null || dataSubjectCategories == null) {
            return true; // No restrictions
        }
        
        // Check if any data subject category restricts storage to specific regions
        return dataSubjectCategories.stream()
                .allMatch(category -> {
                    if (category == DataSubjectCategory.MINOR && 
                        !storageRegion.hasStrictPrivacyLaws()) {
                        return false;
                    }
                    return true;
                });
    }

    /**
     * Get compliance score based on various factors
     */
    @Transient
    public int getComplianceScore() {
        int score = 100; // Start with perfect score
        
        // Deduct for violations
        score -= Math.min(violationCount * 10, 50);
        
        // Deduct for overdue reviews
        if (isReviewDue()) {
            score -= 15;
        }
        
        // Deduct for overdue declassification
        if (isDeclassificationDue()) {
            score -= 10;
        }
        
        // Deduct for non-compliant storage
        if (!isStorageRegionCompliant()) {
            score -= 20;
        }
        
        // Add points for good practices
        if (isAnonymized || isPseudonymized) {
            score += 5;
        }
        
        if ("ENCRYPTED".equals(encryptionStatus)) {
            score += 10;
        }
        
        return Math.max(score, 0); // Don't go below 0
    }

    /**
     * Update compliance check timestamp
     */
    @Transient
    public void updateComplianceCheck() {
        lastComplianceCheck = LocalDateTime.now();
    }

    /**
     * Record a compliance violation
     */
    @Transient
    public void recordViolation() {
        violationCount++;
        lastViolationDate = LocalDateTime.now();
        complianceStatus = "NON_COMPLIANT";
    }

    @Override
    public String toString() {
        return String.format("DocumentComplianceMapping{id=%d, document=%d, classification=%s, status=%s}", 
                           getId(), 
                           document != null ? document.getId() : null,
                           complianceClassification != null ? complianceClassification.getName() : null,
                           complianceStatus);
    }
}
