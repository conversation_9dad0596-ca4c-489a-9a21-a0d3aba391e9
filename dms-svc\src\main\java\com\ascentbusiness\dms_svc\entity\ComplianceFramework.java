package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.ComplianceFrameworkType;
import com.ascentbusiness.dms_svc.enums.GeographicRegion;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Entity representing a compliance framework configuration
 */
@Entity
@Table(name = "compliance_frameworks")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ComplianceFramework extends BaseEntity {

    @Enumerated(EnumType.STRING)
    @Column(name = "framework_type", nullable = false, length = 50)
    private ComplianceFrameworkType frameworkType;

    @Column(name = "name", nullable = false, length = 200)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "version", length = 50)
    private String version;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "effective_date")
    private LocalDateTime effectiveDate;

    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;

    @ElementCollection(targetClass = GeographicRegion.class)
    @Enumerated(EnumType.STRING)
    @CollectionTable(
        name = "compliance_framework_regions",
        joinColumns = @JoinColumn(name = "framework_id")
    )
    @Column(name = "region")
    private Set<GeographicRegion> applicableRegions;

    @Column(name = "authority", length = 200)
    private String authority; // Regulatory authority or organization

    @Column(name = "website_url", length = 500)
    private String websiteUrl;

    @Builder.Default
    @Column(name = "requires_data_subject_rights", nullable = false)
    private Boolean requiresDataSubjectRights = false;

    @Builder.Default
    @Column(name = "requires_consent_management", nullable = false)
    private Boolean requiresConsentManagement = false;

    @Builder.Default
    @Column(name = "requires_data_localization", nullable = false)
    private Boolean requiresDataLocalization = false;

    @Builder.Default
    @Column(name = "requires_encryption", nullable = false)
    private Boolean requiresEncryption = false;

    @Builder.Default
    @Column(name = "requires_audit_trail", nullable = false)
    private Boolean requiresAuditTrail = true;

    @Column(name = "max_retention_days")
    private Integer maxRetentionDays;

    @Column(name = "notification_requirements", columnDefinition = "TEXT")
    private String notificationRequirements;

    @Column(name = "penalty_information", columnDefinition = "TEXT")
    private String penaltyInformation;

    @Column(name = "implementation_notes", columnDefinition = "TEXT")
    private String implementationNotes;

    @Builder.Default
    @Column(name = "priority", nullable = false)
    private Integer priority = 0; // Higher number = higher priority

    // Relationships
    @OneToMany(mappedBy = "complianceFramework", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<RegulationMapping> regulationMappings;

    /**
     * Check if this framework is currently effective
     */
    @Transient
    public boolean isCurrentlyEffective() {
        LocalDateTime now = LocalDateTime.now();
        
        if (!isActive) {
            return false;
        }
        
        if (effectiveDate != null && now.isBefore(effectiveDate)) {
            return false;
        }
        
        if (expiryDate != null && now.isAfter(expiryDate)) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if this framework applies to a specific region
     */
    @Transient
    public boolean appliesTo(GeographicRegion region) {
        return applicableRegions != null && applicableRegions.contains(region);
    }

    /**
     * Get display name for the framework
     */
    @Transient
    public String getDisplayName() {
        if (name != null && !name.trim().isEmpty()) {
            return name;
        }
        return frameworkType != null ? frameworkType.getDisplayName() : "Unknown Framework";
    }

    /**
     * Check if framework requires special handling for minors
     */
    @Transient
    public boolean requiresMinorProtection() {
        return frameworkType == ComplianceFrameworkType.GDPR || 
               frameworkType == ComplianceFrameworkType.CCPA ||
               frameworkType == ComplianceFrameworkType.PIPEDA ||
               frameworkType == ComplianceFrameworkType.LGPD;
    }

    /**
     * Get compliance score based on requirements
     */
    @Transient
    public int getComplianceScore() {
        int score = 0;
        
        if (requiresDataSubjectRights) score += 20;
        if (requiresConsentManagement) score += 15;
        if (requiresDataLocalization) score += 25;
        if (requiresEncryption) score += 20;
        if (requiresAuditTrail) score += 10;
        if (maxRetentionDays != null && maxRetentionDays < 1825) score += 10; // Less than 5 years
        
        return score;
    }

    @Override
    public String toString() {
        return String.format("ComplianceFramework{id=%d, type=%s, name='%s', active=%s}", 
                           getId(), frameworkType, name, isActive);
    }
}
