package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ProcessingStrategy;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for processing strategy counts.
 * Corresponds to StrategyCount GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StrategyCount {

    /**
     * The processing strategy.
     */
    private ProcessingStrategy strategy;

    /**
     * Number of uploads using this strategy.
     */
    private Long count;

    /**
     * Percentage of total uploads using this strategy.
     */
    private Float percentage;
}
