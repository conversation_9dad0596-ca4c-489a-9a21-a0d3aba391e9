# Document Sharing Implementation Guide

## Architecture Overview

The Document Sharing feature is implemented as a comprehensive system with the following components:

### Core Components

1. **Entities**: JPA entities for data persistence
2. **Repositories**: Data access layer with custom queries
3. **Services**: Business logic and transaction management
4. **Resolvers**: GraphQL API layer
5. **DTOs**: Data transfer objects for API communication
6. **Exceptions**: Custom exception handling

### Database Schema

#### DocumentShareLink Table
```sql
CREATE TABLE document_share_links (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    link_id VARCHAR(36) NOT NULL UNIQUE,
    document_id BIGINT NOT NULL,
    created_by_user_id VARCHAR(100) NOT NULL,
    permission VARCHAR(20) NOT NULL,
    target_user_id VARCHAR(100),
    target_role_name VARCHAR(100),
    created_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    password VARCHAR(255),
    max_uses INT,
    use_count INT NOT NULL DEFAULT 0,
    notes <PERSON><PERSON>HA<PERSON>(1000)
);
```

#### BulkShareOperation Table
```sql
CREATE TABLE bulk_share_operations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    operation_id VARCHAR(36) NOT NULL UNIQUE,
    created_by_user_id VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    permission VARCHAR(20) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    notes VARCHAR(1000),
    is_completed BOOLEAN NOT NULL DEFAULT FALSE,
    completed_at TIMESTAMP,
    total_documents INT NOT NULL,
    total_recipients INT NOT NULL,
    success_count INT NOT NULL DEFAULT 0,
    failure_count INT NOT NULL DEFAULT 0
);
```

#### BulkShareItem Table
```sql
CREATE TABLE bulk_share_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    operation_id BIGINT NOT NULL,
    document_id BIGINT NOT NULL,
    recipient_id VARCHAR(100) NOT NULL,
    is_role BOOLEAN NOT NULL DEFAULT FALSE,
    is_successful BOOLEAN NOT NULL DEFAULT FALSE,
    error_message VARCHAR(1000),
    share_link_id VARCHAR(36)
);
```

## Service Layer Implementation

### DocumentShareService

**Key Responsibilities:**
- Share link creation and validation
- Document access via share links
- Link expiration and cleanup
- Security and permission checks

**Core Methods:**
```java
public DocumentShareLink createShareLink(Long documentId, CreateShareLinkInput input)
public Document accessDocumentViaShareLink(String linkId, String password)
public List<DocumentShareLink> getShareLinksForDocument(Long documentId)
public void revokeShareLink(String linkId)
public String generateShareUrl(DocumentShareLink shareLink)
```

**Security Features:**
- UUID-based link IDs for cryptographic security
- Permission validation before link creation
- Automatic link deactivation on expiration/max uses
- Password protection support
- Audit logging for all operations

### BulkShareService

**Key Responsibilities:**
- Bulk document sharing operations
- Progress tracking and error handling
- Transaction management for large operations
- Success/failure statistics

**Core Methods:**
```java
public BulkShareResult bulkShareDocuments(BulkShareInput input)
public BulkShareOperation getBulkShareOperation(String operationId)
public List<BulkShareOperation> getMyBulkShareOperations()
```

**Performance Optimizations:**
- Batch processing for large operations
- Individual error handling per item
- Progress tracking and completion status
- Efficient database operations

## GraphQL Integration

### Schema Design

The GraphQL schema extends the existing DMS schema with document sharing types:

```graphql
extend type Document {
  shareLinks: [DocumentShareLink!]
}

extend type Query {
  documentShareLinks(documentId: ID!): [DocumentShareLink!]!
  shareLink(linkId: String!): DocumentShareLink
  accessSharedDocument(input: AccessSharedDocumentInput!): AccessSharedDocumentResponse!
}

extend type Mutation {
  createDocumentShareLink(documentId: ID!, input: CreateShareLinkInput!): ShareLinkResponse!
  revokeDocumentShareLink(linkId: String!): ShareLinkResponse!
  bulkShareDocuments(input: BulkShareInput!): BulkShareResponse!
}
```

### Resolver Implementation

**DocumentShareResolver** handles all GraphQL operations:

```java
@Controller
@RequiredArgsConstructor
public class DocumentShareResolver {
    
    @QueryMapping
    @PreAuthorize("isAuthenticated()")
    public List<DocumentShareLink> documentShareLinks(@Argument Long documentId)
    
    @MutationMapping
    @PreAuthorize("isAuthenticated()")
    @RateLimit(value = 10, window = 300)
    public ShareLinkResponse createDocumentShareLink(@Argument Long documentId, @Argument CreateShareLinkInput input)
    
    @QueryMapping // Public endpoint
    public AccessSharedDocumentResponse accessSharedDocument(@Argument AccessSharedDocumentInput input)
}
```

## Security Implementation

### Authentication & Authorization

1. **Authenticated Endpoints**: Most operations require authentication
2. **Public Access**: Document access via share links is public
3. **Permission Checks**: Validate user permissions before operations
4. **Rate Limiting**: Prevent abuse with configurable rate limits

### Security Features

```java
// Link ID generation using UUID v4
String linkId = UUID.randomUUID().toString();

// Password validation
if (shareLink.requiresPassword()) {
    if (!password.equals(shareLink.getPassword())) {
        throw InvalidShareLinkException.invalidPassword(linkId);
    }
}

// Automatic expiration handling
if (shareLink.getExpiresAt().isBefore(LocalDateTime.now())) {
    shareLink.setIsActive(false);
    throw InvalidShareLinkException.expired(linkId);
}
```

### Audit Integration

All sharing operations are logged:

```java
auditService.logAudit(
    AuditAction.CREATE,
    documentId,
    currentUserId,
    String.format("Created share link %s with permission %s", linkId, permission)
);
```

## Data Access Layer

### Repository Pattern

Custom repository methods for efficient queries:

```java
@Repository
public interface DocumentShareLinkRepository extends JpaRepository<DocumentShareLink, Long> {
    
    Optional<DocumentShareLink> findByLinkId(String linkId);
    
    List<DocumentShareLink> findByDocumentIdAndIsActiveTrue(Long documentId);
    
    List<DocumentShareLink> findByExpiresAtBeforeAndIsActiveTrue(LocalDateTime dateTime);
    
    @Query("SELECT COUNT(dsl) FROM DocumentShareLink dsl WHERE dsl.document.id = :documentId AND dsl.isActive = true")
    long countActiveByDocumentId(@Param("documentId") Long documentId);
}
```

### Performance Optimizations

1. **Indexes**: Strategic database indexes for common queries
2. **Lazy Loading**: Fetch strategies for related entities
3. **Batch Operations**: Efficient bulk processing
4. **Query Optimization**: Custom JPQL queries for complex operations

## Error Handling

### Custom Exceptions

```java
public class InvalidShareLinkException extends DmsBusinessException {
    public static InvalidShareLinkException expired(String linkId)
    public static InvalidShareLinkException inactive(String linkId)
    public static InvalidShareLinkException maxUsesReached(String linkId)
    public static InvalidShareLinkException passwordRequired(String linkId)
}
```

### Response Patterns

Consistent response structure across all operations:

```java
public class ShareLinkResponse {
    private Boolean success;
    private String message;
    private DocumentShareLink shareLink;
    
    public static ShareLinkResponse success(String message, DocumentShareLink shareLink)
    public static ShareLinkResponse error(String message)
}
```

## Configuration

### Application Properties

```properties
# Document Sharing Configuration
dms.application.base-url=${DMS_BASE_URL:http://localhost:9093}
```

### Scheduled Tasks

Automatic cleanup of expired links:

```java
@Scheduled(cron = "0 0 * * * *") // Run every hour
public void cleanupExpiredLinks() {
    List<DocumentShareLink> expiredLinks = shareLinkRepository.findByExpiresAtBeforeAndIsActiveTrue(LocalDateTime.now());
    // Deactivate expired links
}
```

## Testing Strategy

### Unit Tests
- Service layer business logic
- Repository custom queries
- GraphQL resolver operations
- Input validation and error handling

### Integration Tests
- End-to-end document sharing flows
- Database transaction management
- Security and permission validation
- Bulk operation processing

### GraphQL Contract Tests
- API schema validation
- Request/response format verification
- Error handling scenarios
- Authentication and authorization

## Deployment Considerations

### Database Migration

Liquibase changelog for schema updates:

```xml
<include file="db/changelog/046-create-document-sharing-tables.sql" relativeToChangelogFile="false"/>
```

### Environment Configuration

```bash
# Production environment
DMS_BASE_URL=https://dms.company.com

# Development environment  
DMS_BASE_URL=http://localhost:9093
```

### Monitoring

Key metrics to monitor:
- Share link creation rate
- Document access frequency
- Bulk operation success rates
- Security violation attempts
- Performance metrics

## Maintenance

### Regular Tasks

1. **Link Cleanup**: Automated cleanup of expired links
2. **Usage Analytics**: Monitor sharing patterns
3. **Security Audits**: Review sharing activities
4. **Performance Monitoring**: Track operation performance

### Troubleshooting

Common issues and solutions:
- High bulk operation failure rates
- Performance degradation with large operations
- Security violations and suspicious activity
- Database constraint violations
