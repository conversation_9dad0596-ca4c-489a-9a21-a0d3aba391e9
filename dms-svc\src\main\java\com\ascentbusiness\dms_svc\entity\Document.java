package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.converter.PIIEncryptionConverter;
import com.ascentbusiness.dms_svc.enums.DocumentStatus;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.enums.LegalHoldStatus;
import com.ascentbusiness.dms_svc.enums.DispositionStatus;
import com.ascentbusiness.dms_svc.enums.ComplianceClassificationLevel;
import com.ascentbusiness.dms_svc.enums.DataSubjectCategory;
import com.ascentbusiness.dms_svc.enums.GeographicRegion;
import com.ascentbusiness.dms_svc.enums.TaskStatus;
import com.ascentbusiness.dms_svc.enums.WorkflowStatus;
import com.ascentbusiness.dms_svc.security.UserPrincipal;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "documents")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Document extends BaseEntity {
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "original_file_name", nullable = false)
    private String originalFileName;
    
    @Column(name = "file_size", nullable = false)
    private Long fileSize;
    
    @Column(name = "mime_type", nullable = false, length = 100)
    private String mimeType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "storage_provider", nullable = false, length = 20)
    private StorageProvider storageProvider;
    
    @Column(name = "storage_path", nullable = false, length = 500)
    private String storagePath;
    
    @Builder.Default
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private DocumentStatus status = DocumentStatus.ACTIVE;
    
    @Builder.Default
    @Column(name = "version", nullable = false)
    private Integer version = 1;
    
    @Column(name = "tags", columnDefinition = "JSON")
    private String tags; // JSON string for MySQL
    
    @Lob
    @Column(name = "file_content", columnDefinition = "LONGBLOB")
    private byte[] fileContent;
    
    @Column(name = "creator_user_id", nullable = false, length = 100)
    private String creatorUserId;
    
    @Column(name = "creator_roles", columnDefinition = "JSON")
    private String creatorRoles; // JSON string containing roles of the user who created the document
    
    @Column(name = "creator_permissions", columnDefinition = "JSON")
    private String creatorPermissions; // JSON string containing permissions of the user who created the document
    
    @Column(name = "last_modifier_roles", columnDefinition = "JSON")
    private String lastModifierRoles; // JSON string containing roles of the user who last modified the document
    
    @Column(name = "last_modifier_permissions", columnDefinition = "JSON")
    private String lastModifierPermissions; // JSON string containing permissions of the user who last modified the document
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_document_id")
    private Document parentDocument;
    
    @OneToMany(mappedBy = "parentDocument", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Document> versions;

    @OneToMany(mappedBy = "documentId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<AuditLog> auditLogs;

    @OneToMany(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<DocumentPermission> permissions;
    
    @OneToMany(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<DocumentVersion> documentVersions;

    @OneToOne(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private DocumentClassificationMetadata classificationMetadata;

    @OneToOne(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private DocumentOwnershipMetadata ownershipMetadata;

    @OneToOne(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private DocumentComplianceMetadata complianceMetadata;

    // Enhanced Compliance Fields
    @Enumerated(EnumType.STRING)
    @Column(name = "compliance_classification_level", length = 50)
    private ComplianceClassificationLevel complianceClassificationLevel;

    @ElementCollection(targetClass = DataSubjectCategory.class)
    @Enumerated(EnumType.STRING)
    @CollectionTable(
        name = "document_data_subject_categories",
        joinColumns = @JoinColumn(name = "document_id")
    )
    @Column(name = "data_subject_category")
    private Set<DataSubjectCategory> dataSubjectCategories;

    @Enumerated(EnumType.STRING)
    @Column(name = "storage_region", length = 50)
    private GeographicRegion storageRegion;

    @ElementCollection(targetClass = GeographicRegion.class)
    @Enumerated(EnumType.STRING)
    @CollectionTable(
        name = "document_geographic_restrictions",
        joinColumns = @JoinColumn(name = "document_id")
    )
    @Column(name = "region")
    private Set<GeographicRegion> geographicRestrictions;

    @Column(name = "requires_consent", nullable = false)
    @Builder.Default
    private Boolean requiresConsent = false;

    @Column(name = "consent_reference", length = 1000) // Increased length for encrypted data
    @Convert(converter = PIIEncryptionConverter.class)
    private String consentReference;

    @Column(name = "is_anonymized", nullable = false)
    @Builder.Default
    private Boolean isAnonymized = false;

    @Column(name = "is_pseudonymized", nullable = false)
    @Builder.Default
    private Boolean isPseudonymized = false;

    @Column(name = "encryption_status", length = 50)
    private String encryptionStatus; // ENCRYPTED, NOT_ENCRYPTED, PARTIALLY_ENCRYPTED

    @Column(name = "compliance_notes", columnDefinition = "TEXT")
    private String complianceNotes;

    // Compliance Relationships
    @OneToMany(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<RegulationMapping> regulationMappings;

    @OneToMany(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<DocumentComplianceMapping> complianceMappings;

    // Retention Policy Fields
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "retention_policy_id")
    private RetentionPolicy retentionPolicy;

    @Column(name = "retention_expiry_date")
    private LocalDateTime retentionExpiryDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "legal_hold_status", length = 20)
    @Builder.Default
    private LegalHoldStatus legalHoldStatus = LegalHoldStatus.NONE;

    @Enumerated(EnumType.STRING)
    @Column(name = "disposition_status", length = 20)
    @Builder.Default
    private DispositionStatus dispositionStatus = DispositionStatus.ACTIVE;

    @Column(name = "legal_hold_reason", columnDefinition = "TEXT")
    private String legalHoldReason;

    @Column(name = "legal_hold_applied_date")
    private LocalDateTime legalHoldAppliedDate;

    @Column(name = "legal_hold_applied_by", length = 100)
    private String legalHoldAppliedBy;

    @Column(name = "disposition_review_date")
    private LocalDateTime dispositionReviewDate;

    @Column(name = "disposition_notes", columnDefinition = "TEXT")
    private String dispositionNotes;

    // Workflow Management Fields
    @Enumerated(EnumType.STRING)
    @Column(name = "workflow_status", length = 50)
    @Builder.Default
    private WorkflowStatus workflowStatus = WorkflowStatus.NONE;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "current_workflow_instance_id")
    private WorkflowInstance currentWorkflowInstance;

    @Column(name = "workflow_completion_date")
    private LocalDateTime workflowCompletionDate;

    // Template Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "source_template_id")
    private DocumentTemplate sourceTemplate;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "template_field_values", columnDefinition = "JSON")
    private JsonNode templateFieldValues;

    // Workflow Relationships
    @OneToMany(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<WorkflowInstance> workflowInstances;

    private static final Logger logger = LoggerFactory.getLogger(Document.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // Manual auditing fallback methods to ensure created_by is populated
    @PrePersist
    protected void onDocumentCreate() {
        logger.debug("Document @PrePersist called - checking auditing fields");
        
        // If JPA auditing didn't set created_by, set it manually
        if (getCreatedBy() == null) {
            String currentUser = getCurrentUserFromSecurity();
            if (currentUser != null) {
                setCreatedBy(currentUser);
                logger.info("Document @PrePersist: Manually set created_by to: {}", currentUser);
            } else {
                setCreatedBy("system");
                logger.warn("Document @PrePersist: No authenticated user found, set created_by to 'system'");
            }
        } else {
            logger.debug("Document @PrePersist: created_by already set to: {}", getCreatedBy());
        }
        
        // If JPA auditing didn't set last_modified_by, set it manually
        if (getLastModifiedBy() == null) {
            String currentUser = getCurrentUserFromSecurity();
            if (currentUser != null) {
                setLastModifiedBy(currentUser);
                logger.debug("Document @PrePersist: Manually set last_modified_by to: {}", currentUser);
            } else {
                setLastModifiedBy("system");
                logger.debug("Document @PrePersist: No authenticated user found, set last_modified_by to 'system'");
            }
        }
    }
    
    @PreUpdate
    protected void onDocumentUpdate() {
        logger.debug("Document @PreUpdate called - checking auditing fields");
        
        // If JPA auditing didn't set last_modified_by, set it manually
        if (getLastModifiedBy() == null) {
            String currentUser = getCurrentUserFromSecurity();
            if (currentUser != null) {
                setLastModifiedBy(currentUser);
                logger.info("Document @PreUpdate: Manually set last_modified_by to: {}", currentUser);
            } else {
                setLastModifiedBy("system");
                logger.warn("Document @PreUpdate: No authenticated user found, set last_modified_by to 'system'");
            }
        } else {
            logger.debug("Document @PreUpdate: last_modified_by already set to: {}", getLastModifiedBy());
        }
    }
    
    // Helper method to get current user from SecurityContext (same logic as DocumentService)
    private String getCurrentUserFromSecurity() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.isAuthenticated()) {
                if (authentication.getPrincipal() instanceof UserPrincipal) {
                    UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                    String username = userPrincipal.getUsername();
                    logger.debug("Document: Found UserPrincipal in SecurityContext: {}", username);
                    return username;
                }
                
                if (authentication.getPrincipal() instanceof String) {
                    String username = (String) authentication.getPrincipal();
                    logger.debug("Document: Found String principal in SecurityContext: {}", username);
                    return username;
                }
                
                logger.debug("Document: Unknown principal type in SecurityContext: {}", 
                        authentication.getPrincipal().getClass().getName());
            }
        } catch (Exception e) {
            logger.warn("Document: Exception while getting current user from SecurityContext", e);
        }
        
        return null;
    }
    
    // Helper method to get current active version of a document
    @Transient
    @com.fasterxml.jackson.annotation.JsonIgnore
    public Document getCurrentVersion() {
        if (status == DocumentStatus.ACTIVE) {
            return this;
        }
        
        if (versions != null) {
            return versions.stream()
                    .filter(doc -> doc.getStatus() == DocumentStatus.ACTIVE)
                    .findFirst()
                    .orElse(null);
        }
        
        return null;
    }
    
    // GraphQL compatibility methods
    @Transient
    public String getUserId() {
        return this.creatorUserId;
    }
    
    public void setUserId(String userId) {
        this.creatorUserId = userId;
    }
    
    // GraphQL schema field mappings
    @Transient
    public String getFilePath() {
        return this.storagePath;
    }

    public void setFilePath(String filePath) {
        this.storagePath = filePath;
    }

    @Transient
    public String getFileName() {
        return this.originalFileName;
    }

    public void setFileName(String fileName) {
        this.originalFileName = fileName;
    }
    
    // Override BaseEntity getCreatedBy to ensure proper access to JPA audit field
    @Override
    public String getCreatedBy() {
        return super.getCreatedBy(); // Use the actual JPA audit field from BaseEntity
    }
    
    @Override
    public void setCreatedBy(String createdBy) {
        super.setCreatedBy(createdBy); // Set the actual JPA audit field from BaseEntity
    }
    
    @Transient
    public String getFileContentBase64() {
        if (fileContent != null && fileContent.length > 0) {
            return java.util.Base64.getEncoder().encodeToString(fileContent);
        }
        return null;
    }
    
    public void setFileContentBase64(String base64Content) {
        if (base64Content != null && !base64Content.trim().isEmpty()) {
            try {
                this.fileContent = java.util.Base64.getDecoder().decode(base64Content);
            } catch (IllegalArgumentException e) {
                logger.warn("Failed to decode base64 content", e);
                this.fileContent = null;
            }
        } else {
            this.fileContent = null;
        }
    }
    
    // Keywords/Tags conversion methods for GraphQL compatibility
    @Transient
    public List<String> getKeywords() {
        if (tags == null || tags.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            return objectMapper.readValue(tags, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            logger.warn("Failed to parse tags JSON: {}", tags, e);
            return new ArrayList<>();
        }
    }
    
    public void setKeywords(List<String> keywords) {
        if (keywords == null || keywords.isEmpty()) {
            this.tags = null;
            return;
        }
        
        try {
            this.tags = objectMapper.writeValueAsString(keywords);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize keywords to JSON: {}", keywords, e);
            this.tags = null;
        }
    }
    
    // Tags conversion methods for GraphQL compatibility (legacy support)
    @Transient
    public List<String> getTagsList() {
        return getKeywords();
    }
    
    public void setTagsList(List<String> tagsList) {
        setKeywords(tagsList);
    }
    
    
    // User context metadata helper methods
    
    @Transient
    public List<String> getCreatorRolesList() {
        if (creatorRoles == null || creatorRoles.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            return objectMapper.readValue(creatorRoles, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            logger.warn("Failed to parse creator roles JSON: {}", creatorRoles, e);
            return new ArrayList<>();
        }
    }
    
    public void setCreatorRolesList(List<String> roles) {
        if (roles == null || roles.isEmpty()) {
            this.creatorRoles = null;
            return;
        }
        
        try {
            this.creatorRoles = objectMapper.writeValueAsString(roles);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize creator roles to JSON: {}", roles, e);
            this.creatorRoles = null;
        }
    }
    
    @Transient
    public List<String> getCreatorPermissionsList() {
        if (creatorPermissions == null || creatorPermissions.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            return objectMapper.readValue(creatorPermissions, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            logger.warn("Failed to parse creator permissions JSON: {}", creatorPermissions, e);
            return new ArrayList<>();
        }
    }
    
    public void setCreatorPermissionsList(List<String> permissions) {
        if (permissions == null || permissions.isEmpty()) {
            this.creatorPermissions = null;
            return;
        }
        
        try {
            this.creatorPermissions = objectMapper.writeValueAsString(permissions);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize creator permissions to JSON: {}", permissions, e);
            this.creatorPermissions = null;
        }
    }
    
    @Transient
    public List<String> getLastModifierRolesList() {
        if (lastModifierRoles == null || lastModifierRoles.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            return objectMapper.readValue(lastModifierRoles, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            logger.warn("Failed to parse last modifier roles JSON: {}", lastModifierRoles, e);
            return new ArrayList<>();
        }
    }
    
    public void setLastModifierRolesList(List<String> roles) {
        if (roles == null || roles.isEmpty()) {
            this.lastModifierRoles = null;
            return;
        }
        
        try {
            this.lastModifierRoles = objectMapper.writeValueAsString(roles);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize last modifier roles to JSON: {}", roles, e);
            this.lastModifierRoles = null;
        }
    }
    
    @Transient
    public List<String> getLastModifierPermissionsList() {
        if (lastModifierPermissions == null || lastModifierPermissions.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            return objectMapper.readValue(lastModifierPermissions, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            logger.warn("Failed to parse last modifier permissions JSON: {}", lastModifierPermissions, e);
            return new ArrayList<>();
        }
    }
    
    public void setLastModifierPermissionsList(List<String> permissions) {
        if (permissions == null || permissions.isEmpty()) {
            this.lastModifierPermissions = null;
            return;
        }
        
        try {
            this.lastModifierPermissions = objectMapper.writeValueAsString(permissions);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize last modifier permissions to JSON: {}", permissions, e);
            this.lastModifierPermissions = null;
        }
    }

    // Retention-related utility methods

    /**
     * Check if document is eligible for disposition
     */
    @Transient
    public boolean isEligibleForDisposition() {
        if (legalHoldStatus == LegalHoldStatus.ACTIVE) {
            return false;
        }

        if (retentionExpiryDate == null) {
            return false;
        }

        return LocalDateTime.now().isAfter(retentionExpiryDate);
    }

    /**
     * Check if document is under legal hold
     */
    @Transient
    public boolean isUnderLegalHold() {
        return legalHoldStatus == LegalHoldStatus.ACTIVE;
    }

    /**
     * Calculate retention expiry date based on assigned policy
     */
    @Transient
    public LocalDateTime calculateRetentionExpiryDate() {
        if (retentionPolicy == null) {
            return null;
        }

        // Use creation date as the trigger event by default
        LocalDateTime triggerDate = getCreatedDate();
        if (triggerDate == null) {
            triggerDate = LocalDateTime.now();
        }

        return retentionPolicy.calculateExpiryDate(triggerDate);
    }

    /**
     * Apply legal hold to the document
     */
    @Transient
    public void applyLegalHold(String reason, String appliedBy) {
        this.legalHoldStatus = LegalHoldStatus.ACTIVE;
        this.legalHoldReason = reason;
        this.legalHoldAppliedDate = LocalDateTime.now();
        this.legalHoldAppliedBy = appliedBy;
        this.dispositionStatus = DispositionStatus.ON_HOLD;
    }

    /**
     * Release legal hold from the document
     */
    @Transient
    public void releaseLegalHold() {
        this.legalHoldStatus = LegalHoldStatus.RELEASED;
        this.legalHoldReason = null;
        this.legalHoldAppliedDate = null;
        this.legalHoldAppliedBy = null;

        // Update disposition status based on retention expiry
        if (isEligibleForDisposition()) {
            this.dispositionStatus = DispositionStatus.ELIGIBLE;
        } else {
            this.dispositionStatus = DispositionStatus.ACTIVE;
        }
    }

    // Compliance-related methods

    /**
     * Check if document contains PII based on data subject categories
     */
    @Transient
    public boolean containsPII() {
        if (dataSubjectCategories == null || dataSubjectCategories.isEmpty()) {
            return false;
        }

        return dataSubjectCategories.stream()
                .anyMatch(category -> category != DataSubjectCategory.ANONYMOUS &&
                                    category != DataSubjectCategory.PSEUDONYMIZED);
    }

    /**
     * Check if document requires special protection for minors
     */
    @Transient
    public boolean requiresMinorProtection() {
        return dataSubjectCategories != null &&
               dataSubjectCategories.contains(DataSubjectCategory.MINOR);
    }

    /**
     * Get the highest classification level required by data subjects
     */
    @Transient
    public ComplianceClassificationLevel getRequiredClassificationLevel() {
        if (dataSubjectCategories == null || dataSubjectCategories.isEmpty()) {
            return complianceClassificationLevel;
        }

        ComplianceClassificationLevel maxRequired = dataSubjectCategories.stream()
                .map(DataSubjectCategory::getMinimumClassificationLevel)
                .max((a, b) -> a.getLevel() - b.getLevel())
                .orElse(ComplianceClassificationLevel.PUBLIC);

        if (complianceClassificationLevel == null) {
            return maxRequired;
        }

        return complianceClassificationLevel.getLevel() > maxRequired.getLevel() ?
               complianceClassificationLevel : maxRequired;
    }

    /**
     * Check if document storage region is compliant with geographic restrictions
     */
    @Transient
    public boolean isStorageRegionCompliant() {
        if (geographicRestrictions == null || geographicRestrictions.isEmpty()) {
            return true; // No restrictions
        }

        if (storageRegion == null) {
            return false; // Storage region not specified but restrictions exist
        }

        return geographicRestrictions.contains(storageRegion) ||
               geographicRestrictions.contains(GeographicRegion.GLOBAL);
    }

    /**
     * Check if document requires encryption based on classification
     */
    @Transient
    public boolean requiresEncryption() {
        ComplianceClassificationLevel effectiveLevel = getRequiredClassificationLevel();
        return effectiveLevel != null && effectiveLevel.requiresEncryptionAtRest();
    }

    /**
     * Check if document is properly encrypted
     */
    @Transient
    public boolean isProperlyEncrypted() {
        if (!requiresEncryption()) {
            return true; // Encryption not required
        }

        return "ENCRYPTED".equals(encryptionStatus);
    }

    /**
     * Get compliance score for this document
     */
    @Transient
    public int getComplianceScore() {
        int score = 100; // Start with perfect score

        // Deduct for missing classification
        if (complianceClassificationLevel == null && containsPII()) {
            score -= 30;
        }

        // Deduct for improper storage region
        if (!isStorageRegionCompliant()) {
            score -= 25;
        }

        // Deduct for missing encryption
        if (!isProperlyEncrypted()) {
            score -= 20;
        }

        // Deduct for missing consent when required
        if (requiresConsent && (consentReference == null || consentReference.trim().isEmpty())) {
            score -= 15;
        }

        // Add points for good practices
        if (isAnonymized || isPseudonymized) {
            score += 5;
        }

        return Math.max(score, 0); // Don't go below 0
    }

    // Workflow-related utility methods

    /**
     * Check if document is currently in a workflow
     */
    @Transient
    public boolean isInWorkflow() {
        return workflowStatus != null &&
               (WorkflowStatus.PENDING.equals(workflowStatus) ||
                WorkflowStatus.IN_PROGRESS.equals(workflowStatus));
    }

    /**
     * Check if document workflow is completed
     */
    @Transient
    public boolean isWorkflowCompleted() {
        return WorkflowStatus.COMPLETED.equals(workflowStatus);
    }

    /**
     * Check if document workflow is cancelled or failed
     */
    @Transient
    public boolean isWorkflowTerminated() {
        return WorkflowStatus.CANCELLED.equals(workflowStatus) ||
               WorkflowStatus.FAILED.equals(workflowStatus);
    }

    /**
     * Get the current workflow instance name
     */
    @Transient
    public String getCurrentWorkflowInstanceName() {
        return currentWorkflowInstance != null ? currentWorkflowInstance.getInstanceName() : null;
    }

    /**
     * Get the current workflow definition name
     */
    @Transient
    public String getCurrentWorkflowDefinitionName() {
        return currentWorkflowInstance != null && currentWorkflowInstance.getWorkflowDefinition() != null ?
               currentWorkflowInstance.getWorkflowDefinition().getName() : null;
    }

    /**
     * Get the number of pending workflow tasks for this document
     */
    @Transient
    public long getPendingWorkflowTaskCount() {
        if (currentWorkflowInstance == null || currentWorkflowInstance.getTasks() == null) {
            return 0;
        }
        return currentWorkflowInstance.getTasks().stream()
                .filter(task -> TaskStatus.PENDING.equals(task.getStatus()) || TaskStatus.IN_PROGRESS.equals(task.getStatus()))
                .count();
    }

    /**
     * Check if document can be modified (not locked by workflow)
     */
    @Transient
    public boolean canBeModified() {
        // Document can be modified if not in an active workflow or if workflow allows modifications
        return !isInWorkflow() ||
               (currentWorkflowInstance != null &&
                currentWorkflowInstance.getWorkflowDefinition() != null &&
                // This would be determined by workflow configuration
                true); // For now, allow modifications during workflow
    }

    /**
     * Start a workflow for this document
     */
    @Transient
    public void startWorkflow(WorkflowInstance workflowInstance) {
        this.currentWorkflowInstance = workflowInstance;
        this.workflowStatus = WorkflowStatus.IN_PROGRESS;
        this.workflowCompletionDate = null;
    }

    /**
     * Complete the workflow for this document
     */
    @Transient
    public void completeWorkflow(String completionReason) {
        this.workflowStatus = WorkflowStatus.COMPLETED;
        this.workflowCompletionDate = LocalDateTime.now();
        if (currentWorkflowInstance != null) {
            currentWorkflowInstance.setCompletionReason(completionReason);
            currentWorkflowInstance.setCompletedDate(LocalDateTime.now());
        }
    }

    /**
     * Cancel the workflow for this document
     */
    @Transient
    public void cancelWorkflow(String cancellationReason) {
        this.workflowStatus = WorkflowStatus.CANCELLED;
        this.workflowCompletionDate = LocalDateTime.now();
        if (currentWorkflowInstance != null) {
            currentWorkflowInstance.setStatus(WorkflowStatus.CANCELLED);
            currentWorkflowInstance.setCompletionReason(cancellationReason);
            currentWorkflowInstance.setCompletedDate(LocalDateTime.now());
        }
    }

    /**
     * Get workflow history count
     */
    @Transient
    public long getWorkflowHistoryCount() {
        if (workflowInstances == null) return 0;
        return workflowInstances.stream()
                .mapToLong(instance -> instance.getHistory() != null ? instance.getHistory().size() : 0)
                .sum();
    }

    // Template-related utility methods

    /**
     * Check if document was created from a template
     */
    @Transient
    public boolean isCreatedFromTemplate() {
        return sourceTemplate != null;
    }

    /**
     * Get the source template name
     */
    @Transient
    public String getSourceTemplateName() {
        return sourceTemplate != null ? sourceTemplate.getName() : null;
    }

    /**
     * Get the source template category
     */
    @Transient
    public String getSourceTemplateCategory() {
        return sourceTemplate != null ? sourceTemplate.getCategory() : null;
    }

    /**
     * Check if document has template field values
     */
    @Transient
    public boolean hasTemplateFieldValues() {
        return templateFieldValues != null && !templateFieldValues.isNull() && templateFieldValues.size() > 0;
    }

    /**
     * Get the number of template fields filled
     */
    @Transient
    public int getTemplateFieldCount() {
        if (templateFieldValues == null || templateFieldValues.isNull()) return 0;
        return templateFieldValues.size();
    }

    /**
     * Set the source template and initialize field values
     */
    @Transient
    public void setSourceTemplateWithDefaults(DocumentTemplate template) {
        this.sourceTemplate = template;
        if (template != null && template.getDefaultValues() != null) {
            this.templateFieldValues = template.getDefaultValues().deepCopy();
        }
    }

    /**
     * Update template field value
     */
    @Transient
    public void updateTemplateFieldValue(String fieldName, Object value) {
        if (templateFieldValues == null) {
            templateFieldValues = com.fasterxml.jackson.databind.node.JsonNodeFactory.instance.objectNode();
        }
        if (templateFieldValues.isObject()) {
            ((com.fasterxml.jackson.databind.node.ObjectNode) templateFieldValues).put(fieldName, value.toString());
        }
    }

    /**
     * Get template field value
     */
    @Transient
    public String getTemplateFieldValue(String fieldName) {
        if (templateFieldValues == null || !templateFieldValues.isObject()) return null;
        JsonNode fieldValue = templateFieldValues.get(fieldName);
        return fieldValue != null ? fieldValue.asText() : null;
    }

    /**
     * Check if document can be regenerated from template
     */
    @Transient
    public boolean canRegenerateFromTemplate() {
        return isCreatedFromTemplate() &&
               sourceTemplate.isCurrentlyActive() &&
               sourceTemplate.isPublished();
    }
}
