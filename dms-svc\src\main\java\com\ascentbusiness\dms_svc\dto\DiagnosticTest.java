package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.DiagnosticTestType;
import com.ascentbusiness.dms_svc.enums.TestStatus;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * Diagnostic test DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class DiagnosticTest {
    private String testId;
    private DiagnosticTestType testType;
    private String name;
    private TestStatus status;
    private String message;
    private Long duration;
    private OffsetDateTime timestamp;
    private String details;
}
