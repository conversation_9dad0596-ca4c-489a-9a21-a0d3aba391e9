# ===== AUDIT SCHEMA =====
# GraphQL schema for audit operations (replacing AuditController REST endpoints)

# Audit Log Types
type AuditLog {
  id: ID!
  documentId: ID
  action: AuditAction!
  metadata: String
  userId: String!
  timestamp: DateTime!
  ipAddress: String
  userAgent: String
  correlationId: String
  sessionId: String
  entityType: String
  entityId: String
  oldValue: String
  newValue: String
  success: Boolean!
  errorMessage: String
  processingTimeMs: Long
}

# Enhanced Audit Action enum
enum AuditAction {
  CREATE
  READ
  UPDATE
  DELETE
  UPLOAD
  DOWNLOAD
  SHARE
  PERMISSION_GRANT
  PERMISSION_REVOKE
  LOGIN
  LOGOUT
  EXPORT
  IMPORT
  BACKUP
  RESTORE
  SECURITY_VIOLATION
  COMPLIANCE_CHECK
  RETENTION_APPLIED
  LEGAL_HOLD_APPLIED
  LEGAL_HOLD_RELEASED
  WORKFLOW_STARTED
  WORKFLOW_COMPLETED
  TEMPLATE_USED
  CONVERSION_PERFORMED
}

# Audit Log Page Response
type AuditLogPage {
  content: [AuditLog!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
  numberOfElements: Int!
  empty: Boolean!
}

# Audit Export Types
type AuditExportResponse {
  exportId: String!
  status: ExportStatus!
  fileName: String
  downloadUrl: String
  fileSize: Long
  recordCount: Int
  requestedBy: String!
  requestedAt: DateTime!
  completedAt: DateTime
  expiresAt: DateTime!
  format: ExportFormat!
  message: String
  errorDetails: String
}

enum ExportStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  EXPIRED
  CANCELLED
}

enum ExportFormat {
  CSV
  JSON
  XML
  PDF
  EXCEL
}

# Audit Verification Types
type AuditVerificationResponse {
  auditLogId: ID!
  verificationStatus: VerificationStatus!
  verifiedBy: String!
  verifiedAt: DateTime!
  integrityHash: String!
  previousHash: String
  isValid: Boolean!
  tamperingDetected: Boolean!
  verificationDetails: String
  message: String!
}

type BulkAuditVerificationResponse {
  success: Boolean!
  verifiedCount: Int!
  tamperingDetected: Boolean!
  message: String!
  verificationResults: [AuditVerificationResponse!]!
}

enum VerificationStatus {
  VERIFIED
  TAMPERED
  CORRUPTED
  MISSING
  PENDING_VERIFICATION
}

# Tampering Detection Types
type AuditVerificationLog {
  id: ID!
  verificationId: String!
  auditLogId: ID!
  chainId: String
  verificationType: String!
  verificationDate: DateTime
  verifiedBy: String
  verificationMethod: String
  expectedHash: String
  actualHash: String
  hashMatch: Boolean
  signatureValid: Boolean
  chainIntegrityValid: Boolean
  overallStatus: String!
  anomaliesDetected: String
  verificationDetails: String
  correlationId: String
  createdDate: DateTime
}

enum TamperingType {
  HASH_MISMATCH
  SEQUENCE_GAP
  TIMESTAMP_ANOMALY
  UNAUTHORIZED_MODIFICATION
  MISSING_RECORD
  DUPLICATE_RECORD
  SIGNATURE_INVALID
}

enum TamperingSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum InvestigationStatus {
  PENDING
  IN_PROGRESS
  RESOLVED
  ESCALATED
  CLOSED
}

# Input Types
# Alias for backward compatibility
input AuditLogFilter {
  documentId: ID
  userId: String
  action: AuditAction
  dateFrom: DateTime
  dateTo: DateTime
  ipAddress: String
  correlationId: String
  sessionId: String
  entityType: String
  entityId: String
  success: Boolean
  searchTerm: String
}

input AuditLogFilterInput {
  documentId: ID
  userId: String
  action: AuditAction
  dateFrom: DateTime
  dateTo: DateTime
  ipAddress: String
  correlationId: String
  sessionId: String
  entityType: String
  entityId: String
  success: Boolean
  searchTerm: String
}

input ComplianceAuditFilterInput {
  complianceStandard: String
  auditRelevance: String
  controlId: String
  dateFrom: DateTime
  dateTo: DateTime
  userId: String
  documentId: ID
  severity: String
  status: String
}

input AuditExportRequest {
  filterCriteria: AuditLogFilterInput!
  format: ExportFormat!
  includeMetadata: Boolean = true
  includeUserDetails: Boolean = true
  dateRange: DateRangeInput
  maxRecords: Int = 10000
  compressionEnabled: Boolean = false
  encryptionEnabled: Boolean = false
  notificationEmail: String
}


input AuditPaginationInput {
  page: Int = 0
  size: Int = 20
  sortBy: String = "timestamp"
  sortDirection: String = "DESC"
}

# Statistics Types
type AuditStatistics {
  totalRecords: Long!
  recordsToday: Long!
  recordsThisWeek: Long!
  recordsThisMonth: Long!
  actionStatistics: [ActionCount!]!
  userStatistics: [UserActivityCount!]!
  hourlyActivity: [HourlyActivityCount!]!
  topDocuments: [DocumentActivityCount!]!
  securityEvents: Long!
  complianceEvents: Long!
}

type ActionCount {
  action: AuditAction!
  count: Long!
  percentage: Float!
}

type UserActivityCount {
  userId: String!
  actionCount: Long!
  lastActivity: DateTime!
  riskScore: Float
}

type HourlyActivityCount {
  hour: Int!
  count: Long!
  date: DateTime!
}

type DocumentActivityCount {
  documentId: ID!
  documentName: String
  activityCount: Long!
  lastActivity: DateTime!
}

# Compliance Audit Types
type ComplianceAuditLog {
  id: ID!
  auditLog: AuditLog!
  complianceStandard: String!
  controlId: String
  requirementId: String
  complianceStatus: ComplianceStatus!
  riskLevel: RiskLevel!
  findings: String
  recommendations: String
  reviewedBy: String
  reviewedAt: DateTime
  dueDate: DateTime
  isOverdue: Boolean!
}

enum ComplianceStatus {
  COMPLIANT
  NON_COMPLIANT
  PARTIALLY_COMPLIANT
  UNDER_REVIEW
  REMEDIATION_REQUIRED
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

type ComplianceAuditPage {
  content: [ComplianceAuditLog!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}

# Query Extensions
extend type Query {
  # Get audit logs with filtering and pagination
  getAuditLogs(
    filter: AuditLogFilterInput
    pagination: AuditPaginationInput
  ): AuditLogPage!

  # Get compliance-specific audit logs
  getComplianceAuditLogs(
    filter: ComplianceAuditFilterInput
    pagination: AuditPaginationInput
  ): ComplianceAuditPage!

  # Get audit statistics
  getAuditStatistics(
    dateFrom: DateTime
    dateTo: DateTime
  ): AuditStatistics!

  # Get tampering detections
  getTamperingDetections(
    severity: TamperingSeverity
    resolved: Boolean
    dateFrom: DateTime
    dateTo: DateTime
  ): [AuditVerificationLog!]!

  # Get audit export status
  getAuditExportStatus(exportId: String!): AuditExportResponse

  # Get user activity summary
  getUserActivitySummary(
    userId: String!
    dateFrom: DateTime
    dateTo: DateTime
  ): UserActivityCount

  # Get document audit trail
  getDocumentAuditTrail(
    documentId: ID!
    pagination: AuditPaginationInput
  ): AuditLogPage!

  # Search audit logs
  searchAuditLogs(
    searchTerm: String!
    filter: AuditLogFilterInput
    pagination: AuditPaginationInput
  ): AuditLogPage!
}

# Mutation Extensions
extend type Mutation {
  # Request audit export (async operation)
  requestAuditExport(request: AuditExportRequest!): AuditExportResponse!

  # Verify audit log integrity
  verifyAuditLog(auditLogId: ID!): AuditVerificationResponse!

  # Bulk verify audit logs
  bulkVerifyAuditLogs(
    auditLogIds: [ID!]!
    verificationLevel: String = "STANDARD"
  ): BulkAuditVerificationResponse!

  # Mark tampering detection as resolved
  resolveTamperingDetection(
    detectionId: ID!
    resolution: String!
    investigatedBy: String!
  ): AuditVerificationLog!

  # Cancel audit export
  cancelAuditExport(exportId: String!): AuditExportResponse!

  # Create manual audit entry (for system events)
  createAuditEntry(
    action: AuditAction!
    entityType: String!
    entityId: String!
    metadata: String
    userId: String!
  ): AuditLog!

  # Purge old audit logs (admin only)
  purgeOldAuditLogs(
    olderThanDays: Int!
    dryRun: Boolean = true
  ): PurgeResult!
}

# Purge Result Type
type PurgeResult {
  recordsToDelete: Int!
  recordsDeleted: Int!
  success: Boolean!
  message: String!
  executedAt: DateTime!
  dryRun: Boolean!
}
