@echo off
REM ============================================================================
REM DMS Documentation and Automation Test Suite Runner
REM ============================================================================
REM This script runs comprehensive tests for all documentation and automation
REM features implemented in the DMS system.
REM
REM Test Categories:
REM - API Documentation Tests
REM - JavaDoc Coverage Tests  
REM - Troubleshooting Guides Tests
REM - Dependency Management Tests
REM - Rollback Procedures Tests
REM - Integration Tests
REM
REM Prerequisites:
REM - Java 21+ installed and in PATH
REM - Maven 3.8+ installed and in PATH
REM - All documentation and automation features implemented
REM ============================================================================

setlocal enabledelayedexpansion

REM Set script variables
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set TEST_RESULTS_DIR=%PROJECT_ROOT%\target\documentation-test-results
set LOG_FILE=%TEST_RESULTS_DIR%\documentation-tests.log
set REPORT_FILE=%TEST_RESULTS_DIR%\documentation-test-report.html
set TIMESTAMP=%DATE:~-4,4%%DATE:~-10,2%%DATE:~-7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

echo ============================================================================
echo DMS Documentation and Automation Test Suite
echo ============================================================================
echo Timestamp: %TIMESTAMP%
echo Project Root: %PROJECT_ROOT%
echo Test Results: %TEST_RESULTS_DIR%
echo Log File: %LOG_FILE%
echo Report File: %REPORT_FILE%
echo ============================================================================

REM Parse command line arguments
set TEST_CATEGORY=%1
if "%TEST_CATEGORY%"=="" set TEST_CATEGORY=all

REM Create necessary directories
if not exist "%TEST_RESULTS_DIR%" mkdir "%TEST_RESULTS_DIR%"

REM Initialize logging
echo [%TIME%] Starting documentation and automation test suite > "%LOG_FILE%"
echo [%TIME%] Starting documentation and automation test suite

REM Change to project directory
cd /d "%PROJECT_ROOT%"

REM Initialize test counters
set TOTAL_TEST_CLASSES=0
set PASSED_TEST_CLASSES=0
set FAILED_TEST_CLASSES=0

REM Route to appropriate test category
if "%TEST_CATEGORY%"=="api" goto :run_api_tests
if "%TEST_CATEGORY%"=="javadoc" goto :run_javadoc_tests
if "%TEST_CATEGORY%"=="troubleshooting" goto :run_troubleshooting_tests
if "%TEST_CATEGORY%"=="dependency" goto :run_dependency_tests
if "%TEST_CATEGORY%"=="rollback" goto :run_rollback_tests
if "%TEST_CATEGORY%"=="integration" goto :run_integration_tests
if "%TEST_CATEGORY%"=="all" goto :run_all_tests
goto :show_help

:run_all_tests
echo [%TIME%] Running complete documentation and automation test suite...
echo [%TIME%] Running complete documentation and automation test suite... >> "%LOG_FILE%"

call :run_api_tests
call :run_javadoc_tests
call :run_troubleshooting_tests
call :run_dependency_tests
call :run_rollback_tests
call :run_integration_tests
call :generate_comprehensive_report
goto :end

:run_api_tests
echo.
echo [TEST CATEGORY] API Documentation Tests
echo ======================================
echo [%TIME%] Running API documentation tests...
echo [%TIME%] Running API documentation tests... >> "%LOG_FILE%"

call :run_test_class "com.ascentbusiness.dms_svc.documentation.ApiDocumentationTest"
goto :end_category

:run_javadoc_tests
echo.
echo [TEST CATEGORY] JavaDoc Coverage Tests
echo ======================================
echo [%TIME%] Running JavaDoc coverage tests...
echo [%TIME%] Running JavaDoc coverage tests... >> "%LOG_FILE%"

call :run_test_class "com.ascentbusiness.dms_svc.documentation.JavaDocCoverageTest"
goto :end_category

:run_troubleshooting_tests
echo.
echo [TEST CATEGORY] Troubleshooting Guides Tests
echo ============================================
echo [%TIME%] Running troubleshooting guides tests...
echo [%TIME%] Running troubleshooting guides tests... >> "%LOG_FILE%"

call :run_test_class "com.ascentbusiness.dms_svc.documentation.TroubleshootingGuidesTest"
goto :end_category

:run_dependency_tests
echo.
echo [TEST CATEGORY] Dependency Management Tests
echo ===========================================
echo [%TIME%] Running dependency management tests...
echo [%TIME%] Running dependency management tests... >> "%LOG_FILE%"

call :run_test_class "com.ascentbusiness.dms_svc.automation.DependencyManagementTest"
goto :end_category

:run_rollback_tests
echo.
echo [TEST CATEGORY] Rollback Procedures Tests
echo =========================================
echo [%TIME%] Running rollback procedures tests...
echo [%TIME%] Running rollback procedures tests... >> "%LOG_FILE%"

call :run_test_class "com.ascentbusiness.dms_svc.automation.RollbackProceduresTest"
goto :end_category

:run_integration_tests
echo.
echo [TEST CATEGORY] Integration Tests
echo =================================
echo [%TIME%] Running integration tests...
echo [%TIME%] Running integration tests... >> "%LOG_FILE%"

call :run_test_class "com.ascentbusiness.dms_svc.integration.DocumentationAndAutomationIntegrationTest"
goto :end_category

:run_test_class
set TEST_CLASS=%~1
set /a TOTAL_TEST_CLASSES+=1

echo [%TIME%] Running test class: %TEST_CLASS%
echo [%TIME%] Running test class: %TEST_CLASS% >> "%LOG_FILE%"

REM Run the specific test class
mvn test -Dtest=%TEST_CLASS% -Dspring.profiles.active=test >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% equ 0 (
    echo [PASS] %TEST_CLASS%
    echo [PASS] %TEST_CLASS% >> "%LOG_FILE%"
    set /a PASSED_TEST_CLASSES+=1
) else (
    echo [FAIL] %TEST_CLASS%
    echo [FAIL] %TEST_CLASS% >> "%LOG_FILE%"
    set /a FAILED_TEST_CLASSES+=1
)

exit /b 0

:end_category
exit /b 0

:generate_comprehensive_report
echo [%TIME%] Generating comprehensive test report...
echo [%TIME%] Generating comprehensive test report... >> "%LOG_FILE%"

REM Calculate success rate
if %TOTAL_TEST_CLASSES% gtr 0 (
    set /a SUCCESS_RATE=(%PASSED_TEST_CLASSES% * 100) / %TOTAL_TEST_CLASSES%
) else (
    set SUCCESS_RATE=0
)

REM Create HTML report
(
echo ^<!DOCTYPE html^>
echo ^<html lang="en"^>
echo ^<head^>
echo     ^<meta charset="UTF-8"^>
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
echo     ^<title^>DMS Documentation and Automation Test Report^</title^>
echo     ^<style^>
echo         body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
echo         .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1^); }
echo         .header { text-align: center; margin-bottom: 30px; }
echo         .header h1 { color: #2c3e50; margin-bottom: 10px; }
echo         .header .subtitle { color: #7f8c8d; font-size: 16px; }
echo         .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr^)^); gap: 20px; margin-bottom: 30px; }
echo         .summary-card { background: #ecf0f1; padding: 20px; border-radius: 6px; text-align: center; }
echo         .summary-card h3 { margin: 0 0 10px 0; color: #2c3e50; }
echo         .summary-card .number { font-size: 24px; font-weight: bold; }
echo         .passed { color: #27ae60; }
echo         .failed { color: #e74c3c; }
echo         .success-rate { color: #3498db; }
echo         .section { margin-bottom: 30px; }
echo         .section h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
echo         .test-category { background: #f8f9fa; border-radius: 6px; padding: 15px; margin-bottom: 15px; }
echo         .test-category h3 { margin: 0 0 10px 0; color: #2c3e50; }
echo         .test-status { font-weight: bold; }
echo         .test-status.passed { color: #27ae60; }
echo         .test-status.failed { color: #e74c3c; }
echo         .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ecf0f1; color: #7f8c8d; }
echo     ^</style^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="container"^>
echo         ^<div class="header"^>
echo             ^<h1^>DMS Documentation and Automation Test Report^</h1^>
echo             ^<div class="subtitle"^>Generated on %DATE% at %TIME%^</div^>
echo         ^</div^>
echo         ^<div class="summary"^>
echo             ^<div class="summary-card"^>
echo                 ^<h3^>Total Test Classes^</h3^>
echo                 ^<div class="number"^>%TOTAL_TEST_CLASSES%^</div^>
echo             ^</div^>
echo             ^<div class="summary-card"^>
echo                 ^<h3^>Passed^</h3^>
echo                 ^<div class="number passed"^>%PASSED_TEST_CLASSES%^</div^>
echo             ^</div^>
echo             ^<div class="summary-card"^>
echo                 ^<h3^>Failed^</h3^>
echo                 ^<div class="number failed"^>%FAILED_TEST_CLASSES%^</div^>
echo             ^</div^>
echo             ^<div class="summary-card"^>
echo                 ^<h3^>Success Rate^</h3^>
echo                 ^<div class="number success-rate"^>%SUCCESS_RATE%%%^</div^>
echo             ^</div^>
echo         ^</div^>
echo         ^<div class="section"^>
echo             ^<h2^>Test Categories^</h2^>
echo             ^<div class="test-category"^>
echo                 ^<h3^>API Documentation Tests^</h3^>
echo                 ^<p^>Validates API documentation completeness, accuracy, and live endpoint compatibility.^</p^>
echo                 ^<div class="test-status"^>Status: Executed^</div^>
echo             ^</div^>
echo             ^<div class="test-category"^>
echo                 ^<h3^>JavaDoc Coverage Tests^</h3^>
echo                 ^<p^>Verifies JavaDoc coverage, style compliance, and documentation quality.^</p^>
echo                 ^<div class="test-status"^>Status: Executed^</div^>
echo             ^</div^>
echo             ^<div class="test-category"^>
echo                 ^<h3^>Troubleshooting Guides Tests^</h3^>
echo                 ^<p^>Validates troubleshooting documentation completeness and accuracy.^</p^>
echo                 ^<div class="test-status"^>Status: Executed^</div^>
echo             ^</div^>
echo             ^<div class="test-category"^>
echo                 ^<h3^>Dependency Management Tests^</h3^>
echo                 ^<p^>Tests automated dependency management, security scanning, and update procedures.^</p^>
echo                 ^<div class="test-status"^>Status: Executed^</div^>
echo             ^</div^>
echo             ^<div class="test-category"^>
echo                 ^<h3^>Rollback Procedures Tests^</h3^>
echo                 ^<p^>Verifies rollback script functionality, documentation, and emergency procedures.^</p^>
echo                 ^<div class="test-status"^>Status: Executed^</div^>
echo             ^</div^>
echo             ^<div class="test-category"^>
echo                 ^<h3^>Integration Tests^</h3^>
echo                 ^<p^>Validates integration between all documentation and automation features.^</p^>
echo                 ^<div class="test-status"^>Status: Executed^</div^>
echo             ^</div^>
echo         ^</div^>
echo         ^<div class="section"^>
echo             ^<h2^>Test Results Summary^</h2^>
echo             ^<p^>^<strong^>Overall Status:^</strong^> 
if %FAILED_TEST_CLASSES% equ 0 (
    echo                 ^<span class="test-status passed"^>ALL TESTS PASSED^</span^>^</p^>
) else (
    echo                 ^<span class="test-status failed"^>SOME TESTS FAILED^</span^>^</p^>
)
echo             ^<p^>^<strong^>Success Rate:^</strong^> %SUCCESS_RATE%%%^</p^>
echo             ^<p^>^<strong^>Total Test Classes:^</strong^> %TOTAL_TEST_CLASSES%^</p^>
echo             ^<p^>^<strong^>Passed:^</strong^> %PASSED_TEST_CLASSES%^</p^>
echo             ^<p^>^<strong^>Failed:^</strong^> %FAILED_TEST_CLASSES%^</p^>
echo         ^</div^>
echo         ^<div class="section"^>
echo             ^<h2^>Next Steps^</h2^>
if %FAILED_TEST_CLASSES% equ 0 (
    echo             ^<p^>✅ All documentation and automation tests passed successfully!^</p^>
    echo             ^<ul^>
    echo                 ^<li^>Documentation is complete and accurate^</li^>
    echo                 ^<li^>Automation features are working correctly^</li^>
    echo                 ^<li^>Integration between components is validated^</li^>
    echo                 ^<li^>System is ready for production use^</li^>
    echo             ^</ul^>
) else (
    echo             ^<p^>⚠️ Some tests failed. Please review the following:^</p^>
    echo             ^<ul^>
    echo                 ^<li^>Check the detailed log file: %LOG_FILE%^</li^>
    echo                 ^<li^>Fix any failing test cases^</li^>
    echo                 ^<li^>Update documentation as needed^</li^>
    echo                 ^<li^>Re-run tests to verify fixes^</li^>
    echo             ^</ul^>
)
echo         ^</div^>
echo         ^<div class="footer"^>
echo             ^<p^>Test execution completed on %DATE% at %TIME%^</p^>
echo             ^<p^>Log file: %LOG_FILE%^</p^>
echo             ^<p^>Generated by DMS Documentation and Automation Test Suite^</p^>
echo         ^</div^>
echo     ^</div^>
echo ^</body^>
echo ^</html^>
) > "%REPORT_FILE%"

echo [INFO] Comprehensive test report generated: %REPORT_FILE%
echo [INFO] Comprehensive test report generated: %REPORT_FILE% >> "%LOG_FILE%"

REM Open report in browser
if exist "%REPORT_FILE%" (
    echo [INFO] Opening test report in browser...
    start "" "%REPORT_FILE%"
)

exit /b 0

:show_help
echo.
echo DMS Documentation and Automation Test Suite
echo.
echo Usage: %~nx0 [category]
echo.
echo Test Categories:
echo   api            - Run API documentation tests only
echo   javadoc        - Run JavaDoc coverage tests only
echo   troubleshooting - Run troubleshooting guides tests only
echo   dependency     - Run dependency management tests only
echo   rollback       - Run rollback procedures tests only
echo   integration    - Run integration tests only
echo   all            - Run all test categories (default)
echo.
echo Examples:
echo   %~nx0                    # Run all tests
echo   %~nx0 api                # Run API documentation tests only
echo   %~nx0 integration        # Run integration tests only
echo.
echo Test Results:
echo   HTML Report: %TEST_RESULTS_DIR%\documentation-test-report.html
echo   Log File: %TEST_RESULTS_DIR%\documentation-tests.log
echo.
goto :end

:end
echo.
echo ============================================================================
echo Documentation and Automation Test Suite Summary
echo ============================================================================
echo Total Test Classes: %TOTAL_TEST_CLASSES%
echo Passed: %PASSED_TEST_CLASSES%
echo Failed: %FAILED_TEST_CLASSES%
if defined SUCCESS_RATE echo Success Rate: %SUCCESS_RATE%%%
echo.
echo Test Results: %TEST_RESULTS_DIR%
echo Log File: %LOG_FILE%
if exist "%REPORT_FILE%" echo HTML Report: %REPORT_FILE%
echo ============================================================================

if %FAILED_TEST_CLASSES% gtr 0 (
    echo.
    echo ⚠️  Some tests failed. Please review the log file and fix any issues.
    exit /b 1
) else (
    echo.
    echo ✅ All documentation and automation tests passed successfully!
    exit /b 0
)

endlocal
