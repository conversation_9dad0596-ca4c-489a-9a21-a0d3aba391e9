package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.converter.PIIEncryptionConverter;
import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDateTime;

/**
 * Entity representing ownership and lifecycle metadata for documents.
 * Stores information about document ownership, approval status, expiry dates,
 * and retention policies.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@Entity
@Table(name = "document_ownership_metadata")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentOwnershipMetadata extends BaseEntity {

    /** Primary key for the ownership metadata record */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** The document this ownership metadata is associated with */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;

    /** User/Role/Team responsible for the document (encrypted for PII protection) */
    @Column(name = "owner", length = 500)
    @Convert(converter = PIIEncryptionConverter.class)
    private String owner;

    /** Assigned user/role who approved the document (encrypted for PII protection) */
    @Column(name = "approver", length = 500)
    @Convert(converter = PIIEncryptionConverter.class)
    private String approver;

    /** Current status of the document (e.g., Draft, Under Review, Approved, Archived) */
    @Column(name = "status", length = 50)
    private String status;

    /** Date when the document was approved */
    @Column(name = "approval_date")
    private LocalDateTime approvalDate;

    /** Date when the document becomes obsolete */
    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;

    /** Date to notify for renewal or review */
    @Column(name = "renewal_reminder")
    private LocalDateTime renewalReminder;

    /** How long the document should be stored */
    @Column(name = "retention_period", length = 50)
    private String retentionPeriod;

    /** Boolean flag to mark if the document is archived */
    @Column(name = "archived")
    private Boolean archived;
}
