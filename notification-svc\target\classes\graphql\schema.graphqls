enum NotificationType {
  EMAIL
  IN_APP
}

enum RecipientType {
  TO
  CC
  BCC
}

enum MessageType {
  INFORMATIONAL
  ACTIONABLE
  ALERT
  REMINDER
  PROMOTIONAL
}

enum Importance {
  HIGH
  MEDIUM
  LOW
}

type NotificationTemplate {
  id: ID!
  alias: String!
  subject: String
  body: String
}

type Notification {
  id: ID!
  senderId: String!
  content: String
  templateName: String
  type: NotificationType!
  messageType: MessageType
  importance: Importance
  recipients: [NotificationRecipient!]
}

type NotificationRecipient {
  id: ID!
  notification: Notification!
  recipientId: String!
  isRead: Boolean!
  delivered: Boolean!
}

type InAppNotificationMessage {
  notificationId: ID!
  senderId: String!
  recipientId: String!
  content: String!
  type: NotificationType!
  timestamp: String!
  variables: String
  isRead: Boolean!
  delivered: Boolean!
}

input AttachmentInput {
  filename: String!
  contentType: String!
  content: String!
  description: String
  filePath: String
}

input RecipientInput {
  email: String!
  variables: [VariableInput!]
  attachments: [AttachmentInput!]
  type: RecipientType
}

input SendNotificationInput {
  sender: String!
  type: NotificationType!
  content: String
  templateName: String
  messageType: MessageType
  importance: Importance
  recipients: [String!] # Deprecated: Use recipientDetails for recipient-specific variables, attachments, and CC/BCC
  recipientDetails: [RecipientInput!] # New: Detailed recipients with optional variables, attachments, and CC/BCC
  variables: [VariableInput!] # Global variables applied to all recipients
  commonAttachments: [AttachmentInput!] # Common attachments sent to all recipients
}

input VariableInput {
  key: String!
  value: String!
}

input NotificationTemplateInput {
  alias: String!
  subject: String!
  body: String!
}

type Mutation {
  addUpdateNotificationTemplate(input: NotificationTemplateInput!): NotificationTemplate!
  sendNotification(input: SendNotificationInput!): Boolean!
  markNotificationAsRead(userId: String!): Boolean!
  markNotificationAsReadById(notificationId: ID!): Boolean!
  consumeInAppNotifications(userId: String!): [InAppNotificationMessage!]!
}

type Query {
  getNotificationsForUser(userId: String!): [NotificationRecipient!]
  getTemplate(templateName: String!): NotificationTemplate!
  getAllTemplates: [NotificationTemplate!]!
  getUnreadInAppNotifications(userId: String!): [InAppNotificationMessage!]!
  pollInAppNotifications(userId: String!, limit: Int): [InAppNotificationMessage!]!
}

type AuditLog {
  id: ID!
  timestamp: String!
  correlationId: String
  requestId: String
  userId: String
  sourceService: String
  action: String!
  result: String!
  entityType: String
  entityId: String
  details: String
  additionalData: String
}

input AuditLogFilter {
  startTime: String
  endTime: String
  userId: String
  action: String
  result: String
  entityType: String
  entityId: String
}

type AuditLogPage {
  content: [AuditLog!]!
  totalElements: Int!
  totalPages: Int!
  pageNumber: Int!
  pageSize: Int!
}

extend type Query {
  getAuditLogs(filter: AuditLogFilter, page: Int, size: Int): AuditLogPage!
  getAuditLogById(id: ID!): AuditLog
  getAuditLogsByCorrelationId(correlationId: String!): [AuditLog!]!
}
