# Elasticsearch Test Configuration
# This configuration is used for testing Elasticsearch functionality

# Application Configuration
spring.application.name=dms-svc-elasticsearch-test
server.port=0

# H2 In-Memory Database for Testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for Testing
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Disable Liquibase for tests
spring.liquibase.enabled=false

# Elasticsearch Configuration (will be overridden by TestContainers)
elasticsearch.enabled=true
elasticsearch.host=localhost
elasticsearch.port=9200
elasticsearch.protocol=http
elasticsearch.username=
elasticsearch.password=
elasticsearch.connection-timeout=5000
elasticsearch.socket-timeout=10000

# GraphQL Configuration
spring.graphql.graphiql.enabled=false
spring.graphql.schema.printer.enabled=false
spring.graphql.schema.locations=classpath:graphql/

# File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# JWT Configuration for Testing
dms.jwt.secret=testSecretKey123456789012345678901234567890
dms.jwt.expiration=86400000
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer 

# Disable Redis for tests
spring.cache.type=simple
management.health.redis.enabled=false

# Logging Configuration for Tests
logging.level.com.ascentbusiness.dms_svc=DEBUG
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.elasticsearch=INFO
logging.level.org.testcontainers=INFO
logging.pattern.console=%d{HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Correlation ID Configuration
dms.correlation.header-name=X-Correlation-ID

# Test-specific Storage Configuration
dms.storage.provider=LOCAL
dms.storage.local.base-path=./test-storage/documents
dms.storage.initialize-default-configuration=false

# Disable Actuator endpoints for tests
management.endpoints.web.exposure.include=health
management.endpoint.health.show-details=never
