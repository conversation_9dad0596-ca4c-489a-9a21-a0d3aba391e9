package com.ascentbusiness.dms_svc.aspect;

import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * Aspect to ensure correlation ID is properly propagated through service method calls.
 * This aspect intercepts service method calls and ensures the MDC context contains
 * the correlation ID for proper logging and traceability.
 */
@Aspect
@Component
@Order(1) // Execute early to ensure correlation ID is available for other aspects
public class CorrelationIdAspect {

    private static final Logger logger = LoggerFactory.getLogger(CorrelationIdAspect.class);
    private static final String CORRELATION_ID_KEY = "correlationId";
    private static final String CORRELATION_HEADER = "X-Correlation-ID";

    /**
     * Intercept all service method calls to ensure correlation ID is available in MDC
     */
    @Around("execution(* com.ascentbusiness.dms_svc.service..*(..))")
    public Object ensureCorrelationIdInServices(ProceedingJoinPoint joinPoint) throws Throwable {
        return ensureCorrelationIdContext(joinPoint, "SERVICE");
    }

    /**
     * Intercept all resolver method calls to ensure correlation ID is available in MDC
     */
    @Around("execution(* com.ascentbusiness.dms_svc.resolver..*(..))")
    public Object ensureCorrelationIdInResolvers(ProceedingJoinPoint joinPoint) throws Throwable {
        return ensureCorrelationIdContext(joinPoint, "RESOLVER");
    }

    /**
     * Intercept all controller method calls to ensure correlation ID is available in MDC
     */
    @Around("execution(* com.ascentbusiness.dms_svc.controller..*(..))")
    public Object ensureCorrelationIdInControllers(ProceedingJoinPoint joinPoint) throws Throwable {
        return ensureCorrelationIdContext(joinPoint, "CONTROLLER");
    }

    /**
     * Core method to ensure correlation ID context is available
     */
    private Object ensureCorrelationIdContext(ProceedingJoinPoint joinPoint, String layer) throws Throwable {
        // Capture current MDC state
        Map<String, String> originalMdcContext = MDC.getCopyOfContextMap();
        String originalCorrelationId = MDC.get(CORRELATION_ID_KEY);
        
        boolean mdcWasSet = false;
        String correlationId = null;

        try {
            // Check if correlation ID is already in MDC
            correlationId = originalCorrelationId;
            
            // If not in MDC, try to get it from various sources
            if (correlationId == null || correlationId.trim().isEmpty()) {
                // Try CorrelationIdUtil first
                correlationId = CorrelationIdUtil.getCurrentCorrelationId();
                
                // If still not found, try to get from request header directly
                if (correlationId == null || correlationId.trim().isEmpty()) {
                    correlationId = getCorrelationIdFromRequest();
                }
                
                // If we found a correlation ID, set it in MDC
                if (correlationId != null && !correlationId.trim().isEmpty()) {
                    MDC.put(CORRELATION_ID_KEY, correlationId);
                    mdcWasSet = true;
                    
                    logger.trace("CorrelationIdAspect: Set correlation ID {} in MDC for {}.{} in layer {}",
                            correlationId, joinPoint.getTarget().getClass().getSimpleName(), 
                            joinPoint.getSignature().getName(), layer);
                }
            } else {
                logger.trace("CorrelationIdAspect: Correlation ID {} already in MDC for {}.{} in layer {}",
                        correlationId, joinPoint.getTarget().getClass().getSimpleName(), 
                        joinPoint.getSignature().getName(), layer);
            }

            // Proceed with the method execution
            return joinPoint.proceed();

        } catch (Exception e) {
            logger.error("CorrelationIdAspect: Error in {} layer for {}.{} with correlation ID {}: {}",
                    layer, joinPoint.getTarget().getClass().getSimpleName(), 
                    joinPoint.getSignature().getName(), correlationId, e.getMessage(), e);
            throw e;
        } finally {
            // Restore original MDC state if we modified it
            if (mdcWasSet && originalCorrelationId == null) {
                // We added the correlation ID, but it wasn't there originally
                // Keep it in MDC for downstream calls but log that we're maintaining it
                logger.trace("CorrelationIdAspect: Maintaining correlation ID {} in MDC after {}.{} in layer {}",
                        correlationId, joinPoint.getTarget().getClass().getSimpleName(), 
                        joinPoint.getSignature().getName(), layer);
            } else if (originalMdcContext != null) {
                // Restore the original MDC context
                MDC.setContextMap(originalMdcContext);
            } else if (originalMdcContext == null && mdcWasSet) {
                // Original MDC was empty, but we want to keep the correlation ID
                MDC.clear();
                if (correlationId != null && !correlationId.trim().isEmpty()) {
                    MDC.put(CORRELATION_ID_KEY, correlationId);
                }
            }
        }
    }

    /**
     * Get correlation ID from the current HTTP request header
     */
    private String getCorrelationIdFromRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String correlationId = request.getHeader(CORRELATION_HEADER);
                if (correlationId != null && !correlationId.trim().isEmpty()) {
                    return correlationId.trim();
                }
            }
        } catch (Exception e) {
            logger.trace("CorrelationIdAspect: Could not get correlation ID from request: {}", e.getMessage());
        }
        return null;
    }
}
