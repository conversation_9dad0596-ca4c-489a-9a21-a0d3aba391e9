package com.ascentbusiness.dms_svc.dto;

import lombok.Data;

import java.time.OffsetDateTime;

/**
 * Response DTO for audit verification operations
 * Used in GraphQL audit verification mutations
 */
@Data
public class AuditVerificationResponse {
    private String verificationId;
    private String auditLogId;
    private String verificationStatus;
    private String verifiedBy;
    private OffsetDateTime verifiedAt;
    private Boolean isValid;
    private Boolean tamperingDetected;
    private String message;
    private String errorDetails;
}
