#!/bin/bash
# Deployment Script for DMS Service
# This script handles deployment to different environments with blue-green strategy

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/tmp/dms-deploy-$(date +%Y%m%d-%H%M%S).log"

# Default values
ENVIRONMENT=""
IMAGE_TAG=""
NAMESPACE=""
DRY_RUN=false
ROLLBACK=false
SKIP_TESTS=false
FORCE_DEPLOY=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 -e ENVIRONMENT -t IMAGE_TAG [OPTIONS]

Deploy DMS Service to Kubernetes cluster

Required Arguments:
    -e, --environment   Target environment (dev, staging, prod)
    -t, --tag          Docker image tag to deploy

Optional Arguments:
    -n, --namespace    Kubernetes namespace (default: dms-ENVIRONMENT)
    -d, --dry-run      Perform a dry run without making changes
    -r, --rollback     Rollback to previous deployment
    -s, --skip-tests   Skip pre-deployment tests
    -f, --force        Force deployment even if health checks fail
    -h, --help         Show this help message

Examples:
    $0 -e dev -t v1.2.3
    $0 -e prod -t v1.2.3 --dry-run
    $0 -e staging -t v1.2.3 --skip-tests
    $0 -e prod --rollback

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -r|--rollback)
                ROLLBACK=true
                shift
                ;;
            -s|--skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            -f|--force)
                FORCE_DEPLOY=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$ENVIRONMENT" ]]; then
        log_error "Environment is required"
        usage
        exit 1
    fi

    if [[ ! "$ROLLBACK" == "true" && -z "$IMAGE_TAG" ]]; then
        log_error "Image tag is required for deployment"
        usage
        exit 1
    fi

    # Set default namespace if not provided
    if [[ -z "$NAMESPACE" ]]; then
        case "$ENVIRONMENT" in
            dev)
                NAMESPACE="dms-dev"
                ;;
            staging)
                NAMESPACE="dms-staging"
                ;;
            prod)
                NAMESPACE="dms-system"
                ;;
            *)
                NAMESPACE="dms-$ENVIRONMENT"
                ;;
        esac
    fi

    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
        log_error "Invalid environment: $ENVIRONMENT. Must be dev, staging, or prod"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check if kubectl is installed and configured
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi

    # Check if we can connect to the cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    # Check if namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "Namespace $NAMESPACE does not exist. Creating it..."
        if [[ "$DRY_RUN" == "false" ]]; then
            kubectl create namespace "$NAMESPACE"
        fi
    fi

    log_success "Prerequisites check completed"
}

# Run pre-deployment tests
run_pre_deployment_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_warning "Skipping pre-deployment tests"
        return 0
    fi

    log_info "Running pre-deployment tests..."

    # Check if the image exists and is accessible
    if [[ -n "$IMAGE_TAG" ]]; then
        log_info "Verifying Docker image: dms-service:$IMAGE_TAG"
        # This would typically involve checking the registry
        # docker manifest inspect "dms-service:$IMAGE_TAG" &> /dev/null || {
        #     log_error "Docker image dms-service:$IMAGE_TAG not found"
        #     exit 1
        # }
    fi

    # Run configuration validation
    log_info "Validating Kubernetes manifests..."
    kubectl apply --dry-run=client -f "$PROJECT_ROOT/k8s/" -n "$NAMESPACE" &> /dev/null || {
        log_error "Kubernetes manifest validation failed"
        exit 1
    }

    log_success "Pre-deployment tests completed"
}

# Backup current deployment
backup_current_deployment() {
    log_info "Backing up current deployment..."

    local backup_dir="/tmp/dms-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"

    # Backup current deployment configuration
    kubectl get deployment dms-deployment -n "$NAMESPACE" -o yaml > "$backup_dir/deployment.yaml" 2>/dev/null || true
    kubectl get service dms-service -n "$NAMESPACE" -o yaml > "$backup_dir/service.yaml" 2>/dev/null || true
    kubectl get configmap dms-config -n "$NAMESPACE" -o yaml > "$backup_dir/configmap.yaml" 2>/dev/null || true

    echo "$backup_dir" > "/tmp/dms-last-backup-$ENVIRONMENT"
    log_success "Backup created at: $backup_dir"
}

# Deploy application
deploy_application() {
    log_info "Starting deployment to $ENVIRONMENT environment..."

    # Update image tag in deployment manifest
    local temp_manifest="/tmp/dms-deployment-$ENVIRONMENT.yaml"
    cp "$PROJECT_ROOT/k8s/deployment.yaml" "$temp_manifest"
    
    if [[ -n "$IMAGE_TAG" ]]; then
        sed -i "s|image: dms-service:latest|image: dms-service:$IMAGE_TAG|g" "$temp_manifest"
        log_info "Updated image tag to: dms-service:$IMAGE_TAG"
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: Would apply the following manifests:"
        kubectl apply --dry-run=client -f "$PROJECT_ROOT/k8s/" -n "$NAMESPACE"
        return 0
    fi

    # Apply Kubernetes manifests
    log_info "Applying Kubernetes manifests..."
    kubectl apply -f "$PROJECT_ROOT/k8s/namespace.yaml" || true
    kubectl apply -f "$PROJECT_ROOT/k8s/configmap.yaml" -n "$NAMESPACE"
    kubectl apply -f "$PROJECT_ROOT/k8s/secrets.yaml" -n "$NAMESPACE"
    kubectl apply -f "$PROJECT_ROOT/k8s/persistent-volumes.yaml" -n "$NAMESPACE"
    kubectl apply -f "$PROJECT_ROOT/k8s/rbac.yaml" -n "$NAMESPACE"
    kubectl apply -f "$PROJECT_ROOT/k8s/services.yaml" -n "$NAMESPACE"
    kubectl apply -f "$temp_manifest" -n "$NAMESPACE"
    kubectl apply -f "$PROJECT_ROOT/k8s/ingress.yaml" -n "$NAMESPACE"

    # Wait for rollout to complete
    log_info "Waiting for deployment rollout..."
    kubectl rollout status deployment/dms-deployment -n "$NAMESPACE" --timeout=600s

    # Clean up temporary file
    rm -f "$temp_manifest"

    log_success "Deployment completed successfully"
}

# Perform health checks
perform_health_checks() {
    log_info "Performing health checks..."

    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts"

        # Check if pods are ready
        local ready_pods=$(kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=dms --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l)
        local total_pods=$(kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=dms --no-headers 2>/dev/null | wc -l)

        if [[ $ready_pods -gt 0 && $ready_pods -eq $total_pods ]]; then
            # Test application health endpoint
            if kubectl run health-check-$RANDOM --image=curlimages/curl --rm -i --restart=Never -n "$NAMESPACE" -- \
                curl -f "http://dms-service.$NAMESPACE.svc.cluster.local:8080/dms/actuator/health" &> /dev/null; then
                log_success "Health checks passed"
                return 0
            fi
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            if [[ "$FORCE_DEPLOY" == "true" ]]; then
                log_warning "Health checks failed but continuing due to --force flag"
                return 0
            else
                log_error "Health checks failed after $max_attempts attempts"
                return 1
            fi
        fi

        sleep 10
        ((attempt++))
    done
}

# Rollback deployment
rollback_deployment() {
    log_info "Rolling back deployment..."

    if [[ ! -f "/tmp/dms-last-backup-$ENVIRONMENT" ]]; then
        log_error "No backup found for rollback"
        exit 1
    fi

    local backup_dir=$(cat "/tmp/dms-last-backup-$ENVIRONMENT")
    
    if [[ ! -d "$backup_dir" ]]; then
        log_error "Backup directory not found: $backup_dir"
        exit 1
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: Would rollback using backup from: $backup_dir"
        return 0
    fi

    # Restore from backup
    kubectl apply -f "$backup_dir/" -n "$NAMESPACE" || {
        log_error "Rollback failed"
        exit 1
    }

    # Wait for rollback to complete
    kubectl rollout status deployment/dms-deployment -n "$NAMESPACE" --timeout=300s

    log_success "Rollback completed successfully"
}

# Post-deployment tasks
post_deployment_tasks() {
    log_info "Running post-deployment tasks..."

    # Update deployment annotations
    kubectl annotate deployment dms-deployment -n "$NAMESPACE" \
        deployment.kubernetes.io/revision- \
        deployment.dms/deployed-at="$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
        deployment.dms/deployed-by="$(whoami)" \
        deployment.dms/image-tag="$IMAGE_TAG" \
        --overwrite

    # Display deployment status
    log_info "Deployment Status:"
    kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=dms
    kubectl get services -n "$NAMESPACE" -l app.kubernetes.io/name=dms

    log_success "Post-deployment tasks completed"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    # Add any cleanup tasks here
}

# Main function
main() {
    log_info "Starting DMS deployment script..."
    log_info "Log file: $LOG_FILE"

    parse_args "$@"

    log_info "Deployment Configuration:"
    log_info "  Environment: $ENVIRONMENT"
    log_info "  Namespace: $NAMESPACE"
    log_info "  Image Tag: ${IMAGE_TAG:-'N/A (rollback)'}"
    log_info "  Dry Run: $DRY_RUN"
    log_info "  Rollback: $ROLLBACK"
    log_info "  Skip Tests: $SKIP_TESTS"
    log_info "  Force Deploy: $FORCE_DEPLOY"

    # Set trap for cleanup
    trap cleanup EXIT

    check_prerequisites

    if [[ "$ROLLBACK" == "true" ]]; then
        rollback_deployment
    else
        run_pre_deployment_tests
        backup_current_deployment
        deploy_application
        perform_health_checks
        post_deployment_tasks
    fi

    log_success "Deployment script completed successfully!"
}

# Run main function with all arguments
main "$@"
