package com.ascentbusiness.dms_svc.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TestCaseResponse {
    private String serialNumber;
    private String category;
    private String jwtRequest;
    private String jwtToken;
    private String request;
    private String response;
    private String result;
    private String comment;
    private String description;

    public TestCaseResponse() {}

    public TestCaseResponse(String serialNumber, String category, String jwtRequest, 
                           String jwtToken, String request, String response, 
                           String result, String comment) {
        this.serialNumber = serialNumber;
        this.category = category;
        this.jwtRequest = jwtRequest;
        this.jwtToken = jwtToken;
        this.request = request;
        this.response = response;
        this.result = result;
        this.comment = comment;
        this.description = comment; // Use comment as description
    }

    // Getters and Setters
    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getJwtRequest() {
        return jwtRequest;
    }

    public void setJwtRequest(String jwtRequest) {
        this.jwtRequest = jwtRequest;
    }

    public String getJwtToken() {
        return jwtToken;
    }

    public void setJwtToken(String jwtToken) {
        this.jwtToken = jwtToken;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
