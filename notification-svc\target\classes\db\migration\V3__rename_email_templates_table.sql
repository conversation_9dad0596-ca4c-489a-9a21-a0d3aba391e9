-- Rename email_templates table to notification_templates if it exists
-- This handles the case where the old table name exists

-- Check if email_templates table exists and rename it
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'email_templates');

SET @sql = IF(@table_exists > 0, 
              'RENAME TABLE email_templates TO notification_templates', 
              'SELECT "Table email_templates does not exist, skipping rename" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt; 