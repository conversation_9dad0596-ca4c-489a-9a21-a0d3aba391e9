package com.ascentbusiness.dms_svc.dto;

import java.util.List;

public class JwtTokenRequest {
    private String username;
    private List<String> roles;
    private List<String> permissions;

    public JwtTokenRequest() {}

    public JwtTokenRequest(String username, List<String> roles, List<String> permissions) {
        this.username = username;
        this.roles = roles;
        this.permissions = permissions;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public List<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<String> permissions) {
        this.permissions = permissions;
    }
}
