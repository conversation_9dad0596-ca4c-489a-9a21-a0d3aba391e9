-- MySQL Initialization Script for Notification Service Only
-- This script creates database and user for Notification service

-- Create database
CREATE DATABASE IF NOT EXISTS `notification_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user and grant permissions
CREATE USER IF NOT EXISTS 'notification_user'@'%' IDENTIFIED BY 'notification_password';
GRANT ALL PRIVILEGES ON `notification_db`.* TO 'notification_user'@'%';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Display created database
SHOW DATABASES;

-- Display user
SELECT User, Host FROM mysql.user WHERE User = 'notification_user';
