package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Input DTO for document permission operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentPermissionInput {

    private Long documentId;
    private String userId;
    private String roleName;
    private Permission permissionType;
    private LocalDateTime expiresAt;
    private String notes;

    public void validate() {
        validateForCreate();
    }

    public void validateForCreate() {
        if (documentId == null) {
            throw new IllegalArgumentException("Document ID is required");
        }

        if (userId == null && roleName == null) {
            throw new IllegalArgumentException("Either userId or roleName must be provided");
        }

        if (userId != null && roleName != null) {
            throw new IllegalArgumentException("Cannot specify both userId and roleName");
        }

        if (permissionType == null) {
            throw new IllegalArgumentException("Permission type is required");
        }
    }

    public void validateForUpdate() {
        if (documentId == null) {
            throw new IllegalArgumentException("Document ID is required");
        }

        // For updates, userId and roleName are optional since we're updating by permission ID
        if (userId != null && roleName != null) {
            throw new IllegalArgumentException("Cannot specify both userId and roleName");
        }

        if (permissionType == null) {
            throw new IllegalArgumentException("Permission type is required");
        }
    }
}
