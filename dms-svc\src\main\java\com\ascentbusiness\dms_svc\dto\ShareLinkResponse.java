package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.DocumentShareLink;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for document share link operations.
 * 
 * This DTO provides a standardized response format for share link operations
 * including success/failure status, descriptive messages, and the share link data.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShareLinkResponse {
    
    /** Whether the operation was successful */
    private Boolean success;
    
    /** Descriptive message about the operation result */
    private String message;
    
    /** The share link data (if operation was successful) */
    private DocumentShareLink shareLink;
    
    /**
     * Create a successful response with share link data
     */
    public static ShareLinkResponse success(String message, DocumentShareLink shareLink) {
        return ShareLinkResponse.builder()
                .success(true)
                .message(message)
                .shareLink(shareLink)
                .build();
    }
    
    /**
     * Create a successful response without share link data
     */
    public static ShareLinkResponse success(String message) {
        return ShareLinkResponse.builder()
                .success(true)
                .message(message)
                .build();
    }
    
    /**
     * Create an error response
     */
    public static ShareLinkResponse error(String message) {
        return ShareLinkResponse.builder()
                .success(false)
                .message(message)
                .build();
    }
    
    /**
     * Create a successful response for share link creation
     */
    public static ShareLinkResponse created(DocumentShareLink shareLink) {
        return success("Share link created successfully", shareLink);
    }
    
    /**
     * Create a successful response for share link revocation
     */
    public static ShareLinkResponse revoked() {
        return success("Share link revoked successfully");
    }
    
    /**
     * Create an error response for unauthorized access
     */
    public static ShareLinkResponse unauthorized(String message) {
        return error("Unauthorized: " + message);
    }
    
    /**
     * Create an error response for not found
     */
    public static ShareLinkResponse notFound(String linkId) {
        return error("Share link not found: " + linkId);
    }
    
    /**
     * Create an error response for invalid link
     */
    public static ShareLinkResponse invalid(String reason) {
        return error("Invalid share link: " + reason);
    }
}
