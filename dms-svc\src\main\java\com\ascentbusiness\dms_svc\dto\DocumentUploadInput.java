package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class DocumentUploadInput {
    private String name;
    private String description;
    private StorageProvider storageProvider;    // Optional - defaults to application.properties setting
    private MultipartFile file;
    private List<String> tags;                  // Optional in GraphQL (keywords)
    private Boolean overrideFile = false;       // NEW - duplicate control (default: false)
    private String comment;                     // NEW - optional comment for audit trail

    // Metadata fields (optional)
    private DocumentClassificationMetadataInput classificationMetadata;
    private DocumentOwnershipMetadataInput ownershipMetadata;
    private DocumentComplianceMetadataInput complianceMetadata;
}
