# Prometheus Configuration for DMS Service Only
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # DMS Service Metrics
  - job_name: 'dms-svc'
    static_configs:
      - targets: ['dms-svc:9464']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # MySQL Metrics
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql:3306']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Elasticsearch Metrics
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']
    metrics_path: '/_prometheus/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
