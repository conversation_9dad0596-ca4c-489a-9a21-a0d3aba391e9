package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.WebhookAuthType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Entity representing a webhook endpoint configuration
 */
@Entity
@Table(name = "webhook_endpoints")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WebhookEndpoint extends BaseEntity {

    @Column(name = "name", nullable = false, length = 255)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "url", nullable = false, length = 2000)
    private String url;

    // Webhook configuration
    @Column(name = "http_method", nullable = false, length = 10)
    @Builder.Default
    private String httpMethod = "POST";

    @Column(name = "content_type", nullable = false, length = 100)
    @Builder.Default
    private String contentType = "application/json";

    @Column(name = "timeout_seconds", nullable = false)
    @Builder.Default
    private Integer timeoutSeconds = 30;

    // Authentication
    @Enumerated(EnumType.STRING)
    @Column(name = "auth_type", length = 50)
    private WebhookAuthType authType;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "auth_config", columnDefinition = "JSON")
    private JsonNode authConfig;

    // Headers and payload
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "custom_headers", columnDefinition = "JSON")
    private JsonNode customHeaders;

    @Column(name = "payload_template", columnDefinition = "TEXT")
    private String payloadTemplate;

    // Event filtering
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_types", columnDefinition = "JSON")
    private JsonNode eventTypes;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_filters", columnDefinition = "JSON")
    private JsonNode eventFilters;

    // Status and configuration
    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Builder.Default
    @Column(name = "is_verified", nullable = false)
    private Boolean isVerified = false;

    @Column(name = "verification_token", length = 255)
    private String verificationToken;

    @Column(name = "secret_key", length = 255)
    private String secretKey;

    // Retry configuration
    @Column(name = "max_retries", nullable = false)
    @Builder.Default
    private Integer maxRetries = 3;

    @Column(name = "retry_delay_seconds", nullable = false)
    @Builder.Default
    private Integer retryDelaySeconds = 60;

    @Builder.Default
    @Column(name = "exponential_backoff", nullable = false)
    private Boolean exponentialBackoff = true;

    // Rate limiting
    @Column(name = "rate_limit_per_minute")
    @Builder.Default
    private Integer rateLimitPerMinute = 60;

    @Column(name = "rate_limit_per_hour")
    @Builder.Default
    private Integer rateLimitPerHour = 1000;

    // Monitoring
    @Column(name = "success_count")
    @Builder.Default
    private Integer successCount = 0;

    @Column(name = "failure_count")
    @Builder.Default
    private Integer failureCount = 0;

    @Column(name = "last_success_date")
    private LocalDateTime lastSuccessDate;

    @Column(name = "last_failure_date")
    private LocalDateTime lastFailureDate;

    @Column(name = "last_failure_reason", columnDefinition = "TEXT")
    private String lastFailureReason;

    // Relationships
    @OneToMany(mappedBy = "webhookEndpoint", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WebhookDelivery> deliveries;

    /**
     * Check if this webhook endpoint is currently active
     */
    @Transient
    public boolean isCurrentlyActive() {
        return isActive != null && isActive;
    }

    /**
     * Check if this webhook endpoint is verified
     */
    @Transient
    public boolean isEndpointVerified() {
        return isVerified != null && isVerified;
    }

    /**
     * Check if this webhook has authentication configured
     */
    @Transient
    public boolean hasAuthentication() {
        return authType != null && !WebhookAuthType.NONE.equals(authType);
    }

    /**
     * Check if this webhook has event filters
     */
    @Transient
    public boolean hasEventFilters() {
        return eventFilters != null && !eventFilters.isNull() && eventFilters.size() > 0;
    }

    /**
     * Check if this webhook has custom headers
     */
    @Transient
    public boolean hasCustomHeaders() {
        return customHeaders != null && !customHeaders.isNull() && customHeaders.size() > 0;
    }

    /**
     * Get success rate as percentage
     */
    @Transient
    public double getSuccessRate() {
        int total = getTotalDeliveries();
        if (total == 0) return 0.0;
        return (double) successCount / total * 100.0;
    }

    /**
     * Get total number of deliveries
     */
    @Transient
    public int getTotalDeliveries() {
        return (successCount != null ? successCount : 0) + (failureCount != null ? failureCount : 0);
    }

    /**
     * Check if webhook is healthy (success rate > 90%)
     */
    @Transient
    public boolean isHealthy() {
        return getSuccessRate() > 90.0 && getTotalDeliveries() > 10;
    }

    /**
     * Check if webhook has recent failures
     */
    @Transient
    public boolean hasRecentFailures() {
        return lastFailureDate != null && 
               lastFailureDate.isAfter(LocalDateTime.now().minusHours(24));
    }

    /**
     * Record successful delivery
     */
    public void recordSuccess() {
        this.successCount = (this.successCount != null ? this.successCount : 0) + 1;
        this.lastSuccessDate = LocalDateTime.now();
    }

    /**
     * Record failed delivery
     */
    public void recordFailure(String reason) {
        this.failureCount = (this.failureCount != null ? this.failureCount : 0) + 1;
        this.lastFailureDate = LocalDateTime.now();
        this.lastFailureReason = reason;
    }

    /**
     * Generate verification token
     */
    public void generateVerificationToken() {
        this.verificationToken = java.util.UUID.randomUUID().toString();
    }

    /**
     * Generate secret key for signature verification
     */
    public void generateSecretKey() {
        this.secretKey = java.util.UUID.randomUUID().toString().replace("-", "");
    }

    @Override
    public String toString() {
        return String.format("WebhookEndpoint{id=%d, name='%s', url='%s', active=%s, verified=%s}", 
                           getId(), name, url, isActive, isVerified);
    }
}
