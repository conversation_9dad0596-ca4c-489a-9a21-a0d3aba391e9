package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.exception.InvalidTokenException;
import com.ascentbusiness.dms_svc.security.JwtTokenProvider;
import com.ascentbusiness.dms_svc.security.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.server.WebGraphQlInterceptor;
import org.springframework.graphql.server.WebGraphQlRequest;
import org.springframework.graphql.server.WebGraphQlResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;
import java.util.List;
import java.util.Map;

/**
 * WebGraphQL interceptor that handles JWT authentication for GraphQL requests.
 * This interceptor extracts JWT tokens from the Authorization header and sets up
 * the SecurityContext for GraphQL resolvers.
 */
@Component
public class GraphQLSecurityInterceptor implements WebGraphQlInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(GraphQLSecurityInterceptor.class);

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Override
    public Mono<WebGraphQlResponse> intercept(WebGraphQlRequest request, Chain chain) {
        // Capture the current MDC context to preserve correlation ID and tracing info
        Map<String, String> mdcContext = MDC.getCopyOfContextMap();

        // Set MDC context immediately to ensure it's available for logging
        if (mdcContext != null && !mdcContext.isEmpty()) {
            MDC.setContextMap(mdcContext);
        }

        // Extract correlation ID directly from request headers for logging
        String correlationId = request.getHeaders().getFirst("X-Correlation-ID");
        if (correlationId != null && !correlationId.isEmpty()) {
            MDC.put("correlationId", correlationId);
            logger.info("GraphQL Security Interceptor - Processing request with correlationId: {}", correlationId);
        } else {
            logger.info("GraphQL Security Interceptor - Processing request");
        }

        try {
            // Check if this is a schema introspection query - allow without authentication
            if (isSchemaIntrospectionRequest(request)) {
                logger.info("GraphQL Security Interceptor - Schema introspection query detected, allowing without authentication");
                // Set a system authentication context for introspection
                UserPrincipal introspectionPrincipal = new UserPrincipal("graphql-introspection", List.of("SYSTEM"), List.of("INTROSPECT"));
                UsernamePasswordAuthenticationToken introspectionAuth =
                        new UsernamePasswordAuthenticationToken(introspectionPrincipal, null, introspectionPrincipal.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(introspectionAuth);
                return preserveMDCContext(chain.next(request), mdcContext);
            }

            // Check if this is a generateTestToken mutation - allow without authentication
            if (isGenerateTestTokenRequest(request)) {
                logger.info("GraphQL Security Interceptor - generateTestToken mutation detected, allowing without authentication");
                // Set a special authentication context for generateTestToken
                UserPrincipal testTokenPrincipal = new UserPrincipal("test-token-generator", List.of("SYSTEM"), List.of("GENERATE_TOKEN"));
                UsernamePasswordAuthenticationToken testAuth =
                        new UsernamePasswordAuthenticationToken(testTokenPrincipal, null, testTokenPrincipal.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(testAuth);
                return preserveMDCContext(chain.next(request), mdcContext);
            }

            String jwt = getJwtFromRequest(request);
            logger.info("GraphQL Security Interceptor - JWT present: {}", StringUtils.hasText(jwt));

            if (StringUtils.hasText(jwt)) {
                try {
                    if (tokenProvider.validateToken(jwt)) {
                        logger.info("GraphQL Security Interceptor - Valid JWT token found");

                        String username = tokenProvider.getUsernameFromToken(jwt);
                        List<String> roles = tokenProvider.getRolesFromToken(jwt);
                        List<String> permissions = tokenProvider.getPermissionsFromToken(jwt);

                        logger.info("GraphQL Security Interceptor - Setting authentication for user: {}", username);

                        // Create UserPrincipal with roles and permissions
                        UserPrincipal userPrincipal = new UserPrincipal(username, roles, permissions);

                        // Create authentication token
                        UsernamePasswordAuthenticationToken authentication =
                                new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());

                        // Set authentication in SecurityContext
                        SecurityContextHolder.getContext().setAuthentication(authentication);

                        logger.info("GraphQL Security Interceptor - Authentication set successfully for user: {}", username);
                    }
                } catch (InvalidTokenException ex) {
                    // JWT validation failed - this is expected behavior for invalid tokens
                    // Log at debug level only, not as an error
                    logger.debug("GraphQL Security Interceptor - JWT token validation failed: {}", ex.getMessage());

                    // Clear context and set anonymous authentication
                    SecurityContextHolder.clearContext();
                    UsernamePasswordAuthenticationToken anonymousAuth =
                        new UsernamePasswordAuthenticationToken("anonymousUser", null, List.of());
                    SecurityContextHolder.getContext().setAuthentication(anonymousAuth);
                }
            } else {
                logger.info("GraphQL Security Interceptor - No JWT token found");
                SecurityContextHolder.clearContext();

                // Set anonymous authentication to trigger proper authentication checks in resolvers
                UsernamePasswordAuthenticationToken anonymousAuth =
                    new UsernamePasswordAuthenticationToken("anonymousUser", null, List.of());
                SecurityContextHolder.getContext().setAuthentication(anonymousAuth);
            }
        } catch (Exception ex) {
            logger.error("GraphQL Security Interceptor - Unexpected error processing request", ex);
            SecurityContextHolder.clearContext();
        }

        return preserveMDCContext(chain.next(request), mdcContext);
    }

    /**
     * Preserve MDC context throughout the reactive chain to ensure correlation ID
     * and tracing information is available in downstream processing
     */
    private Mono<WebGraphQlResponse> preserveMDCContext(Mono<WebGraphQlResponse> responseMono, Map<String, String> mdcContext) {
        if (mdcContext == null || mdcContext.isEmpty()) {
            return responseMono;
        }

        // Wrap the entire reactive chain to ensure MDC is available throughout
        return Mono.deferContextual(contextView -> {
            // Set MDC context immediately when the chain starts
            MDC.setContextMap(mdcContext);

            return responseMono
                .doOnSubscribe(subscription -> {
                    // Ensure MDC context is set when subscription starts
                    MDC.setContextMap(mdcContext);
                })
                .doOnNext(response -> {
                    // Ensure MDC context is set for each emission
                    MDC.setContextMap(mdcContext);
                })
                .doOnError(error -> {
                    // Ensure MDC context is set for error handling
                    MDC.setContextMap(mdcContext);
                })
                .doFinally(signalType -> {
                    // Ensure MDC context is available even during cleanup
                    MDC.setContextMap(mdcContext);
                });
        });
    }

    private String getJwtFromRequest(WebGraphQlRequest request) {
        String bearerToken = request.getHeaders().getFirst("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * Check if the GraphQL request is for schema introspection
     */
    private boolean isSchemaIntrospectionRequest(WebGraphQlRequest request) {
        try {
            String query = request.getDocument();
            if (StringUtils.hasText(query)) {
                // Check if the query contains schema introspection patterns
                return query.contains("__schema") || 
                       query.contains("__type") || 
                       query.contains("__typename") ||
                       query.contains("IntrospectionQuery") ||
                       (query.contains("query") && query.contains("__"));
            }
        } catch (Exception ex) {
            logger.debug("Error checking for schema introspection request", ex);
        }
        return false;
    }

    /**
     * Check if the GraphQL request is for generateTestToken mutation
     */
    private boolean isGenerateTestTokenRequest(WebGraphQlRequest request) {
        try {
            String query = request.getDocument();
            if (StringUtils.hasText(query)) {
                // Check if the query contains generateTestToken mutation
                return query.contains("generateTestToken") &&
                       (query.contains("mutation") || query.toLowerCase().contains("mutation"));
            }
        } catch (Exception ex) {
            logger.debug("Error checking for generateTestToken request", ex);
        }
        return false;
    }
}
