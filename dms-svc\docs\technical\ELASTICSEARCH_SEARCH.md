# Advanced Elasticsearch Search for Document Management System

## Overview

The Document Management System (DMS) includes advanced search capabilities powered by Elasticsearch, providing fast, scalable, and feature-rich document search functionality with comprehensive security integration.

## Features

### 🔍 Search Capabilities
- **Full-text search** across document metadata (title, description, tags)
- **Content search** within document bodies (PDF, DOCX, XLSX, TXT, etc.)
- **Fuzzy matching** for handling typos and variations
- **Phrase queries** for exact phrase matching
- **Relevance scoring** with customizable boosting
- **Faceted search** with filters for document type, date ranges, creators
- **Semantic search** for natural language queries
- **Auto-complete suggestions** based on document content

### 🔒 Security Features
- **Document-level access control** integration
- **JWT-based user authentication** and role filtering
- **Permission-aware search results** (READ, WRITE, DELETE, ADMIN)
- **Confidentiality level filtering** (PUBLIC, CONFIDENTIAL, etc.)
- **No data leakage** - unauthorized users cannot see document existence
- **Audit logging** for search operations

### ⚡ Performance Features
- **Efficient document chunking** for large files
- **Optimized index mappings** for fast search performance
- **Near real-time indexing** with configurable refresh intervals
- **Result highlighting** for matched terms
- **Pagination support** with configurable page sizes
- **Concurrent search** handling

## Architecture

### Components

1. **ElasticsearchService** - Core search functionality with security filtering
2. **DocumentParsingService** - Text extraction from various file formats
3. **SearchableDocument** - Elasticsearch-optimized document model
4. **UserContext** - Security context for permission-aware searches
5. **GraphQL Resolvers** - API endpoints for advanced search

### Document Processing Pipeline

```
Document Upload → Text Extraction → Security Attributes → Index Creation → Search Ready
```

## Configuration

### Elasticsearch Settings

Add to `application.properties`:

```properties
# Elasticsearch Configuration
elasticsearch.host=localhost
elasticsearch.port=9200
elasticsearch.protocol=http
elasticsearch.username=
elasticsearch.password=
elasticsearch.connection-timeout=10000
elasticsearch.socket-timeout=30000
```

### Index Configuration

The system uses custom analyzers and mappings optimized for document search:

- **content_analyzer** - Standard tokenization with stemming and stop words
- **keyword_analyzer** - Exact keyword matching
- **suggest_analyzer** - Auto-complete functionality with edge n-grams
- **fuzzy_analyzer** - Phonetic matching for typo tolerance

## GraphQL API

### Advanced Search Query

```graphql
query AdvancedSearch($input: AdvancedSearchInput!, $pagination: PaginationInput) {
  advancedSearch(input: $input, pagination: $pagination) {
    documents {
      content {
        id
        name
        description
        relevanceScore
        highlights {
          name
          description
          content
        }
      }
      totalElements
      totalPages
    }
    facets {
      documentTypes { value count }
      storageProviders { value count }
      creators { value count }
      modules { value count }
    }
    suggestions
    totalTime
    maxScore
  }
}
```

### Search Input Parameters

```graphql
input AdvancedSearchInput {
  # Text search
  query: String
  searchType: SearchType = MULTI_FIELD
  fuzzySearch: Boolean = false
  phraseSearch: Boolean = false
  
  # Content options
  includeContent: Boolean = true
  contentOnly: Boolean = false
  
  # Filters
  storageProvider: StorageProvider
  status: DocumentStatus
  mimeTypes: [String!]
  createdDateFrom: DateTime
  createdDateTo: DateTime
  module: String
  businessUnit: String
  confidentialityLevel: String
  
  # Version control
  includeHistoricalVersions: Boolean = false
  currentVersionOnly: Boolean = true
}
```

### Search Suggestions

```graphql
query SearchSuggestions($query: String!, $limit: Int = 10) {
  searchSuggestions(query: $query, limit: $limit)
}
```

### Search Facets

```graphql
query GetSearchFacets($input: AdvancedSearchInput!) {
  getSearchFacets(input: $input) {
    documentTypes { value count }
    storageProviders { value count }
    creators { value count }
    modules { value count }
    businessUnits { value count }
    confidentialityLevels { value count }
  }
}
```

## Security Model

### Access Control Flow

1. **User Authentication** - JWT token validation
2. **Role Extraction** - Extract user roles and permissions
3. **Document Filtering** - Apply security filters to search queries
4. **Result Filtering** - Remove unauthorized documents from results
5. **Audit Logging** - Log search operations for compliance

### Permission Levels

- **READ** - Can search and view documents
- **WRITE** - Can search, view, and modify documents
- **DELETE** - Can search, view, modify, and delete documents
- **ADMIN** - Full access to all documents and permissions

### Security Filters

Search results are automatically filtered based on:

- Document creator (full access to own documents)
- Explicit user permissions
- Role-based permissions
- Confidentiality level access
- JWT token permissions

## Supported File Formats

### Text Extraction Support

- **PDF** - Using Apache PDFBox
- **Microsoft Word** - .doc (POI HWPF), .docx (POI XWPF)
- **Microsoft Excel** - .xls, .xlsx (POI)
- **Plain Text** - .txt, .md, .csv
- **HTML/XML** - Basic text extraction
- **Other formats** - Via Apache Tika fallback

### Content Processing

- **Large file chunking** - Documents split into searchable chunks
- **Metadata extraction** - File properties and custom metadata
- **Language detection** - Automatic language identification
- **Content type validation** - MIME type verification

## Performance Optimization

### Indexing Performance

- **Asynchronous indexing** - Non-blocking document processing
- **Batch operations** - Bulk indexing for multiple documents
- **Error handling** - Graceful degradation if search is unavailable
- **Retry logic** - Automatic retry for failed operations

### Search Performance

- **Query optimization** - Efficient Elasticsearch queries
- **Result caching** - Configurable result caching
- **Pagination** - Efficient large result set handling
- **Field boosting** - Relevance tuning for better results

### Monitoring

- **Search metrics** - Response times and result counts
- **Index health** - Monitoring index status and performance
- **Error tracking** - Failed operations and recovery
- **Usage analytics** - Search patterns and popular queries

## Testing

### Unit Tests

```bash
# Run document parsing tests
mvn test -Dtest=DocumentParsingServiceTest

# Run Elasticsearch service tests
mvn test -Dtest=ElasticsearchServiceTest
```

### Integration Tests

```bash
# Run Elasticsearch integration tests (requires Docker)
mvn test -Dtest=ElasticsearchIntegrationTest

# Run GraphQL E2E tests
mvn test -Dtest=AdvancedSearchGraphQLE2ETest
```

### Performance Tests

```bash
# Run performance tests (disabled by default)
mvn test -Dtest=SearchPerformanceTest -Drun.performance.tests=true
```

## Deployment

### Development Environment

1. **Start Elasticsearch**:
   ```bash
   docker run -d --name elasticsearch \
     -p 9200:9200 -p 9300:9300 \
     -e "discovery.type=single-node" \
     -e "xpack.security.enabled=false" \
     docker.elastic.co/elasticsearch/elasticsearch:8.11.0
   ```

2. **Configure application**:
   ```properties
   elasticsearch.host=localhost
   elasticsearch.port=9200
   ```

3. **Start application**:
   ```bash
   mvn spring-boot:run
   ```

### Production Environment

1. **Elasticsearch cluster setup** with proper security
2. **Index template configuration** for optimal performance
3. **Monitoring and alerting** setup
4. **Backup and recovery** procedures
5. **Security hardening** with authentication and encryption

## Troubleshooting

### Common Issues

1. **Elasticsearch not available** - Service gracefully degrades
2. **Index mapping conflicts** - Automatic index recreation
3. **Memory issues** - Document chunking and pagination
4. **Security errors** - Permission validation and logging

### Debug Logging

Enable debug logging for search operations:

```properties
logging.level.com.ascentbusiness.dms_svc.service.ElasticsearchService=DEBUG
logging.level.org.elasticsearch=INFO
```

## Future Enhancements

- **Machine Learning** - Document classification and recommendation
- **Advanced Analytics** - Search behavior analysis
- **Multi-language Support** - Enhanced language processing
- **Vector Search** - Semantic similarity search
- **Real-time Collaboration** - Live document updates in search
