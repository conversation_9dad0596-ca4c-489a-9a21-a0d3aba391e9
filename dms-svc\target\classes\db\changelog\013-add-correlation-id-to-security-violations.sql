--liquibase formatted sql

--changeset dms:013-add-correlation-id-to-security-violations

-- Add correlation_id column to security_violations table for request traceability
ALTER TABLE security_violations 
ADD COLUMN correlation_id VARCHAR(100) NULL 
COMMENT 'Correlation ID for request tracing and client-side debugging';

-- Add index for correlation_id to improve query performance
CREATE INDEX idx_security_violations_correlation_id ON security_violations(correlation_id);

-- Add comment to the table to document the correlation_id purpose
ALTER TABLE security_violations 
COMMENT = 'Security violations log with correlation ID for end-to-end request tracing';
