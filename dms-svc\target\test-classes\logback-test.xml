<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Console appender with pattern that includes correlation ID -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Application loggers -->
    <logger name="com.ascentbusiness.dms_svc" level="INFO" />
    <logger name="com.ascentbusiness.dms_svc.security.JwtAuthenticationFilter" level="DEBUG" />
    
    <!-- Suppress audit service logging during tests to prevent exception logs -->
    <logger name="com.ascentbusiness.dms_svc.service.AuditCryptographyService" level="ERROR" />
    <logger name="com.ascentbusiness.dms_svc.service.EnhancedAuditService" level="WARN" />
    
    <!-- Spring Framework loggers -->
    <logger name="org.springframework.security" level="WARN" />
    <logger name="org.springframework.web" level="WARN" />
    
    <!-- Hibernate loggers -->
    <logger name="org.hibernate.SQL" level="WARN" />
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="WARN" />
    
    <!-- Suppress Hibernate schema warnings during tests -->
    <logger name="org.hibernate.tool.schema.internal.ExceptionHandlerLoggedImpl" level="ERROR" />
    <logger name="org.hibernate.tool.schema.internal.Helper" level="ERROR" />
    <logger name="org.hibernate.tool.schema.internal.SchemaDropperImpl" level="ERROR" />
    <logger name="org.hibernate.tool.schema.spi.CommandAcceptanceException" level="ERROR" />
    
    <!-- TestContainers loggers -->
    <logger name="org.testcontainers" level="INFO" />
    <logger name="com.github.dockerjava" level="WARN" />
    
    <!-- Database driver loggers -->
    <logger name="com.mysql.cj" level="WARN" />
    <logger name="com.zaxxer.hikari" level="INFO" />
    
    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
    </root>
</configuration>
