package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.DocumentStatus;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class DocumentSearchInput {
    private String name;
    private String creator;
    private LocalDateTime creationDate;
    private StorageProvider storageProvider;
    private DocumentStatus status;
    private List<String> keywords;
}
