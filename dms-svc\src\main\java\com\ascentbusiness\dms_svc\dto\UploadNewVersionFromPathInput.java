package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;

import java.util.List;

@Data
public class UploadNewVersionFromPathInput {
    private Long documentId;                        // Required - ID of existing document to create version for
    private String sourceFilePath;                  // Required - server file path to read
    private String name;                            // Optional - inherit from original if not provided
    private String description;                     // Optional - inherit from original if not provided  
    private StorageProvider storageProvider;        // Optional - defaults to application.properties setting
    private List<String> keywords;                  // Optional in GraphQL (inherit from original if not provided)
    private Boolean overrideFile = false;           // NEW - duplicate control (default: false)
    private String comment;                         // NEW - optional comment for audit trail

    // Metadata fields (optional) - will update existing metadata if provided
    private DocumentClassificationMetadataInput classificationMetadata;
    private DocumentOwnershipMetadataInput ownershipMetadata;
    private DocumentComplianceMetadataInput complianceMetadata;
}
