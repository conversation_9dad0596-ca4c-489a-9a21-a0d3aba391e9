package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.HealthStatus;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * Component health DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class ComponentHealth {
    private String name;
    private HealthStatus status;
    private ComponentHealthDetails details;
    private OffsetDateTime lastChecked;
    private Long responseTime; // milliseconds
    private Integer errorCount;
    private Integer warningCount;
}
