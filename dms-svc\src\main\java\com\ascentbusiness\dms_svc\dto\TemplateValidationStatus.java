package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * DTO for template validation status
 * Corresponds to TemplateValidationStatus GraphQL type
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateValidationStatus {
    
    private Boolean isValid;
    private OffsetDateTime lastValidated;
    private String validatedBy;
    private String validationVersion;
    private List<TemplateValidationError> validationErrors;
    private List<String> warnings;
    private List<String> recommendations;
    private OffsetDateTime validatedAt;
}
