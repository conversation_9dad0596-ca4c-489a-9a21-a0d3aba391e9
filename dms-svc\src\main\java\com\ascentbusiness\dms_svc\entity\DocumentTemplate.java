package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.TemplateType;
import com.ascentbusiness.dms_svc.enums.TemplateApprovalStatus;
import com.ascentbusiness.dms_svc.enums.TemplateAccessLevel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Set;

/**
 * Entity representing a document template
 */
@Entity
@Table(name = "document_templates")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentTemplate extends BaseEntity {

    @Column(name = "name", nullable = false, length = 255)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "category", nullable = false, length = 100)
    private String category;

    @Enumerated(EnumType.STRING)
    @Column(name = "template_type", nullable = false, length = 50)
    @Builder.Default
    private TemplateType templateType = TemplateType.DOCUMENT;

    // Template content and structure
    @Lob
    @Column(name = "template_content", columnDefinition = "LONGBLOB")
    private byte[] templateContent;

    @Column(name = "template_format", nullable = false, length = 50)
    private String templateFormat; // DOCX, PDF, HTML, MARKDOWN, JSON

    @Column(name = "mime_type", nullable = false, length = 100)
    private String mimeType;

    @Column(name = "file_size")
    private Long fileSize;

    // Template configuration stored as JSON
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "configuration_json", columnDefinition = "JSON")
    private JsonNode configurationJson;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "field_definitions", columnDefinition = "JSON")
    private JsonNode fieldDefinitions;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "validation_rules", columnDefinition = "JSON")
    private JsonNode validationRules;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "default_values", columnDefinition = "JSON")
    private JsonNode defaultValues;

    // Template metadata
    @Column(name = "version", nullable = false, length = 50)
    @Builder.Default
    private String version = "1.0";

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Builder.Default
    @Column(name = "is_system_template", nullable = false)
    private Boolean isSystemTemplate = false;

    @Builder.Default
    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = false;

    // Usage and analytics
    @Column(name = "usage_count")
    @Builder.Default
    private Integer usageCount = 0;

    @Column(name = "last_used_date")
    private OffsetDateTime lastUsedDate;

    // Access control
    @Column(name = "owner_user_id", nullable = false, length = 255)
    private String ownerUserId;

    @Column(name = "owner_department", length = 255)
    private String ownerDepartment;

    @Enumerated(EnumType.STRING)
    @Column(name = "access_level", nullable = false, length = 50)
    @Builder.Default
    private TemplateAccessLevel accessLevel = TemplateAccessLevel.PRIVATE;

    // Approval and publishing
    @Enumerated(EnumType.STRING)
    @Column(name = "approval_status", nullable = false, length = 50)
    @Builder.Default
    private TemplateApprovalStatus approvalStatus = TemplateApprovalStatus.DRAFT;

    @Column(name = "approved_by", length = 255)
    private String approvedBy;

    @Column(name = "approved_date")
    private OffsetDateTime approvedDate;

    @Column(name = "published_date")
    private OffsetDateTime publishedDate;

    // Relationships
    @OneToMany(mappedBy = "template", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<TemplateVersion> versions;

    @OneToMany(mappedBy = "template", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<TemplateField> fields;

    @OneToMany(mappedBy = "template", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<TemplatePermission> permissions;

    @OneToMany(mappedBy = "template", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<TemplateUsageHistory> usageHistory;

    @OneToMany(mappedBy = "sourceTemplate", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<Document> documentsCreated;

    /**
     * Check if this template is currently active and available
     */
    @Transient
    public boolean isCurrentlyActive() {
        return isActive != null && isActive;
    }

    /**
     * Check if this template is published and ready for use
     */
    @Transient
    public boolean isPublished() {
        return TemplateApprovalStatus.PUBLISHED.equals(approvalStatus);
    }

    /**
     * Check if this template is pending approval
     */
    @Transient
    public boolean isPendingApproval() {
        return TemplateApprovalStatus.PENDING_APPROVAL.equals(approvalStatus);
    }

    /**
     * Check if this template is publicly accessible
     */
    @Transient
    public boolean isPubliclyAccessible() {
        return isPublic != null && isPublic && isPublished();
    }

    /**
     * Check if this template has been used recently (within last 30 days)
     */
    @Transient
    public boolean isRecentlyUsed() {
        return lastUsedDate != null &&
               lastUsedDate.isAfter(OffsetDateTime.now().minusDays(30));
    }

    /**
     * Check if this template is popular (used more than 10 times)
     */
    @Transient
    public boolean isPopular() {
        return usageCount != null && usageCount > 10;
    }

    /**
     * Check if this template has dynamic fields
     */
    @Transient
    public boolean hasDynamicFields() {
        return fieldDefinitions != null && !fieldDefinitions.isNull() && fieldDefinitions.size() > 0;
    }

    /**
     * Get the number of dynamic fields
     */
    @Transient
    public int getFieldCount() {
        if (fieldDefinitions == null || fieldDefinitions.isNull()) return 0;
        return fieldDefinitions.size();
    }

    /**
     * Increment usage count and update last used date
     */
    public void recordUsage() {
        this.usageCount = (this.usageCount != null ? this.usageCount : 0) + 1;
        this.lastUsedDate = OffsetDateTime.now();
    }

    /**
     * Publish the template
     */
    public void publish(String publishedBy) {
        this.approvalStatus = TemplateApprovalStatus.PUBLISHED;
        this.approvedBy = publishedBy;
        this.approvedDate = OffsetDateTime.now();
        this.publishedDate = OffsetDateTime.now();
    }

    /**
     * Approve the template
     */
    public void approve(String approvedBy) {
        this.approvalStatus = TemplateApprovalStatus.APPROVED;
        this.approvedBy = approvedBy;
        this.approvedDate = OffsetDateTime.now();
    }

    /**
     * Reject the template
     */
    public void reject() {
        this.approvalStatus = TemplateApprovalStatus.REJECTED;
        this.approvedBy = null;
        this.approvedDate = null;
    }

    /**
     * Get template content as Base64 string
     */
    @Transient
    public String getTemplateContentBase64() {
        if (templateContent != null && templateContent.length > 0) {
            return java.util.Base64.getEncoder().encodeToString(templateContent);
        }
        return null;
    }

    /**
     * Set template content from Base64 string
     */
    public void setTemplateContentBase64(String base64Content) {
        if (base64Content != null && !base64Content.trim().isEmpty()) {
            try {
                this.templateContent = java.util.Base64.getDecoder().decode(base64Content);
                this.fileSize = (long) this.templateContent.length;
            } catch (IllegalArgumentException e) {
                this.templateContent = null;
                this.fileSize = null;
            }
        } else {
            this.templateContent = null;
            this.fileSize = null;
        }
    }

    @Override
    public String toString() {
        return String.format("DocumentTemplate{id=%d, name='%s', category='%s', type=%s, status=%s}", 
                           getId(), name, category, templateType, approvalStatus);
    }
}
