--liquibase formatted sql

--changeset dms:025-create-retention-policy-tables

-- Create retention_policies table
CREATE TABLE retention_policies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    scope VARCHAR(100),
    retention_period INT NOT NULL,
    retention_period_unit ENUM('DAYS', 'MONTHS', 'YEARS') NOT NULL,
    disposition_action ENUM('ARCHIVE', 'DELETE', 'REVIEW', 'EXTEND', 'TRANSFER') NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    allow_legal_hold BOOLEAN NOT NULL DEFAULT TRUE,
    auto_apply BOOLEAN NOT NULL DEFAULT FALSE,
    priority INT NOT NULL DEFAULT 0,
    trigger_event VARCHAR(50),
    business_justification TEXT,
    legal_basis TEXT,
    review_frequency_months INT,
    notification_before_days INT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    last_modified_by VARCHAR(100),
    
    INDEX idx_retention_policies_name (name),
    INDEX idx_retention_policies_scope (scope),
    INDEX idx_retention_policies_active (is_active),
    INDEX idx_retention_policies_priority (priority),
    INDEX idx_retention_policies_auto_apply (auto_apply)
);

-- Create retention_policy_assignments table
CREATE TABLE retention_policy_assignments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    retention_policy_id BIGINT NOT NULL,
    assignment_type VARCHAR(50) NOT NULL,
    assignment_value VARCHAR(255) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    priority INT NOT NULL DEFAULT 0,
    conditions JSON,
    description TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    last_modified_by VARCHAR(100),
    
    FOREIGN KEY (retention_policy_id) REFERENCES retention_policies(id) ON DELETE CASCADE,
    UNIQUE KEY uk_retention_assignment (retention_policy_id, assignment_type, assignment_value),
    INDEX idx_retention_assignments_policy (retention_policy_id),
    INDEX idx_retention_assignments_type (assignment_type),
    INDEX idx_retention_assignments_value (assignment_value),
    INDEX idx_retention_assignments_active (is_active),
    INDEX idx_retention_assignments_priority (priority)
);

-- Add retention fields to documents table
ALTER TABLE documents 
ADD COLUMN retention_policy_id BIGINT,
ADD COLUMN retention_expiry_date TIMESTAMP,
ADD COLUMN legal_hold_status ENUM('NONE', 'ACTIVE', 'RELEASED', 'PENDING_REVIEW') DEFAULT 'NONE',
ADD COLUMN disposition_status ENUM('ACTIVE', 'ELIGIBLE', 'PENDING', 'DISPOSED', 'ON_HOLD', 'REVIEW_REQUIRED', 'SUSPENDED') DEFAULT 'ACTIVE',
ADD COLUMN legal_hold_reason TEXT,
ADD COLUMN legal_hold_applied_date TIMESTAMP,
ADD COLUMN legal_hold_applied_by VARCHAR(100),
ADD COLUMN disposition_review_date TIMESTAMP,
ADD COLUMN disposition_notes TEXT;

-- Add foreign key constraint for retention policy
ALTER TABLE documents 
ADD CONSTRAINT fk_documents_retention_policy 
FOREIGN KEY (retention_policy_id) REFERENCES retention_policies(id) ON DELETE SET NULL;

-- Add indexes for retention fields
CREATE INDEX idx_documents_retention_policy ON documents(retention_policy_id);
CREATE INDEX idx_documents_retention_expiry ON documents(retention_expiry_date);
CREATE INDEX idx_documents_legal_hold_status ON documents(legal_hold_status);
CREATE INDEX idx_documents_disposition_status ON documents(disposition_status);
CREATE INDEX idx_documents_retention_eligible ON documents(retention_expiry_date, legal_hold_status, disposition_status);

-- Update audit_logs check constraint to include new retention actions
ALTER TABLE audit_logs DROP CONSTRAINT chk_audit_action;
ALTER TABLE audit_logs 
ADD CONSTRAINT chk_audit_action 
CHECK (action IN (
    'UPLOAD', 'DOWNLOAD', 'DELETE', 'UPDATE', 'VERSION_CREATE',
    'PERMISSION_GRANTED', 'PERMISSION_REVOKED', 'PERMISSION_EXPIRED', 'PERMISSION_CHECKED',
    'SECURITY_VIOLATION', 'TOKEN_VALIDATION_FAILED', 'RATE_LIMIT_EXCEEDED',
    'MIGRATION_STARTED', 'MIGRATION_COMPLETED', 'MIGRATION_FAILED',
    'MIGRATION_FILE_PROCESSED', 'MIGRATION_FILE_VERIFIED', 'MIGRATION_FILE_CLEANUP',
    'MIGRATION_SECURITY_CHECK', 'MIGRATION_VALIDATION_FAILED',
    'RETENTION_POLICY_CREATED', 'RETENTION_POLICY_UPDATED', 'RETENTION_POLICY_DELETED',
    'RETENTION_POLICY_ASSIGNED', 'RETENTION_POLICY_UNASSIGNED', 'RETENTION_EXPIRY_CALCULATED',
    'RETENTION_PROCESSING_STARTED', 'RETENTION_PROCESSING_COMPLETED', 'RETENTION_PROCESSING_FAILED',
    'DOCUMENT_ARCHIVED', 'DOCUMENT_DISPOSED', 'LEGAL_HOLD_APPLIED', 'LEGAL_HOLD_RELEASED',
    'DISPOSITION_REVIEW_REQUIRED', 'DISPOSITION_APPROVED', 'DISPOSITION_REJECTED'
));
