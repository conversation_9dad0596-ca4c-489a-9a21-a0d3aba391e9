package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.AuditAction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for action count statistics
 * Corresponds to ActionCount type in audit-schema.graphqls
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionCount {
    
    private AuditAction action;
    private Long count;
    private Float percentage;
}
