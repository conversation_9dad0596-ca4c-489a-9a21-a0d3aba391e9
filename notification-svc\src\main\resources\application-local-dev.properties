# Local Development Configuration for Notification Service
# This file contains local development-specific settings

# Server Configuration
server.port=9091

# Database Configuration - Local Development
spring.datasource.url=*************************************************************************************************************************************************************
spring.datasource.username=notification_user
spring.datasource.password=notification_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration for Local Development
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=900000

# JPA Configuration - Local Development
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

# Performance Settings for Local Development
spring.jpa.properties.hibernate.jdbc.batch_size=10
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# Flyway Configuration - Disabled for Local Development
spring.flyway.enabled=false

# GraphQL Configuration - Local Development
spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:true}
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.printer.enabled=true
spring.graphql.path=/graphql
spring.graphql.graphiql.path=/graphiql

# CORS Configuration - Local Development
spring.graphql.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:4200,http://localhost:3000,http://localhost:8080}
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID,Accept
spring.graphql.cors.allow-credentials=true
spring.graphql.cors.max-age=3600

# RabbitMQ Configuration - Local Development
spring.rabbitmq.host=rabbitmq-shared
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin123
spring.rabbitmq.virtual-host=/

# RabbitMQ Connection Pool Configuration
spring.rabbitmq.connection-timeout=30000
spring.rabbitmq.requested-heartbeat=60
spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true

# Redis Configuration - Local Development
spring.redis.host=redis-shared
spring.redis.port=6379
spring.redis.password=local_redis_password
spring.redis.database=0
spring.redis.timeout=5000ms
spring.redis.connect-timeout=3000ms

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=300
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=notification:local:

# Redis Connection Pool Configuration (Lettuce)
spring.data.redis.lettuce.pool.max-active=5
spring.data.redis.lettuce.pool.max-idle=3
spring.data.redis.lettuce.pool.min-idle=1
spring.data.redis.lettuce.pool.max-wait=2000ms

# Email Configuration - Local Development (Mock)
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=*

# Notification Configuration - Local Development
notification.email.from=${NOTIFICATION_FROM_EMAIL:noreply@localhost}
notification.email.enabled=${EMAIL_ENABLED:true}
notification.email.mock=${EMAIL_MOCK:true}

# Security Configuration - Local Development
security.jwt.enabled=true
security.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:4200,http://localhost:3000,http://localhost:8080}
security.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
security.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID,Accept
security.cors.allow-credentials=true
security.cors.max-age=3600

# JWT Configuration
jwt.issuer=notification-service-local
jwt.audience=notification-clients-local
jwt.secret=${JWT_SECRET:localDevSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
jwt.expiration=3600000

# Actuator Configuration - Local Development
management.endpoints.web.exposure.include=health,info,metrics,prometheus,env,configprops
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always
management.health.defaults.enabled=true

# Health Indicators
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=true
management.health.rabbitmq.enabled=true
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true

# Logging Configuration - Local Development
logging.level.com.ascentbusiness.notification_svc=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.amqp=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Application Configuration
spring.application.name=notification-svc-local

# Development specific settings
spring.main.allow-bean-definition-overriding=true

# Server Configuration - Local Development
server.compression.enabled=false
server.tomcat.connection-timeout=20000
server.tomcat.max-connections=200
server.tomcat.threads.max=50
server.tomcat.threads.min-spare=5

# Async Configuration
spring.task.execution.pool.core-size=5
spring.task.execution.pool.max-size=10
spring.task.execution.pool.queue-capacity=50
spring.task.execution.thread-name-prefix=notification-async-local-

# Notification Processing Configuration - Local Development
notification.processing.batch-size=10
notification.processing.retry-attempts=3
notification.processing.retry-delay=5000

# Template Configuration
notification.template.cache-enabled=true
notification.template.cache-ttl=300

# Webhook Configuration
notification.webhook.enabled=true
notification.webhook.timeout=30000
notification.webhook.retry-attempts=3

# Cross-service Communication - Local Development
dms.service.url=${DMS_SERVICE_URL:http://localhost:9093}

# Development Features
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
