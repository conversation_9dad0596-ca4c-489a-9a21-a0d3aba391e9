package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.*;

/**
 * Entity representing security configuration settings.
 * Stores key-value pairs for security-related configuration
 * that can be dynamically managed and updated.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@Entity
@Table(name = "security_config")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SecurityConfig extends BaseEntity {

    /** Unique key identifying the security configuration setting */
    @Column(name = "config_key", nullable = false, unique = true, length = 100)
    private String configKey;

    /** Value of the security configuration setting */
    @Column(name = "config_value", nullable = false, columnDefinition = "TEXT")
    private String configValue;

    /** Description of what this security configuration setting controls */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /** Whether this security configuration setting is currently active */
    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    /**
     * Helper method to get the configuration value as a boolean.
     * @return the configuration value parsed as a boolean
     */
    public Boolean getBooleanValue() {
        return Boolean.parseBoolean(configValue);
    }

    /**
     * Helper method to get the configuration value as an integer.
     * @return the configuration value parsed as an integer, or null if parsing fails
     */
    public Integer getIntegerValue() {
        try {
            return Integer.parseInt(configValue);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * Helper method to get the configuration value as a long.
     * @return the configuration value parsed as a long, or null if parsing fails
     */
    public Long getLongValue() {
        try {
            return Long.parseLong(configValue);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
