package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.EventType;
import com.ascentbusiness.dms_svc.enums.EventCategory;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Entity representing a system event
 */
@Entity
@Table(name = "system_events")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemEvent extends BaseEntity {

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", nullable = false, length = 100)
    private EventType eventType;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_category", nullable = false, length = 50)
    private EventCategory eventCategory;

    @Column(name = "event_name", nullable = false, length = 255)
    private String eventName;

    // Event source
    @Column(name = "source_entity_type", length = 50)
    private String sourceEntityType;

    @Column(name = "source_entity_id")
    private Long sourceEntityId;

    // Event details
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_data", columnDefinition = "JSON")
    private JsonNode eventData;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_metadata", columnDefinition = "JSON")
    private JsonNode eventMetadata;

    // Actor information
    @Column(name = "actor_user_id", length = 255)
    private String actorUserId;

    @Column(name = "actor_type", length = 50)
    private String actorType; // USER, SYSTEM, SERVICE

    // Context
    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    @Column(name = "session_id", length = 100)
    private String sessionId;

    @Column(name = "request_id", length = 100)
    private String requestId;

    // Timing
    @Column(name = "event_timestamp", nullable = false)
    @Builder.Default
    private LocalDateTime eventTimestamp = LocalDateTime.now();

    // Processing status
    @Column(name = "processing_status", nullable = false, length = 50)
    @Builder.Default
    private String processingStatus = "PENDING"; // PENDING, PROCESSING, COMPLETED, FAILED

    @Column(name = "webhook_delivery_count")
    @Builder.Default
    private Integer webhookDeliveryCount = 0;

    // Relationships
    @OneToMany(mappedBy = "systemEvent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WebhookDelivery> webhookDeliveries;

    @OneToMany(mappedBy = "systemEvent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<EventProcessingLog> processingLogs;

    /**
     * Check if event is pending processing
     */
    @Transient
    public boolean isPending() {
        return "PENDING".equals(processingStatus);
    }

    /**
     * Check if event is currently being processed
     */
    @Transient
    public boolean isProcessing() {
        return "PROCESSING".equals(processingStatus);
    }

    /**
     * Check if event processing is completed
     */
    @Transient
    public boolean isCompleted() {
        return "COMPLETED".equals(processingStatus);
    }

    /**
     * Check if event processing failed
     */
    @Transient
    public boolean isFailed() {
        return "FAILED".equals(processingStatus);
    }

    /**
     * Check if event has been delivered to webhooks
     */
    @Transient
    public boolean hasWebhookDeliveries() {
        return webhookDeliveryCount != null && webhookDeliveryCount > 0;
    }

    /**
     * Check if event is a document-related event
     */
    @Transient
    public boolean isDocumentEvent() {
        return EventCategory.DOCUMENT.equals(eventCategory);
    }

    /**
     * Check if event is a workflow-related event
     */
    @Transient
    public boolean isWorkflowEvent() {
        return EventCategory.WORKFLOW.equals(eventCategory);
    }

    /**
     * Check if event is a system-level event
     */
    @Transient
    public boolean isSystemEvent() {
        return EventCategory.SYSTEM.equals(eventCategory);
    }

    /**
     * Check if event is security-related
     */
    @Transient
    public boolean isSecurityEvent() {
        return EventCategory.SECURITY.equals(eventCategory);
    }

    /**
     * Get event age in hours
     */
    @Transient
    public long getEventAgeHours() {
        return java.time.Duration.between(eventTimestamp, LocalDateTime.now()).toHours();
    }

    /**
     * Check if event is recent (within last hour)
     */
    @Transient
    public boolean isRecent() {
        return getEventAgeHours() < 1;
    }

    /**
     * Check if event is old (older than 24 hours)
     */
    @Transient
    public boolean isOld() {
        return getEventAgeHours() > 24;
    }

    /**
     * Get successful webhook deliveries count
     */
    @Transient
    public long getSuccessfulDeliveryCount() {
        if (webhookDeliveries == null) return 0;
        return webhookDeliveries.stream()
                .filter(delivery -> "SUCCESS".equals(delivery.getDeliveryStatus()))
                .count();
    }

    /**
     * Get failed webhook deliveries count
     */
    @Transient
    public long getFailedDeliveryCount() {
        if (webhookDeliveries == null) return 0;
        return webhookDeliveries.stream()
                .filter(delivery -> "FAILED".equals(delivery.getDeliveryStatus()))
                .count();
    }

    /**
     * Mark event as processing
     */
    public void markAsProcessing() {
        this.processingStatus = "PROCESSING";
    }

    /**
     * Mark event as completed
     */
    public void markAsCompleted() {
        this.processingStatus = "COMPLETED";
    }

    /**
     * Mark event as failed
     */
    public void markAsFailed() {
        this.processingStatus = "FAILED";
    }

    /**
     * Increment webhook delivery count
     */
    public void incrementWebhookDeliveryCount() {
        this.webhookDeliveryCount = (this.webhookDeliveryCount != null ? this.webhookDeliveryCount : 0) + 1;
    }

    /**
     * Get event display name
     */
    @Transient
    public String getEventDisplayName() {
        if (eventName != null && !eventName.trim().isEmpty()) {
            return eventName;
        }
        return eventType != null ? eventType.name().replace("_", " ").toLowerCase() : "Unknown Event";
    }

    @Override
    public String toString() {
        return String.format("SystemEvent{id=%d, type=%s, category=%s, actor='%s', timestamp=%s}", 
                           getId(), eventType, eventCategory, actorUserId, eventTimestamp);
    }
}
