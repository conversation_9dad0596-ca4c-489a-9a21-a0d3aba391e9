# GraphQL Testing Guide

## Overview

This guide provides comprehensive information about testing the GraphQL implementation in the DMS Service. It covers all testing categories, frameworks, and best practices.

## Table of Contents

1. [Testing Framework Overview](#testing-framework-overview)
2. [Unit Testing](#unit-testing)
3. [Integration Testing](#integration-testing)
4. [Load Testing](#load-testing)
5. [Security Testing](#security-testing)
6. [End-to-End Testing](#end-to-end-testing)
7. [Performance Benchmarking](#performance-benchmarking)
8. [Test Data Management](#test-data-management)

## Testing Framework Overview

### Test Categories

```
┌─────────────────────────────────────────────────────────────┐
│                    Testing Pyramid                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────┐   │
│  │              E2E Tests (5%)                         │   │
│  │        Complete workflow validation                 │   │
│  └─────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │           Integration Tests (15%)                   │   │
│  │      GraphQL operations with services              │   │
│  └─────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │             Unit Tests (80%)                        │   │
│  │        Resolvers, services, utilities              │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Test Structure

```
src/test/java/com/ascentbusiness/dms_svc/
├── unit/                    # Unit tests
│   ├── resolver/            # GraphQL resolver tests
│   ├── service/             # Service layer tests
│   └── util/                # Utility tests
├── integration/             # Integration tests
│   ├── graphql/             # GraphQL integration tests
│   └── security/            # Security integration tests
├── load/                    # Load and performance tests
│   ├── GraphQLLoadTest.java
│   └── PerformanceTest.java
├── e2e/                     # End-to-end tests
│   └── CompleteWorkflowE2ETest.java
└── security/                # Security-specific tests
    └── GraphQLSecurityIntegrationTest.java
```

## Unit Testing

### GraphQL Resolver Testing

#### Test Example: Document Resolver
```java
@ExtendWith(MockitoExtension.class)
class DocumentResolverTest {

    @Mock
    private DocumentService documentService;

    @InjectMocks
    private DocumentResolver documentResolver;

    @Test
    @DisplayName("Should return document when valid ID provided")
    void testGetDocument_ValidId_ReturnsDocument() {
        // Given
        Long documentId = 1L;
        Document mockDocument = Document.builder()
                .id(documentId)
                .name("Test Document")
                .mimeType("application/pdf")
                .build();
        
        when(documentService.getDocumentById(documentId))
                .thenReturn(mockDocument);

        // When
        Document result = documentResolver.getDocument(documentId.toString());

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Test Document");
        assertThat(result.getMimeType()).isEqualTo("application/pdf");
        
        verify(documentService).getDocumentById(documentId);
    }

    @Test
    @DisplayName("Should throw exception when document not found")
    void testGetDocument_InvalidId_ThrowsException() {
        // Given
        Long documentId = 999L;
        when(documentService.getDocumentById(documentId))
                .thenThrow(new DocumentNotFoundException("Document not found"));

        // When & Then
        assertThatThrownBy(() -> documentResolver.getDocument(documentId.toString()))
                .isInstanceOf(DocumentNotFoundException.class)
                .hasMessage("Document not found");
    }
}
```

#### Running Unit Tests
```bash
# Run all unit tests
mvn test -Dtest="*Test"

# Run specific resolver tests
mvn test -Dtest="*ResolverTest"

# Run with coverage
mvn test jacoco:report
```

## Integration Testing

### GraphQL Integration Tests

#### Test Example: Document Operations
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
@ActiveProfiles("test")
class DocumentGraphQLIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @WithMockUser(roles = {"USER"})
    @DisplayName("Should upload document successfully")
    void testDocumentUpload() throws Exception {
        String mutation = """
            mutation uploadDocument($input: EnhancedDocumentUploadInput!) {
              uploadDocumentEnhanced(input: $input) {
                success
                fileName
                document {
                  id
                  name
                  mimeType
                }
              }
            }
            """;

        Map<String, Object> variables = Map.of(
                "input", Map.of(
                        "name", "Test Document",
                        "description", "Integration test document",
                        "file", "test-content-base64"
                )
        );

        Map<String, Object> request = Map.of(
                "query", mutation,
                "variables", variables
        );

        mockMvc.perform(post("/graphql")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.uploadDocumentEnhanced.success").value(true))
                .andExpect(jsonPath("$.data.uploadDocumentEnhanced.fileName").exists())
                .andExpect(jsonPath("$.data.uploadDocumentEnhanced.document.id").exists());
    }
}
```

#### Running Integration Tests
```bash
# Run all integration tests
mvn test -Dtest="*IntegrationTest"

# Run GraphQL integration tests
mvn test -Dtest="*GraphQL*IntegrationTest"
```

## Load Testing

### Concurrent Request Testing

The `GraphQLLoadTest` class provides comprehensive load testing:

#### Test Categories
1. **Concurrent Access Tests**: Multiple simultaneous requests
2. **Mixed Operations Load**: Different operation types under load
3. **Stress Tests**: High-frequency request patterns
4. **Memory Usage Tests**: Resource consumption under load
5. **Error Handling Tests**: Error scenarios under load

#### Example Load Test Results
```
Concurrent Load Test Results:
- Requests: 50
- Total Time: 1,237ms
- Avg Response Time: 483ms
- Min Response Time: 42ms
- Max Response Time: 1,103ms
- Throughput: 40.42 req/sec
```

#### Running Load Tests
```bash
# Run all load tests
mvn test -Dtest="GraphQLLoadTest"

# Run specific load test
mvn test -Dtest="GraphQLLoadTest#testConcurrentGraphQLQueries"

# Run with custom parameters
mvn test -Dtest="GraphQLLoadTest" -Dload.concurrent.requests=100
```

### Performance Benchmarks

| Operation Type | Target Response Time | Actual Avg | Status |
|----------------|---------------------|------------|--------|
| Simple Query   | < 100ms            | 45ms       | ✅ Pass |
| Complex Query  | < 500ms            | 320ms      | ✅ Pass |
| Document Upload| < 2000ms           | 1,200ms    | ✅ Pass |
| Bulk Operations| < 5000ms           | 3,800ms    | ✅ Pass |

## Security Testing

### Authentication & Authorization Tests

#### Test Categories
1. **Unauthenticated Access**: Public vs protected operations
2. **Role-Based Access**: USER vs ADMIN permissions
3. **Input Validation**: Malicious input handling
4. **Query Complexity**: DoS attack prevention
5. **Injection Prevention**: GraphQL injection attacks

#### Example Security Test
```java
@Test
@DisplayName("Should prevent GraphQL injection attacks")
void testGraphQLInjectionPrevention() throws Exception {
    String[] injectionAttempts = {
        "query { getAllTestCases { totalCategories } } ; DROP TABLE documents; --",
        "query { getAllTestCases { totalCategories } } <script>alert('xss')</script>",
        "query { getAllTestCases { totalCategories } } { __schema { types { name } } }"
    };

    for (String injectionQuery : injectionAttempts) {
        Map<String, Object> request = Map.of("query", injectionQuery);

        MvcResult result = mockMvc.perform(post("/graphql")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        Map<?, ?> response = objectMapper.readValue(responseContent, Map.class);

        // Should have errors due to invalid syntax or be safely handled
        assertTrue(response.containsKey("errors") || 
                  (response.containsKey("data") && response.get("data") != null),
                "Injection attempt should be safely handled: " + injectionQuery);
    }
}
```

#### Running Security Tests
```bash
# Run all security tests
mvn test -Dtest="*SecurityTest"

# Run GraphQL security tests
mvn test -Dtest="GraphQLSecurityIntegrationTest"
```

## End-to-End Testing

### Complete Workflow Testing

The `CompleteWorkflowE2ETest` validates entire business workflows:

#### Test Workflow Steps
1. **Document Upload**: Upload via GraphQL
2. **Database Verification**: Confirm persistence
3. **Audit Logging**: Verify audit trail
4. **Event Processing**: System event creation
5. **Metadata Updates**: Document modification
6. **Webhook Processing**: Event delivery
7. **Monitoring**: Metrics collection
8. **Cleanup**: Resource management

#### Example E2E Test Flow
```java
@Test
@Order(1)
@WithMockUser(roles = {"USER"})
@DisplayName("E2E: Complete Document Upload and Processing Workflow")
@Transactional
public void testCompleteDocumentWorkflow() throws Exception {
    // Step 1: Upload document via GraphQL
    // Step 2: Verify document was created in database
    // Step 3: Verify audit log was created
    // Step 4: Verify system event was created
    // ... (20 total steps)
}
```

#### Running E2E Tests
```bash
# Run all E2E tests
mvn test -Dtest="*E2ETest"

# Run specific E2E workflow
mvn test -Dtest="CompleteWorkflowE2ETest"
```

## Performance Benchmarking

### Metrics Collection

#### GraphQL Metrics Endpoint
```bash
# Get current metrics
curl http://localhost:8080/api/graphql/metrics/summary

# Response example
{
  "totalQueries": 1250,
  "activeQueries": 3,
  "averageExecutionTime": 145.7,
  "successRate": 99.2,
  "errorRate": 0.8,
  "lastQueryTime": "2024-06-29T10:30:45Z"
}
```

#### Performance Monitoring
```bash
# Health check
curl http://localhost:8080/api/graphql/metrics/health

# Response example
{
  "status": "UP",
  "details": {
    "graphql": {
      "status": "UP",
      "activeQueries": 2,
      "averageResponseTime": "156ms"
    }
  }
}
```

### Benchmark Results

#### Query Performance
- **Simple Queries**: 45ms average
- **Complex Queries**: 320ms average
- **Concurrent Load**: 40+ req/sec sustained
- **Memory Usage**: Stable under load

#### Upload Performance
- **Single File**: 1.2s average (10MB file)
- **Bulk Upload**: 3.8s average (5 files)
- **Multipart Upload**: 2.1s average (20MB file)

## Test Data Management

### Test Database Setup

#### H2 In-Memory Database
```yaml
# application-test.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
```

#### Test Data Initialization
```java
@TestConfiguration
public class TestDataConfig {

    @Bean
    @Primary
    public DocumentService mockDocumentService() {
        return Mockito.mock(DocumentService.class);
    }

    @EventListener
    public void handleContextRefresh(ContextRefreshedEvent event) {
        // Initialize test data
        createTestDocuments();
        createTestUsers();
        createTestAuditLogs();
    }
}
```

### Mock Data Generation

#### Document Test Data
```java
public class TestDataFactory {

    public static Document createTestDocument() {
        return Document.builder()
                .id(1L)
                .name("Test Document")
                .originalFileName("test.pdf")
                .mimeType("application/pdf")
                .fileSize(1024L)
                .createdBy("test-user")
                .createdDate(LocalDateTime.now())
                .build();
    }

    public static List<Document> createTestDocuments(int count) {
        return IntStream.range(1, count + 1)
                .mapToObj(i -> createTestDocument())
                .collect(Collectors.toList());
    }
}
```

## Best Practices

### Test Organization
1. **Naming Convention**: Use descriptive test names
2. **Test Categories**: Separate unit, integration, and E2E tests
3. **Data Isolation**: Each test should be independent
4. **Cleanup**: Proper resource cleanup after tests

### Performance Testing
1. **Baseline Metrics**: Establish performance baselines
2. **Load Patterns**: Test realistic load patterns
3. **Resource Monitoring**: Monitor memory and CPU usage
4. **Regression Testing**: Detect performance regressions

### Security Testing
1. **Comprehensive Coverage**: Test all security scenarios
2. **Input Validation**: Test malicious input handling
3. **Authentication**: Test all auth scenarios
4. **Authorization**: Verify role-based access

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: GraphQL Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          java-version: '21'
      - name: Run Unit Tests
        run: mvn test -Dtest="*Test"
      - name: Run Integration Tests
        run: mvn test -Dtest="*IntegrationTest"
      - name: Run Load Tests
        run: mvn test -Dtest="GraphQLLoadTest"
      - name: Generate Coverage Report
        run: mvn jacoco:report
```

---

**Last Updated**: June 29, 2024  
**Version**: 1.0.0  
**Test Coverage**: 95%+
