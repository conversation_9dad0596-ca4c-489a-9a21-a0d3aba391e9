package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.HealthStatus;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * Health summary DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class HealthSummary {
    private HealthStatus overallStatus;
    private List<ComponentHealth> components;
    private OffsetDateTime lastUpdated;

    // Additional fields for test compatibility
    private Integer componentsUp;
    private Integer componentsDown;
    private Integer componentsDegraded;
    private Integer totalComponents;
    private Integer criticalIssues;
    private Integer warnings;
    private Double systemLoad;
    private Double availabilityPercentage;

    // Legacy fields
    private Double score;
    private OffsetDateTime lastChecked;
    private OffsetDateTime nextCheckDue;
    // private List<HealthTrend> trends; // TODO: implement if needed
}
