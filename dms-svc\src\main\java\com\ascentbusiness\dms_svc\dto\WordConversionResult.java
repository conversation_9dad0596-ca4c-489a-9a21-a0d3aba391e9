package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Result of Word to PDF conversion operation.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WordConversionResult {
    
    /**
     * Unique session identifier for the conversion operation.
     */
    private String sessionId;
    
    /**
     * Original Word document file name.
     */
    private String originalFileName;
    
    /**
     * Converted PDF file name.
     */
    private String convertedFileName;
    
    /**
     * Full path to the converted file in the download directory.
     */
    private String downloadPath;
    
    /**
     * Size of the converted file in bytes.
     */
    private long fileSize;
    
    /**
     * Result of virus scanning performed on the original Word document.
     */
    private VirusScanResponse virusScanResponse;
    
    /**
     * Whether the conversion was successful.
     */
    private boolean success;
    
    /**
     * Success or error message.
     */
    private String message;
    
    /**
     * Error details if conversion failed.
     */
    private String errorDetails;
    
    /**
     * Timestamp when conversion was completed.
     */
    private java.time.OffsetDateTime completedAt;
    
    /**
     * Duration of the conversion process in milliseconds.
     */
    private long processingTimeMs;
}
