package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Statistics DTO for conversion operations from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionStatistics {
    private Long totalConversions;
    private Long successfulConversions;
    private Long failedConversions;
    private Float averageProcessingTime;
    private Long totalProcessingTime;
    private List<ConversionTrendData> conversionsByDate;
    private List<String> topErrorReasons;
    private List<ConversionTypeStats> conversionsByType;
    private List<ConversionMethodStats> conversionsByMethod;
    private List<ConversionTrendData> conversionTrends;
    private List<FormatPopularity> popularFormats;
}
