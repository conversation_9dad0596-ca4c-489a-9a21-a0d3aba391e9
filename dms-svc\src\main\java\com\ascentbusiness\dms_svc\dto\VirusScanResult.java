package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * DTO for virus scan result.
 * Corresponds to VirusScanResult GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VirusScanResult {

    /**
     * Whether the file is clean (no threats detected).
     */
    private Boolean isClean;

    /**
     * The type of scanner that performed the scan.
     */
    private VirusScannerType scannerUsed;

    /**
     * When the scan was performed.
     */
    private OffsetDateTime scanDate;

    /**
     * Details about any threats found (optional).
     */
    private String threatDetails;

    /**
     * Whether the file was quarantined.
     */
    private Boolean quarantined;
}
