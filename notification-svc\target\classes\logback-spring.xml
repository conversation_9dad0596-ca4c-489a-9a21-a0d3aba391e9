<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    
    <!-- Define properties -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId:-}] [%X{requestId:-}] [%X{userId:-}] [%X{sourceService:-}] %logger{36} - %msg%n"/>
    <property name="AUDIT_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId:-}] [%X{requestId:-}] [%X{userId:-}] [%X{sourceService:-}] %logger{36} - %msg%n"/>
    <property name="LOG_FILE" value="logs/notification-service.log"/>
    <property name="AUDIT_LOG_FILE" value="logs/audit.log"/>
    
    <!-- Console Appender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    
    <!-- File Appender for Application Logs -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/notification-service.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- Separate Appender for Audit Logs -->
    <appender name="AUDIT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${AUDIT_LOG_FILE}</file>
        <encoder>
            <pattern>${AUDIT_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/audit.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- JSON Appender for Structured Logging (ELK Stack) -->
    <appender name="JSON_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/notification-service-json.log</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <message/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "service": "notification-service",
                            "correlationId": "%X{correlationId:-}",
                            "requestId": "%X{requestId:-}",
                            "userId": "%X{userId:-}",
                            "sourceService": "%X{sourceService:-}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/notification-service-json.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- Audit Logger -->
    <logger name="AUDIT" level="INFO" additivity="false">
        <appender-ref ref="AUDIT_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- Spring Boot specific loggers -->
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="org.springframework.graphql" level="INFO"/>
    <logger name="org.springframework.amqp" level="INFO"/>
    <logger name="org.springframework.mail" level="INFO"/>
    
    <!-- Application specific loggers -->
    <logger name="com.ascentbusiness.notification_svc" level="DEBUG"/>
    <logger name="com.ascentbusiness.notification_svc.service.AuditLogService" level="INFO"/>
    <logger name="com.ascentbusiness.notification_svc.config.GraphQLAuditInterceptor" level="INFO"/>
    
    <!-- Database loggers -->
    <logger name="org.hibernate.SQL" level="DEBUG"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>
    
    <!-- RabbitMQ loggers -->
    <logger name="com.rabbitmq" level="INFO"/>
    
    <!-- Profile-specific configurations -->
    <springProfile name="dev">
        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
    
    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
    
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="FILE"/>
            <appender-ref ref="JSON_FILE"/>
        </root>
    </springProfile>
    
    <!-- Default configuration -->
    <springProfile name="!dev &amp; !test &amp; !prod">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
    
</configuration> 