package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.DocumentPermission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for document permission operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentPermissionResponse {
    
    private Boolean success;
    private String message;
    private DocumentPermission permission;
    
    public static DocumentPermissionResponse success(String message, DocumentPermission permission) {
        return DocumentPermissionResponse.builder()
                .success(true)
                .message(message)
                .permission(permission)
                .build();
    }
    
    public static DocumentPermissionResponse success(String message) {
        return DocumentPermissionResponse.builder()
                .success(true)
                .message(message)
                .build();
    }
    
    public static DocumentPermissionResponse error(String message) {
        return DocumentPermissionResponse.builder()
                .success(false)
                .message(message)
                .build();
    }
}
