package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.TaskStatus;
import com.ascentbusiness.dms_svc.enums.WorkflowAction;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;

/**
 * Entity representing an individual task within a workflow instance
 */
@Entity
@Table(name = "workflow_tasks")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowTask extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_instance_id", nullable = false)
    @JsonIgnore
    private WorkflowInstance workflowInstance;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_stage_id", nullable = false)
    @JsonIgnore
    private WorkflowStage workflowStage;

    @Column(name = "task_name", nullable = false, length = 255)
    private String taskName;

    // Assignment details
    @Column(name = "assigned_to_user_id", length = 255)
    private String assignedToUserId;

    @Column(name = "assigned_to_role", length = 255)
    private String assignedToRole;

    @Column(name = "assigned_to_department", length = 255)
    private String assignedToDepartment;

    @Column(name = "assigned_date")
    private LocalDateTime assignedDate;

    // Status and completion
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 50)
    @Builder.Default
    private TaskStatus status = TaskStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(name = "action_taken", length = 50)
    private WorkflowAction actionTaken;

    @Column(name = "completed_date")
    private LocalDateTime completedDate;

    @Column(name = "completed_by_user_id", length = 255)
    private String completedByUserId;

    // Task details
    @Column(name = "priority", nullable = false, length = 20)
    @Builder.Default
    private String priority = "MEDIUM"; // LOW, MEDIUM, HIGH, URGENT

    @Column(name = "due_date")
    private LocalDateTime dueDate;

    @Column(name = "comments", columnDefinition = "TEXT")
    private String comments;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "attachments", columnDefinition = "JSON")
    private JsonNode attachments;

    // Delegation and escalation
    @Column(name = "delegated_to_user_id", length = 255)
    private String delegatedToUserId;

    @Column(name = "delegated_date")
    private LocalDateTime delegatedDate;

    @Column(name = "escalated_to_user_id", length = 255)
    private String escalatedToUserId;

    @Column(name = "escalated_date")
    private LocalDateTime escalatedDate;

    @Column(name = "escalation_reason", length = 255)
    private String escalationReason;

    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    /**
     * Check if this task is currently active
     */
    @Transient
    public boolean isActive() {
        return TaskStatus.PENDING.equals(status) || TaskStatus.IN_PROGRESS.equals(status);
    }

    /**
     * Check if this task is completed
     */
    @Transient
    public boolean isCompleted() {
        return TaskStatus.COMPLETED.equals(status);
    }

    /**
     * Check if this task is overdue
     */
    @Transient
    public boolean isOverdue() {
        return dueDate != null && LocalDateTime.now().isAfter(dueDate) && isActive();
    }

    /**
     * Check if this task has been delegated
     */
    @Transient
    public boolean isDelegated() {
        return TaskStatus.DELEGATED.equals(status) || delegatedToUserId != null;
    }

    /**
     * Check if this task has been escalated
     */
    @Transient
    public boolean isEscalated() {
        return TaskStatus.ESCALATED.equals(status) || escalatedToUserId != null;
    }

    /**
     * Get the current assignee (considering delegation)
     */
    @Transient
    public String getCurrentAssignee() {
        if (delegatedToUserId != null) {
            return delegatedToUserId;
        }
        if (escalatedToUserId != null) {
            return escalatedToUserId;
        }
        return assignedToUserId;
    }

    /**
     * Get the duration of this task in hours
     */
    @Transient
    public long getDurationHours() {
        if (assignedDate == null) return 0;
        LocalDateTime endTime = completedDate != null ? completedDate : LocalDateTime.now();
        return java.time.Duration.between(assignedDate, endTime).toHours();
    }

    /**
     * Get hours remaining until due date
     */
    @Transient
    public long getHoursUntilDue() {
        if (dueDate == null) return Long.MAX_VALUE;
        return java.time.Duration.between(LocalDateTime.now(), dueDate).toHours();
    }

    /**
     * Check if task is approaching due date (within 4 hours)
     */
    @Transient
    public boolean isApproachingDue() {
        return getHoursUntilDue() <= 4 && getHoursUntilDue() > 0;
    }

    /**
     * Get the workflow instance name
     */
    @Transient
    public String getWorkflowInstanceName() {
        return workflowInstance != null ? workflowInstance.getInstanceName() : null;
    }

    /**
     * Get the workflow stage name
     */
    @Transient
    public String getWorkflowStageName() {
        return workflowStage != null ? workflowStage.getStageName() : null;
    }

    /**
     * Get the document name associated with this task
     */
    @Transient
    public String getDocumentName() {
        return workflowInstance != null && workflowInstance.getDocument() != null ? 
               workflowInstance.getDocument().getName() : null;
    }

    /**
     * Check if this task requires comments
     */
    @Transient
    public boolean requiresComments() {
        return workflowStage != null && 
               workflowStage.getWorkflowDefinition() != null &&
               // This would be determined by workflow configuration
               (WorkflowAction.REJECT.equals(actionTaken) || WorkflowAction.REQUEST_CHANGES.equals(actionTaken));
    }

    @Override
    public String toString() {
        return String.format("WorkflowTask{id=%d, name='%s', status=%s, assignee='%s', stage='%s'}", 
                           getId(), taskName, status, getCurrentAssignee(), getWorkflowStageName());
    }
}
