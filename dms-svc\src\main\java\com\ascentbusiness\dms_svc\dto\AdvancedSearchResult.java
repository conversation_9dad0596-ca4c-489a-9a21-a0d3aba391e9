package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Result DTO for advanced search operations.
 * 
 * Contains search results with faceted navigation, suggestions,
 * and performance metrics.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdvancedSearchResult {

    /**
     * Paginated search results
     */
    private SearchDocumentPage documents;

    /**
     * Faceted search results for filtering
     */
    private SearchFacets facets;

    /**
     * Search suggestions for query improvement
     */
    private List<String> suggestions;

    /**
     * Total search time in milliseconds
     */
    private Integer totalTime;

    /**
     * Maximum relevance score in results
     */
    private Float maxScore;

    /**
     * Total number of documents that matched the query (before pagination)
     */
    private Long totalMatches;

    /**
     * Whether the search was truncated due to limits
     */
    private Boolean truncated;

    /**
     * Search query that was executed
     */
    private String executedQuery;

    /**
     * Search type that was used
     */
    private SearchType searchType;
}


