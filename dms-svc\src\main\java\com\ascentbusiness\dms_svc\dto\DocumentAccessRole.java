package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.Permission;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentAccessRole {
    private String role;           // Role name (for role-based permissions) or user ID (for user-based permissions)
    private Permission permission; // Permission level (READ, WRITE, DELETE, ADMIN)
    
    // Constructor for role-based permissions
    public static DocumentAccessRole forRole(String roleName, Permission permission) {
        return new DocumentAccessRole(roleName, permission);
    }
    
    // Constructor for user-based permissions (user ID acts as "role" in GraphQL response)
    public static DocumentAccessRole forUser(String userId, Permission permission) {
        return new DocumentAccessRole(userId, permission);
    }
}
