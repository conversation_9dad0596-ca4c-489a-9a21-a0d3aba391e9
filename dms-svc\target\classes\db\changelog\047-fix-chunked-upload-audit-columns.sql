--liquibase formatted sql

--changeset dms:047-fix-chunked-upload-audit-columns-001
-- Add missing audit columns to chunked_upload_sessions table
ALTER TABLE chunked_upload_sessions 
ADD COLUMN created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN last_modified_date TIM<PERSON><PERSON>MP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN last_modified_by VARCHAR(255);

--changeset dms:047-fix-chunked-upload-audit-columns-002
-- Update existing records to have proper audit values
UPDATE chunked_upload_sessions 
SET created_date = created_at,
    last_modified_date = COALESCE(last_activity_at, created_at),
    last_modified_by = created_by
WHERE created_date IS NULL;

--changeset dms:047-fix-chunked-upload-audit-columns-003
-- Make the audit columns NOT NULL after populating them
ALTER TABLE chunked_upload_sessions 
MODIFY COLUMN created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
MODIFY COLUMN last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
MODIFY COLUMN last_modified_by VARCHAR(255) NOT NULL;

--changeset dms:047-fix-chunked-upload-audit-columns-004
-- Add indexes for the new audit columns
CREATE INDEX idx_chunked_upload_sessions_created_date ON chunked_upload_sessions(created_date);
CREATE INDEX idx_chunked_upload_sessions_last_modified_date ON chunked_upload_sessions(last_modified_date);
CREATE INDEX idx_chunked_upload_sessions_last_modified_by ON chunked_upload_sessions(last_modified_by);

--changeset dms:047-fix-chunked-upload-audit-columns-005
-- Add missing audit columns to chunked_upload_chunks table
ALTER TABLE chunked_upload_chunks 
ADD COLUMN created_by VARCHAR(255),
ADD COLUMN created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN last_modified_by VARCHAR(255),
ADD COLUMN last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

--changeset dms:047-fix-chunked-upload-audit-columns-006
-- Update existing records to have audit data
UPDATE chunked_upload_chunks 
SET created_by = 'system', 
    last_modified_by = 'system',
    created_date = COALESCE(uploaded_at, CURRENT_TIMESTAMP),
    last_modified_date = COALESCE(uploaded_at, CURRENT_TIMESTAMP)
WHERE created_by IS NULL;

--changeset dms:047-fix-chunked-upload-audit-columns-007
-- Make created_by and created_date non-nullable after populating data
ALTER TABLE chunked_upload_chunks 
MODIFY COLUMN created_by VARCHAR(255) NOT NULL,
MODIFY COLUMN created_date TIMESTAMP NOT NULL,
MODIFY COLUMN last_modified_date TIMESTAMP NOT NULL;

--changeset dms:047-fix-chunked-upload-audit-columns-008
-- Add indexes for audit columns
CREATE INDEX idx_chunks_created_by ON chunked_upload_chunks(created_by);
CREATE INDEX idx_chunks_created_date ON chunked_upload_chunks(created_date);
