# Test Case Operations Schema
# Defines GraphQL operations for test case management

# Input Types
input TestCaseSearchInput {
  query: String!
  limit: Int
  category: String
}

# Test Case Types
type TestCategorySummary {
  categoryName: String!
  testCount: Int
  description: String
}

type TestCaseResponse {
  serialNumber: String!
  category: String!
  description: String!
  testSteps: [String!]!
  expectedResults: [String!]!
  priority: String
  status: String
}

type TestCaseCollection {
  testCases: [TestCaseResponse!]!
  totalCount: Int!
  category: String!
}

# Extend Query type to add test case operations
extend type Query {
  # Get overview of all test categories with counts and descriptions
  getAllTestCases: TestCaseOverview!
  
  # Get all available categories
  getAllCategories: [String!]!
  
  # Get test cases by category
  getTestCasesByCategory(category: String!): TestCaseCollection!
  
  # Get individual test case
  getTestCase(category: String!, serialNumber: String!): TestCaseResponse
  
  # Get test case summary
  getTestCasesSummary: TestCaseSummary!
  
  # Search test cases
  searchTestCases(input: TestCaseSearchInput!): TestCaseSearchResult!
  
  # Health check
  testCaseHealthCheck: TestCaseHealthStatus!
  
  # Category-specific operations
  getReadPermissionTestCases: TestCaseCollection!
  getWritePermissionTestCases: TestCaseCollection!
  getDeletePermissionTestCases: TestCaseCollection!
  getAdminPermissionTestCases: TestCaseCollection!
  getCreatorPrivilegesTestCases: TestCaseCollection!
  getMultiRoleTestCases: TestCaseCollection!
  getNoAccessTestCases: TestCaseCollection!
  getSecurityValidationTestCases: TestCaseCollection!
  getPerformanceTestCases: TestCaseCollection!
  getIntegrationTestCases: TestCaseCollection!
  getBoundaryTestsTestCases: TestCaseCollection!
  getErrorHandlingTestCases: TestCaseCollection!
  getStorageProvidersTestCases: TestCaseCollection!
  getAuditLogsTestCases: TestCaseCollection!
  getSearchFilterTestCases: TestCaseCollection!
}

# Response types for test case operations
type TestCaseOverview {
  totalCategories: Int!
  totalTestCases: Int!
  categories: [TestCategorySummary!]!
  availableCategories: [String!]!
}

type TestCaseSummary {
  totalTestCases: Int!
  categoriesCount: Int!
  categories: [TestCategorySummary!]!
}

type TestCaseSearchResult {
  query: String!
  totalResults: Int!
  testCases: [TestCaseResponse!]!
}

type TestCaseHealthStatus {
  status: String!
  service: String!
  timestamp: String!
  availableCategories: Int!
  categories: [String!]!
}
