groups:
  - name: dms_application_alerts
    rules:
      # Application Health Alerts
      - alert: DMSApplicationDown
        expr: up{job="dms-service"} == 0
        for: 1m
        labels:
          severity: critical
          service: dms
          category: availability
        annotations:
          summary: "DMS Application is down"
          description: "DMS service has been down for more than 1 minute"
          runbook_url: "https://wiki.company.com/dms/runbooks/application-down"

      - alert: DMSHighResponseTime
        expr: histogram_quantile(0.95, rate(http_server_requests_seconds_bucket{job="dms-service"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: dms
          category: performance
        annotations:
          summary: "DMS High Response Time"
          description: "95th percentile response time is {{ $value }}s for the last 5 minutes"
          runbook_url: "https://wiki.company.com/dms/runbooks/high-response-time"

      - alert: DMSHighErrorRate
        expr: rate(http_server_requests_seconds_count{job="dms-service",status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          service: dms
          category: errors
        annotations:
          summary: "DMS High Error Rate"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          runbook_url: "https://wiki.company.com/dms/runbooks/high-error-rate"

  - name: dms_system_alerts
    rules:
      # JVM Memory Alerts
      - alert: DMSHighHeapMemoryUsage
        expr: jvm_memory_used_bytes{job="dms-service",area="heap"} / jvm_memory_max_bytes{job="dms-service",area="heap"} > 0.9
        for: 10m
        labels:
          severity: warning
          service: dms
          category: memory
        annotations:
          summary: "DMS High Heap Memory Usage"
          description: "Heap memory usage is {{ $value | humanizePercentage }} for the last 10 minutes"
          runbook_url: "https://wiki.company.com/dms/runbooks/high-memory-usage"

      - alert: DMSCriticalHeapMemoryUsage
        expr: jvm_memory_used_bytes{job="dms-service",area="heap"} / jvm_memory_max_bytes{job="dms-service",area="heap"} > 0.95
        for: 5m
        labels:
          severity: critical
          service: dms
          category: memory
        annotations:
          summary: "DMS Critical Heap Memory Usage"
          description: "Heap memory usage is {{ $value | humanizePercentage }} - immediate action required"
          runbook_url: "https://wiki.company.com/dms/runbooks/critical-memory-usage"

      # CPU Alerts
      - alert: DMSHighCPUUsage
        expr: process_cpu_usage{job="dms-service"} > 0.8
        for: 15m
        labels:
          severity: warning
          service: dms
          category: cpu
        annotations:
          summary: "DMS High CPU Usage"
          description: "CPU usage is {{ $value | humanizePercentage }} for the last 15 minutes"
          runbook_url: "https://wiki.company.com/dms/runbooks/high-cpu-usage"

  - name: dms_database_alerts
    rules:
      # Database Connection Alerts
      - alert: DMSDatabaseConnectionPoolExhausted
        expr: hikaricp_connections_active{job="dms-service"} / hikaricp_connections_max{job="dms-service"} > 0.9
        for: 5m
        labels:
          severity: critical
          service: dms
          category: database
        annotations:
          summary: "DMS Database Connection Pool Exhausted"
          description: "Database connection pool usage is {{ $value | humanizePercentage }}"
          runbook_url: "https://wiki.company.com/dms/runbooks/db-connection-pool"

      - alert: DMSSlowDatabaseQueries
        expr: histogram_quantile(0.95, rate(dms_database_query_duration_seconds_bucket[5m])) > 1
        for: 10m
        labels:
          severity: warning
          service: dms
          category: database
        annotations:
          summary: "DMS Slow Database Queries"
          description: "95th percentile database query time is {{ $value }}s"
          runbook_url: "https://wiki.company.com/dms/runbooks/slow-queries"

      - alert: DMSDatabaseDown
        expr: dms_database_query_count{job="dms-service"} == 0
        for: 5m
        labels:
          severity: critical
          service: dms
          category: database
        annotations:
          summary: "DMS Database Connectivity Issues"
          description: "No database queries recorded for the last 5 minutes"
          runbook_url: "https://wiki.company.com/dms/runbooks/database-down"

  - name: dms_business_alerts
    rules:
      # Document Processing Alerts
      - alert: DMSHighDocumentUploadFailureRate
        expr: rate(dms_document_upload_failure_total[5m]) / rate(dms_document_upload_count_total[5m]) > 0.1
        for: 10m
        labels:
          severity: warning
          service: dms
          category: business
        annotations:
          summary: "DMS High Document Upload Failure Rate"
          description: "Document upload failure rate is {{ $value | humanizePercentage }}"
          runbook_url: "https://wiki.company.com/dms/runbooks/upload-failures"

      - alert: DMSLowCacheHitRatio
        expr: dms_cache_hit_count_total / (dms_cache_hit_count_total + dms_cache_miss_count_total) < 0.5
        for: 15m
        labels:
          severity: warning
          service: dms
          category: performance
        annotations:
          summary: "DMS Low Cache Hit Ratio"
          description: "Cache hit ratio is {{ $value | humanizePercentage }}"
          runbook_url: "https://wiki.company.com/dms/runbooks/low-cache-hit-ratio"

      # Security Alerts
      - alert: DMSHighFailedLoginAttempts
        expr: increase(dms_business_user_login_failed_total[5m]) > 10
        for: 1m
        labels:
          severity: warning
          service: dms
          category: security
        annotations:
          summary: "DMS High Failed Login Attempts"
          description: "{{ $value }} failed login attempts in the last 5 minutes"
          runbook_url: "https://wiki.company.com/dms/runbooks/failed-logins"

      - alert: DMSComplianceViolations
        expr: increase(dms_business_compliance_violations_total[1h]) > 0
        for: 1m
        labels:
          severity: critical
          service: dms
          category: compliance
        annotations:
          summary: "DMS Compliance Violations Detected"
          description: "{{ $value }} compliance violations detected in the last hour"
          runbook_url: "https://wiki.company.com/dms/runbooks/compliance-violations"

  - name: dms_storage_alerts
    rules:
      # Storage Health Alerts
      - alert: DMSStorageHighUtilization
        expr: dms_business_storage_quota_utilization > 90
        for: 5m
        labels:
          severity: warning
          service: dms
          category: storage
        annotations:
          summary: "DMS Storage High Utilization"
          description: "Storage utilization is {{ $value }}%"
          runbook_url: "https://wiki.company.com/dms/runbooks/storage-utilization"

      - alert: DMSStorageCriticalUtilization
        expr: dms_business_storage_quota_utilization > 95
        for: 1m
        labels:
          severity: critical
          service: dms
          category: storage
        annotations:
          summary: "DMS Storage Critical Utilization"
          description: "Storage utilization is {{ $value }}% - immediate action required"
          runbook_url: "https://wiki.company.com/dms/runbooks/storage-critical"

      - alert: DMSSlowStorageOperations
        expr: histogram_quantile(0.95, rate(dms_business_storage_operation_duration_seconds_bucket[5m])) > 5
        for: 10m
        labels:
          severity: warning
          service: dms
          category: storage
        annotations:
          summary: "DMS Slow Storage Operations"
          description: "95th percentile storage operation time is {{ $value }}s"
          runbook_url: "https://wiki.company.com/dms/runbooks/slow-storage"

  - name: dms_elasticsearch_alerts
    rules:
      # Elasticsearch Health Alerts
      - alert: DMSElasticsearchDown
        expr: elasticsearch_cluster_health_status{job="elasticsearch"} == 0
        for: 2m
        labels:
          severity: critical
          service: dms
          category: search
        annotations:
          summary: "DMS Elasticsearch Cluster Down"
          description: "Elasticsearch cluster is not responding"
          runbook_url: "https://wiki.company.com/dms/runbooks/elasticsearch-down"

      - alert: DMSElasticsearchClusterRed
        expr: elasticsearch_cluster_health_status{job="elasticsearch"} == 2
        for: 5m
        labels:
          severity: critical
          service: dms
          category: search
        annotations:
          summary: "DMS Elasticsearch Cluster Status Red"
          description: "Elasticsearch cluster status is RED - data loss possible"
          runbook_url: "https://wiki.company.com/dms/runbooks/elasticsearch-red"

      - alert: DMSSlowSearchQueries
        expr: histogram_quantile(0.95, rate(dms_business_search_query_duration_seconds_bucket[5m])) > 2
        for: 10m
        labels:
          severity: warning
          service: dms
          category: search
        annotations:
          summary: "DMS Slow Search Queries"
          description: "95th percentile search query time is {{ $value }}s"
          runbook_url: "https://wiki.company.com/dms/runbooks/slow-search"

  - name: dms_graphql_alerts
    rules:
      # GraphQL Performance Alerts
      - alert: DMSSlowGraphQLOperations
        expr: histogram_quantile(0.95, rate(dms_business_graphql_operation_duration_seconds_bucket[5m])) > 3
        for: 10m
        labels:
          severity: warning
          service: dms
          category: api
        annotations:
          summary: "DMS Slow GraphQL Operations"
          description: "95th percentile GraphQL operation time is {{ $value }}s"
          runbook_url: "https://wiki.company.com/dms/runbooks/slow-graphql"

      - alert: DMSHighGraphQLErrorRate
        expr: rate(graphql_request_total{job="dms-service",outcome="error"}[5m]) / rate(graphql_request_total{job="dms-service"}[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: dms
          category: api
        annotations:
          summary: "DMS High GraphQL Error Rate"
          description: "GraphQL error rate is {{ $value | humanizePercentage }}"
          runbook_url: "https://wiki.company.com/dms/runbooks/graphql-errors"

  - name: dms_health_alerts
    rules:
      # Health Check Alerts
      - alert: DMSHealthCheckFailing
        expr: spring_boot_actuator_health_status{job="dms-service"} != 1
        for: 3m
        labels:
          severity: critical
          service: dms
          category: health
        annotations:
          summary: "DMS Health Check Failing"
          description: "DMS health check has been failing for more than 3 minutes"
          runbook_url: "https://wiki.company.com/dms/runbooks/health-check-failing"
