--liquibase formatted sql

--changeset dms:007-create-document-versions-and-access-roles

-- Create document_versions table
CREATE TABLE document_versions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    version_number INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    storage_path VARCHAR(500) NOT NULL,
    status ENUM('ACTIVE', 'HISTORICAL', 'DELETED') NOT NULL DEFAULT 'ACTIVE',
    file_content LONGBLOB,
    checksum VARCHAR(128),
    version_notes TEXT,
    is_current BOOLEAN NOT NULL DEFAULT FALSE,
    created_by VA<PERSON>HAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    UNIQUE KEY uk_document_version (document_id, version_number)
);

-- Create document_access_roles table
CREATE TABLE document_access_roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    role_name VARCHAR(100) NOT NULL,
    user_id VARCHAR(100),
    permission_type ENUM('READ', 'WRITE', 'DELETE', 'ADMIN') NOT NULL,
    granted_by VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    notes TEXT,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX idx_document_versions_version_number ON document_versions(version_number);
CREATE INDEX idx_document_versions_status ON document_versions(status);
CREATE INDEX idx_document_versions_is_current ON document_versions(is_current);
CREATE INDEX idx_document_versions_created_date ON document_versions(created_date);

CREATE INDEX idx_document_access_roles_document_id ON document_access_roles(document_id);
CREATE INDEX idx_document_access_roles_role_name ON document_access_roles(role_name);
CREATE INDEX idx_document_access_roles_user_id ON document_access_roles(user_id);
CREATE INDEX idx_document_access_roles_permission_type ON document_access_roles(permission_type);
CREATE INDEX idx_document_access_roles_is_active ON document_access_roles(is_active);
CREATE INDEX idx_document_access_roles_expires_at ON document_access_roles(expires_at);
