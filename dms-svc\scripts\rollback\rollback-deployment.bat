@echo off
REM ============================================================================
REM DMS Deployment Rollback Script
REM ============================================================================
REM This script performs automated rollback of DMS application deployments
REM including JAR files, configuration, and service restart procedures.
REM
REM Features:
REM - Automated backup and restore of application files
REM - Service health monitoring during rollback
REM - Rollback validation and verification
REM - Comprehensive logging and reporting
REM - Emergency rollback capabilities
REM
REM Prerequisites:
REM - Administrative privileges for service management
REM - Access to backup directories
REM - Network connectivity for health checks
REM ============================================================================

setlocal enabledelayedexpansion

REM Set script variables
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..\..
set BACKUP_DIR=%PROJECT_ROOT%\backups\deployments
set LOG_FILE=%PROJECT_ROOT%\logs\rollback-deployment.log
set SERVICE_NAME=dms-service
set SERVICE_PORT=9092
set HEALTH_ENDPOINT=http://localhost:%SERVICE_PORT%/actuator/health
set TIMESTAMP=%DATE:~-4,4%%DATE:~-10,2%%DATE:~-7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

echo ============================================================================
echo DMS Deployment Rollback Script
echo ============================================================================
echo Timestamp: %TIMESTAMP%
echo Project Root: %PROJECT_ROOT%
echo Backup Directory: %BACKUP_DIR%
echo Log File: %LOG_FILE%
echo ============================================================================

REM Parse command line arguments
set TARGET_VERSION=%1
set CONFIRM_FLAG=%2
set FORCE_FLAG=%3

if "%TARGET_VERSION%"=="" (
    echo ERROR: Target version not specified
    echo Usage: %~nx0 ^<version^> [--confirm] [--force]
    echo Example: %~nx0 1.2.3 --confirm
    goto :show_help
)

REM Create necessary directories
if not exist "%PROJECT_ROOT%\logs" mkdir "%PROJECT_ROOT%\logs"
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

REM Initialize logging
echo [%TIME%] Starting deployment rollback to version %TARGET_VERSION% > "%LOG_FILE%"
echo [%TIME%] Starting deployment rollback to version %TARGET_VERSION%

REM Validate target version exists
set TARGET_BACKUP=%BACKUP_DIR%\%TARGET_VERSION%
if not exist "%TARGET_BACKUP%" (
    echo [ERROR] Backup for version %TARGET_VERSION% not found: %TARGET_BACKUP%
    echo [ERROR] Backup for version %TARGET_VERSION% not found: %TARGET_BACKUP% >> "%LOG_FILE%"
    goto :error_exit
)

REM Safety confirmation
if not "%CONFIRM_FLAG%"=="--confirm" (
    echo.
    echo WARNING: This will rollback the DMS service to version %TARGET_VERSION%
    echo Current service will be stopped and replaced with the backup version.
    echo.
    set /p CONFIRM="Are you sure you want to proceed? (yes/no): "
    if not "!CONFIRM!"=="yes" (
        echo [INFO] Rollback cancelled by user
        echo [INFO] Rollback cancelled by user >> "%LOG_FILE%"
        goto :end
    )
)

REM Pre-rollback health check
echo [%TIME%] Performing pre-rollback health check...
echo [%TIME%] Performing pre-rollback health check... >> "%LOG_FILE%"

curl -f -s "%HEALTH_ENDPOINT%" > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [INFO] Service is currently running and healthy
    echo [INFO] Service is currently running and healthy >> "%LOG_FILE%"
    set SERVICE_WAS_RUNNING=true
) else (
    echo [WARNING] Service is not responding to health checks
    echo [WARNING] Service is not responding to health checks >> "%LOG_FILE%"
    set SERVICE_WAS_RUNNING=false
)

REM Create current state backup
echo [%TIME%] Creating backup of current deployment...
echo [%TIME%] Creating backup of current deployment... >> "%LOG_FILE%"

set CURRENT_BACKUP=%BACKUP_DIR%\rollback-backup-%TIMESTAMP%
mkdir "%CURRENT_BACKUP%" 2>nul

REM Backup current JAR file
if exist "%PROJECT_ROOT%\target\dms-svc-*.jar" (
    copy "%PROJECT_ROOT%\target\dms-svc-*.jar" "%CURRENT_BACKUP%\" >> "%LOG_FILE%" 2>&1
)

REM Backup current configuration
if exist "%PROJECT_ROOT%\src\main\resources\application.properties" (
    copy "%PROJECT_ROOT%\src\main\resources\application.properties" "%CURRENT_BACKUP%\application.properties" >> "%LOG_FILE%" 2>&1
)

REM Backup current logs
if exist "%PROJECT_ROOT%\logs\dms-service.log" (
    copy "%PROJECT_ROOT%\logs\dms-service.log" "%CURRENT_BACKUP%\dms-service.log" >> "%LOG_FILE%" 2>&1
)

echo [INFO] Current deployment backed up to: %CURRENT_BACKUP%
echo [INFO] Current deployment backed up to: %CURRENT_BACKUP% >> "%LOG_FILE%"

REM Stop current service
echo [%TIME%] Stopping current service...
echo [%TIME%] Stopping current service... >> "%LOG_FILE%"

REM Find and kill Java processes running DMS
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "dms-svc"') do (
    echo [INFO] Stopping DMS process: %%i
    echo [INFO] Stopping DMS process: %%i >> "%LOG_FILE%"
    taskkill /pid %%i /f >> "%LOG_FILE%" 2>&1
)

REM Wait for service to stop
echo [%TIME%] Waiting for service to stop...
timeout /t 10 /nobreak > nul

REM Verify service is stopped
curl -f -s "%HEALTH_ENDPOINT%" > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [WARNING] Service still responding, forcing shutdown...
    echo [WARNING] Service still responding, forcing shutdown... >> "%LOG_FILE%"
    
    REM Force kill any remaining Java processes
    taskkill /f /im java.exe >> "%LOG_FILE%" 2>&1
    timeout /t 5 /nobreak > nul
)

echo [INFO] Service stopped successfully
echo [INFO] Service stopped successfully >> "%LOG_FILE%"

REM Restore files from backup
echo [%TIME%] Restoring files from version %TARGET_VERSION%...
echo [%TIME%] Restoring files from version %TARGET_VERSION%... >> "%LOG_FILE%"

REM Restore JAR file
if exist "%TARGET_BACKUP%\dms-svc-*.jar" (
    copy "%TARGET_BACKUP%\dms-svc-*.jar" "%PROJECT_ROOT%\target\" >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo [ERROR] Failed to restore JAR file
        echo [ERROR] Failed to restore JAR file >> "%LOG_FILE%"
        goto :rollback_failed
    )
    echo [INFO] JAR file restored successfully
    echo [INFO] JAR file restored successfully >> "%LOG_FILE%"
)

REM Restore configuration
if exist "%TARGET_BACKUP%\application.properties" (
    copy "%TARGET_BACKUP%\application.properties" "%PROJECT_ROOT%\src\main\resources\application.properties" >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo [ERROR] Failed to restore configuration
        echo [ERROR] Failed to restore configuration >> "%LOG_FILE%"
        goto :rollback_failed
    )
    echo [INFO] Configuration restored successfully
    echo [INFO] Configuration restored successfully >> "%LOG_FILE%"
)

REM Start service with restored version
echo [%TIME%] Starting service with version %TARGET_VERSION%...
echo [%TIME%] Starting service with version %TARGET_VERSION%... >> "%LOG_FILE%"

cd /d "%PROJECT_ROOT%"

REM Find the restored JAR file
for %%f in (target\dms-svc-*.jar) do set JAR_FILE=%%f

if not defined JAR_FILE (
    echo [ERROR] No JAR file found to start
    echo [ERROR] No JAR file found to start >> "%LOG_FILE%"
    goto :rollback_failed
)

REM Start the service
start "DMS Service" java -jar "%JAR_FILE%" >> "%LOG_FILE%" 2>&1

echo [INFO] Service startup initiated
echo [INFO] Service startup initiated >> "%LOG_FILE%"

REM Wait for service to start
echo [%TIME%] Waiting for service to start...
echo [%TIME%] Waiting for service to start... >> "%LOG_FILE%"

set STARTUP_TIMEOUT=120
set STARTUP_COUNTER=0

:wait_for_startup
timeout /t 5 /nobreak > nul
set /a STARTUP_COUNTER+=5

curl -f -s "%HEALTH_ENDPOINT%" > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [INFO] Service is responding to health checks
    echo [INFO] Service is responding to health checks >> "%LOG_FILE%"
    goto :validate_rollback
)

if %STARTUP_COUNTER% geq %STARTUP_TIMEOUT% (
    echo [ERROR] Service failed to start within %STARTUP_TIMEOUT% seconds
    echo [ERROR] Service failed to start within %STARTUP_TIMEOUT% seconds >> "%LOG_FILE%"
    goto :rollback_failed
)

echo [INFO] Waiting for service startup... (%STARTUP_COUNTER%/%STARTUP_TIMEOUT% seconds)
goto :wait_for_startup

:validate_rollback
echo [%TIME%] Validating rollback success...
echo [%TIME%] Validating rollback success... >> "%LOG_FILE%"

REM Test health endpoint
curl -f -s "%HEALTH_ENDPOINT%" > health_check.json 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Health check failed
    echo [ERROR] Health check failed >> "%LOG_FILE%"
    goto :rollback_failed
)

REM Parse health check response
findstr "UP" health_check.json > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Service health check indicates DOWN status
    echo [ERROR] Service health check indicates DOWN status >> "%LOG_FILE%"
    goto :rollback_failed
)

del health_check.json 2>nul

REM Test basic functionality
echo [%TIME%] Testing basic functionality...
curl -f -s "%HEALTH_ENDPOINT%/db" > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [WARNING] Database health check failed
    echo [WARNING] Database health check failed >> "%LOG_FILE%"
)

curl -f -s "%HEALTH_ENDPOINT%/redis" > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [WARNING] Redis health check failed
    echo [WARNING] Redis health check failed >> "%LOG_FILE%"
)

echo [SUCCESS] Rollback to version %TARGET_VERSION% completed successfully!
echo [SUCCESS] Rollback to version %TARGET_VERSION% completed successfully! >> "%LOG_FILE%"

REM Generate rollback report
echo [%TIME%] Generating rollback report...
(
echo Deployment Rollback Report
echo ==========================
echo Timestamp: %TIMESTAMP%
echo Target Version: %TARGET_VERSION%
echo Rollback Status: SUCCESS
echo Service Status: RUNNING
echo Health Check: PASSED
echo Current Backup: %CURRENT_BACKUP%
echo Restored From: %TARGET_BACKUP%
echo.
echo Validation Results:
echo - Service Health: PASSED
echo - Database Health: %DB_HEALTH_STATUS%
echo - Redis Health: %REDIS_HEALTH_STATUS%
echo.
echo Next Steps:
echo 1. Monitor service performance
echo 2. Verify user functionality
echo 3. Update incident documentation
echo 4. Plan forward fix if needed
) > "%PROJECT_ROOT%\logs\rollback-report-%TIMESTAMP%.txt"

echo [INFO] Rollback report generated: %PROJECT_ROOT%\logs\rollback-report-%TIMESTAMP%.txt
echo [INFO] Rollback report generated: %PROJECT_ROOT%\logs\rollback-report-%TIMESTAMP%.txt >> "%LOG_FILE%"

goto :end

:rollback_failed
echo [ERROR] Rollback failed! Attempting to restore previous state...
echo [ERROR] Rollback failed! Attempting to restore previous state... >> "%LOG_FILE%"

REM Attempt to restore from current backup
if exist "%CURRENT_BACKUP%" (
    echo [INFO] Restoring from current backup: %CURRENT_BACKUP%
    copy "%CURRENT_BACKUP%\dms-svc-*.jar" "%PROJECT_ROOT%\target\" >> "%LOG_FILE%" 2>&1
    copy "%CURRENT_BACKUP%\application.properties" "%PROJECT_ROOT%\src\main\resources\" >> "%LOG_FILE%" 2>&1
    
    REM Restart service
    cd /d "%PROJECT_ROOT%"
    for %%f in (target\dms-svc-*.jar) do set JAR_FILE=%%f
    start "DMS Service" java -jar "%JAR_FILE%" >> "%LOG_FILE%" 2>&1
)

echo [ERROR] Rollback failed. Manual intervention required.
echo [ERROR] Check log file: %LOG_FILE%
goto :error_exit

:show_help
echo.
echo DMS Deployment Rollback Script
echo.
echo Usage: %~nx0 ^<version^> [--confirm] [--force]
echo.
echo Parameters:
echo   version    - Target version to rollback to (required)
echo   --confirm  - Skip confirmation prompt
echo   --force    - Force rollback even if service is unhealthy
echo.
echo Examples:
echo   %~nx0 1.2.3                    # Interactive rollback
echo   %~nx0 1.2.3 --confirm          # Automatic rollback
echo   %~nx0 1.2.3 --confirm --force  # Force rollback
echo.
echo Available Versions:
if exist "%BACKUP_DIR%" (
    dir /b "%BACKUP_DIR%" 2>nul
)
echo.
goto :end

:error_exit
echo [%TIME%] Script execution failed. Check log file: %LOG_FILE%
exit /b 1

:end
echo [%TIME%] Rollback script completed.
endlocal
pause
