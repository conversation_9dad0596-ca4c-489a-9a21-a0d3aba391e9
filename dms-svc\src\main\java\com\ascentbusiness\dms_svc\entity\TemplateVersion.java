package com.ascentbusiness.dms_svc.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/**
 * Entity representing a version of a document template
 */
@Entity
@Table(name = "template_versions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateVersion extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", nullable = false)
    @JsonIgnore
    private DocumentTemplate template;

    @Column(name = "version_number", nullable = false)
    private Integer versionNumber;

    @Column(name = "version_name", length = 255)
    private String versionName;

    // Version content
    @Lob
    @Column(name = "template_content", columnDefinition = "LONGBLOB")
    private byte[] templateContent;

    @Column(name = "template_format", nullable = false, length = 50)
    private String templateFormat;

    @Column(name = "mime_type", nullable = false, length = 100)
    private String mimeType;

    @Column(name = "file_size")
    private Long fileSize;

    // Version configuration stored as JSON
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "configuration_json", columnDefinition = "JSON")
    private JsonNode configurationJson;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "field_definitions", columnDefinition = "JSON")
    private JsonNode fieldDefinitions;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "validation_rules", columnDefinition = "JSON")
    private JsonNode validationRules;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "default_values", columnDefinition = "JSON")
    private JsonNode defaultValues;

    // Version metadata
    @Builder.Default
    @Column(name = "is_current", nullable = false)
    private Boolean isCurrent = false;

    @Column(name = "change_summary", columnDefinition = "TEXT")
    private String changeSummary;

    @Column(name = "change_reason", length = 255)
    private String changeReason;

    @Column(name = "created_by", nullable = false, length = 255)
    private String createdBy;

    /**
     * Check if this is the current version
     */
    @Transient
    public boolean isCurrentVersion() {
        return isCurrent != null && isCurrent;
    }

    /**
     * Get template content as Base64 string
     */
    @Transient
    public String getTemplateContentBase64() {
        if (templateContent != null && templateContent.length > 0) {
            return java.util.Base64.getEncoder().encodeToString(templateContent);
        }
        return null;
    }

    /**
     * Set template content from Base64 string
     */
    public void setTemplateContentBase64(String base64Content) {
        if (base64Content != null && !base64Content.trim().isEmpty()) {
            try {
                this.templateContent = java.util.Base64.getDecoder().decode(base64Content);
                this.fileSize = (long) this.templateContent.length;
            } catch (IllegalArgumentException e) {
                this.templateContent = null;
                this.fileSize = null;
            }
        } else {
            this.templateContent = null;
            this.fileSize = null;
        }
    }

    /**
     * Get the template name
     */
    @Transient
    public String getTemplateName() {
        return template != null ? template.getName() : null;
    }

    /**
     * Get version display name
     */
    @Transient
    public String getVersionDisplayName() {
        if (versionName != null && !versionName.trim().isEmpty()) {
            return String.format("v%d - %s", versionNumber, versionName);
        }
        return String.format("v%d", versionNumber);
    }

    /**
     * Check if this version has dynamic fields
     */
    @Transient
    public boolean hasDynamicFields() {
        return fieldDefinitions != null && !fieldDefinitions.isNull() && fieldDefinitions.size() > 0;
    }

    /**
     * Get the number of dynamic fields in this version
     */
    @Transient
    public int getFieldCount() {
        if (fieldDefinitions == null || fieldDefinitions.isNull()) return 0;
        return fieldDefinitions.size();
    }

    @Override
    public String toString() {
        return String.format("TemplateVersion{id=%d, template='%s', version=%d, current=%s}", 
                           getId(), getTemplateName(), versionNumber, isCurrent);
    }
}
