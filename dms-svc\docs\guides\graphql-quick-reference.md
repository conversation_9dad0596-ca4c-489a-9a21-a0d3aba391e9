# GraphQL Quick Reference Guide

## Endpoint
```
POST /graphql
Content-Type: application/json
Authorization: Bearer <jwt-token>
```

## Common Query Patterns

### Basic Query Structure
```graphql
query OperationName($variable: Type!) {
  fieldName(argument: $variable) {
    field1
    field2
    nestedObject {
      nestedField
    }
  }
}
```

### Basic Mutation Structure
```graphql
mutation OperationName($input: InputType!) {
  mutationName(input: $input) {
    success
    message
    result {
      id
      field1
      field2
    }
  }
}
```

## Test Case Operations

### Get All Test Cases
```graphql
query {
  getAllTestCases {
    totalCategories
    totalTestCases
    availableCategories
  }
}
```

### Search Test Cases
```graphql
query SearchTestCases($query: String!, $limit: Int) {
  searchTestCases(input: { query: $query, limit: $limit }) {
    totalResults
    testCases {
      serialNumber
      category
      description
      priority
      status
    }
  }
}
```

### Create Test Case
```graphql
mutation CreateTestCase($input: TestCaseCreateInput!) {
  createTestCase(input: $input) {
    success
    message
    testCase {
      id
      serialNumber
      category
    }
  }
}
```

## Webhook Operations

### Get Webhook Statistics
```graphql
query {
  getWebhookEndpointStatistics {
    totalEndpoints
    activeEndpoints
    successRate
    totalDeliveries
  }
}
```

### Create Webhook Endpoint
```graphql
mutation CreateWebhook($input: WebhookEndpointCreateInput!) {
  createWebhookEndpoint(input: $input) {
    success
    message
    endpoint {
      id
      name
      url
      isActive
    }
  }
}
```

### Get Failed Deliveries
```graphql
query {
  getFailedWebhookDeliveries {
    id
    deliveryAttempt
    httpStatusCode
    errorMessage
    attemptedDate
    webhookEndpoint {
      name
      url
    }
  }
}
```

## Workflow Operations

### Get Workflow Statistics
```graphql
query {
  getWorkflowManagementStatistics {
    totalActive
    totalCompleted
    averageCompletionTime
    statusDistribution {
      status
      count
      percentage
    }
  }
}
```

### Get Workflow Definitions (Paginated)
```graphql
query GetWorkflows($page: Int!, $size: Int!) {
  getWorkflowDefinitions(pagination: { page: $page, size: $size }) {
    content {
      id
      name
      workflowType
      isActive
      version
    }
    totalElements
    totalPages
    hasNext
  }
}
```

### Start Workflow
```graphql
mutation StartWorkflow($input: StartWorkflowInput!) {
  startWorkflow(input: $input) {
    success
    message
    workflowInstance {
      id
      status
      startedDate
      currentStage
    }
  }
}
```

## Tracing Operations

### Test Tracing
```graphql
query TestTracing($operationName: String!) {
  testTracing(input: { operationName: $operationName }) {
    message
    traceId
    spanId
    timestamp
    status
    duration
  }
}
```

### Test Nested Spans
```graphql
query TestNestedSpans($input: NestedSpanInput!) {
  testNestedSpans(input: $input) {
    message
    traceId
    totalSpans
    executionTime
    childSpans {
      spanId
      operationName
      duration
    }
  }
}
```

## System Operations

### Health Check
```graphql
query {
  getSystemHealth {
    status
    timestamp
    components {
      name
      status
      details
    }
  }
}
```

### Test Case Health Check
```graphql
query {
  testCaseHealthCheck {
    status
    service
    availableCategories
    totalTestCases
    lastUpdated
  }
}
```

## Multipart Upload Examples

### Document Upload (JavaScript)
```javascript
const formData = new FormData();

// GraphQL operation
const operations = {
  query: `
    mutation UploadDocument($input: EnhancedDocumentUploadInput!) {
      uploadDocumentEnhanced(input: $input) {
        success
        uploadId
        fileName
        processingStatus
      }
    }
  `,
  variables: {
    input: {
      file: null,
      name: "document.pdf",
      description: "Important document"
    }
  }
};

// Map file to variable
const map = {
  "0": ["variables.input.file"]
};

formData.append('operations', JSON.stringify(operations));
formData.append('map', JSON.stringify(map));
formData.append('0', fileBlob, 'document.pdf');

fetch('/graphql', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
});
```

### Bulk Upload
```javascript
const formData = new FormData();

const operations = {
  query: `
    mutation BulkUpload($input: BulkDocumentUploadInput!) {
      bulkUploadDocuments(input: $input) {
        totalFiles
        successfulUploads
        failedUploads
        results {
          fileName
          success
          uploadId
        }
      }
    }
  `,
  variables: {
    input: {
      files: [null, null],
      scannerType: "CLAMAV"
    }
  }
};

const map = {
  "0": ["variables.input.files.0"],
  "1": ["variables.input.files.1"]
};

formData.append('operations', JSON.stringify(operations));
formData.append('map', JSON.stringify(map));
formData.append('0', file1, 'document1.pdf');
formData.append('1', file2, 'document2.pdf');
```

## Error Handling

### Standard Error Response
```json
{
  "errors": [
    {
      "message": "Validation failed",
      "locations": [{"line": 2, "column": 3}],
      "path": ["fieldName"],
      "extensions": {
        "code": "VALIDATION_ERROR",
        "field": "input.name"
      }
    }
  ],
  "data": null
}
```

### Common Error Codes
- `VALIDATION_ERROR`: Input validation failed
- `AUTHENTICATION_ERROR`: Invalid or missing JWT token
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_ERROR`: Server error

## Variables and Input Types

### Common Input Patterns
```graphql
# Search Input
input TestCaseSearchInput {
  query: String!
  category: String
  priority: Priority
  status: TestCaseStatus
  limit: Int = 10
  offset: Int = 0
}

# Create Input
input TestCaseCreateInput {
  category: String!
  description: String!
  testSteps: [String!]!
  expectedResults: [String!]!
  priority: Priority = MEDIUM
  tags: [String!]
}

# Update Input
input TestCaseUpdateInput {
  description: String
  testSteps: [String!]
  expectedResults: [String!]
  priority: Priority
  status: TestCaseStatus
}
```

### Pagination Input
```graphql
input PaginationInput {
  page: Int! = 0
  size: Int! = 20
  sort: String = "id"
  direction: SortDirection = ASC
}
```

## Performance Tips

1. **Request Only Needed Fields**: GraphQL allows selecting specific fields
2. **Use Pagination**: For large result sets, always use pagination
3. **Batch Operations**: Combine multiple queries in a single request
4. **Cache Results**: Implement client-side caching for frequently accessed data
5. **Monitor Performance**: Use the performance test suite to validate changes

## Testing

### Run Performance Tests
```bash
# Basic performance tests
mvn test -Dtest=GraphQLPerformanceTest

# Load tests (requires system property)
mvn test -Drun.load.tests=true -Dtest=GraphQLLoadTest
```

### GraphQL Playground
Access GraphQL Playground in development mode:
```
http://localhost:8080/graphql
```

## Migration from REST

### REST to GraphQL Mapping
| REST Endpoint | GraphQL Operation |
|---------------|-------------------|
| `GET /api/test-cases` | `query { getAllTestCases }` |
| `POST /api/test-cases/search` | `query { searchTestCases }` |
| `POST /api/test-cases` | `mutation { createTestCase }` |
| `PUT /api/test-cases/{id}` | `mutation { updateTestCase }` |
| `GET /api/webhooks/statistics` | `query { getWebhookEndpointStatistics }` |
| `GET /api/workflows/statistics` | `query { getWorkflowManagementStatistics }` |

### Authentication Migration
- REST: `Authorization: Bearer <token>`
- GraphQL: Same - `Authorization: Bearer <token>`

### Content Type Migration
- REST: Various (`application/json`, `multipart/form-data`)
- GraphQL: Always `application/json` (except multipart uploads)

## Support Resources

- **Schema Documentation**: Available in GraphQL Playground
- **API Guide**: See `graphql-migration-api-guide.md`
- **Performance Guide**: See performance test results
- **Examples**: Check integration tests for usage examples
