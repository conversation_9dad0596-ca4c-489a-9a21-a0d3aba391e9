package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.DispositionAction;
import com.ascentbusiness.dms_svc.enums.RetentionPeriodUnit;
import lombok.Data;

import java.util.List;

/**
 * Input DTO for creating/updating retention policies
 */
@Data
public class RetentionPolicyInput {
    
    private String name;
    private String description;
    private String scope;
    private Integer retentionPeriod;
    private RetentionPeriodUnit retentionPeriodUnit;
    private DispositionAction dispositionAction;
    private Boolean isActive;
    private Boolean allowLegalHold;
    private Boolean autoApply;
    private Integer priority;
    private String triggerEvent;
    private String businessJustification;
    private String legalBasis;
    private Integer reviewFrequencyMonths;
    private Integer notificationBeforeDays;
    
    // Assignment information
    private List<RetentionPolicyAssignmentInput> assignments;
}
