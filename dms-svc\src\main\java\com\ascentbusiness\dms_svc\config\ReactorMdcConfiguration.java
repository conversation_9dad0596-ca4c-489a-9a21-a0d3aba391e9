package com.ascentbusiness.dms_svc.config;

import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Hooks;
import reactor.core.publisher.Operators;
import reactor.util.context.Context;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.Map;

/**
 * Configuration to enable automatic MDC (Mapped Diagnostic Context) propagation
 * in reactive streams. This ensures that correlation IDs and other MDC values
 * are properly propagated throughout the entire reactive chain.
 */
@Configuration
public class ReactorMdcConfiguration {

    private static final String MDC_CONTEXT_KEY = "MDC_CONTEXT";

    @PostConstruct
    public void enableMdcPropagation() {
        // Enable automatic MDC propagation for all reactive operations
        Hooks.onEachOperator(MDC_CONTEXT_KEY, Operators.lift((scannable, coreSubscriber) -> {
            return new MdcPropagatingSubscriber<>(coreSubscriber);
        }));
    }

    @PreDestroy
    public void disableMdcPropagation() {
        // Clean up the hook when the application shuts down
        Hooks.resetOnEachOperator(MDC_CONTEXT_KEY);
    }

    /**
     * Custom subscriber that propagates MDC context throughout the reactive chain
     */
    private static class MdcPropagatingSubscriber<T> implements reactor.core.CoreSubscriber<T> {
        private final reactor.core.CoreSubscriber<T> actual;
        private final Map<String, String> mdcContext;

        public MdcPropagatingSubscriber(reactor.core.CoreSubscriber<T> actual) {
            this.actual = actual;
            // Capture the current MDC context
            this.mdcContext = MDC.getCopyOfContextMap();
        }

        @Override
        public void onSubscribe(org.reactivestreams.Subscription s) {
            // Set MDC context when subscription starts
            if (mdcContext != null) {
                MDC.setContextMap(mdcContext);
            }
            actual.onSubscribe(s);
        }

        @Override
        public void onNext(T t) {
            // Set MDC context for each emission
            if (mdcContext != null) {
                MDC.setContextMap(mdcContext);
            }
            actual.onNext(t);
        }

        @Override
        public void onError(Throwable t) {
            // Set MDC context for error handling
            if (mdcContext != null) {
                MDC.setContextMap(mdcContext);
            }
            actual.onError(t);
        }

        @Override
        public void onComplete() {
            // Set MDC context for completion
            if (mdcContext != null) {
                MDC.setContextMap(mdcContext);
            }
            actual.onComplete();
        }

        @Override
        public Context currentContext() {
            // Store MDC context in the reactive context for downstream access
            Context context = actual.currentContext();
            if (mdcContext != null) {
                context = context.put(MDC_CONTEXT_KEY, mdcContext);
            }
            return context;
        }
    }
}
