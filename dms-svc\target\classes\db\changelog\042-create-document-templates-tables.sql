--liquibase formatted sql

--changeset system:042-create-document-templates-tables
--comment: Create comprehensive document templates system tables

-- Create document_templates table
CREATE TABLE document_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL, -- FORM, REPORT, CONTRACT, POLICY, MEMO, LETTER, CUSTOM
    template_type VARCHAR(50) NOT NULL DEFAULT 'DOCUMENT', -- DOCUMENT, FORM, STRUCTURED
    
    -- Template content and structure
    template_content LONGBLOB, -- Template file content (Word, PDF, etc.)
    template_format VARCHAR(50) NOT NULL, -- DOCX, PDF, HTML, MARKDOWN, JSON
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT,
    
    -- Template configuration
    configuration_json JSON,
    field_definitions JSON, -- Defines fillable fields and their types
    validation_rules JSON, -- Validation rules for template fields
    default_values JSON, -- Default values for template fields
    
    -- Template metadata
    version VARCHAR(50) NOT NULL DEFAULT '1.0',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_system_template BOOLEAN NOT NULL DEFAULT FALSE,
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Usage and analytics
    usage_count INTEGER DEFAULT 0,
    last_used_date TIMESTAMP,
    
    -- Access control
    created_by VARCHAR(255) NOT NULL,
    owner_user_id VARCHAR(255) NOT NULL,
    owner_department VARCHAR(255),
    access_level VARCHAR(50) NOT NULL DEFAULT 'PRIVATE', -- PRIVATE, DEPARTMENT, PUBLIC, RESTRICTED, SYSTEM
    
    -- Approval and publishing
    approval_status VARCHAR(50) NOT NULL DEFAULT 'DRAFT', -- DRAFT, PENDING_APPROVAL, APPROVED, REJECTED, PUBLISHED
    approved_by VARCHAR(255),
    approved_date TIMESTAMP,
    published_date TIMESTAMP,
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(255),
    
    -- Constraints
    UNIQUE KEY uk_template_name_version (name, version),
    INDEX idx_template_category (category),
    INDEX idx_template_type (template_type),
    INDEX idx_template_active (is_active),
    INDEX idx_template_public (is_public),
    INDEX idx_template_owner (owner_user_id),
    INDEX idx_template_department (owner_department),
    INDEX idx_template_approval_status (approval_status),
    INDEX idx_template_usage (usage_count),
    INDEX idx_template_last_used (last_used_date)
);

-- Create template_versions table for version history
CREATE TABLE template_versions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_id BIGINT NOT NULL,
    version_number INTEGER NOT NULL,
    version_name VARCHAR(255),
    
    -- Version content
    template_content LONGBLOB,
    template_format VARCHAR(50) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT,
    
    -- Version configuration
    configuration_json JSON,
    field_definitions JSON,
    validation_rules JSON,
    default_values JSON,
    
    -- Version metadata
    is_current BOOLEAN NOT NULL DEFAULT FALSE,
    change_summary TEXT,
    change_reason VARCHAR(255),
    
    -- Version lifecycle
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    
    -- Constraints
    FOREIGN KEY (template_id) REFERENCES document_templates(id) ON DELETE CASCADE,
    UNIQUE KEY uk_template_version (template_id, version_number),
    INDEX idx_version_current (is_current),
    INDEX idx_version_created (created_date)
);

-- Create template_fields table for dynamic field definitions
CREATE TABLE template_fields (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_id BIGINT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    field_label VARCHAR(255) NOT NULL,
    field_type VARCHAR(50) NOT NULL, -- TEXT, NUMBER, DATE, BOOLEAN, SELECT, MULTISELECT, FILE, SIGNATURE
    
    -- Field configuration
    field_order INTEGER NOT NULL DEFAULT 0,
    is_required BOOLEAN NOT NULL DEFAULT FALSE,
    is_readonly BOOLEAN NOT NULL DEFAULT FALSE,
    is_hidden BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Field validation
    validation_pattern VARCHAR(500), -- Regex pattern for validation
    min_length INTEGER,
    max_length INTEGER,
    min_value DECIMAL(19,4),
    max_value DECIMAL(19,4),
    allowed_values JSON, -- For SELECT/MULTISELECT fields
    
    -- Field appearance
    placeholder_text VARCHAR(255),
    help_text TEXT,
    default_value TEXT,
    
    -- Field behavior
    conditional_logic JSON, -- Show/hide based on other field values
    calculation_formula TEXT, -- For calculated fields
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (template_id) REFERENCES document_templates(id) ON DELETE CASCADE,
    UNIQUE KEY uk_template_field (template_id, field_name),
    INDEX idx_field_type (field_type),
    INDEX idx_field_order (field_order),
    INDEX idx_field_required (is_required)
);

-- Create template_categories table for organizing templates
CREATE TABLE template_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    parent_category_id BIGINT,
    
    -- Category configuration
    icon VARCHAR(100),
    color_code VARCHAR(7), -- Hex color code
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Access control
    access_level VARCHAR(50) NOT NULL DEFAULT 'PUBLIC', -- PRIVATE, DEPARTMENT, PUBLIC, RESTRICTED, SYSTEM
    department_restrictions JSON, -- Array of departments that can access
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    last_modified_by VARCHAR(255),
    
    -- Constraints
    FOREIGN KEY (parent_category_id) REFERENCES template_categories(id) ON DELETE SET NULL,
    INDEX idx_category_parent (parent_category_id),
    INDEX idx_category_active (is_active),
    INDEX idx_category_sort (sort_order)
);

-- Create template_usage_history table for analytics
CREATE TABLE template_usage_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_id BIGINT NOT NULL,
    document_id BIGINT,
    
    -- Usage details
    used_by_user_id VARCHAR(255) NOT NULL,
    used_by_department VARCHAR(255),
    usage_type VARCHAR(50) NOT NULL, -- CREATE_DOCUMENT, PREVIEW, DOWNLOAD, COPY
    
    -- Context information
    field_values JSON, -- Values filled in template fields
    generation_time_ms INTEGER, -- Time taken to generate document
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    
    -- Correlation and tracking
    correlation_id VARCHAR(100),
    session_id VARCHAR(100),
    
    -- Metadata
    usage_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (template_id) REFERENCES document_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL,
    INDEX idx_usage_template (template_id),
    INDEX idx_usage_user (used_by_user_id),
    INDEX idx_usage_date (usage_date),
    INDEX idx_usage_type (usage_type),
    INDEX idx_usage_correlation (correlation_id)
);

-- Create template_permissions table for fine-grained access control
CREATE TABLE template_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_id BIGINT NOT NULL,

    -- Permission assignment
    assignee_type VARCHAR(50) NOT NULL, -- USER, ROLE, DEPARTMENT
    assignee_value VARCHAR(255) NOT NULL,

    -- Permission details
    permission_type VARCHAR(50) NOT NULL, -- VIEW, USE, EDIT, ADMIN
    granted_by VARCHAR(255) NOT NULL,
    granted_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_date TIMESTAMP,

    -- Permission status
    is_active BOOLEAN NOT NULL DEFAULT TRUE,

    -- Constraints
    FOREIGN KEY (template_id) REFERENCES document_templates(id) ON DELETE CASCADE,
    UNIQUE KEY uk_template_permission (template_id, assignee_type, assignee_value, permission_type),
    INDEX idx_permission_assignee (assignee_type, assignee_value),
    INDEX idx_permission_type (permission_type),
    INDEX idx_permission_active (is_active)
);

-- Note: Template relationship to documents table is handled in separate changeset 044-add-source-template-id-to-documents.sql

-- Insert default template categories
INSERT INTO template_categories (name, description, icon, color_code, sort_order, created_by) VALUES
('Business Documents', 'General business document templates', 'briefcase', '#2563eb', 10, 'system'),
('Legal Documents', 'Legal contracts and agreements', 'scale', '#dc2626', 20, 'system'),
('HR Documents', 'Human resources forms and policies', 'users', '#059669', 30, 'system'),
('Financial Documents', 'Financial reports and forms', 'calculator', '#d97706', 40, 'system'),
('Technical Documents', 'Technical specifications and manuals', 'cog', '#7c3aed', 50, 'system'),
('Marketing Documents', 'Marketing materials and templates', 'megaphone', '#db2777', 60, 'system'),
('Forms', 'Interactive forms and questionnaires', 'clipboard-list', '#0891b2', 70, 'system'),
('Reports', 'Report templates and formats', 'chart-bar', '#ea580c', 80, 'system');

-- Insert sample document templates
INSERT INTO document_templates (
    name, description, category, template_type, template_format, mime_type,
    file_size, version, is_system_template, is_public, created_by, owner_user_id,
    access_level, approval_status, field_definitions, default_values
) VALUES
('Meeting Minutes Template', 'Standard template for recording meeting minutes', 'Business Documents', 'DOCUMENT', 'DOCX', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
 25600, '1.0', TRUE, TRUE, 'system', 'system', 'PUBLIC', 'PUBLISHED',
 JSON_ARRAY(
   JSON_OBJECT('name', 'meeting_title', 'label', 'Meeting Title', 'type', 'TEXT', 'required', TRUE),
   JSON_OBJECT('name', 'meeting_date', 'label', 'Meeting Date', 'type', 'DATE', 'required', TRUE),
   JSON_OBJECT('name', 'attendees', 'label', 'Attendees', 'type', 'TEXT', 'required', TRUE),
   JSON_OBJECT('name', 'agenda_items', 'label', 'Agenda Items', 'type', 'TEXT', 'required', FALSE),
   JSON_OBJECT('name', 'action_items', 'label', 'Action Items', 'type', 'TEXT', 'required', FALSE)
 ),
 JSON_OBJECT('meeting_date', CURDATE())
),

('Employee Onboarding Checklist', 'Comprehensive checklist for new employee onboarding', 'HR Documents', 'FORM', 'HTML', 'text/html',
 15360, '1.0', TRUE, TRUE, 'system', 'system', 'PUBLIC', 'PUBLISHED',
 JSON_ARRAY(
   JSON_OBJECT('name', 'employee_name', 'label', 'Employee Name', 'type', 'TEXT', 'required', TRUE),
   JSON_OBJECT('name', 'start_date', 'label', 'Start Date', 'type', 'DATE', 'required', TRUE),
   JSON_OBJECT('name', 'department', 'label', 'Department', 'type', 'SELECT', 'required', TRUE, 'options', JSON_ARRAY('HR', 'IT', 'Finance', 'Marketing', 'Operations')),
   JSON_OBJECT('name', 'manager', 'label', 'Direct Manager', 'type', 'TEXT', 'required', TRUE),
   JSON_OBJECT('name', 'equipment_needed', 'label', 'Equipment Needed', 'type', 'MULTISELECT', 'required', FALSE, 'options', JSON_ARRAY('Laptop', 'Monitor', 'Phone', 'Desk', 'Chair'))
 ),
 JSON_OBJECT('start_date', CURDATE())
),

('Project Status Report', 'Weekly project status reporting template', 'Reports', 'STRUCTURED', 'JSON', 'application/json',
 8192, '1.0', TRUE, TRUE, 'system', 'system', 'PUBLIC', 'PUBLISHED',
 JSON_ARRAY(
   JSON_OBJECT('name', 'project_name', 'label', 'Project Name', 'type', 'TEXT', 'required', TRUE),
   JSON_OBJECT('name', 'report_week', 'label', 'Report Week', 'type', 'DATE', 'required', TRUE),
   JSON_OBJECT('name', 'overall_status', 'label', 'Overall Status', 'type', 'SELECT', 'required', TRUE, 'options', JSON_ARRAY('Green', 'Yellow', 'Red')),
   JSON_OBJECT('name', 'completed_tasks', 'label', 'Completed Tasks', 'type', 'TEXT', 'required', FALSE),
   JSON_OBJECT('name', 'upcoming_tasks', 'label', 'Upcoming Tasks', 'type', 'TEXT', 'required', FALSE),
   JSON_OBJECT('name', 'risks_issues', 'label', 'Risks and Issues', 'type', 'TEXT', 'required', FALSE)
 ),
 JSON_OBJECT('report_week', CURDATE(), 'overall_status', 'Green')
);

-- Add comments for documentation
ALTER TABLE document_templates COMMENT = 'Stores document template definitions and configurations';
ALTER TABLE template_versions COMMENT = 'Maintains version history for document templates';
ALTER TABLE template_fields COMMENT = 'Defines dynamic fields within document templates';
ALTER TABLE template_categories COMMENT = 'Organizes templates into hierarchical categories';
ALTER TABLE template_usage_history COMMENT = 'Tracks template usage for analytics and reporting';
ALTER TABLE template_permissions COMMENT = 'Manages fine-grained access control for templates';
