package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionPriority;
import com.ascentbusiness.dms_svc.enums.ConversionStatus;
import com.ascentbusiness.dms_svc.enums.ConversionType;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO for conversion job information from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionJob {
    private String jobId;
    private String sessionId;
    private ConversionType conversionType;
    private String fileName;
    private ConversionStatus status;
    private ConversionPriority priority;
    private LocalDateTime submittedAt;
    private LocalDateTime startedAt;
    private Long estimatedProcessingTime; // milliseconds
    private String userId;
}
