package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

/**
 * Connection test details DTO from diagnostics-schema.graphqls.
 * Updated to match test expectations.
 */
@Data
@Builder
public class ConnectionTestDetails {
    private String endpoint;
    private String method;
    private Integer statusCode;
    private String headers; // JSON string
    private Long responseSize;
    private SSLInfo sslInfo;
    private NetworkInfo networkInfo;
    private String errorDetails;

    // Fields expected by tests
    private String version;
    private ConnectionPool connectionPool;

    // Storage provider specific fields
    private String region;
    private String bucket;
    private String lastError;
}
