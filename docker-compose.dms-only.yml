# DMS Service Only - Docker Compose Configuration
# Deploy only the DMS Service with its required infrastructure
version: '3.8'

services:
  # =============================================================================
  # DMS SERVICE ONLY
  # =============================================================================
  
  # DMS Service
  dms-service:
    build:
      context: ./dms-svc
      dockerfile: Dockerfile
      target: runtime
    container_name: dms-service
    ports:
      - "9093:9093"
      - "9464:9464"  # Prometheus metrics endpoint
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=9093
      - SPRING_DATASOURCE_URL=***********************/${MYSQL_DMS_DATABASE:-dms_db}?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
      - SPRING_DATASOURCE_USERNAME=${MYSQL_DMS_USER:-dms_user}
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_DMS_PASSWORD:-dms_password}
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-shared_redis_password}
      - DMS_STORAGE_PROVIDER=${DMS_STORAGE_PROVIDER:-LOCAL}
      - DMS_STORAGE_LOCAL_BASE_PATH=${DMS_STORAGE_LOCAL_BASE_PATH:-/app/storage}
      - JWT_SECRET=${JWT_SECRET:-sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      - OTEL_TRACES_EXPORTER=zipkin
      - OTEL_EXPORTER_ZIPKIN_ENDPOINT=http://zipkin:9411/api/v2/spans
      - OTEL_METRICS_EXPORTER=console
      - OTEL_LOGS_EXPORTER=console
      - JAVA_OPTS=${DMS_JAVA_OPTS:--Xmx2g -Xms1g}
    volumes:
      - dms_storage:/app/storage
      - dms_logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    networks:
      - dms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9093/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # =============================================================================
  # REQUIRED INFRASTRUCTURE FOR DMS SERVICE
  # =============================================================================

  # MySQL Database (DMS DB only)
  mysql:
    image: mysql:8.0
    container_name: dms-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      - MYSQL_DATABASE=${MYSQL_DMS_DATABASE:-dms_db}
      - MYSQL_USER=${MYSQL_DMS_USER:-dms_user}
      - MYSQL_PASSWORD=${MYSQL_DMS_PASSWORD:-dms_password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/dms-init:/docker-entrypoint-initdb.d:ro
    networks:
      - dms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-root_password}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: dms-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-shared_redis_password} --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - dms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-shared_redis_password}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch for Search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: dms-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - dms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # OPTIONAL MONITORING SERVICES
  # =============================================================================

  # Zipkin for Distributed Tracing (Optional)
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: dms-zipkin
    ports:
      - "9411:9411"
    networks:
      - dms-network
    restart: unless-stopped

  # Prometheus for Metrics (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: dms-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/dms-prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - dms-network
    restart: unless-stopped

  # Grafana for Dashboards (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: dms-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - dms-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  dms_storage:
    driver: local
  dms_logs:
    driver: local

networks:
  dms-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
