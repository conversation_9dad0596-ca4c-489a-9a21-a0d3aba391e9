<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Define properties -->
    <springProfile name="!prod">
        <property name="LOG_LEVEL" value="DEBUG"/>
        <property name="LOG_FORMAT" value="CONSOLE"/>
    </springProfile>
    <springProfile name="prod">
        <property name="LOG_LEVEL" value="INFO"/>
        <property name="LOG_FORMAT" value="JSON"/>
    </springProfile>

    <!-- Console Appender for Development -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] [%X{traceId}] [%X{spanId}] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- JSON Appender for Production -->
    <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <version/>
                <logLevel/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "service": "dms-svc",
                            "environment": "${ENVIRONMENT:-local}",
                            "version": "${APPLICATION_VERSION:-unknown}",
                            "correlationId": "%X{correlationId:-}",
                            "traceId": "%X{traceId:-}",
                            "spanId": "%X{spanId:-}",
                            "thread": "%thread",
                            "logger": "%logger"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!-- File Appender for Application Logs -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/dms-application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/dms-application.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <version/>
                <logLevel/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "service": "dms-svc",
                            "environment": "${ENVIRONMENT:-local}",
                            "version": "${APPLICATION_VERSION:-unknown}",
                            "correlationId": "%X{correlationId:-}",
                            "traceId": "%X{traceId:-}",
                            "spanId": "%X{spanId:-}",
                            "thread": "%thread",
                            "logger": "%logger"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!-- Audit Log Appender -->
    <appender name="AUDIT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/dms-audit.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/dms-audit.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <version/>
                <logLevel/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "service": "dms-svc",
                            "logType": "AUDIT",
                            "environment": "${ENVIRONMENT:-local}",
                            "version": "${APPLICATION_VERSION:-unknown}",
                            "correlationId": "%X{correlationId:-}",
                            "traceId": "%X{traceId:-}",
                            "spanId": "%X{spanId:-}",
                            "userId": "%X{userId:-}",
                            "action": "%X{auditAction:-}",
                            "resource": "%X{auditResource:-}",
                            "resourceId": "%X{auditResourceId:-}",
                            "thread": "%thread",
                            "logger": "%logger"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!-- Security Log Appender -->
    <appender name="SECURITY" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/dms-security.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/dms-security.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <version/>
                <logLevel/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "service": "dms-svc",
                            "logType": "SECURITY",
                            "environment": "${ENVIRONMENT:-local}",
                            "version": "${APPLICATION_VERSION:-unknown}",
                            "correlationId": "%X{correlationId:-}",
                            "traceId": "%X{traceId:-}",
                            "spanId": "%X{spanId:-}",
                            "userId": "%X{userId:-}",
                            "ipAddress": "%X{clientIp:-}",
                            "userAgent": "%X{userAgent:-}",
                            "securityEvent": "%X{securityEvent:-}",
                            "severity": "%X{securitySeverity:-}",
                            "thread": "%thread",
                            "logger": "%logger"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!-- Performance Log Appender -->
    <appender name="PERFORMANCE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/dms-performance.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/dms-performance.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <version/>
                <logLevel/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "service": "dms-svc",
                            "logType": "PERFORMANCE",
                            "environment": "${ENVIRONMENT:-local}",
                            "version": "${APPLICATION_VERSION:-unknown}",
                            "correlationId": "%X{correlationId:-}",
                            "traceId": "%X{traceId:-}",
                            "spanId": "%X{spanId:-}",
                            "operation": "%X{operation:-}",
                            "duration": "%X{duration:-}",
                            "status": "%X{status:-}",
                            "thread": "%thread",
                            "logger": "%logger"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!-- Logger configurations -->
    <springProfile name="!prod">
        <logger name="com.ascentbusiness.dms_svc" level="${LOG_LEVEL}" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </logger>
    </springProfile>

    <springProfile name="prod">
        <logger name="com.ascentbusiness.dms_svc" level="${LOG_LEVEL}" additivity="false">
            <appender-ref ref="JSON"/>
            <appender-ref ref="FILE"/>
        </logger>
    </springProfile>

    <!-- Audit Logger -->
    <logger name="AUDIT" level="INFO" additivity="false">
        <appender-ref ref="AUDIT"/>
    </logger>

    <!-- Security Logger -->
    <logger name="SECURITY" level="INFO" additivity="false">
        <appender-ref ref="SECURITY"/>
    </logger>

    <!-- Performance Logger -->
    <logger name="PERFORMANCE" level="INFO" additivity="false">
        <appender-ref ref="PERFORMANCE"/>
    </logger>

    <!-- Spring Framework loggers -->
    <logger name="org.springframework.security" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.springframework.graphql" level="INFO"/>
    
    <!-- Database loggers -->
    <logger name="org.hibernate.SQL" level="WARN"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="WARN"/>
    
    <!-- External service loggers -->
    <logger name="com.microsoft.graph" level="INFO"/>
    <logger name="software.amazon.awssdk" level="INFO"/>
    
    <!-- Tracing loggers -->
    <logger name="io.micrometer.tracing" level="INFO"/>
    <logger name="brave" level="INFO"/>
    <logger name="zipkin2" level="INFO"/>

    <!-- Root logger -->
    <springProfile name="!prod">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="JSON"/>
        </root>
    </springProfile>
</configuration>
