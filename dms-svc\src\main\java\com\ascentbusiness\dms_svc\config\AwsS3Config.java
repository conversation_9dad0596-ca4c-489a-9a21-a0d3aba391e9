package com.ascentbusiness.dms_svc.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;

import java.net.URI;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class AwsS3Config {

    private final StorageConfigurationProperties storageConfig;

    @Bean
    @ConditionalOnProperty(name = "dms.storage.provider", havingValue = "S3")
    public S3Client s3Client() {
        log.info("Configuring S3 client for bucket: {}", storageConfig.getS3().getBucketName());
        
        validateS3Configuration();
        
        S3ClientBuilder builder = S3Client.builder()
                .region(Region.of(storageConfig.getS3().getRegion()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(
                                storageConfig.getS3().getAccessKey(),
                                storageConfig.getS3().getSecretKey()
                        )
                ));

        // Configure custom endpoint if provided (for localstack testing)
        if (storageConfig.getS3().getEndpoint() != null && !storageConfig.getS3().getEndpoint().isEmpty()) {
            log.info("Using custom S3 endpoint: {}", storageConfig.getS3().getEndpoint());
            builder.endpointOverride(URI.create(storageConfig.getS3().getEndpoint()));
        }

        S3Client client = builder.build();
        log.info("S3 client configured successfully for region: {}", storageConfig.getS3().getRegion());
        
        return client;
    }

    private void validateS3Configuration() {
        StorageConfigurationProperties.S3 s3Config = storageConfig.getS3();
        
        if (s3Config.getAccessKey() == null || s3Config.getAccessKey().isEmpty()) {
            throw new IllegalArgumentException("AWS access key is required when using S3 storage provider");
        }
        
        if (s3Config.getSecretKey() == null || s3Config.getSecretKey().isEmpty()) {
            throw new IllegalArgumentException("AWS secret key is required when using S3 storage provider");
        }
        
        if (s3Config.getBucketName() == null || s3Config.getBucketName().isEmpty()) {
            throw new IllegalArgumentException("S3 bucket name is required when using S3 storage provider");
        }
        
        if (s3Config.getRegion() == null || s3Config.getRegion().isEmpty()) {
            throw new IllegalArgumentException("AWS region is required when using S3 storage provider");
        }
        
        log.info("S3 configuration validation passed");
    }
}
