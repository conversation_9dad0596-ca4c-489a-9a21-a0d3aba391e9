package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

/**
 * Metadata DTO for conversion operations from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionMetadata {
    private String originalMimeType;
    private String convertedMimeType;
    private Long originalSize;
    private Long convertedSize;
    private Float compressionRatio;
    private String qualitySettings;
    private String conversionOptions; // JSON string
    private String engineVersion;
    private String processingNode;
}
