package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.WorkflowStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Entity representing an active workflow instance for a specific document
 */
@Entity
@Table(name = "workflow_instances")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowInstance extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_definition_id", nullable = false)
    @JsonIgnore
    private WorkflowDefinition workflowDefinition;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    @JsonIgnore
    private Document document;

    @Column(name = "instance_name", length = 255)
    private String instanceName;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 50)
    @Builder.Default
    private WorkflowStatus status = WorkflowStatus.PENDING;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "current_stage_id")
    @JsonIgnore
    private WorkflowStage currentStage;

    @Column(name = "priority", nullable = false, length = 20)
    @Builder.Default
    private String priority = "MEDIUM"; // LOW, MEDIUM, HIGH, URGENT

    @Column(name = "started_date", nullable = false)
    @Builder.Default
    private LocalDateTime startedDate = LocalDateTime.now();

    @Column(name = "completed_date")
    private LocalDateTime completedDate;

    @Column(name = "due_date")
    private LocalDateTime dueDate;

    // Context and metadata stored as JSON
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "context_data", columnDefinition = "JSON")
    private JsonNode contextData;

    @Column(name = "initiator_user_id", nullable = false, length = 255)
    private String initiatorUserId;

    @Column(name = "completion_reason", length = 255)
    private String completionReason;

    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_workflow_instance_id")
    @JsonIgnore
    private WorkflowInstance parentWorkflowInstance;

    // Relationships
    @OneToMany(mappedBy = "workflowInstance", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowTask> tasks;

    @OneToMany(mappedBy = "workflowInstance", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowHistory> history;

    @OneToMany(mappedBy = "workflowInstance", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowNotification> notifications;

    @OneToMany(mappedBy = "parentWorkflowInstance", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<WorkflowInstance> childWorkflows;

    /**
     * Check if this workflow instance is currently active
     */
    @Transient
    public boolean isActive() {
        return WorkflowStatus.IN_PROGRESS.equals(status) || WorkflowStatus.PENDING.equals(status);
    }

    /**
     * Check if this workflow instance is completed
     */
    @Transient
    public boolean isCompleted() {
        return WorkflowStatus.COMPLETED.equals(status);
    }

    /**
     * Check if this workflow instance is cancelled or failed
     */
    @Transient
    public boolean isTerminated() {
        return WorkflowStatus.CANCELLED.equals(status) || WorkflowStatus.FAILED.equals(status);
    }

    /**
     * Check if this workflow instance is overdue
     */
    @Transient
    public boolean isOverdue() {
        return dueDate != null && LocalDateTime.now().isAfter(dueDate) && isActive();
    }

    /**
     * Get the duration of this workflow instance in hours
     */
    @Transient
    public long getDurationHours() {
        LocalDateTime endTime = completedDate != null ? completedDate : LocalDateTime.now();
        return java.time.Duration.between(startedDate, endTime).toHours();
    }

    /**
     * Get the number of pending tasks
     */
    @Transient
    public long getPendingTaskCount() {
        if (tasks == null) return 0;
        return tasks.stream()
                .filter(task -> "PENDING".equals(task.getStatus()) || "IN_PROGRESS".equals(task.getStatus()))
                .count();
    }

    /**
     * Get the number of completed tasks
     */
    @Transient
    public long getCompletedTaskCount() {
        if (tasks == null) return 0;
        return tasks.stream()
                .filter(task -> "COMPLETED".equals(task.getStatus()))
                .count();
    }

    /**
     * Calculate completion percentage
     */
    @Transient
    public double getCompletionPercentage() {
        if (tasks == null || tasks.isEmpty()) return 0.0;
        long totalTasks = tasks.size();
        long completedTasks = getCompletedTaskCount();
        return (double) completedTasks / totalTasks * 100.0;
    }

    /**
     * Check if this is a child workflow
     */
    @Transient
    public boolean isChildWorkflow() {
        return parentWorkflowInstance != null;
    }

    /**
     * Check if this workflow has child workflows
     */
    @Transient
    public boolean hasChildWorkflows() {
        return childWorkflows != null && !childWorkflows.isEmpty();
    }

    /**
     * Get the workflow definition name
     */
    @Transient
    public String getWorkflowDefinitionName() {
        return workflowDefinition != null ? workflowDefinition.getName() : null;
    }

    /**
     * Get the document name
     */
    @Transient
    public String getDocumentName() {
        return document != null ? document.getName() : null;
    }

    @Override
    public String toString() {
        return String.format("WorkflowInstance{id=%d, name='%s', status=%s, document='%s', initiator='%s'}", 
                           getId(), instanceName, status, getDocumentName(), initiatorUserId);
    }
}
