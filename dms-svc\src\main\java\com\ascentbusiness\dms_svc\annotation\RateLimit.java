package com.ascentbusiness.dms_svc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for rate limiting with Redis-based distributed support
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {

    /**
     * Maximum number of requests allowed
     */
    int value() default 5;

    /**
     * Time window in seconds
     */
    int window() default 60;

    /**
     * Key for rate limiting (defaults to user ID)
     * Supported values: "user", "ip", "global", or custom key
     */
    String key() default "user";

    /**
     * Rate limit type for different operations
     */
    RateLimitType type() default RateLimitType.GENERAL;

    /**
     * Whether to use Redis for distributed rate limiting
     */
    boolean distributed() default true;

    /**
     * Custom error message when rate limit is exceeded
     */
    String message() default "Rate limit exceeded. Please try again later.";

    /**
     * Rate limit types for different operations
     */
    enum RateLimitType {
        GENERAL,
        GRAPHQL_QUERY,
        GRAPHQL_MUTATION,
        DOCUMENT_UPLOAD,
        DOCUMENT_DOWNLOAD,
        PERMISSION_OPERATION,
        AUTHENTICATION,
        SEARCH_OPERATION,
        DOCUMENT_OPERATION,
        BULK_OPERATION
    }
}
