# Business Features Schema Extension
# Workflow Management, Document Templates, and Webhook/Event System

# ===== WORKFLOW MANAGEMENT =====

enum WorkflowType {
  DOCUMENT_APPROVAL
  DOCUMENT_REVIEW
  COMPLIANCE_REVIEW
  LEGAL_REVIEW
  CHANGE_MANAGEMENT
  PUBLICATION_APPROVAL
  CUSTOM
}

enum WorkflowStatus {
  NONE
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
  FAILED
  SUSPENDED
}

enum ApprovalType {
  SEQUENTIAL
  PARALLEL
  CONDITIONAL
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  SKIPPED
  ESCALATED
  DELEGATED
  CANCELLED
}

enum WorkflowAction {
  APPROVE
  REJECT
  DELEGATE
  REQUEST_CHANGES
  ESCALATE
  CANCEL
  SUSPEND
  RESUME
}

type WorkflowDefinition {
  id: ID!
  name: String!
  description: String
  version: String!
  workflowType: WorkflowType!
  isActive: Boolean!
  isDefault: Boolean!
  approvalType: ApprovalType!
  autoStart: Boolean!
  timeoutHours: Int
  escalationEnabled: Boolean!
  escalationHours: Int
  createdBy: String!
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  lastModifiedBy: String
  
  # Relationships
  stages: [WorkflowStage!]!
  instances: [WorkflowInstance!]!
  
  # Computed fields
  stageCount: Int!
  estimatedCompletionHours: Int!
  supportsParallelProcessing: Boolean!
}

type WorkflowStage {
  id: ID!
  stageName: String!
  stageOrder: Int!
  stageType: String!
  isRequired: Boolean!
  isParallel: Boolean!
  minApprovalsRequired: Int
  timeoutHours: Int
  escalationEnabled: Boolean!
  escalationHours: Int
  
  # Relationships
  workflowDefinition: WorkflowDefinition!
  tasks: [WorkflowTask!]!
  
  # Computed fields
  requiresMultipleApprovals: Boolean!
  hasEscalation: Boolean!
}

type WorkflowInstance {
  id: ID!
  instanceName: String
  status: WorkflowStatus!
  priority: String!
  startedDate: DateTime!
  completedDate: DateTime
  dueDate: DateTime
  initiatorUserId: String!
  completionReason: String
  correlationId: String
  
  # Relationships
  workflowDefinition: WorkflowDefinition!
  document: Document!
  currentStage: WorkflowStage
  tasks: [WorkflowTask!]!
  history: [WorkflowHistory!]!
  
  # Computed fields
  isActive: Boolean!
  isCompleted: Boolean!
  isOverdue: Boolean!
  durationHours: Long!
  pendingTaskCount: Long!
  completedTaskCount: Long!
  completionPercentage: Float!
}

type WorkflowTask {
  id: ID!
  taskName: String!
  assignedToUserId: String
  assignedToRole: String
  assignedToDepartment: String
  assignedDate: DateTime
  status: TaskStatus!
  actionTaken: WorkflowAction
  completedDate: DateTime
  completedByUserId: String
  priority: String!
  dueDate: DateTime
  comments: String
  
  # Relationships
  workflowInstance: WorkflowInstance!
  workflowStage: WorkflowStage!
  
  # Computed fields
  isActive: Boolean!
  isCompleted: Boolean!
  isOverdue: Boolean!
  currentAssignee: String
  durationHours: Long!
  hoursUntilDue: Long!
}

type WorkflowHistory {
  id: ID!
  eventType: String!
  eventDescription: String
  actorUserId: String
  oldStatus: String
  newStatus: String
  stageName: String
  taskName: String
  actionTaken: String
  comments: String
  eventTimestamp: DateTime!
  
  # Relationships
  workflowInstance: WorkflowInstance!
  workflowTask: WorkflowTask
  
  # Computed fields
  isTaskEvent: Boolean!
  isStatusChange: Boolean!
  formattedDescription: String!
}

type WorkflowStatistics {
  totalActive: Long!
  totalCompleted: Long!
  totalCancelled: Long!
  averageCompletionTime: Float
  statusStatistics: [StatusCount!]!
  priorityStatistics: [PriorityCount!]!
}

type StatusCount {
  status: String!
  count: Long!
}

type PriorityCount {
  priority: String!
  count: Long!
}

# ===== DOCUMENT TEMPLATES =====

enum TemplateType {
  DOCUMENT
  FORM
  STRUCTURED
}

enum TemplateApprovalStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  REJECTED
  PUBLISHED
}

enum TemplateAccessLevel {
  PRIVATE
  DEPARTMENT
  PUBLIC
  RESTRICTED
  SYSTEM
}

enum TemplateFieldType {
  TEXT
  TEXTAREA
  NUMBER
  DATE
  DATETIME
  BOOLEAN
  SELECT
  MULTISELECT
  FILE
  SIGNATURE
  EMAIL
  PHONE
  URL
  CURRENCY
  PERCENTAGE
  CALCULATED
}

type DocumentTemplate {
  id: ID!
  name: String!
  description: String
  category: String!
  templateType: TemplateType!
  templateFormat: String!
  mimeType: String!
  fileSize: Long
  version: String!
  isActive: Boolean!
  isSystemTemplate: Boolean!
  isPublic: Boolean!
  usageCount: Int!
  lastUsedDate: DateTime
  ownerUserId: String!
  ownerDepartment: String
  accessLevel: TemplateAccessLevel!
  approvalStatus: TemplateApprovalStatus!
  approvedBy: String
  approvedDate: DateTime
  publishedDate: DateTime
  publishedBy: String
  createdBy: String!
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  lastModifiedBy: String
  
  # Relationships
  fields: [TemplateField!]!
  versions: [TemplateVersion!]!
  documentsCreated: [Document!]!
  
  # Computed fields
  isPublished: Boolean!
  isPendingApproval: Boolean!
  isRecentlyUsed: Boolean!
  isPopular: Boolean!
  hasDynamicFields: Boolean!
  fieldCount: Int!
}

type TemplateField {
  id: ID!
  fieldName: String!
  fieldLabel: String!
  fieldType: TemplateFieldType!
  fieldOrder: Int!
  isRequired: Boolean!
  isReadonly: Boolean!
  isHidden: Boolean!
  validationPattern: String
  minLength: Int
  maxLength: Int
  placeholderText: String
  helpText: String
  defaultValue: String
  
  # Relationships
  template: DocumentTemplate!
  
  # Computed fields
  hasValidation: Boolean!
  isSelectionType: Boolean!
  isNumericType: Boolean!
  isDateTimeType: Boolean!
  displayLabel: String!
}

type TemplateVersion {
  id: ID!
  versionNumber: Int!
  versionName: String
  templateFormat: String!
  mimeType: String!
  fileSize: Long
  isCurrent: Boolean!
  changeSummary: String
  changeReason: String
  createdBy: String!
  createdDate: DateTime!
  
  # Relationships
  template: DocumentTemplate!
  
  # Computed fields
  versionDisplayName: String!
  hasDynamicFields: Boolean!
  fieldCount: Int!
}

# TemplateUsageHistory type moved to template-management-schema.graphqls to avoid duplication

# TemplateStatistics and related count types moved to template-management-schema.graphqls to avoid duplication

# ===== WEBHOOK AND EVENT SYSTEM =====

enum EventType {
  DOCUMENT_CREATED
  DOCUMENT_UPDATED
  DOCUMENT_DELETED
  DOCUMENT_VIEWED
  DOCUMENT_DOWNLOADED
  WORKFLOW_STARTED
  WORKFLOW_COMPLETED
  WORKFLOW_CANCELLED
  WORKFLOW_FAILED
  TEMPLATE_CREATED
  TEMPLATE_UPDATED
  TEMPLATE_USED
  TEMPLATE_PUBLISHED
  USER_LOGIN
  USER_LOGOUT
  SYSTEM_ERROR
  SECURITY_VIOLATION
  COMPLIANCE_VIOLATION
}

enum EventCategory {
  DOCUMENT
  WORKFLOW
  TEMPLATE
  USER
  SYSTEM
  SECURITY
  COMPLIANCE
  INTEGRATION
}

enum WebhookAuthType {
  NONE
  BASIC
  BEARER
  API_KEY
  CUSTOM
}

enum DeliveryStatus {
  PENDING
  SUCCESS
  FAILED
  CANCELLED
  RETRYING
}

type SystemEvent {
  id: ID!
  eventType: EventType!
  eventCategory: EventCategory!
  eventName: String!
  sourceEntityType: String
  sourceEntityId: Long
  actorUserId: String
  actorType: String
  correlationId: String
  sessionId: String
  requestId: String
  eventTimestamp: DateTime!
  processingStatus: String!
  webhookDeliveryCount: Int!
  
  # Relationships
  webhookDeliveries: [WebhookDelivery!]!
  
  # Computed fields
  isPending: Boolean!
  isCompleted: Boolean!
  isFailed: Boolean!
  isRecent: Boolean!
  eventAgeHours: Long!
  eventDisplayName: String!
}

type WebhookEndpoint {
  id: ID!
  name: String!
  description: String
  url: String!
  httpMethod: String!
  contentType: String!
  timeoutSeconds: Int!
  authType: WebhookAuthType
  isActive: Boolean!
  isVerified: Boolean!
  maxRetries: Int!
  retryDelaySeconds: Int!
  exponentialBackoff: Boolean!
  rateLimitPerMinute: Int
  rateLimitPerHour: Int
  successCount: Int!
  failureCount: Int!
  lastSuccessDate: DateTime
  lastFailureDate: DateTime
  lastFailureReason: String
  createdBy: String!
  createdDate: DateTime!
  lastModifiedDate: DateTime!
  lastModifiedBy: String
  
  # Relationships
  deliveries: [WebhookDelivery!]!
  
  # Computed fields
  hasAuthentication: Boolean!
  hasEventFilters: Boolean!
  hasCustomHeaders: Boolean!
  successRate: Float!
  totalDeliveries: Int!
  isHealthy: Boolean!
  hasRecentFailures: Boolean!
}

type WebhookDelivery {
  id: ID!
  deliveryAttempt: Int!
  deliveryStatus: DeliveryStatus!
  httpStatusCode: Int
  responseBody: String
  requestPayload: String
  scheduledDate: DateTime!
  attemptedDate: DateTime
  completedDate: DateTime
  durationMs: Int
  errorMessage: String
  errorCode: String
  correlationId: String
  
  # Relationships
  webhookEndpoint: WebhookEndpoint!
  systemEvent: SystemEvent!
  
  # Computed fields
  isPending: Boolean!
  isSuccessful: Boolean!
  isFailed: Boolean!
  isFirstAttempt: Boolean!
  isRetryAttempt: Boolean!
  isHttpSuccess: Boolean!
  shouldRetry: Boolean!
  durationSeconds: Float!
  isFastDelivery: Boolean!
  isSlowDelivery: Boolean!
}

type WebhookStatistics {
  totalEndpoints: Long!
  verifiedEndpoints: Long!
  totalSuccesses: Long!
  totalFailures: Long!
  successRate: Float!
}

type WebhookEndpointStatistics {
  totalEndpoints: Int!
  activeEndpoints: Int!
  verifiedEndpoints: Int!
  totalDeliveries: Long!
  successfulDeliveries: Long!
  failedDeliveries: Long!
  averageResponseTime: Float!
  successRate: Float!
  lastDeliveryTime: DateTime
  healthyEndpoints: Int!
  unhealthyEndpoints: Int!
}

type EventStatistics {
  totalEvents: Long!
  pendingEvents: Long!
  completedEvents: Long!
  failedEvents: Long!
  typeStatistics: [EventTypeCount!]!
  categoryStatistics: [EventCategoryCount!]!
}

type EventTypeCount {
  eventType: String!
  count: Long!
}

type EventCategoryCount {
  eventCategory: String!
  count: Long!
}

# ===== INPUT TYPES =====

# Workflow Input Types
input WorkflowDefinitionInput {
  name: String!
  description: String
  workflowType: WorkflowType!
  approvalType: ApprovalType = SEQUENTIAL
  autoStart: Boolean = false
  timeoutHours: Int = 72
  escalationEnabled: Boolean = true
  escalationHours: Int = 24
  stages: [WorkflowStageInput!]!
}

input WorkflowStageInput {
  stageName: String!
  stageOrder: Int!
  stageType: String = "APPROVAL"
  isRequired: Boolean = true
  isParallel: Boolean = false
  minApprovalsRequired: Int = 1
  timeoutHours: Int = 24
  escalationEnabled: Boolean = true
  escalationHours: Int = 8
  assigneeType: String!
  assigneeValues: [String!]!
}

input StartWorkflowInput {
  documentId: ID!
  workflowDefinitionId: ID!
  priority: String = "MEDIUM"
  dueDate: DateTime
  comments: String
}

input CompleteTaskInput {
  taskId: ID!
  action: WorkflowAction!
  comments: String
  delegateToUserId: String
}

# Template Input Types
input DocumentTemplateInput {
  name: String!
  description: String
  category: String!
  templateType: TemplateType = DOCUMENT
  templateFormat: String!
  mimeType: String!
  templateContent: String  # Base64 encoded content
  accessLevel: TemplateAccessLevel = PRIVATE
  fields: [TemplateFieldInput!]
}

input TemplateFieldInput {
  fieldName: String!
  fieldLabel: String!
  fieldType: TemplateFieldType!
  fieldOrder: Int!
  isRequired: Boolean = false
  isReadonly: Boolean = false
  isHidden: Boolean = false
  validationPattern: String
  minLength: Int
  maxLength: Int
  placeholderText: String
  helpText: String
  defaultValue: String
  allowedValues: [String!]
}

# CreateDocumentFromTemplateInput and FieldValueInput moved to template-management-schema.graphqls to avoid duplication

# Webhook Input Types
input WebhookEndpointInput {
  name: String!
  description: String
  url: String!
  httpMethod: String = "POST"
  contentType: String = "application/json"
  timeoutSeconds: Int = 30
  authType: WebhookAuthType = NONE
  authConfig: String  # JSON string for auth configuration
  customHeaders: String  # JSON string for custom headers
  eventTypes: [EventType!]!
  maxRetries: Int = 3
  retryDelaySeconds: Int = 60
  exponentialBackoff: Boolean = true
  rateLimitPerMinute: Int = 60
  rateLimitPerHour: Int = 1000
}

input EventFilterInput {
  eventTypes: [EventType!]
  eventCategories: [EventCategory!]
  actorUserId: String
  sourceEntityType: String
  dateFrom: DateTime
  dateTo: DateTime
}

# Pagination Input Types
input WorkflowPaginationInput {
  page: Int = 0
  size: Int = 10
  sortBy: String = "createdDate"
  sortDirection: String = "DESC"
}

input TemplatePaginationInput {
  page: Int = 0
  size: Int = 10
  sortBy: String = "lastModifiedDate"
  sortDirection: String = "DESC"
}

input EventPaginationInput {
  page: Int = 0
  size: Int = 10
  sortBy: String = "eventTimestamp"
  sortDirection: String = "DESC"
}

# Page Types
type WorkflowDefinitionPage {
  content: [WorkflowDefinition!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}

type WorkflowInstancePage {
  content: [WorkflowInstance!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}

type WorkflowTaskPage {
  content: [WorkflowTask!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}

type DocumentTemplatePage {
  content: [DocumentTemplate!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}

type SystemEventPage {
  content: [SystemEvent!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}

type WebhookEndpointPage {
  content: [WebhookEndpoint!]!
  totalElements: Int!
  totalPages: Int!
  size: Int!
  number: Int!
  first: Boolean!
  last: Boolean!
}
