--liquibase formatted sql

--changeset system:042-add-priority-to-workflow-notifications
--comment: Add priority field to workflow_notifications table

-- Add priority column to workflow_notifications table
ALTER TABLE workflow_notifications ADD COLUMN priority VARCHAR(20) DEFAULT 'NORMAL';

-- Add is_read column to workflow_notifications table
ALTER TABLE workflow_notifications ADD COLUMN is_read BOOLEAN DEFAULT FALSE NOT NULL;

-- Add read_date column to workflow_notifications table
ALTER TABLE workflow_notifications ADD COLUMN read_date TIMESTAMP NULL;

-- Add failure_count column to workflow_notifications table
ALTER TABLE workflow_notifications ADD COLUMN failure_count INTEGER DEFAULT 0 NOT NULL;

-- Add last_failure_date column to workflow_notifications table
ALTER TABLE workflow_notifications ADD COLUMN last_failure_date TIMESTAMP NULL;

-- Add index for priority queries
ALTER TABLE workflow_notifications ADD INDEX idx_notification_priority (priority);

-- Add index for read status queries
ALTER TABLE workflow_notifications ADD INDEX idx_notification_read_status (is_read);

-- Update existing records to have NORMAL priority and unread status
UPDATE workflow_notifications SET priority = 'NORMAL' WHERE priority IS NULL;
UPDATE workflow_notifications SET is_read = FALSE WHERE is_read IS NULL;

-- Add comment for documentation
ALTER TABLE workflow_notifications COMMENT = 'Notification queue for workflow-related communications with priority support';
