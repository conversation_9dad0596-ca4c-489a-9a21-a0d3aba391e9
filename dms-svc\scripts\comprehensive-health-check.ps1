# Comprehensive health check for DMS Service
$baseUrl = "http://localhost:9093"
$healthResults = @()

function Add-HealthResult {
    param(
        [string]$Component,
        [string]$Status,
        [string]$Details
    )
    
    $script:healthResults += @{
        Component = $Component
        Status = $Status
        Details = $Details
        Timestamp = Get-Date
    }
}

function Test-Component {
    param(
        [string]$Name,
        [scriptblock]$TestScript
    )
    
    Write-Host "`nTesting $Name..." -ForegroundColor Yellow
    
    try {
        $result = & $TestScript
        if ($result) {
            Write-Host "✓ $Name: HEALTHY" -ForegroundColor Green
            Add-HealthResult -Component $Name -Status "HEALTHY" -Details $result
        } else {
            Write-Host "✗ $Name: UNHEALTHY" -ForegroundColor Red
            Add-HealthResult -Component $Name -Status "UNHEALTHY" -Details "Test returned false"
        }
    } catch {
        Write-Host "✗ $Name: ERROR - $($_.Exception.Message)" -ForegroundColor Red
        Add-HealthResult -Component $Name -Status "ERROR" -Details $_.Exception.Message
    }
}

Write-Host "=== DMS Service Comprehensive Health Check ===" -ForegroundColor Cyan
Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Gray

# Test 1: Application Health Endpoint
Test-Component -Name "Application Health" -TestScript {
    $response = Invoke-RestMethod -Uri "$baseUrl/actuator/health" -TimeoutSec 10
    if ($response.status -eq "UP") {
        return "Status: $($response.status)"
    }
    return $false
}

# Test 2: Application Info
Test-Component -Name "Application Info" -TestScript {
    $response = Invoke-RestMethod -Uri "$baseUrl/actuator/info" -TimeoutSec 10
    return "Application info accessible"
}

# Test 3: Database Connectivity
Test-Component -Name "Database Connectivity" -TestScript {
    # Try to access a simple endpoint that would require database
    $response = Invoke-WebRequest -Uri "$baseUrl/actuator/health" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        return "Database connection healthy (inferred from health endpoint)"
    }
    return $false
}

# Test 4: GraphQL Schema Loading
Test-Component -Name "GraphQL Schema" -TestScript {
    $response = Invoke-WebRequest -Uri "$baseUrl/graphql" -Method Get -TimeoutSec 10
    if ($response.StatusCode -eq 401) {
        return "GraphQL endpoint accessible (401 expected without auth)"
    }
    return $false
}

# Test 5: Security Configuration
Test-Component -Name "Security Configuration" -TestScript {
    # Test that protected endpoints return 401
    try {
        Invoke-RestMethod -Uri "$baseUrl/graphql" -Method Post -ContentType "application/json" -Body '{"query":"query { __schema { queryType { name } } }"}' -TimeoutSec 10
        return $false  # Should not succeed without auth
    } catch {
        if ($_.Exception.Response.StatusCode.value__ -eq 401) {
            return "Security properly configured (401 Unauthorized)"
        }
        return $false
    }
}

# Test 6: Actuator Endpoints
Test-Component -Name "Actuator Endpoints" -TestScript {
    $endpoints = @("/actuator", "/actuator/health", "/actuator/info")
    $successCount = 0
    
    foreach ($endpoint in $endpoints) {
        try {
            $response = Invoke-WebRequest -Uri "$baseUrl$endpoint" -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                $successCount++
            }
        } catch {
            # Continue checking other endpoints
        }
    }
    
    if ($successCount -eq $endpoints.Count) {
        return "All actuator endpoints accessible ($successCount/$($endpoints.Count))"
    } else {
        return "Some actuator endpoints accessible ($successCount/$($endpoints.Count))"
    }
}

# Test 7: Application Startup Time
Test-Component -Name "Application Performance" -TestScript {
    $startTime = Get-Date
    $response = Invoke-WebRequest -Uri "$baseUrl/actuator/health" -TimeoutSec 10
    $endTime = Get-Date
    $responseTime = ($endTime - $startTime).TotalMilliseconds
    
    if ($responseTime -lt 5000) {
        return "Response time: $([math]::Round($responseTime, 2))ms (Good)"
    } elseif ($responseTime -lt 10000) {
        return "Response time: $([math]::Round($responseTime, 2))ms (Acceptable)"
    } else {
        return "Response time: $([math]::Round($responseTime, 2))ms (Slow)"
    }
}

# Generate Health Report
Write-Host "`n=== Health Check Summary ===" -ForegroundColor Cyan

$healthyCount = ($healthResults | Where-Object { $_.Status -eq "HEALTHY" }).Count
$unhealthyCount = ($healthResults | Where-Object { $_.Status -eq "UNHEALTHY" }).Count
$errorCount = ($healthResults | Where-Object { $_.Status -eq "ERROR" }).Count

Write-Host "Total Components Checked: $($healthResults.Count)" -ForegroundColor White
Write-Host "Healthy: $healthyCount" -ForegroundColor Green
Write-Host "Unhealthy: $unhealthyCount" -ForegroundColor Yellow
Write-Host "Errors: $errorCount" -ForegroundColor Red

if ($unhealthyCount -eq 0 -and $errorCount -eq 0) {
    Write-Host "`n🎉 ALL SYSTEMS HEALTHY! 🎉" -ForegroundColor Green
    $overallStatus = "HEALTHY"
} elseif ($errorCount -eq 0) {
    Write-Host "`n⚠️  SOME ISSUES DETECTED ⚠️" -ForegroundColor Yellow
    $overallStatus = "DEGRADED"
} else {
    Write-Host "`n❌ CRITICAL ISSUES DETECTED ❌" -ForegroundColor Red
    $overallStatus = "UNHEALTHY"
}

Write-Host "`nOverall System Status: $overallStatus" -ForegroundColor $(if ($overallStatus -eq "HEALTHY") { "Green" } elseif ($overallStatus -eq "DEGRADED") { "Yellow" } else { "Red" })

Write-Host "`n=== Detailed Results ===" -ForegroundColor Cyan
foreach ($result in $healthResults) {
    $color = switch ($result.Status) {
        "HEALTHY" { "Green" }
        "UNHEALTHY" { "Yellow" }
        "ERROR" { "Red" }
    }
    Write-Host "$($result.Component): $($result.Status) - $($result.Details)" -ForegroundColor $color
}

Write-Host "`nHealth check completed at $(Get-Date)" -ForegroundColor Gray
