package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.TestStatus;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * Diagnostic result DTO from diagnostics-schema.graphqls.
 * Updated to match test expectations.
 */
@Data
@Builder
public class DiagnosticResult {
    // Fields expected by tests
    private String testId;
    private String testSuite;
    private TestStatus status;
    private TestStatus overallResult;
    private OffsetDateTime startTime;
    private OffsetDateTime endTime;
    private Long duration; // milliseconds
    private Integer testsRun;
    private Integer testsPassed;
    private Integer testsFailed;
    private Integer testsSkipped;
    private DiagnosticSummary summary;
    private List<String> recommendations;

    // Schema fields (for GraphQL compatibility)
    private String diagnosticId;
    private OffsetDateTime startedAt;
    private OffsetDateTime completedAt;
    private Integer totalTests;
    private Integer passedTests;
    private Integer failedTests;
    private Integer skippedTests;
    private List<DiagnosticTestResult> results;
}
