package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Input DTO for single permission in bulk operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SinglePermissionInput {
    
    private String userId;
    private String roleName;
    private Permission permissionType;
    private LocalDateTime expiresAt;
    private String notes;
    
    public void validate() {
        if (userId == null && roleName == null) {
            throw new IllegalArgumentException("Either userId or roleName must be provided");
        }
        
        if (userId != null && roleName != null) {
            throw new IllegalArgumentException("Cannot specify both userId and roleName");
        }
        
        if (permissionType == null) {
            throw new IllegalArgumentException("Permission type is required");
        }
    }
}
