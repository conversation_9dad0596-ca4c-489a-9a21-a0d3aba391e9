# AWS EC2 Production Deployment - Docker Compose Configuration
# This configuration is optimized for AWS EC2 deployment with proper CORS settings
version: '3.8'

services:
  # =============================================================================
  # APPLICATION SERVICES
  # =============================================================================
  
  # DMS Service
  dms-svc:
    build:
      context: ./dms-svc
      dockerfile: Dockerfile
      target: runtime
    container_name: dms-svc
    ports:
      - "9093:9093"
      - "9464:9464"  # Prometheus metrics endpoint
    environment:
      - SPRING_PROFILES_ACTIVE=aws-ec2
      - SERVER_PORT=9093
      - SPRING_DATASOURCE_URL=***********************/${MYSQL_DMS_DATABASE:-dms_db}?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
      - SPRING_DATASOURCE_USERNAME=${MYSQL_DMS_USER:-dms_user}
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_DMS_PASSWORD:-dms_password}
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-shared_redis_password}
      - DMS_STORAGE_PROVIDER=${DMS_STORAGE_PROVIDER:-LOCAL}
      - DMS_STORAGE_LOCAL_BASE_PATH=${DMS_STORAGE_LOCAL_BASE_PATH:-/app/storage}
      - JWT_SECRET=${JWT_SECRET:-sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      
      # AWS EC2 Specific Configuration
      - AWS_EC2_PUBLIC_IP=${AWS_EC2_PUBLIC_IP}
      - AWS_EC2_DOMAIN=${AWS_EC2_DOMAIN}
      
      # CORS Configuration for Production
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - CORS_ALLOWED_ORIGIN_PATTERNS=${CORS_ALLOWED_ORIGIN_PATTERNS}
      
      # Tracing Configuration
      - OTEL_TRACES_EXPORTER=zipkin
      - OTEL_EXPORTER_ZIPKIN_ENDPOINT=http://zipkin:9411/api/v2/spans
      - OTEL_METRICS_EXPORTER=console
      - OTEL_LOGS_EXPORTER=console
      
    volumes:
      - dms_storage:/app/storage
      - dms_logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9093/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Notification Service
  notification-svc:
    build:
      context: ./notification-svc
      dockerfile: Dockerfile
    container_name: notification-svc
    ports:
      - "9091:9091"
    environment:
      # Database Configuration
      - SPRING_DATASOURCE_URL=***********************/${MYSQL_NOTIFICATION_DATABASE:-notification_db}?createDatabaseIfNotExist=true&useSSL=false&allowPublicKeyRetrieval=true
      - SPRING_DATASOURCE_USERNAME=${MYSQL_NOTIFICATION_USER:-notification_user}
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_NOTIFICATION_PASSWORD:-notification_password}
      
      # RabbitMQ Configuration
      - SPRING_RABBITMQ_HOST=rabbitmq
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=${RABBITMQ_DEFAULT_USER:-admin}
      - SPRING_RABBITMQ_PASSWORD=${RABBITMQ_DEFAULT_PASS:-admin123}
      
      # Redis Configuration
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-shared_redis_password}
      
      # Application Configuration
      - SPRING_PROFILES_ACTIVE=aws-ec2
      - SERVER_PORT=9091
      - JAVA_OPTS=${NOTIFICATION_JAVA_OPTS:--Xmx1g -Xms512m}
      
      # AWS EC2 Specific Configuration
      - AWS_EC2_PUBLIC_IP=${AWS_EC2_PUBLIC_IP}
      - AWS_EC2_DOMAIN=${AWS_EC2_DOMAIN}
      
      # CORS Configuration for Production
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      
      # Security Configuration
      - SECURITY_JWT_ENABLED=true
      - JWT_ISSUER=notification-service
      - JWT_AUDIENCE=notification-clients
      - JWT_SECRET=${JWT_SECRET:-sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      
      # Email Configuration
      - MAIL_HOST=${MAIL_HOST:-smtp.gmail.com}
      - MAIL_PORT=${MAIL_PORT:-587}
      - MAIL_USERNAME=${MAIL_USERNAME:-}
      - MAIL_PASSWORD=${MAIL_PASSWORD:-}
      - NOTIFICATION_FROM_EMAIL=${NOTIFICATION_FROM_EMAIL:-<EMAIL>}
      - EMAIL_ENABLED=${EMAIL_ENABLED:-true}
      - EMAIL_MOCK=${EMAIL_MOCK:-false}
      
    volumes:
      - ./notification-svc/logs:/app/logs
      - ./notification-svc/keys:/app/keys:ro
    depends_on:
      mysql:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # =============================================================================
  # SHARED INFRASTRUCTURE SERVICES
  # =============================================================================

  # Shared MySQL Database
  mysql:
    image: mysql:8.0
    container_name: mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      - MYSQL_DATABASE=${MYSQL_DMS_DATABASE:-dms_db}
      - MYSQL_USER=${MYSQL_DMS_USER:-dms_user}
      - MYSQL_PASSWORD=${MYSQL_DMS_PASSWORD:-dms_password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d:ro
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-root_password}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: --default-authentication-plugin=mysql_native_password

  # Shared Redis Cache
  redis:
    image: redis:7-alpine
    container_name: redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-shared_redis_password} --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-shared_redis_password}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS:-admin123}
      - RABBITMQ_DEFAULT_VHOST=/
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Elasticsearch for Search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - grc-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Zipkin for Distributed Tracing
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: zipkin
    ports:
      - "9411:9411"
    networks:
      - grc-platform-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  elasticsearch_data:
    driver: local
  dms_storage:
    driver: local
  dms_logs:
    driver: local

networks:
  grc-platform-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
