# Shared Infrastructure Deployment Guide

## 🎯 **Problem Solved**

You correctly identified that the original individual service configurations would create **separate infrastructure instances** instead of sharing them. This guide provides the **correct solution** for true infrastructure sharing.

## ⚠️ **Original Problem**

With the original configurations:
```bash
# Step 1: Deploy notification service
./docker-manage-notification-only.sh start-all
# Creates: notification-mysql, notification-redis, notification-rabbitmq

# Step 2: Later deploy DMS service  
./docker-manage-dms-only.sh start-all
# Creates: dms-mysql, dms-redis, dms-elasticsearch
# ❌ RESULT: TWO separate MySQL instances! No sharing!
```

## ✅ **New Solution: Shared Infrastructure Approach**

I've created a **3-tier deployment architecture**:

### 1. **Shared Infrastructure Layer**
- **File**: `docker-compose.infrastructure.yml`
- **Script**: `./docker-manage-shared-infra.sh start-infra`
- **Contains**: MySQL, Redis, RabbitMQ, Elasticsearch, Monitoring

### 2. **Application Layer - Notification Service**
- **File**: `docker-compose.notification-app-only.yml`
- **Script**: `./docker-manage-shared-infra.sh start-notification`
- **Connects to**: Shared infrastructure

### 3. **Application Layer - DMS Service**
- **File**: `docker-compose.dms-app-only.yml`
- **Script**: `./docker-manage-shared-infra.sh start-dms`
- **Connects to**: Shared infrastructure

## 🚀 **Deployment Scenarios**

### **Scenario 1: Deploy Notification Service First, Add DMS Later**

```bash
# Step 1: Deploy shared infrastructure
./docker-manage-shared-infra.sh start-infra

# Step 2: Deploy notification service (connects to shared infra)
./docker-manage-shared-infra.sh start-notification

# ✅ Notification service is running with shared MySQL, Redis, RabbitMQ

# Step 3: Later, add DMS service (uses SAME infrastructure)
./docker-manage-shared-infra.sh start-dms

# ✅ DMS service connects to the SAME MySQL instance!
# ✅ Both services share Redis, monitoring, etc.
```

### **Scenario 2: Deploy DMS Service First, Add Notification Later**

```bash
# Step 1: Deploy shared infrastructure
./docker-manage-shared-infra.sh start-infra

# Step 2: Deploy DMS service
./docker-manage-shared-infra.sh start-dms

# Step 3: Later, add notification service
./docker-manage-shared-infra.sh start-notification
```

### **Scenario 3: Deploy Everything at Once**

```bash
# Deploy infrastructure + both services
./docker-manage-shared-infra.sh start-all
```

## 🏗️ **Architecture Overview**

### **Shared Infrastructure Containers**
```
grc-mysql-shared          (Port 3306)
├── Database: dms_db (dms_user/dms_password)
└── Database: notification_db (notification_user/notification_password)

grc-redis-shared          (Port 6379)
├── Prefix: dms: (for DMS service)
└── Prefix: notification: (for Notification service)

grc-rabbitmq-shared       (Port 5672, 15672)
grc-elasticsearch-shared  (Port 9200, 9300)
grc-prometheus-shared     (Port 9090)
grc-grafana-shared        (Port 3000)
grc-zipkin-shared         (Port 9411)
```

### **Application Containers**
```
notification-service-app  (Port 9091)
├── Connects to: grc-mysql-shared
├── Connects to: grc-redis-shared
└── Connects to: grc-rabbitmq-shared

dms-service-app          (Port 9093, 9464)
├── Connects to: grc-mysql-shared
├── Connects to: grc-redis-shared
└── Connects to: grc-elasticsearch-shared
```

## 📊 **Resource Usage**

### **Infrastructure Only**
```
Memory: ~2GB
Containers: 7 (MySQL, Redis, RabbitMQ, Elasticsearch, Prometheus, Grafana, Zipkin)
```

### **+ Notification Service**
```
Memory: ~3GB (+1GB)
Containers: 8 (+1 app container)
```

### **+ DMS Service**
```
Memory: ~4GB (+1GB)
Containers: 9 (+1 app container)
```

## 🔧 **Database Sharing Details**

### **Single MySQL Instance with Multiple Databases**
```sql
-- Created by shared-init script
CREATE DATABASE `dms_db`;
CREATE DATABASE `notification_db`;

-- Users with appropriate permissions
CREATE USER 'dms_user'@'%';           -- Full access to dms_db
CREATE USER 'notification_user'@'%';  -- Full access to notification_db

-- Cross-service read access for future integrations
GRANT SELECT ON `notification_db`.* TO 'dms_user'@'%';
GRANT SELECT ON `dms_db`.* TO 'notification_user'@'%';
```

### **Connection Details**
- **DMS Service**: Connects to `grc-mysql-shared:3306/dms_db`
- **Notification Service**: Connects to `grc-mysql-shared:3306/notification_db`
- **Same MySQL instance, different databases**

## 🌐 **Service URLs**

### **Infrastructure Services**
- **MySQL**: localhost:3306 (root/root_password)
- **Redis**: localhost:6379 (shared_redis_password)
- **RabbitMQ Management**: http://localhost:15672 (admin/admin123)
- **Elasticsearch**: http://localhost:9200
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)
- **Zipkin**: http://localhost:9411

### **Application Services**
- **Notification GraphQL**: http://localhost:9091/graphql
- **Notification GraphiQL**: http://localhost:9091/graphiql
- **DMS GraphQL**: http://localhost:9093/dms/graphql
- **DMS GraphiQL**: http://localhost:9093/graphiql

## 🛠️ **Management Commands**

### **Infrastructure Management**
```bash
# Start shared infrastructure
./docker-manage-shared-infra.sh start-infra

# Stop shared infrastructure (stops all dependent services)
./docker-manage-shared-infra.sh stop-infra

# Check infrastructure status
./docker-manage-shared-infra.sh status
```

### **Service Management**
```bash
# Start notification service (requires shared infra)
./docker-manage-shared-infra.sh start-notification

# Start DMS service (requires shared infra)
./docker-manage-shared-infra.sh start-dms

# Stop individual services (keeps infrastructure running)
./docker-manage-shared-infra.sh stop-notification
./docker-manage-shared-infra.sh stop-dms
```

### **Combined Operations**
```bash
# Start everything
./docker-manage-shared-infra.sh start-all

# Stop everything
./docker-manage-shared-infra.sh stop-all

# Health check
./docker-manage-shared-infra.sh health

# View logs
./docker-manage-shared-infra.sh logs              # All services
./docker-manage-shared-infra.sh logs infra        # Infrastructure only
./docker-manage-shared-infra.sh logs notification # Notification only
./docker-manage-shared-infra.sh logs dms          # DMS only
```

## 🔒 **Production Considerations**

### **Environment Configuration**
```bash
# Copy and customize environment
cp .env.template .env

# Key settings for shared infrastructure
MYSQL_ROOT_PASSWORD=your-secure-password
REDIS_PASSWORD=your-redis-password
RABBITMQ_DEFAULT_PASS=your-rabbitmq-password
JWT_SECRET=your-jwt-secret
```

### **Security**
- All services use the same JWT secret for consistency
- Cross-database read access for future integrations
- Shared network with proper container naming

### **Monitoring**
- Single Prometheus instance monitors all services
- Single Grafana instance with dashboards for both services
- Unified logging and tracing

## 🎉 **Benefits of This Approach**

### ✅ **True Infrastructure Sharing**
- Single MySQL instance with multiple databases
- Single Redis instance with service-specific prefixes
- Shared monitoring and observability stack

### ✅ **Flexible Deployment**
- Deploy services in any order
- Add/remove services without affecting others
- Infrastructure persists independently

### ✅ **Resource Efficiency**
- No duplicate infrastructure services
- Optimal memory usage
- Shared monitoring reduces overhead

### ✅ **Operational Simplicity**
- Single point of infrastructure management
- Unified monitoring and logging
- Consistent configuration across services

## 🔄 **Migration Path**

### **From Individual Deployments**
```bash
# 1. Stop individual services
./docker-manage-notification-only.sh stop
./docker-manage-dms-only.sh stop

# 2. Start shared infrastructure
./docker-manage-shared-infra.sh start-infra

# 3. Start services using shared infrastructure
./docker-manage-shared-infra.sh start-notification
./docker-manage-shared-infra.sh start-dms
```

### **Data Migration** (if needed)
```bash
# Backup from individual deployments
docker-compose -f docker-compose.notification-only.yml exec mysql mysqldump -u root -p notification_db > notification_backup.sql

# Restore to shared infrastructure
docker-compose -f docker-compose.infrastructure.yml exec mysql mysql -u root -p notification_db < notification_backup.sql
```

## ✅ **Answer to Your Question**

**YES! With this new shared infrastructure approach:**

1. **Deploy notification service first**: `./docker-manage-shared-infra.sh start-infra && ./docker-manage-shared-infra.sh start-notification`

2. **Later deploy DMS service**: `./docker-manage-shared-infra.sh start-dms`

3. **Result**: Both services use the **SAME MySQL instance** with separate databases, **SAME Redis instance** with different prefixes, and **SAME monitoring stack**.

The infrastructure is truly shared and persistent across service deployments! 🎯
