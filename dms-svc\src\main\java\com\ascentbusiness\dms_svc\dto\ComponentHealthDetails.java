package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Component health details DTO from diagnostics-schema.graphqls.
 */
@Data
@Builder
public class ComponentHealthDetails {
    private String description;
    private String version;
    private Boolean connected;
    private String lastError;
    private List<HealthMetric> metrics;
    private String configuration; // JSON string
}
