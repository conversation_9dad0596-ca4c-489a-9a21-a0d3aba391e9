-- liquibase formatted sql

-- changeset dms:049-create-classification-collection-tables
-- comment: Create missing collection tables for ComplianceClassification entity

-- Create classification_data_subjects table for @ElementCollection mapping
CREATE TABLE IF NOT EXISTS classification_data_subjects (
    classification_id BIGINT NOT NULL,
    data_subject_category VARCHAR(50) NOT NULL,
    PRIMARY KEY (classification_id, data_subject_category),
    CONSTRAINT fk_classification_data_subjects_classification
        FOREIGN KEY (classification_id) REFERENCES compliance_classifications(id)
        ON DELETE CASCADE
);

-- Create classification_regions table for @ElementCollection mapping
CREATE TABLE IF NOT EXISTS classification_regions (
    classification_id BIGINT NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (classification_id, region),
    CONSTRAINT fk_classification_regions_classification
        FOREIGN KEY (classification_id) REFERENCES compliance_classifications(id)
        ON DELETE CASCADE
);

-- Add indexes for better performance
CREATE INDEX idx_classification_data_subjects_classification_id 
    ON classification_data_subjects(classification_id);

CREATE INDEX idx_classification_regions_classification_id 
    ON classification_regions(classification_id);

-- rollback DROP TABLE classification_regions;
-- rollback DROP TABLE classification_data_subjects;