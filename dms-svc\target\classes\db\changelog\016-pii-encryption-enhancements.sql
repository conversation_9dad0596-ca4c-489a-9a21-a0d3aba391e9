--liquibase formatted sql

--changeset dms:016-pii-encryption-enhancements-001
--comment: Modify field lengths to support PII encryption in metadata tables

-- Increase field lengths in document_ownership_metadata for encrypted data
ALTER TABLE document_ownership_metadata 
MODIFY COLUMN owner VARCHAR(500),
MODIFY COLUMN approver VARCHAR(500);

-- Increase field lengths in document_compliance_metadata for encrypted data
ALTER TABLE document_compliance_metadata 
MODIFY COLUMN third_party_id VARCHAR(500);

-- Note: consent_reference column modifications moved to migration 029+ where the column is actually created

--changeset dms:016-pii-encryption-enhancements-002
--comment: Add PII encryption configuration to security_config table

-- Insert PII encryption configuration
INSERT INTO security_config (config_key, config_value, description, created_by, is_active, created_date, last_modified_date) VALUES
('PII_ENCRYPTION_ENABLED', 'false', 'Enable field-level encryption for PII data', 'SYSTEM', true, NOW(), NOW()),
('PII_ENCRYPTION_KEY_ROTATION_ENABLED', 'true', 'Enable automatic key rotation for PII encryption', 'SYSTEM', true, NOW(), NOW()),
('PII_ENCRYPTION_KEY_ROTATION_INTERVAL_HOURS', '168', 'PII key rotation interval in hours (weekly)', 'SYSTEM', true, NOW(), NOW()),
('PII_ENCRYPTION_MASTER_KEY', '', 'Base64-encoded master key for PII encryption (leave empty for auto-generation)', 'SYSTEM', true, NOW(), NOW()),
('PII_ENCRYPTION_AUTO_DETECT', 'true', 'Enable automatic PII detection in field content', 'SYSTEM', true, NOW(), NOW()),
('PII_ENCRYPTION_PERFORMANCE_MODE', 'true', 'Enable performance optimizations for PII encryption', 'SYSTEM', true, NOW(), NOW());

--changeset dms:016-pii-encryption-enhancements-003
--comment: Create pii_encryption_keys table for key management

-- Create table for PII encryption key management
CREATE TABLE pii_encryption_keys (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    key_id VARCHAR(100) NOT NULL UNIQUE,
    key_data TEXT NOT NULL,
    algorithm VARCHAR(50) NOT NULL DEFAULT 'AES-256-GCM',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    rotation_date TIMESTAMP,
    usage_count BIGINT DEFAULT 0,
    last_used_date TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'SYSTEM',
    notes TEXT
);

-- Add indexes
CREATE INDEX idx_pii_encryption_keys_key_id ON pii_encryption_keys(key_id);
CREATE INDEX idx_pii_encryption_keys_is_active ON pii_encryption_keys(is_active);
CREATE INDEX idx_pii_encryption_keys_created_date ON pii_encryption_keys(created_date);

--changeset dms:016-pii-encryption-enhancements-004
--comment: Create pii_encryption_metrics table for monitoring

-- Create table for PII encryption metrics and monitoring
CREATE TABLE pii_encryption_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_date DATE NOT NULL,
    total_pii_fields BIGINT DEFAULT 0,
    encrypted_pii_fields BIGINT DEFAULT 0,
    encryption_success_rate DECIMAL(5,2) DEFAULT 0.00,
    decryption_success_rate DECIMAL(5,2) DEFAULT 0.00,
    key_rotations BIGINT DEFAULT 0,
    encryption_errors BIGINT DEFAULT 0,
    decryption_errors BIGINT DEFAULT 0,
    auto_detected_pii BIGINT DEFAULT 0,
    performance_avg_encrypt_ms DECIMAL(10,3) DEFAULT 0.000,
    performance_avg_decrypt_ms DECIMAL(10,3) DEFAULT 0.000,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add indexes
CREATE UNIQUE INDEX idx_pii_encryption_metrics_date ON pii_encryption_metrics(metric_date);
CREATE INDEX idx_pii_encryption_metrics_created_date ON pii_encryption_metrics(created_date);

--changeset dms:016-pii-encryption-enhancements-005
--comment: Add PII encryption event types to audit system

-- Note: audit_actions table doesn't exist yet, so these INSERT statements are commented out
-- These will be added when the audit_actions table is created in a future changeset
-- INSERT INTO audit_actions (action_name, description, risk_level, retention_days) VALUES
-- ('PII_ENCRYPTION_ENABLED', 'PII field-level encryption was enabled', 'LOW', 2555),
-- ('PII_ENCRYPTION_DISABLED', 'PII field-level encryption was disabled', 'MEDIUM', 2555),
-- ('PII_ENCRYPTION_KEY_GENERATED', 'New PII encryption key was generated', 'LOW', 2555),
-- ('PII_ENCRYPTION_KEY_ROTATED', 'PII encryption key was rotated', 'LOW', 2555),
-- ('PII_FIELD_ENCRYPTED', 'PII field was encrypted', 'LOW', 2555),
-- ('PII_FIELD_DECRYPTED', 'PII field was decrypted', 'LOW', 2555),
-- ('PII_ENCRYPTION_ERROR', 'Error occurred during PII field encryption', 'HIGH', 2555),
-- ('PII_DECRYPTION_ERROR', 'Error occurred during PII field decryption', 'HIGH', 2555),
-- ('PII_AUTO_DETECTED', 'PII was automatically detected in field content', 'MEDIUM', 2555),
-- ('PII_ENCRYPTION_KEY_COMPROMISED', 'PII encryption key may be compromised', 'CRITICAL', 2555);

--changeset dms:016-pii-encryption-enhancements-006
--comment: Create stored procedure for PII encryption cleanup - DISABLED

-- Note: Stored procedure creation is disabled due to Liquibase compatibility issues with DELIMITER syntax
-- The cleanup functionality can be implemented in application code instead

-- -- Create stored procedure for cleaning up old PII encryption keys
-- -- This is commented out due to Liquibase not supporting DELIMITER syntax properly
-- -- The cleanup can be done via application code instead

--changeset dms:016-pii-encryption-enhancements-007
--comment: Create view for PII encryption monitoring

-- Create view for PII encryption monitoring and reporting
CREATE VIEW pii_encryption_status AS
SELECT 
    DATE(created_date) as encryption_date,
    'document_ownership_metadata' as table_name,
    'owner' as field_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN owner LIKE '{%encrypted%' THEN 1 ELSE 0 END) as encrypted_records,
    ROUND(
        (SUM(CASE WHEN owner LIKE '{%encrypted%' THEN 1 ELSE 0 END) * 100.0) / COUNT(*), 
        2
    ) as encryption_percentage
FROM document_ownership_metadata
WHERE created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_date)

UNION ALL

SELECT 
    DATE(created_date) as encryption_date,
    'document_ownership_metadata' as table_name,
    'approver' as field_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN approver LIKE '{%encrypted%' THEN 1 ELSE 0 END) as encrypted_records,
    ROUND(
        (SUM(CASE WHEN approver LIKE '{%encrypted%' THEN 1 ELSE 0 END) * 100.0) / COUNT(*), 
        2
    ) as encryption_percentage
FROM document_ownership_metadata
WHERE created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_date)

-- Note: consent_reference monitoring moved to migration 029+ where the column is actually created

ORDER BY encryption_date DESC, table_name, field_name;

--changeset dms:016-pii-encryption-enhancements-008
--comment: Add comments and documentation

-- Add table comments for documentation
ALTER TABLE pii_encryption_keys COMMENT = 'PII encryption key management table for field-level encryption';
ALTER TABLE pii_encryption_metrics COMMENT = 'PII encryption metrics and monitoring data';

-- Add column comments for modified tables
ALTER TABLE document_ownership_metadata 
MODIFY COLUMN owner VARCHAR(500) COMMENT 'Document owner (PII encrypted if enabled)',
MODIFY COLUMN approver VARCHAR(500) COMMENT 'Document approver (PII encrypted if enabled)';

ALTER TABLE document_compliance_metadata 
MODIFY COLUMN third_party_id VARCHAR(500) COMMENT 'Third party identifier (PII encrypted if enabled)';

-- Note: consent_reference column comments moved to migration 029+ where the column is actually created
