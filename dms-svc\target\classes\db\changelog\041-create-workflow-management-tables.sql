--liquibase formatted sql

--changeset system:041-create-workflow-management-tables
--comment: Create comprehensive workflow management system tables

-- Disable foreign key checks temporarily to avoid circular dependency issues
SET FOREIGN_KEY_CHECKS = 0;

-- Create workflow_definitions table
CREATE TABLE workflow_definitions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50) NOT NULL DEFAULT '1.0',
    workflow_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Workflow configuration
    configuration_json JSON,
    approval_type VARCHAR(50) NOT NULL DEFAULT 'SEQUENTIAL', -- SEQUENTIAL, PARALLEL, CONDITIONAL
    auto_start BOOLEAN NOT NULL DEFAULT FALSE,
    timeout_hours INTEGER DEFAULT 72,
    escalation_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    escalation_hours INTEGER DEFAULT 24,
    
    -- Triggers and conditions
    trigger_conditions JSON,
    document_types JSON, -- Array of document types this workflow applies to
    department_restrictions JSON, -- Array of departments
    classification_requirements JSON, -- Array of required classifications
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    last_modified_by VARCHAR(255),
    
    -- Constraints
    UNIQUE KEY uk_workflow_name_version (name, version),
    INDEX idx_workflow_type (workflow_type),
    INDEX idx_workflow_active (is_active),
    INDEX idx_workflow_default (is_default)
);

-- Create workflow_stages table
CREATE TABLE workflow_stages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    workflow_definition_id BIGINT NOT NULL,
    stage_name VARCHAR(255) NOT NULL,
    stage_order INTEGER NOT NULL,
    stage_type VARCHAR(50) NOT NULL DEFAULT 'APPROVAL', -- APPROVAL, REVIEW, NOTIFICATION, CONDITIONAL
    
    -- Stage configuration
    is_required BOOLEAN NOT NULL DEFAULT TRUE,
    is_parallel BOOLEAN NOT NULL DEFAULT FALSE,
    min_approvals_required INTEGER DEFAULT 1,
    approval_percentage_required DECIMAL(5,2), -- For percentage-based approvals
    
    -- Assignee configuration
    assignee_type VARCHAR(50) NOT NULL, -- USER, ROLE, DEPARTMENT, DYNAMIC
    assignee_values JSON, -- Array of user IDs, roles, or departments
    fallback_assignees JSON, -- Fallback assignees if primary not available
    
    -- Timing and escalation
    timeout_hours INTEGER DEFAULT 24,
    escalation_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    escalation_hours INTEGER DEFAULT 8,
    escalation_assignees JSON,
    
    -- Conditions and rules
    entry_conditions JSON,
    exit_conditions JSON,
    auto_approve_conditions JSON,
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (workflow_definition_id) REFERENCES workflow_definitions(id) ON DELETE CASCADE,
    UNIQUE KEY uk_workflow_stage_order (workflow_definition_id, stage_order),
    INDEX idx_stage_type (stage_type),
    INDEX idx_stage_order (stage_order)
);

-- Create workflow_transitions table
CREATE TABLE workflow_transitions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    workflow_definition_id BIGINT NOT NULL,
    from_stage_id BIGINT,
    to_stage_id BIGINT,
    transition_name VARCHAR(255) NOT NULL,
    transition_type VARCHAR(50) NOT NULL DEFAULT 'APPROVE', -- APPROVE, REJECT, DELEGATE, ESCALATE, CONDITIONAL
    
    -- Transition conditions
    conditions JSON,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    requires_comment BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (workflow_definition_id) REFERENCES workflow_definitions(id) ON DELETE CASCADE,
    FOREIGN KEY (from_stage_id) REFERENCES workflow_stages(id) ON DELETE CASCADE,
    FOREIGN KEY (to_stage_id) REFERENCES workflow_stages(id) ON DELETE CASCADE,
    INDEX idx_transition_type (transition_type),
    INDEX idx_from_stage (from_stage_id),
    INDEX idx_to_stage (to_stage_id)
);

-- Create workflow_instances table
CREATE TABLE workflow_instances (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    workflow_definition_id BIGINT NOT NULL,
    document_id BIGINT NOT NULL,
    instance_name VARCHAR(255),
    
    -- Status and state
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, IN_PROGRESS, COMPLETED, CANCELLED, FAILED
    current_stage_id BIGINT,
    priority VARCHAR(20) NOT NULL DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, URGENT
    
    -- Timing
    started_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_date TIMESTAMP,
    due_date TIMESTAMP,
    
    -- Context and metadata
    context_data JSON,
    initiator_user_id VARCHAR(255) NOT NULL,
    completion_reason VARCHAR(255),
    
    -- Correlation and tracking
    correlation_id VARCHAR(100),
    parent_workflow_instance_id BIGINT, -- For sub-workflows
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (workflow_definition_id) REFERENCES workflow_definitions(id),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (current_stage_id) REFERENCES workflow_stages(id),
    FOREIGN KEY (parent_workflow_instance_id) REFERENCES workflow_instances(id),
    INDEX idx_workflow_status (status),
    INDEX idx_workflow_document (document_id),
    INDEX idx_workflow_initiator (initiator_user_id),
    INDEX idx_workflow_due_date (due_date),
    INDEX idx_correlation_id (correlation_id)
);

-- Create workflow_tasks table
CREATE TABLE workflow_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    workflow_instance_id BIGINT NOT NULL,
    workflow_stage_id BIGINT NOT NULL,
    task_name VARCHAR(255) NOT NULL,
    
    -- Assignment
    assigned_to_user_id VARCHAR(255),
    assigned_to_role VARCHAR(255),
    assigned_to_department VARCHAR(255),
    assigned_date TIMESTAMP,
    
    -- Status and completion
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, IN_PROGRESS, COMPLETED, SKIPPED, ESCALATED, DELEGATED
    action_taken VARCHAR(50), -- APPROVE, REJECT, DELEGATE, REQUEST_CHANGES, ESCALATE
    completed_date TIMESTAMP,
    completed_by_user_id VARCHAR(255),
    
    -- Task details
    priority VARCHAR(20) NOT NULL DEFAULT 'MEDIUM',
    due_date TIMESTAMP,
    comments TEXT,
    attachments JSON,
    
    -- Delegation and escalation
    delegated_to_user_id VARCHAR(255),
    delegated_date TIMESTAMP,
    escalated_to_user_id VARCHAR(255),
    escalated_date TIMESTAMP,
    escalation_reason VARCHAR(255),
    
    -- Correlation and tracking
    correlation_id VARCHAR(100),
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (workflow_instance_id) REFERENCES workflow_instances(id) ON DELETE CASCADE,
    FOREIGN KEY (workflow_stage_id) REFERENCES workflow_stages(id),
    INDEX idx_task_status (status),
    INDEX idx_task_assigned_user (assigned_to_user_id),
    INDEX idx_task_assigned_role (assigned_to_role),
    INDEX idx_task_due_date (due_date),
    INDEX idx_task_completed_by (completed_by_user_id),
    INDEX idx_task_correlation_id (correlation_id)
);

-- Create workflow_history table
CREATE TABLE workflow_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    workflow_instance_id BIGINT NOT NULL,
    workflow_task_id BIGINT,

    -- Event details
    event_type VARCHAR(50) NOT NULL, -- STARTED, STAGE_ENTERED, TASK_ASSIGNED, TASK_COMPLETED, ESCALATED, DELEGATED, COMPLETED, CANCELLED
    event_description TEXT,

    -- Actor information
    actor_user_id VARCHAR(255),
    actor_role VARCHAR(255),
    on_behalf_of_user_id VARCHAR(255),

    -- Event data
    old_status VARCHAR(50),
    new_status VARCHAR(50),
    stage_name VARCHAR(255),
    task_name VARCHAR(255),
    action_taken VARCHAR(50),
    comments TEXT,

    -- Timing
    event_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Correlation and tracking
    correlation_id VARCHAR(100),

    -- Constraints
    FOREIGN KEY (workflow_instance_id) REFERENCES workflow_instances(id) ON DELETE CASCADE,
    FOREIGN KEY (workflow_task_id) REFERENCES workflow_tasks(id) ON DELETE SET NULL,
    INDEX idx_history_instance (workflow_instance_id),
    INDEX idx_history_event_type (event_type),
    INDEX idx_history_actor (actor_user_id),
    INDEX idx_history_timestamp (event_timestamp),
    INDEX idx_history_correlation_id (correlation_id)
);

-- Create workflow_notifications table
CREATE TABLE workflow_notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    workflow_instance_id BIGINT NOT NULL,
    workflow_task_id BIGINT,

    -- Notification details
    notification_type VARCHAR(50) NOT NULL, -- TASK_ASSIGNED, TASK_DUE, ESCALATION, COMPLETION, REJECTION
    recipient_user_id VARCHAR(255) NOT NULL,
    recipient_email VARCHAR(255),

    -- Message content
    subject VARCHAR(500) NOT NULL,
    message TEXT,
    notification_data JSON,

    -- Delivery status
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, SENT, DELIVERED, FAILED, CANCELLED
    sent_date TIMESTAMP,
    delivered_date TIMESTAMP,
    failure_reason VARCHAR(255),
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,

    -- Scheduling
    scheduled_date TIMESTAMP,

    -- Correlation and tracking
    correlation_id VARCHAR(100),

    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (workflow_instance_id) REFERENCES workflow_instances(id) ON DELETE CASCADE,
    FOREIGN KEY (workflow_task_id) REFERENCES workflow_tasks(id) ON DELETE CASCADE,
    INDEX idx_notification_type (notification_type),
    INDEX idx_notification_recipient (recipient_user_id),
    INDEX idx_notification_status (status),
    INDEX idx_notification_scheduled (scheduled_date),
    INDEX idx_notification_correlation_id (correlation_id)
);

-- Create workflow_templates table for common workflow patterns
CREATE TABLE workflow_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(100) NOT NULL, -- APPROVAL, REVIEW, COMPLIANCE, CUSTOM

    -- Template configuration
    template_json JSON NOT NULL,
    is_system_template BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,

    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_date TIMESTAMP,

    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    last_modified_by VARCHAR(255),

    -- Constraints
    INDEX idx_template_category (category),
    INDEX idx_template_active (is_active),
    INDEX idx_template_system (is_system_template)
);

-- Add workflow status to documents table
ALTER TABLE documents ADD COLUMN workflow_status VARCHAR(50) DEFAULT 'NONE';
ALTER TABLE documents ADD COLUMN current_workflow_instance_id BIGINT;
ALTER TABLE documents ADD COLUMN workflow_completion_date TIMESTAMP;
ALTER TABLE documents ADD INDEX idx_document_workflow_status (workflow_status);
ALTER TABLE documents ADD CONSTRAINT fk_document_workflow_instance
    FOREIGN KEY (current_workflow_instance_id) REFERENCES workflow_instances(id) ON DELETE SET NULL;

-- Insert default workflow templates
INSERT INTO workflow_templates (name, description, category, template_json, is_system_template, created_by) VALUES
('Simple Document Approval', 'Basic single-stage document approval workflow', 'APPROVAL',
 JSON_OBJECT(
   'stages', JSON_ARRAY(
     JSON_OBJECT('name', 'Manager Approval', 'type', 'APPROVAL', 'assignee_type', 'ROLE', 'assignee_values', JSON_ARRAY('MANAGER'))
   ),
   'approval_type', 'SEQUENTIAL',
   'timeout_hours', 48
 ),
 TRUE, 'system'),

('Multi-Stage Document Review', 'Three-stage document review and approval process', 'REVIEW',
 JSON_OBJECT(
   'stages', JSON_ARRAY(
     JSON_OBJECT('name', 'Initial Review', 'type', 'REVIEW', 'assignee_type', 'ROLE', 'assignee_values', JSON_ARRAY('REVIEWER')),
     JSON_OBJECT('name', 'Manager Approval', 'type', 'APPROVAL', 'assignee_type', 'ROLE', 'assignee_values', JSON_ARRAY('MANAGER')),
     JSON_OBJECT('name', 'Final Approval', 'type', 'APPROVAL', 'assignee_type', 'ROLE', 'assignee_values', JSON_ARRAY('ADMIN'))
   ),
   'approval_type', 'SEQUENTIAL',
   'timeout_hours', 72
 ),
 TRUE, 'system'),

('Compliance Document Workflow', 'Compliance-focused workflow with legal review', 'COMPLIANCE',
 JSON_OBJECT(
   'stages', JSON_ARRAY(
     JSON_OBJECT('name', 'Compliance Review', 'type', 'REVIEW', 'assignee_type', 'ROLE', 'assignee_values', JSON_ARRAY('COMPLIANCE_OFFICER')),
     JSON_OBJECT('name', 'Legal Review', 'type', 'REVIEW', 'assignee_type', 'ROLE', 'assignee_values', JSON_ARRAY('LEGAL_COUNSEL')),
     JSON_OBJECT('name', 'Executive Approval', 'type', 'APPROVAL', 'assignee_type', 'ROLE', 'assignee_values', JSON_ARRAY('EXECUTIVE'))
   ),
   'approval_type', 'SEQUENTIAL',
   'timeout_hours', 120,
   'escalation_enabled', TRUE
 ),
 TRUE, 'system'),

('Parallel Approval Workflow', 'Parallel approval by multiple stakeholders', 'APPROVAL',
 JSON_OBJECT(
   'stages', JSON_ARRAY(
     JSON_OBJECT('name', 'Stakeholder Approval', 'type', 'APPROVAL', 'assignee_type', 'ROLE',
                'assignee_values', JSON_ARRAY('MANAGER', 'TECHNICAL_LEAD', 'BUSINESS_ANALYST'),
                'is_parallel', TRUE, 'min_approvals_required', 2)
   ),
   'approval_type', 'PARALLEL',
   'timeout_hours', 48
 ),
 TRUE, 'system');

-- Add comments for documentation
ALTER TABLE workflow_definitions COMMENT = 'Stores workflow definition templates and configurations';
ALTER TABLE workflow_stages COMMENT = 'Defines individual stages within workflow definitions';
ALTER TABLE workflow_transitions COMMENT = 'Defines valid transitions between workflow stages';
ALTER TABLE workflow_instances COMMENT = 'Active workflow instances for specific documents';
ALTER TABLE workflow_tasks COMMENT = 'Individual tasks assigned to users within workflow instances';
ALTER TABLE workflow_history COMMENT = 'Complete audit trail of workflow events and state changes';
ALTER TABLE workflow_notifications COMMENT = 'Notification queue for workflow-related communications';
ALTER TABLE workflow_templates COMMENT = 'Reusable workflow templates for common approval patterns';

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
