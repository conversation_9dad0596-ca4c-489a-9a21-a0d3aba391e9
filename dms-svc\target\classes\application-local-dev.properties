# Local Development Configuration for DMS Service
# This file contains local development-specific settings

# Server Configuration
server.port=9093

# Application Base URL Configuration - Local Development
dms.application.base-url=${DMS_BASE_URL:http://localhost:9093}

# Database Configuration - Local Development
spring.datasource.url=*****************************************************************************************************************************************
spring.datasource.username=dms_user
spring.datasource.password=dms_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration for Local Development
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=900000

# JPA Configuration - Local Development
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Redis Configuration - Local Development
spring.data.redis.host=redis-shared
spring.data.redis.port=6379
spring.data.redis.password=local_redis_password
spring.data.redis.database=0
spring.data.redis.timeout=5000ms
spring.data.redis.connect-timeout=3000ms

# Redis Connection Pool Configuration (Lettuce)
spring.data.redis.lettuce.pool.max-active=5
spring.data.redis.lettuce.pool.max-idle=3
spring.data.redis.lettuce.pool.min-idle=1
spring.data.redis.lettuce.pool.max-wait=2000ms

# GraphQL Configuration - Local Development
spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:true}
spring.graphql.graphiql.path=/graphiql
spring.graphql.path=/graphql
spring.graphql.schema.printer.enabled=true
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.file-extensions=.graphqls,.gqls
spring.graphql.websocket.path=/graphql

# CORS Configuration - Local Development
spring.graphql.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:4200,http://localhost:3000,http://localhost:8080}
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID,Accept
spring.graphql.cors.allow-credentials=true
spring.graphql.cors.max-age=3600

# Storage Configuration - Local Development
dms.storage.provider=LOCAL
dms.storage.local.base-path=/app/storage

# JWT Configuration - Local Development
dms.jwt.secret=${JWT_SECRET:localDevSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
dms.jwt.expiration=3600000
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# Actuator Configuration - Local Development
management.endpoints.web.exposure.include=health,info,metrics,prometheus,env,configprops
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always
management.health.defaults.enabled=true

# Health Indicators
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=true
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true

# OpenTelemetry Configuration - Local Development
otel.service.name=dms-svc-local
otel.service.version=1.0.0-dev
otel.resource.attributes.service.name=dms-svc-local
otel.resource.attributes.service.version=1.0.0-dev
otel.resource.attributes.deployment.environment=local-dev

# OpenTelemetry Tracing Configuration
otel.traces.exporter=zipkin
otel.exporter.zipkin.endpoint=http://zipkin-shared:9411/api/v2/spans
otel.traces.sampler=traceidratio
otel.traces.sampler.arg=1.0

# OpenTelemetry Metrics Configuration
otel.metrics.exporter=console
otel.metric.export.interval=30s

# OpenTelemetry Logs Configuration
otel.logs.exporter=console

# Logging Configuration - Local Development
logging.level.com.ascentbusiness.dms_svc=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=300000
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dms:local:

# Elasticsearch Configuration - Local Development
elasticsearch.enabled=true
elasticsearch.host=elasticsearch-shared
elasticsearch.port=9200
elasticsearch.protocol=http

# Virus Scanning Configuration - Disabled for Local Development
dms.virus-scanning.enabled=false
dms.virus-scanning.fail-on-unavailable=false
dms.virus-scanning.default-scanner=MOCK

# Mock Scanner Configuration
dms.virus-scanner.mock.enabled=true

# Development Performance Settings
spring.jpa.properties.hibernate.jdbc.batch_size=10
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# File Upload Configuration - Local Development
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# Development Features
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# Cross-service Communication - Local Development
notification.service.url=${NOTIFICATION_SERVICE_URL:http://localhost:9091}
