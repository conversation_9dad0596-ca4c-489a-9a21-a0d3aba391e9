<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.23.xsd">

    <include file="db/changelog/001-create-initial-schema.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/002-insert-initial-data.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/003-add-user-audit-columns.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/004-add-document-permissions.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/005-add-audit-columns-to-document-permissions.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/006-insert-sample-data.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/007-create-document-versions-and-access-roles.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/008-add-user-context-metadata.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/009-consolidate-permissions-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/010-fix-document-permissions-issue.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/011-security-enhancements.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/012-fix-security-audit-columns.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/013-add-correlation-id-to-security-violations.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/014-create-storage-configurations-table.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/015-audit-encryption-enhancements.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/015-create-document-metadata-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/016-pii-encryption-enhancements.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/016-create-user-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/017-api-versioning-support.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/017-remove-user-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/018-fix-audit-action-enum.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/025-create-retention-policy-tables.sql" relativeToChangelogFile="false"/>
    <include file="db/changelog/026-create-compliance-framework-tables.sql" relativeToChangelogFile="false"/>
    <!-- GRC enhancements - H2 compatible for all environments -->
    <include file="db/changelog/028-enhance-audit-system-grc-h2.sql" relativeToChangelogFile="false"/>
    <!-- Add compliance fields to documents table -->
    <include file="db/changelog/029-add-compliance-fields-to-documents.sql" relativeToChangelogFile="false"/>
    <!-- Add missing consent_reference column to document_compliance_mappings table -->
    <include file="db/changelog/031-add-missing-consent-reference-column.sql" relativeToChangelogFile="false"/>
    <!-- Add remaining missing columns to document_compliance_mappings table -->
    <include file="db/changelog/032-add-remaining-missing-columns.sql" relativeToChangelogFile="false"/>
    <!-- Performance optimization indexes - temporarily disabled due to MySQL syntax issues -->
    <!-- <include file="db/changelog/033-add-performance-indexes.sql" relativeToChangelogFile="false"/> -->
    <!-- Workflow Management System -->
    <include file="db/changelog/041-create-workflow-management-tables.sql" relativeToChangelogFile="false"/>
    <!-- Add priority field to workflow notifications -->
    <include file="db/changelog/042-add-priority-to-workflow-notifications.sql" relativeToChangelogFile="false"/>
    <!-- Document Templates System -->
    <include file="db/changelog/042-create-document-templates-tables.sql" relativeToChangelogFile="false"/>
    <!-- Webhook and Event System -->
    <include file="db/changelog/043-create-webhook-event-system-tables.sql" relativeToChangelogFile="false"/>
    <!-- Add source template relationship to documents -->
    <include file="db/changelog/044-add-source-template-id-to-documents.sql" relativeToChangelogFile="false"/>
    <!-- File Processing System - Async Jobs and Chunked Uploads -->
    <include file="db/changelog/045-create-file-processing-tables.sql" relativeToChangelogFile="false"/>
    <!-- Document Sharing System - Share Links and Bulk Operations -->
    <include file="db/changelog/046-create-document-sharing-tables.sql" relativeToChangelogFile="false"/>
    <!-- Fix chunked upload audit columns -->
    <include file="db/changelog/047-fix-chunked-upload-audit-columns.sql" relativeToChangelogFile="false"/>
    <!-- Add missing success column to audit_logs table -->
    <include file="db/changelog/048-add-success-column-to-audit-logs.sql" relativeToChangelogFile="false"/>
    <!-- Create missing collection tables for ComplianceClassification entity -->
    <include file="db/changelog/049-create-classification-collection-tables.sql" relativeToChangelogFile="false"/>
    <!-- Add missing created_by column to compliance_violations table (fixed syntax) -->
    <include file="db/changelog/051-add-created-by-to-compliance-violations-fixed.sql" relativeToChangelogFile="false"/>
    <!-- Add missing last_modified_by column to compliance_violations table -->
    <include file="db/changelog/052-add-last-modified-by-to-compliance-violations.sql" relativeToChangelogFile="false"/>
    <!-- Add missing action_attempted column to compliance_violations table -->
    <include file="db/changelog/053-add-action-attempted-to-compliance-violations.sql" relativeToChangelogFile="false"/>
    <!-- Sync compliance_violations table with entity definition - add all missing columns -->
    <include file="db/changelog/054-sync-compliance-violations-with-entity.sql" relativeToChangelogFile="false"/>
    <!-- Add missing remediation_action column to compliance_violations table -->
    <include file="db/changelog/055-add-remediation-action-column.sql" relativeToChangelogFile="false"/>
    <!-- Add missing resolved_date column to compliance_violations table -->
    <include file="db/changelog/056-add-resolved-date-column.sql" relativeToChangelogFile="false"/>
    <!-- Create missing collection tables for DocumentComplianceMapping entity -->
    <include file="db/changelog/057-create-document-compliance-mapping-collection-tables.sql" relativeToChangelogFile="false"/>
    <!-- Create missing event_processing_log table for EventProcessingLog entity -->
    <include file="db/changelog/058-create-event-processing-log-table.sql" relativeToChangelogFile="false"/>
    <!-- Create missing system tables: system_events and event_subscriptions -->
    <include file="db/changelog/059-create-missing-system-tables.sql" relativeToChangelogFile="false"/>
    <!-- Add foreign key constraints to event_processing_log table -->
    <include file="db/changelog/060-add-event-processing-log-foreign-keys.sql" relativeToChangelogFile="false"/>
    <!-- Fix event_processing_log table column names to match BaseEntity -->
    <include file="db/changelog/061-fix-event-processing-log-columns.sql" relativeToChangelogFile="false"/>
    <!-- Fix system_events and event_subscriptions table column names to match BaseEntity -->
    <include file="db/changelog/062-fix-system-tables-audit-columns.sql" relativeToChangelogFile="false"/>
    <!-- Create missing event_templates table for EventTemplate entity -->
    <include file="db/changelog/063-create-event-templates-table.sql" relativeToChangelogFile="false"/>
    <!-- Fix template tables audit columns to match BaseEntity -->
    <include file="db/changelog/064-fix-template-tables-audit-columns.sql" relativeToChangelogFile="false"/>
    <!-- Add missing created_by column to template_permissions table -->
    <include file="db/changelog/065-fix-template-permissions-created-by.sql" relativeToChangelogFile="false"/>
    <!-- Fix webhook_deliveries table audit columns to match BaseEntity -->
    <include file="db/changelog/066-fix-webhook-deliveries-audit-columns.sql" relativeToChangelogFile="false"/>
    <!-- Fix workflow_history table audit columns to match BaseEntity -->
    <include file="db/changelog/067-fix-workflow-history-audit-columns.sql" relativeToChangelogFile="false"/>
    <!-- Fix workflow_instances table audit columns to match BaseEntity -->
    <include file="db/changelog/068-fix-workflow-instances-audit-columns.sql" relativeToChangelogFile="false"/>
    <!-- Fix workflow_notifications table audit columns to match BaseEntity -->
    <include file="db/changelog/069-fix-workflow-notifications-audit-columns.sql" relativeToChangelogFile="false"/>
    <!-- Fix workflow_stages table audit columns to match BaseEntity -->
    <include file="db/changelog/070-fix-workflow-stages-audit-columns.sql" relativeToChangelogFile="false"/>
    <!-- Fix remaining workflow tables audit columns to match BaseEntity (with existence checks) -->
    <include file="db/changelog/072-fix-remaining-workflow-tables-audit-columns.sql" relativeToChangelogFile="false"/>
    <!-- Fix audit action constraint to include CREATE action -->
    <include file="db/changelog/030-fix-audit-action-constraint-add-create.sql" relativeToChangelogFile="false"/>

</databaseChangeLog>
