package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ProcessingStrategy;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;

import java.util.List;

/**
 * Extended input DTO for document upload from server path with processing strategy options.
 * 
 * <p>This DTO extends the standard document upload from path functionality with
 * additional options for controlling the processing strategy and
 * chunk size for large file uploads.
 */
@Data
public class UploadDocumentFromPathExInput {
    
    /**
     * Name of the document.
     */
    private String name;
    
    /**
     * Optional description of the document.
     */
    private String description;
    
    /**
     * Server-side file path to upload.
     */
    private String sourceFilePath;
    
    /**
     * Optional keywords/tags for the document.
     */
    private List<String> keywords;
    
    /**
     * Optional storage provider override.
     */
    private StorageProvider storageProvider;
    
    /**
     * Whether to override existing files with the same name.
     * Default: false
     */
    private Boolean overrideFile = false;
    
    /**
     * Optional classification metadata for the document.
     */
    private DocumentClassificationMetadataInput classificationMetadata;
    
    /**
     * Optional ownership metadata for the document.
     */
    private DocumentOwnershipMetadataInput ownershipMetadata;
    
    /**
     * Optional compliance metadata for the document.
     */
    private DocumentComplianceMetadataInput complianceMetadata;
    
    /**
     * Force a specific processing strategy instead of auto-detection.
     * If null, the system will automatically determine the best strategy
     * based on file size and configuration.
     */
    private ProcessingStrategy forceProcessingStrategy;
    
    /**
     * Custom chunk size for chunked uploads (in bytes).
     * If null, the system will use the optimal chunk size based on file size.
     * Must be within the configured min/max chunk size limits.
     */
    private Integer chunkSize;

    /**
     * Type of virus scanner to use for scanning the uploaded file.
     * If null, the system will use the default configured scanner.
     */
    private VirusScannerType scannerType;
    
    /**
     * Check if this upload has custom processing options.
     * 
     * @return true if custom processing options are specified
     */
    public boolean hasCustomProcessingOptions() {
        return forceProcessingStrategy != null || chunkSize != null;
    }
    
    /**
     * Check if this upload forces a specific processing strategy.
     * 
     * @return true if a processing strategy is forced
     */
    public boolean hasForceProcessingStrategy() {
        return forceProcessingStrategy != null;
    }
    
    /**
     * Check if this upload specifies a custom chunk size.
     * 
     * @return true if a custom chunk size is specified
     */
    public boolean hasCustomChunkSize() {
        return chunkSize != null && chunkSize > 0;
    }
    
    /**
     * Get the file size if available.
     * 
     * @return file size in bytes, or 0 if not determinable
     */
    public long getFileSize() {
        if (sourceFilePath != null) {
            try {
                java.nio.file.Path path = java.nio.file.Paths.get(sourceFilePath);
                return java.nio.file.Files.size(path);
            } catch (Exception e) {
                return 0;
            }
        }
        return 0;
    }
    
    /**
     * Get the filename from the source path.
     * 
     * @return filename, or null if not available
     */
    public String getFilename() {
        if (sourceFilePath != null) {
            return java.nio.file.Paths.get(sourceFilePath).getFileName().toString();
        }
        return null;
    }
    
    /**
     * Check if the upload has metadata.
     * 
     * @return true if any metadata is present
     */
    public boolean hasMetadata() {
        return classificationMetadata != null || 
               ownershipMetadata != null || 
               complianceMetadata != null;
    }
    
    /**
     * Convert to standard DocumentUploadFromPathInput for compatibility.
     *
     * @return standard upload from path input
     */
    public DocumentUploadFromPathInput toStandardInput() {
        DocumentUploadFromPathInput standardInput = new DocumentUploadFromPathInput();
        standardInput.setName(name);
        standardInput.setSourceFilePath(sourceFilePath);
        standardInput.setKeywords(keywords);
        standardInput.setStorageProvider(storageProvider);
        standardInput.setOverrideFile(overrideFile);
        standardInput.setScannerType(scannerType);
        standardInput.setClassificationMetadata(classificationMetadata);
        standardInput.setOwnershipMetadata(ownershipMetadata);
        standardInput.setComplianceMetadata(complianceMetadata);
        return standardInput;
    }
}
