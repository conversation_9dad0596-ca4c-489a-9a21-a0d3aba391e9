# Extended File Processing Guide

## Overview

The DMS system now supports dynamic file processing strategies that automatically determine the optimal processing approach based on file size and characteristics. This guide covers the new extended file processing features including asynchronous processing and chunked uploads.

## Processing Strategies

### 1. Direct Processing (DIRECT)
- **Use Case**: Small files that can be processed immediately
- **Default Threshold**: Files ≤ 10MB
- **Characteristics**:
  - Synchronous processing
  - Immediate response with document
  - No progress tracking needed
  - Fastest for small files

### 2. Asynchronous Processing (ASYNC)
- **Use Case**: Medium-sized files requiring background processing
- **Default Threshold**: Files > 10MB and ≤ 100MB
- **Characteristics**:
  - Background processing with job tracking
  - Returns job ID for status monitoring
  - Progress tracking available
  - Prevents request timeouts

### 3. Chunked Upload Processing (CHUNKED)
- **Use Case**: Large files requiring chunk-based upload
- **Default Threshold**: Files > 100MB
- **Characteristics**:
  - Upload in smaller chunks
  - Resumable uploads
  - Session-based management
  - Progress tracking per chunk

## Configuration

### Application Properties

```properties
# File Processing Configuration
dms.file-processing.direct-processing-threshold=10485760
dms.file-processing.async-processing-threshold=104857600
dms.file-processing.max-file-size=1073741824

# Chunk upload configuration
dms.file-processing.default-chunk-size=5242880
dms.file-processing.max-chunk-size=52428800
dms.file-processing.min-chunk-size=1048576

# Async processing configuration
dms.file-processing.max-concurrent-async-jobs=10
dms.file-processing.async-job-timeout-seconds=3600

# Session and cleanup configuration
dms.file-processing.chunk-session-timeout-seconds=86400
dms.file-processing.cleanup-interval-seconds=3600
dms.file-processing.temp-directory=./temp/processing

# Feature flags
dms.file-processing.enable-auto-cleanup=true
dms.file-processing.enable-progress-tracking=true
dms.file-processing.enable-detailed-logging=false
```

## GraphQL API

### Extended Upload Mutations

#### uploadDocumentEx
Upload a document with automatic processing strategy determination:

```graphql
mutation UploadDocumentEx($input: UploadDocumentExInput!) {
  uploadDocumentEx(input: $input) {
    id
    name
    processingStrategy
    processingStatus
    processingJobId
    processingProgress
    statusCheckUrl
  }
}
```

#### uploadDocumentFromPathEx
Upload a document from server path with processing strategy:

```graphql
mutation UploadDocumentFromPathEx($input: UploadDocumentFromPathExInput!) {
  uploadDocumentFromPathEx(input: $input) {
    id
    name
    processingStrategy
    processingStatus
    processingJobId
    processingProgress
  }
}
```

### Chunked Upload Operations

#### uploadChunk
Upload an individual chunk:

```graphql
mutation UploadChunk($input: ChunkUploadInput!) {
  uploadChunk(input: $input) {
    sessionId
    receivedChunks
    totalChunks
    progress
    status
  }
}
```

#### completeChunkedUpload
Complete chunked upload and create document:

```graphql
mutation CompleteChunkedUpload($input: CompleteChunkedUploadInput!) {
  completeChunkedUpload(input: $input) {
    id
    name
    processingStrategy
    processingStatus
  }
}
```

### Status Tracking Queries

#### documentProcessingStatus
Check async job status:

```graphql
query DocumentProcessingStatus($jobId: ID!) {
  documentProcessingStatus(jobId: $jobId) {
    jobId
    status
    fileName
    fileSize
    progress
    estimatedTimeRemaining
    startedAt
    completedAt
    errorMessage
    document {
      id
      name
    }
  }
}
```

#### chunkedUploadStatus
Check chunked upload session status:

```graphql
query ChunkedUploadStatus($sessionId: ID!) {
  chunkedUploadStatus(sessionId: $sessionId) {
    sessionId
    fileName
    totalSize
    status
    receivedChunks
    totalChunks
    receivedBytes
    progress
    createdAt
    lastActivityAt
    errorMessage
  }
}
```

## Usage Examples

### 1. Automatic Strategy Selection

```javascript
// Small file - will use DIRECT strategy
const smallFileUpload = await graphql(`
  mutation {
    uploadDocumentEx(input: {
      name: "Small Document"
      file: $smallFile
      storageProvider: LOCAL
    }) {
      id
      processingStrategy
      processingStatus
    }
  }
`);

// Medium file - will use ASYNC strategy
const mediumFileUpload = await graphql(`
  mutation {
    uploadDocumentEx(input: {
      name: "Medium Document"
      file: $mediumFile
      storageProvider: LOCAL
    }) {
      processingJobId
      processingStrategy
      statusCheckUrl
    }
  }
`);
```

### 2. Forced Strategy

```javascript
// Force async processing for a small file
const forcedAsync = await graphql(`
  mutation {
    uploadDocumentEx(input: {
      name: "Small but Async"
      file: $file
      forceProcessingStrategy: ASYNC
      storageProvider: LOCAL
    }) {
      processingJobId
      processingStrategy
    }
  }
`);
```

### 3. Chunked Upload Workflow

```javascript
// 1. Create upload session (handled by client)
const session = createChunkedUploadSession(file);

// 2. Upload chunks
for (let i = 0; i < session.totalChunks; i++) {
  const chunk = file.slice(i * chunkSize, (i + 1) * chunkSize);
  
  const result = await graphql(`
    mutation {
      uploadChunk(input: {
        sessionId: "${session.id}"
        chunkNumber: ${i + 1}
        chunk: $chunk
      }) {
        progress
        receivedChunks
        totalChunks
      }
    }
  `);
  
  console.log(`Progress: ${result.progress}%`);
}

// 3. Complete upload
const document = await graphql(`
  mutation {
    completeChunkedUpload(input: {
      sessionId: "${session.id}"
      name: "Large Document"
      storageProvider: LOCAL
    }) {
      id
      name
    }
  }
`);
```

### 4. Progress Monitoring

```javascript
// Monitor async job progress
const pollJobStatus = async (jobId) => {
  const status = await graphql(`
    query {
      documentProcessingStatus(jobId: "${jobId}") {
        status
        progress
        estimatedTimeRemaining
        document {
          id
        }
      }
    }
  `);
  
  if (status.status === 'COMPLETED') {
    console.log('Document ready:', status.document.id);
  } else if (status.status === 'FAILED') {
    console.error('Processing failed');
  } else {
    console.log(`Progress: ${status.progress}%, ETA: ${status.estimatedTimeRemaining}s`);
    setTimeout(() => pollJobStatus(jobId), 5000); // Poll every 5 seconds
  }
};
```

## Error Handling

### Common Error Scenarios

1. **File Too Large for Direct Upload**
   ```
   FileTooLargeException: File size (150MB) exceeds direct upload limit. 
   Please use chunked upload with recommended chunk size: 5MB
   ```

2. **Invalid Chunk Size**
   ```
   IllegalArgumentException: Chunk size must be between 1MB and 50MB
   ```

3. **Expired Upload Session**
   ```
   IllegalStateException: Upload session has expired
   ```

4. **Missing Chunks**
   ```
   IllegalStateException: Missing chunks - expected 10, found 8
   ```

## Best Practices

1. **File Size Thresholds**: Adjust thresholds based on your infrastructure capacity
2. **Chunk Size**: Use optimal chunk sizes (system calculates automatically)
3. **Progress Tracking**: Implement client-side progress indicators for better UX
4. **Error Recovery**: Handle network failures with retry logic for chunked uploads
5. **Cleanup**: Enable auto-cleanup to manage temporary files
6. **Monitoring**: Monitor async job queues and processing times

## Troubleshooting

### Performance Issues
- Check `max-concurrent-async-jobs` setting
- Monitor temporary directory disk space
- Verify database performance for job tracking

### Upload Failures
- Check file size limits and chunk size constraints
- Verify temporary directory permissions
- Monitor session timeout settings

### Memory Issues
- Adjust chunk sizes for large files
- Enable auto-cleanup for temporary files
- Monitor JVM heap usage during processing
