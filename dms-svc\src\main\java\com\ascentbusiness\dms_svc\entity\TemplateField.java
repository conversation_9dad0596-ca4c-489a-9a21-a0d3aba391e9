package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.TemplateFieldType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;

/**
 * Entity representing a dynamic field within a document template
 */
@Entity
@Table(name = "template_fields")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateField extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", nullable = false)
    @JsonIgnore
    private DocumentTemplate template;

    @Column(name = "field_name", nullable = false, length = 255)
    private String fieldName;

    @Column(name = "field_label", nullable = false, length = 255)
    private String fieldLabel;

    @Enumerated(EnumType.STRING)
    @Column(name = "field_type", nullable = false, length = 50)
    private TemplateFieldType fieldType;

    // Field configuration
    @Column(name = "field_order", nullable = false)
    @Builder.Default
    private Integer fieldOrder = 0;

    @Builder.Default
    @Column(name = "is_required", nullable = false)
    private Boolean isRequired = false;

    @Builder.Default
    @Column(name = "is_readonly", nullable = false)
    private Boolean isReadonly = false;

    @Builder.Default
    @Column(name = "is_hidden", nullable = false)
    private Boolean isHidden = false;

    // Field validation
    @Column(name = "validation_pattern", length = 500)
    private String validationPattern; // Regex pattern

    @Column(name = "min_length")
    private Integer minLength;

    @Column(name = "max_length")
    private Integer maxLength;

    @Column(name = "min_value", precision = 19, scale = 4)
    private BigDecimal minValue;

    @Column(name = "max_value", precision = 19, scale = 4)
    private BigDecimal maxValue;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "allowed_values", columnDefinition = "JSON")
    private JsonNode allowedValues; // For SELECT/MULTISELECT fields

    // Field appearance
    @Column(name = "placeholder_text", length = 255)
    private String placeholderText;

    @Column(name = "help_text", columnDefinition = "TEXT")
    private String helpText;

    @Column(name = "default_value", columnDefinition = "TEXT")
    private String defaultValue;

    // Field behavior
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "conditional_logic", columnDefinition = "JSON")
    private JsonNode conditionalLogic; // Show/hide based on other field values

    @Column(name = "calculation_formula", columnDefinition = "TEXT")
    private String calculationFormula; // For calculated fields

    /**
     * Check if this field is required
     */
    @Transient
    public boolean isFieldRequired() {
        return isRequired != null && isRequired;
    }

    /**
     * Check if this field is readonly
     */
    @Transient
    public boolean isFieldReadonly() {
        return isReadonly != null && isReadonly;
    }

    /**
     * Check if this field is hidden
     */
    @Transient
    public boolean isFieldHidden() {
        return isHidden != null && isHidden;
    }

    /**
     * Check if this field has validation rules
     */
    @Transient
    public boolean hasValidation() {
        return (validationPattern != null && !validationPattern.trim().isEmpty()) ||
               minLength != null || maxLength != null ||
               minValue != null || maxValue != null;
    }

    /**
     * Check if this field has allowed values (for SELECT/MULTISELECT)
     */
    @Transient
    public boolean hasAllowedValues() {
        return allowedValues != null && !allowedValues.isNull() && allowedValues.size() > 0;
    }

    /**
     * Check if this field has conditional logic
     */
    @Transient
    public boolean hasConditionalLogic() {
        return conditionalLogic != null && !conditionalLogic.isNull();
    }

    /**
     * Check if this field is calculated
     */
    @Transient
    public boolean isCalculated() {
        return TemplateFieldType.CALCULATED.equals(fieldType) ||
               (calculationFormula != null && !calculationFormula.trim().isEmpty());
    }

    /**
     * Check if this field supports multiple values
     */
    @Transient
    public boolean supportsMultipleValues() {
        return TemplateFieldType.MULTISELECT.equals(fieldType);
    }

    /**
     * Check if this field is a selection type
     */
    @Transient
    public boolean isSelectionType() {
        return TemplateFieldType.SELECT.equals(fieldType) || 
               TemplateFieldType.MULTISELECT.equals(fieldType);
    }

    /**
     * Check if this field is a numeric type
     */
    @Transient
    public boolean isNumericType() {
        return TemplateFieldType.NUMBER.equals(fieldType) ||
               TemplateFieldType.CURRENCY.equals(fieldType) ||
               TemplateFieldType.PERCENTAGE.equals(fieldType);
    }

    /**
     * Check if this field is a date/time type
     */
    @Transient
    public boolean isDateTimeType() {
        return TemplateFieldType.DATE.equals(fieldType) ||
               TemplateFieldType.DATETIME.equals(fieldType);
    }

    /**
     * Get the template name
     */
    @Transient
    public String getTemplateName() {
        return template != null ? template.getName() : null;
    }

    /**
     * Get field display name with required indicator
     */
    @Transient
    public String getDisplayLabel() {
        String label = fieldLabel != null ? fieldLabel : fieldName;
        return isFieldRequired() ? label + " *" : label;
    }

    @Override
    public String toString() {
        return String.format("TemplateField{id=%d, name='%s', label='%s', type=%s, required=%s}", 
                           getId(), fieldName, fieldLabel, fieldType, isRequired);
    }
}
