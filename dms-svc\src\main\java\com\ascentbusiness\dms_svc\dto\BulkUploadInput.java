package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Input DTO for bulk document upload operations.
 * 
 * <p>This DTO encapsulates all the parameters needed to upload multiple documents
 * in a single operation, providing efficient batch processing for large-scale
 * upload scenarios with virus scanning support.
 * 
 * <p>Key features:
 * <ul>
 *   <li>Multiple file upload support</li>
 *   <li>Individual virus scanning per file</li>
 *   <li>Skip infected files and continue with clean ones</li>
 *   <li>Detailed status reporting per file</li>
 *   <li>Common metadata for all files</li>
 * </ul>
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkUploadInput {
    
    /**
     * List of files to upload.
     */
    private List<MultipartFile> files;
    
    /**
     * Base name prefix for documents (will be combined with original filename).
     */
    private String namePrefix;
    
    /**
     * Common description for all documents.
     */
    private String description;
    
    /**
     * Common keywords/tags for all documents.
     */
    private List<String> keywords;
    
    /**
     * Storage provider to use for all uploads.
     */
    private StorageProvider storageProvider;
    
    /**
     * Whether to override existing files with the same name.
     */
    private Boolean overrideFile = false;
    
    /**
     * Type of virus scanner to use for all files.
     * If null, the default scanner will be used.
     */
    private VirusScannerType scannerType;
    
    /**
     * Whether to continue processing remaining files if some fail virus scanning.
     * Default: true (skip infected files and continue with clean ones).
     */
    private Boolean continueOnVirusDetection = true;
    
    /**
     * Whether to continue processing remaining files if some fail for other reasons.
     * Default: false (stop on first non-virus error).
     */
    private Boolean continueOnError = false;
    
    /**
     * Common classification metadata for all documents.
     */
    private DocumentClassificationMetadataInput classificationMetadata;
    
    /**
     * Common ownership metadata for all documents.
     */
    private DocumentOwnershipMetadataInput ownershipMetadata;
    
    /**
     * Common compliance metadata for all documents.
     */
    private DocumentComplianceMetadataInput complianceMetadata;
    
    /**
     * Gets the total number of files to upload.
     * 
     * @return the number of files
     */
    public int getTotalFiles() {
        return files != null ? files.size() : 0;
    }
    
    /**
     * Checks if there are any files to upload.
     * 
     * @return true if files are present, false otherwise
     */
    public boolean hasFiles() {
        return files != null && !files.isEmpty();
    }
    
    /**
     * Checks if virus scanning should continue on detection.
     * 
     * @return true if processing should continue when viruses are detected
     */
    public boolean shouldContinueOnVirusDetection() {
        return continueOnVirusDetection != null ? continueOnVirusDetection : true;
    }
    
    /**
     * Checks if processing should continue on general errors.
     * 
     * @return true if processing should continue on non-virus errors
     */
    public boolean shouldContinueOnError() {
        return continueOnError != null ? continueOnError : false;
    }
    
    /**
     * Validates the bulk upload input.
     * 
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (!hasFiles()) {
            throw new IllegalArgumentException("At least one file must be provided for bulk upload");
        }
        
        // Check for empty files
        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);
            if (file == null || file.isEmpty()) {
                throw new IllegalArgumentException(String.format("File at index %d is null or empty", i));
            }
        }
    }
}
