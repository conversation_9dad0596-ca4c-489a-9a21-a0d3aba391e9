package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Input DTO for revoking document permissions
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RevokePermissionInput {
    
    private Long documentId;
    private String userId;
    private String roleName;
    
    public void validate() {
        if (documentId == null) {
            throw new IllegalArgumentException("Document ID is required");
        }
        
        if (userId == null && roleName == null) {
            throw new IllegalArgumentException("Either userId or roleName must be provided");
        }
        
        if (userId != null && roleName != null) {
            throw new IllegalArgumentException("Cannot specify both userId and roleName");
        }
    }
}
