package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.SecurityViolationType;
import com.ascentbusiness.dms_svc.enums.ViolationSeverity;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Entity representing security violations in the DMS system.
 * Tracks unauthorized access attempts, policy violations, and security incidents
 * with detailed information for audit and investigation purposes.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@Entity
@Table(name = "security_violations")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SecurityViolation extends BaseEntity {

    /** User ID who committed the security violation */
    @Column(name = "user_id", nullable = false, length = 100)
    private String userId;

    /** Type of security violation that occurred */
    @Enumerated(EnumType.STRING)
    @Column(name = "violation_type", nullable = false)
    private SecurityViolationType violationType;

    /** Document involved in the security violation, if applicable */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id")
    private Document document;

    /** Action that was attempted when the violation occurred */
    @Column(name = "attempted_action", length = 100)
    private String attemptedAction;

    /** IP address from which the violation originated */
    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    /** User agent string of the client that caused the violation */
    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;

    /** Detailed description of the security violation */
    @Column(name = "violation_details", columnDefinition = "TEXT")
    private String violationDetails;

    /** Severity level of the security violation */
    @Enumerated(EnumType.STRING)
    @Column(name = "severity", nullable = false)
    @Builder.Default
    private ViolationSeverity severity = ViolationSeverity.MEDIUM;

    /** Whether this security violation has been resolved */
    @Column(name = "is_resolved", nullable = false)
    @Builder.Default
    private Boolean isResolved = false;

    /** User ID of the person who resolved this violation */
    @Column(name = "resolved_by", length = 100)
    private String resolvedBy;

    /** Date and time when this violation was resolved */
    @Column(name = "resolved_date")
    private LocalDateTime resolvedDate;

    /** Correlation ID for tracking related events */
    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    /**
     * Helper method to mark this security violation as resolved.
     * @param resolvedByUserId the user ID of the person resolving the violation
     */
    public void resolveViolation(String resolvedByUserId) {
        this.isResolved = true;
        this.resolvedBy = resolvedByUserId;
        this.resolvedDate = LocalDateTime.now();
    }
}
