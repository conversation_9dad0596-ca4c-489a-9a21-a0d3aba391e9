# AWS EC2 Production Configuration for DMS Service
# This file contains AWS EC2-specific settings for production deployment

# Server Configuration
server.port=9093

# Application Base URL Configuration - AWS EC2 with Custom Domain
dms.application.base-url=${DMS_BASE_URL:https://dms-service.autoresilience.com}

# Database Configuration - AWS EC2
spring.datasource.url=*********************************************************************************************************************************************************************************************
spring.datasource.username=dms_user
spring.datasource.password=dms_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration for Production
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# Redis Configuration - AWS EC2
spring.data.redis.host=redis
spring.data.redis.port=6379
spring.data.redis.password=shared_redis_password
spring.data.redis.database=0
spring.data.redis.timeout=5000ms
spring.data.redis.connect-timeout=3000ms

# Redis Connection Pool Configuration (Lettuce)
spring.data.redis.lettuce.pool.max-active=20
spring.data.redis.lettuce.pool.max-idle=10
spring.data.redis.lettuce.pool.min-idle=2
spring.data.redis.lettuce.pool.max-wait=2000ms
spring.data.redis.lettuce.pool.time-between-eviction-runs=30s

# GraphQL Configuration - AWS EC2 Production
spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:false}
spring.graphql.graphiql.path=/graphiql
spring.graphql.path=/graphql
spring.graphql.schema.printer.enabled=false
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.file-extensions=.graphqls,.gqls
spring.graphql.websocket.path=/graphql

# CORS Configuration - AWS EC2 Production
# CRITICAL: Specify exact origins for production - DO NOT use wildcards with credentials
spring.graphql.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID,Accept
spring.graphql.cors.allow-credentials=true
spring.graphql.cors.max-age=3600

# Alternative: Use origin patterns if you need subdomain support
# spring.graphql.cors.allowed-origin-patterns=${CORS_ALLOWED_ORIGIN_PATTERNS}

# Storage Configuration - AWS EC2
dms.storage.provider=LOCAL
dms.storage.local.base-path=/app/storage

# JWT Configuration - Production
dms.jwt.secret=${JWT_SECRET}
dms.jwt.expiration=3600000
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# Actuator Configuration - Production
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized
management.endpoint.health.show-components=when-authorized
management.health.defaults.enabled=true

# Health Indicators
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=true
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true

# OpenTelemetry Configuration - AWS EC2
otel.service.name=dms-svc
otel.service.version=1.0.0
otel.resource.attributes.service.name=dms-svc
otel.resource.attributes.service.version=1.0.0
otel.resource.attributes.deployment.environment=aws-ec2

# OpenTelemetry Tracing Configuration
otel.traces.exporter=zipkin
otel.exporter.zipkin.endpoint=http://zipkin:9411/api/v2/spans
otel.traces.sampler=traceidratio
otel.traces.sampler.arg=0.1

# OpenTelemetry Metrics Configuration
otel.metrics.exporter=console
otel.metric.export.interval=30s

# OpenTelemetry Logs Configuration
otel.logs.exporter=console

# Logging Configuration - Production
logging.level.com.ascentbusiness.dms_svc=INFO
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=1800000
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dms:

# Elasticsearch Configuration - AWS EC2
elasticsearch.enabled=false
elasticsearch.host=elasticsearch
elasticsearch.port=9200
elasticsearch.protocol=http

# Virus Scanning Configuration
dms.virus-scanning.enabled=true
dms.virus-scanning.fail-on-unavailable=false
dms.virus-scanning.default-scanner=MOCK

# Mock Scanner Configuration (for AWS EC2 environment)
dms.virus-scanner.mock.enabled=true

# Security Headers Configuration - Production
dms.security.headers.content-security-policy=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';
dms.security.headers.x-frame-options=DENY
dms.security.headers.x-content-type-options=nosniff
dms.security.headers.x-xss-protection=1; mode=block
dms.security.headers.referrer-policy=strict-origin-when-cross-origin
dms.security.headers.permissions-policy=geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(self), sync-xhr=()
dms.security.headers.cross-origin-embedder-policy=unsafe-none
dms.security.headers.cross-origin-opener-policy=unsafe-none
dms.security.headers.cross-origin-resource-policy=cross-origin

# Production Performance Settings
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# File Upload Configuration - Production
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB

# Disable multipart GraphQL Upload scalar auto-registration to avoid conflicts
spring.graphql.multipart.springboot.enabled=false
