# Prometheus Configuration for Local Development
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # DMS Service Metrics
  - job_name: 'dms-svc-local'
    static_configs:
      - targets: ['dms-svc-local:9464']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Notification Service Metrics
  - job_name: 'notification-svc-local'
    static_configs:
      - targets: ['notification-svc-local:9091']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Shared Infrastructure Metrics
  - job_name: 'mysql-shared'
    static_configs:
      - targets: ['mysql-shared-local:3306']
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'redis-shared'
    static_configs:
      - targets: ['redis-shared-local:6379']
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'rabbitmq-shared'
    static_configs:
      - targets: ['rabbitmq-shared-local:15692']
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'elasticsearch-shared'
    static_configs:
      - targets: ['elasticsearch-shared-local:9200']
    metrics_path: '/_prometheus/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
