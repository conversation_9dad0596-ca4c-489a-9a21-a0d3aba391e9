package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionPriority;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * Generic input DTO for file conversion operations from conversion-schema.graphqls.
 */
@Data
public class ConversionInput {
    private MultipartFile file;
    private String filePath;
    private String fromFormat;
    private String toFormat;
    private VirusScannerType scannerType;
    private ConversionOptionsInput options;
    private ConversionPriority priority = ConversionPriority.NORMAL;
}
