package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Search document with highlighting and relevance information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchDocument {

    private String id;
    private String name;
    private String description;
    private Integer version;
    private String status;
    private String storageProvider;
    private String filePath;
    private String createdBy;
    private String createdDate;
    private String lastModifiedDate;
    private List<String> keywords;
    private List<String> tags;

    // Search-specific fields
    private Float relevanceScore;
    private DocumentHighlights highlights;

    // Metadata
    private Object classificationMetadata;
    private Object ownershipMetadata;
    private Object complianceMetadata;
}
