package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.EventType;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;

/**
 * Entity representing customizable event templates
 */
@Entity
@Table(name = "event_templates")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EventTemplate extends BaseEntity {

    @Column(name = "name", nullable = false, unique = true, length = 255)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", nullable = false, length = 100)
    private EventType eventType;

    // Template configuration
    @Column(name = "template_format", nullable = false, length = 50)
    @Builder.Default
    private String templateFormat = "JSON"; // JSON, XML, PLAIN_TEXT

    @Column(name = "template_content", nullable = false, columnDefinition = "TEXT")
    private String templateContent;

    // Template variables
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "available_variables", columnDefinition = "JSON")
    private JsonNode availableVariables;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "required_variables", columnDefinition = "JSON")
    private JsonNode requiredVariables;

    // Template metadata
    @Builder.Default
    @Column(name = "is_system_template", nullable = false)
    private Boolean isSystemTemplate = false;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "version", nullable = false, length = 50)
    @Builder.Default
    private String version = "1.0";

    // Usage tracking
    @Column(name = "usage_count")
    @Builder.Default
    private Integer usageCount = 0;

    @Column(name = "last_used_date")
    private LocalDateTime lastUsedDate;

    @Column(name = "created_by", nullable = false, length = 255)
    private String createdBy;

    @Column(name = "last_modified_by", length = 255)
    private String lastModifiedBy;

    /**
     * Check if this template is currently active
     */
    @Transient
    public boolean isCurrentlyActive() {
        return isActive != null && isActive;
    }

    /**
     * Check if this is a system-provided template
     */
    @Transient
    public boolean isSystemProvided() {
        return isSystemTemplate != null && isSystemTemplate;
    }

    /**
     * Check if this template has been used recently (within last 30 days)
     */
    @Transient
    public boolean isRecentlyUsed() {
        return lastUsedDate != null && 
               lastUsedDate.isAfter(LocalDateTime.now().minusDays(30));
    }

    /**
     * Check if this template is popular (used more than 10 times)
     */
    @Transient
    public boolean isPopular() {
        return usageCount != null && usageCount > 10;
    }

    /**
     * Check if template has available variables defined
     */
    @Transient
    public boolean hasAvailableVariables() {
        return availableVariables != null && !availableVariables.isNull() && availableVariables.size() > 0;
    }

    /**
     * Check if template has required variables defined
     */
    @Transient
    public boolean hasRequiredVariables() {
        return requiredVariables != null && !requiredVariables.isNull() && requiredVariables.size() > 0;
    }

    /**
     * Get the number of available variables
     */
    @Transient
    public int getAvailableVariableCount() {
        if (availableVariables == null || availableVariables.isNull()) return 0;
        return availableVariables.size();
    }

    /**
     * Get the number of required variables
     */
    @Transient
    public int getRequiredVariableCount() {
        if (requiredVariables == null || requiredVariables.isNull()) return 0;
        return requiredVariables.size();
    }

    /**
     * Check if template format is JSON
     */
    @Transient
    public boolean isJsonFormat() {
        return "JSON".equals(templateFormat);
    }

    /**
     * Check if template format is XML
     */
    @Transient
    public boolean isXmlFormat() {
        return "XML".equals(templateFormat);
    }

    /**
     * Check if template format is plain text
     */
    @Transient
    public boolean isPlainTextFormat() {
        return "PLAIN_TEXT".equals(templateFormat);
    }

    /**
     * Increment usage count and update last used date
     */
    public void recordUsage() {
        this.usageCount = (this.usageCount != null ? this.usageCount : 0) + 1;
        this.lastUsedDate = LocalDateTime.now();
    }

    /**
     * Get template format display name
     */
    @Transient
    public String getTemplateFormatDisplayName() {
        if (templateFormat == null) return "Unknown";
        
        switch (templateFormat.toUpperCase()) {
            case "JSON":
                return "JSON";
            case "XML":
                return "XML";
            case "PLAIN_TEXT":
                return "Plain Text";
            default:
                return templateFormat;
        }
    }

    /**
     * Get event type display name
     */
    @Transient
    public String getEventTypeDisplayName() {
        return eventType != null ? eventType.name().replace("_", " ").toLowerCase() : "Unknown";
    }

    @Override
    public String toString() {
        return String.format("EventTemplate{id=%d, name='%s', eventType=%s, format='%s', active=%s}", 
                           getId(), name, eventType, templateFormat, isActive);
    }
}
