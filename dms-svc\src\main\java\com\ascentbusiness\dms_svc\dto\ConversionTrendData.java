package com.ascentbusiness.dms_svc.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

/**
 * DTO for conversion trend data from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionTrendData {
    private LocalDate date;
    private Long totalConversions;
    private Long successfulConversions;
    private Long failedConversions;
    private Float averageProcessingTime;
}
