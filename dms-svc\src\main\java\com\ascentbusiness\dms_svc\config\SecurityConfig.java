package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.filter.CorrelationIdFilter;
import com.ascentbusiness.dms_svc.security.JwtAuthenticationEntryPoint;
import com.ascentbusiness.dms_svc.security.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.beans.factory.annotation.Value;

import java.util.Arrays;
import java.util.List;

/**
 * Security configuration for the DMS application.
 * Configures JWT authentication, CORS, security headers, and access control.
 * Provides comprehensive security settings including HSTS, CSP, and frame options.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    /** JWT authentication entry point for handling unauthorized access */
    @Autowired
    private JwtAuthenticationEntryPoint unauthorizedHandler;

    /** Security headers configuration */
    @Autowired
    private SecurityHeadersConfig securityHeadersConfig;

    /** Allowed origins for CORS configuration */
    //@Value("${spring.graphql.cors.allowed-origins:http://localhost:3000,http://localhost:8080,http://localhost:9093}")
    @Value("${spring.graphql.cors.allowed-origins:*}")
    private String allowedOrigins;

    /**
     * Creates JWT authentication filter bean.
     * @return configured JWT authentication filter
     */
    @Bean
    public JwtAuthenticationFilter jwtAuthenticationFilter() {
        return new JwtAuthenticationFilter();
    }

    /**
     * Creates correlation ID filter bean for request tracking.
     * @return configured correlation ID filter
     */
    @Bean
    public CorrelationIdFilter correlationIdFilter() {
        return new CorrelationIdFilter();
    }

    /**
     * Creates password encoder bean using BCrypt.
     * @return BCrypt password encoder
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * Creates authentication manager bean.
     * @param authConfig authentication configuration
     * @return configured authentication manager
     * @throws Exception if authentication manager creation fails
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    /**
     * Configures the main security filter chain with comprehensive security settings.
     * Includes security headers, CORS, JWT authentication, and access control rules.
     *
     * @param http HTTP security configuration
     * @return configured security filter chain
     * @throws Exception if security configuration fails
     */
    @Bean
    @Order(2)
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // Comprehensive security headers configuration
            .headers(headers -> headers
                // Frame options
                .frameOptions(frameOptions -> {
                    String frameOption = securityHeadersConfig.getFrameOptions();
                    if (frameOption != null) {
                        switch (frameOption.toUpperCase()) {
                            case "DENY":
                                frameOptions.deny();
                                break;
                            case "SAMEORIGIN":
                                frameOptions.sameOrigin();
                                break;
                            default:
                                frameOptions.deny();
                        }
                    } else {
                        // Default to DENY if frameOption is null
                        frameOptions.deny();
                    }
                })
                // Content type options
                .contentTypeOptions(contentTypeOptions -> {})
                // HSTS configuration
                .httpStrictTransportSecurity(hstsConfig -> {
                    if (securityHeadersConfig.isHstsEnabled()) {
                        hstsConfig
                            .maxAgeInSeconds(securityHeadersConfig.getHstsMaxAge())
                            .includeSubDomains(securityHeadersConfig.isHstsIncludeSubdomains())
                            .preload(securityHeadersConfig.isHstsPreload());
                    }
                })
                // Content Security Policy
                .contentSecurityPolicy(csp -> {
                    if (securityHeadersConfig.isCspEnabled()) {
                        csp.policyDirectives(securityHeadersConfig.getCspPolicy());
                    }
                })
                // Additional security headers - only add if not null
                .addHeaderWriter(securityHeadersConfig.additionalSecurityHeadersWriter() != null 
                    ? securityHeadersConfig.additionalSecurityHeadersWriter() 
                    : new org.springframework.security.web.header.writers.StaticHeadersWriter(
                        "X-XSS-Protection", "1; mode=block",
                        "X-Download-Options", "noopen",
                        "X-Permitted-Cross-Domain-Policies", "none"
                    ))
            )
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .exceptionHandling(exceptionHandling ->
                exceptionHandling.authenticationEntryPoint(unauthorizedHandler))
            .sessionManagement(sessionManagement ->
                sessionManagement.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/graphiql", "/graphiql/**").permitAll()
                .requestMatchers("/graphql").permitAll()
                .requestMatchers("/actuator/**").permitAll()
                .requestMatchers("/h2-console/**").permitAll()
                .requestMatchers("/static/**").permitAll()
                .requestMatchers("/css/**", "/js/**", "/images/**").permitAll()
                .requestMatchers("/favicon.ico").permitAll()
                .requestMatchers("/error").permitAll()
                .anyRequest().authenticated()
            );

        // Add correlation ID filter first in the chain
        http.addFilterBefore(correlationIdFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * Configures CORS (Cross-Origin Resource Sharing) settings.
     * Defines allowed origins, methods, headers, and credentials policy.
     *
     * @return configured CORS configuration source
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        // Parse allowed origins from configuration - more secure than wildcard
        List<String> origins = Arrays.asList(allowedOrigins.split(","));
        configuration.setAllowedOrigins(origins);

        // Allowed methods
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));

        // Specific allowed headers instead of wildcard
        configuration.setAllowedHeaders(Arrays.asList(
            "Content-Type",
            "Authorization",
            "X-Correlation-ID",
            "X-Requested-With"
        ));

        // Allow credentials for authenticated requests
        configuration.setAllowCredentials(true);

        // Cache preflight requests for 1 hour
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
