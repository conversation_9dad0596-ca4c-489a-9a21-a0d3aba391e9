package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for Pandoc-based document conversion functionality.
 * 
 * This configuration manages Pandoc executable settings, conversion timeouts,
 * and fallback behavior when Pandoc is not available.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dms.pandoc")
public class PandocConfig {

    /**
     * Path to the Pandoc executable.
     * Default: "pandoc" (assumes it's in PATH)
     */
    private String executablePath = "pandoc";

    /**
     * Whether Pandoc conversion is enabled.
     * Default: true
     */
    private boolean enabled = true;

    /**
     * Whether to use fallback to existing implementations when Pandoc is not available.
     * Default: true
     */
    private boolean enableFallback = true;

    /**
     * Conversion timeout in seconds.
     * Default: 300 (5 minutes)
     */
    private int timeoutSeconds = 300;

    /**
     * Maximum file size for Pandoc conversion in bytes.
     * Default: 52428800 (50MB)
     */
    private long maxFileSize = 52428800L;

    /**
     * Default virus scanner type for Pandoc conversions.
     * Default: MOCK
     */
    private VirusScannerType virusScanner = VirusScannerType.MOCK;

    /**
     * Temporary directory for Pandoc conversion operations.
     * If empty, uses system temp directory.
     */
    private String tempDirectory = "";

    /**
     * Auto-cleanup converted files after specified hours.
     * Default: 24 hours
     */
    private int cleanupAfterHours = 24;

    /**
     * Whether to check Pandoc availability on startup.
     * Default: true
     */
    private boolean checkAvailabilityOnStartup = true;

    /**
     * Additional Pandoc command line arguments.
     * Default: empty array
     */
    private String[] additionalArgs = {};

    /**
     * Get the maximum file size in a human-readable format.
     * 
     * @return formatted file size string
     */
    public String getMaxFileSizeFormatted() {
        if (maxFileSize >= 1024 * 1024 * 1024) {
            return String.format("%.1f GB", maxFileSize / (1024.0 * 1024.0 * 1024.0));
        } else if (maxFileSize >= 1024 * 1024) {
            return String.format("%.1f MB", maxFileSize / (1024.0 * 1024.0));
        } else if (maxFileSize >= 1024) {
            return String.format("%.1f KB", maxFileSize / 1024.0);
        } else {
            return maxFileSize + " bytes";
        }
    }

    /**
     * Get the timeout in a human-readable format.
     * 
     * @return formatted timeout string
     */
    public String getTimeoutFormatted() {
        if (timeoutSeconds >= 3600) {
            return String.format("%.1f hours", timeoutSeconds / 3600.0);
        } else if (timeoutSeconds >= 60) {
            return String.format("%.1f minutes", timeoutSeconds / 60.0);
        } else {
            return timeoutSeconds + " seconds";
        }
    }

    /**
     * Check if a file size is within the allowed limit.
     * 
     * @param fileSize the file size to check
     * @return true if within limit, false otherwise
     */
    public boolean isFileSizeAllowed(long fileSize) {
        return fileSize <= maxFileSize;
    }

    /**
     * Get the cleanup interval in milliseconds.
     * 
     * @return cleanup interval in milliseconds
     */
    public long getCleanupIntervalMs() {
        return cleanupAfterHours * 60L * 60L * 1000L;
    }

    /**
     * Check if temp directory is configured.
     * 
     * @return true if temp directory is configured, false if using system default
     */
    public boolean hasTempDirectory() {
        return tempDirectory != null && !tempDirectory.trim().isEmpty();
    }

    /**
     * Get the full command array for Pandoc execution.
     * 
     * @param inputFile input file path
     * @param outputFile output file path
     * @param fromFormat source format (e.g., "pdf", "docx", "markdown")
     * @param toFormat target format (e.g., "docx", "pdf")
     * @return command array for ProcessBuilder
     */
    public String[] buildCommand(String inputFile, String outputFile, String fromFormat, String toFormat) {
        String[] baseCommand = {
            executablePath,
            "--from", fromFormat,
            "--to", toFormat,
            "--output", outputFile,
            inputFile
        };

        if (additionalArgs.length == 0) {
            return baseCommand;
        }

        // Merge base command with additional arguments
        String[] fullCommand = new String[baseCommand.length + additionalArgs.length];
        System.arraycopy(baseCommand, 0, fullCommand, 0, baseCommand.length);
        System.arraycopy(additionalArgs, 0, fullCommand, baseCommand.length, additionalArgs.length);
        
        return fullCommand;
    }
}
