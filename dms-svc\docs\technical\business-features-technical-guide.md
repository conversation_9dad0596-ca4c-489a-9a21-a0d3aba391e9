# DMS Business Features Technical Guide

This technical guide provides comprehensive information for developers working with the DMS business features including architecture, implementation details, and integration patterns.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Database Schema](#database-schema)
3. [Service Layer Architecture](#service-layer-architecture)
4. [API Design Patterns](#api-design-patterns)
5. [Event-Driven Architecture](#event-driven-architecture)
6. [Security Implementation](#security-implementation)
7. [Performance Considerations](#performance-considerations)
8. [Testing Strategy](#testing-strategy)
9. [Deployment Guide](#deployment-guide)
10. [Monitoring and Observability](#monitoring-and-observability)

## Architecture Overview

### System Architecture
The DMS business features follow a layered architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────┬─────────────────┬─────────────────────────┤
│   REST APIs     │   GraphQL APIs  │    Web Interface        │
├─────────────────┴─────────────────┴─────────────────────────┤
│                    Service Layer                            │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Workflow      │   Template      │   Webhook/Event         │
│   Services      │   Services      │   Services              │
├─────────────────┴─────────────────┴─────────────────────────┤
│                    Data Access Layer                        │
├─────────────────┬─────────────────┬─────────────────────────┤
│   JPA           │   Custom        │   Event Store           │
│   Repositories  │   Queries       │                         │
├─────────────────┴─────────────────┴─────────────────────────┤
│                    Data Layer                               │
├─────────────────┬─────────────────┬─────────────────────────┤
│   PostgreSQL    │   File Storage  │   Event Queue           │
│   Database      │   (S3/Local)    │   (Redis/RabbitMQ)      │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### Core Components

#### 1. Workflow Management
- **WorkflowDefinitionService**: Manages workflow templates and configurations
- **WorkflowInstanceService**: Handles workflow execution and state management
- **WorkflowTaskService**: Manages individual tasks within workflows
- **WorkflowNotificationService**: Handles workflow-related notifications

#### 2. Document Templates
- **DocumentTemplateService**: Template creation, management, and versioning
- **TemplateProcessingService**: Document generation from templates
- **TemplateValidationService**: Field validation and template integrity
- **TemplateUsageService**: Usage tracking and analytics

#### 3. Webhook & Event System
- **EventService**: System event publishing and management
- **WebhookService**: Webhook endpoint management and delivery
- **EventProcessingService**: Event filtering and routing
- **DeliveryService**: Webhook delivery with retry mechanisms

### Technology Stack

#### Backend
- **Framework**: Spring Boot 3.2+
- **Database**: PostgreSQL 15+
- **ORM**: Hibernate/JPA
- **Security**: Spring Security with JWT
- **API**: REST + GraphQL
- **Message Queue**: Redis/RabbitMQ
- **File Storage**: AWS S3 or local filesystem

#### Frontend Integration
- **API Documentation**: OpenAPI 3.0 (Swagger)
- **GraphQL Schema**: SDL format
- **WebSocket**: Real-time notifications
- **File Upload**: Multipart form data

## Database Schema

### Workflow Management Tables

#### workflow_definitions
```sql
CREATE TABLE workflow_definitions (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50) NOT NULL DEFAULT '1.0',
    workflow_type VARCHAR(100) NOT NULL,
    approval_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_default BOOLEAN NOT NULL DEFAULT false,
    auto_start BOOLEAN NOT NULL DEFAULT false,
    timeout_hours INTEGER DEFAULT 72,
    escalation_enabled BOOLEAN NOT NULL DEFAULT true,
    escalation_hours INTEGER DEFAULT 24,
    configuration_json JSONB,
    trigger_conditions JSONB,
    document_types JSONB,
    department_restrictions JSONB,
    classification_requirements JSONB,
    created_by VARCHAR(255) NOT NULL,
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(255),

    CONSTRAINT uk_workflow_name_version UNIQUE (name, version)
);

CREATE INDEX idx_workflow_definitions_type ON workflow_definitions(workflow_type);
CREATE INDEX idx_workflow_definitions_active ON workflow_definitions(is_active);
CREATE INDEX idx_workflow_definitions_default ON workflow_definitions(workflow_type, is_default, is_active);
```

#### workflow_instances
```sql
CREATE TABLE workflow_instances (
    id BIGSERIAL PRIMARY KEY,
    workflow_definition_id BIGINT NOT NULL REFERENCES workflow_definitions(id),
    document_id BIGINT NOT NULL REFERENCES documents(id),
    parent_workflow_instance_id BIGINT REFERENCES workflow_instances(id),
    instance_name VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    priority VARCHAR(50) NOT NULL DEFAULT 'MEDIUM',
    started_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_date TIMESTAMP,
    due_date TIMESTAMP,
    initiator_user_id VARCHAR(255) NOT NULL,
    current_stage_id BIGINT REFERENCES workflow_stages(id),
    completion_reason TEXT,
    correlation_id VARCHAR(100),
    instance_data JSONB,

    CONSTRAINT chk_workflow_status CHECK (status IN ('NONE', 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'FAILED', 'SUSPENDED'))
);

CREATE INDEX idx_workflow_instances_document ON workflow_instances(document_id);
CREATE INDEX idx_workflow_instances_status ON workflow_instances(status);
CREATE INDEX idx_workflow_instances_initiator ON workflow_instances(initiator_user_id);
CREATE INDEX idx_workflow_instances_due_date ON workflow_instances(due_date);
```

### Document Template Tables

#### document_templates
```sql
CREATE TABLE document_templates (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    template_type VARCHAR(50) NOT NULL DEFAULT 'DOCUMENT',
    template_format VARCHAR(50) NOT NULL,
    mime_type VARCHAR(255) NOT NULL,
    file_size BIGINT,
    template_content BYTEA,
    version VARCHAR(50) NOT NULL DEFAULT '1.0',
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_system_template BOOLEAN NOT NULL DEFAULT false,
    is_public BOOLEAN NOT NULL DEFAULT false,
    usage_count INTEGER NOT NULL DEFAULT 0,
    last_used_date TIMESTAMP,
    owner_user_id VARCHAR(255) NOT NULL,
    owner_department VARCHAR(100),
    access_level VARCHAR(50) NOT NULL DEFAULT 'PRIVATE',
    approval_status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
    approved_by VARCHAR(255),
    approved_date TIMESTAMP,
    published_date TIMESTAMP,
    configuration_json JSONB,
    field_definitions JSONB,
    validation_rules JSONB,
    default_values JSONB,
    created_by VARCHAR(255) NOT NULL,
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(255),

    CONSTRAINT chk_template_type CHECK (template_type IN ('DOCUMENT', 'FORM', 'STRUCTURED')),
    CONSTRAINT chk_access_level CHECK (access_level IN ('PRIVATE', 'DEPARTMENT', 'PUBLIC', 'RESTRICTED', 'SYSTEM')),
    CONSTRAINT chk_approval_status CHECK (approval_status IN ('DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'PUBLISHED'))
);

CREATE INDEX idx_document_templates_category ON document_templates(category);
CREATE INDEX idx_document_templates_type ON document_templates(template_type);
CREATE INDEX idx_document_templates_owner ON document_templates(owner_user_id);
CREATE INDEX idx_document_templates_status ON document_templates(approval_status, is_active);
```

### Webhook & Event Tables

#### webhook_endpoints
```sql
CREATE TABLE webhook_endpoints (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    url VARCHAR(2048) NOT NULL,
    http_method VARCHAR(10) NOT NULL DEFAULT 'POST',
    content_type VARCHAR(100) NOT NULL DEFAULT 'application/json',
    timeout_seconds INTEGER NOT NULL DEFAULT 30,
    auth_type VARCHAR(50),
    auth_config JSONB,
    custom_headers JSONB,
    payload_template TEXT,
    event_types JSONB,
    event_filters JSONB,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    verification_token VARCHAR(255),
    secret_key VARCHAR(255),
    max_retries INTEGER NOT NULL DEFAULT 3,
    retry_delay_seconds INTEGER NOT NULL DEFAULT 60,
    exponential_backoff BOOLEAN NOT NULL DEFAULT true,
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    success_count INTEGER NOT NULL DEFAULT 0,
    failure_count INTEGER NOT NULL DEFAULT 0,
    last_success_date TIMESTAMP,
    last_failure_date TIMESTAMP,
    last_failure_reason TEXT,
    created_by VARCHAR(255) NOT NULL,
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(255),

    CONSTRAINT uk_webhook_url UNIQUE (url),
    CONSTRAINT chk_http_method CHECK (http_method IN ('GET', 'POST', 'PUT', 'PATCH', 'DELETE'))
);

CREATE INDEX idx_webhook_endpoints_active ON webhook_endpoints(is_active, is_verified);
CREATE INDEX idx_webhook_endpoints_creator ON webhook_endpoints(created_by);
```

#### system_events
```sql
CREATE TABLE system_events (
    id BIGSERIAL PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    event_category VARCHAR(50) NOT NULL,
    event_name VARCHAR(255) NOT NULL,
    source_entity_type VARCHAR(100),
    source_entity_id BIGINT,
    event_data JSONB,
    event_metadata JSONB,
    actor_user_id VARCHAR(255),
    actor_type VARCHAR(50) DEFAULT 'USER',
    correlation_id VARCHAR(100),
    session_id VARCHAR(100),
    request_id VARCHAR(100),
    event_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processing_status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    webhook_delivery_count INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT chk_event_category CHECK (event_category IN ('DOCUMENT', 'WORKFLOW', 'TEMPLATE', 'USER', 'SYSTEM', 'SECURITY', 'COMPLIANCE', 'INTEGRATION')),
    CONSTRAINT chk_processing_status CHECK (processing_status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'))
);

CREATE INDEX idx_system_events_type ON system_events(event_type);
CREATE INDEX idx_system_events_category ON system_events(event_category);
CREATE INDEX idx_system_events_timestamp ON system_events(event_timestamp);
CREATE INDEX idx_system_events_actor ON system_events(actor_user_id);
CREATE INDEX idx_system_events_correlation ON system_events(correlation_id);
CREATE INDEX idx_system_events_status ON system_events(processing_status);
```

## Service Layer Architecture

### Design Patterns

#### 1. Service Layer Pattern
Each business domain has dedicated service classes:
```java
@Service
@Transactional
public class WorkflowDefinitionService {

    private final WorkflowDefinitionRepository repository;
    private final AuditService auditService;

    public WorkflowDefinition createWorkflowDefinition(WorkflowDefinition definition, String createdBy) {
        // Validation
        validateWorkflowDefinition(definition);

        // Business logic
        definition.setCreatedBy(createdBy);
        definition.setCreatedDate(LocalDateTime.now());

        // Persistence
        WorkflowDefinition saved = repository.save(definition);

        // Audit
        auditService.logWorkflowDefinitionCreated(saved, createdBy);

        return saved;
    }
}
```

#### 2. Repository Pattern
Data access through JPA repositories with custom queries:
```java
@Repository
public interface WorkflowDefinitionRepository extends JpaRepository<WorkflowDefinition, Long> {

    List<WorkflowDefinition> findByWorkflowTypeAndIsActiveTrue(WorkflowType workflowType);

    @Query("SELECT wd FROM WorkflowDefinition wd WHERE wd.isActive = true AND JSON_CONTAINS(wd.documentTypes, :documentType)")
    List<WorkflowDefinition> findByDocumentType(@Param("documentType") String documentType);
}
```

#### 3. Event-Driven Pattern
Asynchronous event processing:
```java
@Component
public class WorkflowEventHandler {

    @EventListener
    @Async
    public void handleWorkflowStarted(WorkflowStartedEvent event) {
        // Send notifications
        notificationService.sendWorkflowStartedNotification(event.getWorkflowInstance());

        // Publish system event
        eventService.publishWorkflowStartedEvent(event.getWorkflowInstance());
    }
}
```

### Service Dependencies

#### Dependency Injection
```java
@Service
@RequiredArgsConstructor
public class DocumentTemplateService {

    private final DocumentTemplateRepository templateRepository;
    private final DocumentService documentService;
    private final AuditService auditService;
    private final EventService eventService;
    private final ObjectMapper objectMapper;
}
```

#### Service Interfaces
```java
public interface TemplateProcessingService {
    byte[] generateDocument(DocumentTemplate template, Map<String, Object> fieldValues);
    boolean validateTemplate(DocumentTemplate template);
    List<TemplateField> extractFields(byte[] templateContent);
}
```

## API Design Patterns

### REST API Design

#### Resource-Based URLs
```
GET    /api/v1/workflows/definitions           # List workflow definitions
POST   /api/v1/workflows/definitions           # Create workflow definition
GET    /api/v1/workflows/definitions/{id}      # Get specific definition
PUT    /api/v1/workflows/definitions/{id}      # Update definition
DELETE /api/v1/workflows/definitions/{id}      # Delete definition

POST   /api/v1/workflows/instances/start       # Start workflow
PUT    /api/v1/workflows/instances/{id}/complete # Complete workflow
```

#### Standard HTTP Status Codes
- `200 OK`: Successful GET, PUT
- `201 Created`: Successful POST
- `204 No Content`: Successful DELETE
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict
- `422 Unprocessable Entity`: Business logic validation failed

#### Error Response Format
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed for field 'name': must not be blank",
  "path": "/api/v1/workflows/definitions",
  "details": {
    "field": "name",
    "rejectedValue": "",
    "code": "NotBlank"
  }
}
```

### GraphQL API Design

#### Schema Definition
```graphql
type WorkflowDefinition {
  id: ID!
  name: String!
  description: String
  workflowType: WorkflowType!
  isActive: Boolean!
  stages: [WorkflowStage!]!
  instances: [WorkflowInstance!]!
  stageCount: Int!
  estimatedCompletionHours: Int!
}

type Query {
  getWorkflowDefinition(id: ID!): WorkflowDefinition
  getWorkflowDefinitions(pagination: WorkflowPaginationInput): WorkflowDefinitionPage!
}

type Mutation {
  createWorkflowDefinition(input: WorkflowDefinitionInput!): WorkflowDefinition!
  updateWorkflowDefinition(id: ID!, input: WorkflowDefinitionInput!): WorkflowDefinition!
}
```

#### Resolver Implementation
```java
@Controller
public class WorkflowDefinitionResolver {

    @QueryMapping
    public WorkflowDefinition getWorkflowDefinition(@Argument Long id) {
        return workflowDefinitionService.getWorkflowDefinitionById(id);
    }

    @MutationMapping
    public WorkflowDefinition createWorkflowDefinition(@Argument WorkflowDefinitionInput input) {
        WorkflowDefinition definition = mapInputToEntity(input);
        return workflowDefinitionService.createWorkflowDefinition(definition, userContext.getUserId());
    }

    @SchemaMapping(typeName = "WorkflowDefinition", field = "stageCount")
    public Integer getStageCount(WorkflowDefinition workflowDefinition) {
        return workflowDefinition.getStageCount();
    }
}
```