-- liquibase formatted sql

-- changeset dms:015-create-document-metadata-tables

-- Create document_classification_metadata table
CREATE TABLE document_classification_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    module VA<PERSON>HA<PERSON>(100),
    sub_module <PERSON><PERSON><PERSON><PERSON>(100),
    business_unit VARCHAR(100),
    region_location VARCHAR(100),
    confidentiality_level VARCHAR(50),
    tags_keywords JSON,
    language VARCHAR(10),
    document_type VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    last_modified_by VA<PERSON><PERSON>R(100),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_classification_document_id (document_id),
    INDEX idx_classification_module (module),
    INDEX idx_classification_confidentiality (confidentiality_level),
    INDEX idx_classification_document_type (document_type)
);

-- Create document_ownership_metadata table
CREATE TABLE document_ownership_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    owner VARCHAR(100),
    approver VARCHAR(100),
    status VARCHAR(50),
    approval_date TIMESTAMP NULL,
    expiry_date TIMESTAMP NULL,
    renewal_reminder TIMESTAMP NULL,
    retention_period VARCHAR(50),
    archived BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    last_modified_by VARCHAR(100),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_ownership_document_id (document_id),
    INDEX idx_ownership_owner (owner),
    INDEX idx_ownership_status (status),
    INDEX idx_ownership_expiry_date (expiry_date),
    INDEX idx_ownership_renewal_reminder (renewal_reminder)
);

-- Create document_compliance_metadata table
CREATE TABLE document_compliance_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    compliance_standard VARCHAR(100),
    audit_relevance VARCHAR(200),
    linked_risks_controls TEXT,
    control_id VARCHAR(100),
    third_party_id VARCHAR(100),
    policy_id VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    last_modified_by VARCHAR(100),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_compliance_document_id (document_id),
    INDEX idx_compliance_standard (compliance_standard),
    INDEX idx_compliance_control_id (control_id),
    INDEX idx_compliance_third_party_id (third_party_id),
    INDEX idx_compliance_policy_id (policy_id)
);

-- Add comments for documentation
ALTER TABLE document_classification_metadata COMMENT = 'Stores classification metadata for documents including module, business unit, confidentiality level, etc.';
ALTER TABLE document_ownership_metadata COMMENT = 'Stores ownership and lifecycle metadata for documents including owner, approver, status, dates, etc.';
ALTER TABLE document_compliance_metadata COMMENT = 'Stores compliance metadata for documents including standards, audit relevance, controls, etc.';
