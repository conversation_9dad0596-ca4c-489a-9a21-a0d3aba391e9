package com.ascentbusiness.dms_svc.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Entity representing fine-grained permissions for document templates
 */
@Entity
@Table(name = "template_permissions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplatePermission extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", nullable = false)
    @JsonIgnore
    private DocumentTemplate template;

    // Permission assignment
    @Column(name = "assignee_type", nullable = false, length = 50)
    private String assigneeType; // USER, ROLE, DEPARTMENT

    @Column(name = "assignee_value", nullable = false, length = 255)
    private String assigneeValue;

    // Permission details
    @Column(name = "permission_type", nullable = false, length = 50)
    private String permissionType; // VIEW, USE, EDIT, ADMIN

    @Column(name = "granted_by", nullable = false, length = 255)
    private String grantedBy;

    @Column(name = "granted_date", nullable = false)
    @Builder.Default
    private LocalDateTime grantedDate = LocalDateTime.now();

    @Column(name = "expires_date")
    private LocalDateTime expiresDate;

    // Permission status
    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    /**
     * Check if this permission is currently active
     */
    @Transient
    public boolean isCurrentlyActive() {
        if (isActive == null || !isActive) {
            return false;
        }
        
        if (expiresDate != null && LocalDateTime.now().isAfter(expiresDate)) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if this permission has expired
     */
    @Transient
    public boolean isExpired() {
        return expiresDate != null && LocalDateTime.now().isAfter(expiresDate);
    }

    /**
     * Check if this permission is for a user
     */
    @Transient
    public boolean isUserPermission() {
        return "USER".equals(assigneeType);
    }

    /**
     * Check if this permission is for a role
     */
    @Transient
    public boolean isRolePermission() {
        return "ROLE".equals(assigneeType);
    }

    /**
     * Check if this permission is for a department
     */
    @Transient
    public boolean isDepartmentPermission() {
        return "DEPARTMENT".equals(assigneeType);
    }

    /**
     * Check if this is a view permission
     */
    @Transient
    public boolean isViewPermission() {
        return "VIEW".equals(permissionType);
    }

    /**
     * Check if this is a use permission
     */
    @Transient
    public boolean isUsePermission() {
        return "USE".equals(permissionType);
    }

    /**
     * Check if this is an edit permission
     */
    @Transient
    public boolean isEditPermission() {
        return "EDIT".equals(permissionType);
    }

    /**
     * Check if this is an admin permission
     */
    @Transient
    public boolean isAdminPermission() {
        return "ADMIN".equals(permissionType);
    }

    /**
     * Get the template name
     */
    @Transient
    public String getTemplateName() {
        return template != null ? template.getName() : null;
    }

    /**
     * Get permission display name
     */
    @Transient
    public String getPermissionDisplayName() {
        if (permissionType == null) return "Unknown";
        
        switch (permissionType.toUpperCase()) {
            case "VIEW":
                return "View Template";
            case "USE":
                return "Use Template";
            case "EDIT":
                return "Edit Template";
            case "ADMIN":
                return "Administer Template";
            default:
                return permissionType;
        }
    }

    /**
     * Get assignee display name
     */
    @Transient
    public String getAssigneeDisplayName() {
        if (assigneeType == null || assigneeValue == null) return "Unknown";
        
        switch (assigneeType.toUpperCase()) {
            case "USER":
                return "User: " + assigneeValue;
            case "ROLE":
                return "Role: " + assigneeValue;
            case "DEPARTMENT":
                return "Department: " + assigneeValue;
            default:
                return assigneeType + ": " + assigneeValue;
        }
    }

    /**
     * Get days until expiration
     */
    @Transient
    public long getDaysUntilExpiration() {
        if (expiresDate == null) return Long.MAX_VALUE;
        return java.time.Duration.between(LocalDateTime.now(), expiresDate).toDays();
    }

    /**
     * Check if permission is expiring soon (within 7 days)
     */
    @Transient
    public boolean isExpiringSoon() {
        return expiresDate != null && getDaysUntilExpiration() <= 7 && getDaysUntilExpiration() > 0;
    }

    /**
     * Revoke this permission
     */
    public void revoke() {
        this.isActive = false;
    }

    /**
     * Extend expiration date
     */
    public void extendExpiration(LocalDateTime newExpirationDate) {
        this.expiresDate = newExpirationDate;
    }

    @Override
    public String toString() {
        return String.format("TemplatePermission{id=%d, template='%s', assignee='%s', permission='%s', active=%s}", 
                           getId(), getTemplateName(), getAssigneeDisplayName(), permissionType, isActive);
    }
}
