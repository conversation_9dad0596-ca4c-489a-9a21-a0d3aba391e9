package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConnectionTestType;
import lombok.Data;

/**
 * Connection test input DTO from diagnostics-schema.graphqls.
 */
@Data
public class ConnectionTestInput {
    private ConnectionTestType testType;
    private String target;
    private Integer timeout = 10; // seconds
    private Integer retryCount = 3;
    private String parameters; // JSON string for test-specific parameters
}
