# Prometheus Alerting Rules for GRC Platform v4
groups:
  - name: grc-platform-alerts
    rules:
      # Service Health Alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute."

      # DMS Service Specific Alerts
      - alert: DMSHighMemoryUsage
        expr: jvm_memory_used_bytes{job="dms-svc"} / jvm_memory_max_bytes{job="dms-svc"} > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "DMS Service high memory usage"
          description: "DMS Service memory usage is above 80% for more than 5 minutes."

      - alert: DMSHighCPUUsage
        expr: system_cpu_usage{job="dms-svc"} > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "DMS Service high CPU usage"
          description: "DMS Service CPU usage is above 80% for more than 5 minutes."

      # Notification Service Specific Alerts
      - alert: NotificationServiceHighMemoryUsage
        expr: jvm_memory_used_bytes{job="notification-svc"} / jvm_memory_max_bytes{job="notification-svc"} > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Notification Service high memory usage"
          description: "Notification Service memory usage is above 80% for more than 5 minutes."

      # Database Alerts
      - alert: MySQLDown
        expr: up{job="mysql"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "MySQL database is down"
          description: "MySQL database has been down for more than 1 minute."

      # Redis Alerts
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis cache is down"
          description: "Redis cache has been down for more than 1 minute."

      # RabbitMQ Alerts
      - alert: RabbitMQDown
        expr: up{job="rabbitmq"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "RabbitMQ message broker is down"
          description: "RabbitMQ message broker has been down for more than 1 minute."
