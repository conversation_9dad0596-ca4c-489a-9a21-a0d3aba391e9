# Docker Environment Configuration for DMS Service
# This file contains Docker-specific settings that override the main application.properties

# Server Configuration
server.port=9093

# Database Configuration - Docker
spring.datasource.url=*********************************************************************************************************************************************************************************************
spring.datasource.username=dms_user
spring.datasource.password=dms_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Redis Configuration - Docker
spring.data.redis.host=redis
spring.data.redis.port=6379
spring.data.redis.password=shared_redis_password
spring.data.redis.database=0
spring.data.redis.timeout=5000ms
spring.data.redis.connect-timeout=3000ms

# Redis Connection Pool Configuration (Lettuce)
spring.data.redis.lettuce.pool.max-active=20
spring.data.redis.lettuce.pool.max-idle=10
spring.data.redis.lettuce.pool.min-idle=2
spring.data.redis.lettuce.pool.max-wait=2000ms
spring.data.redis.lettuce.pool.time-between-eviction-runs=30s

# GraphQL Configuration - Docker
spring.graphql.graphiql.enabled=true
spring.graphql.graphiql.path=/graphiql
spring.graphql.path=/graphql
spring.graphql.schema.printer.enabled=true
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.file-extensions=.graphqls,.gqls
spring.graphql.websocket.path=/graphql
# Use allowedOriginPatterns instead of allowedOrigins when allowCredentials is true
spring.graphql.cors.allowed-origin-patterns=*
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID,Accept
spring.graphql.cors.allow-credentials=true

# Storage Configuration - Docker
dms.storage.provider=LOCAL
dms.storage.local.base-path=/app/storage

# JWT Configuration
dms.jwt.secret=localDevSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ
dms.jwt.expiration=86400000
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always
management.health.defaults.enabled=true

# Health Indicators
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=true
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true

# OpenTelemetry Configuration - Docker
otel.service.name=dms-svc
otel.service.version=1.0.0
otel.resource.attributes.service.name=dms-svc
otel.resource.attributes.service.version=1.0.0
otel.resource.attributes.deployment.environment=docker

# OpenTelemetry Tracing Configuration - Use Zipkin exporter
otel.traces.exporter=zipkin
otel.exporter.zipkin.endpoint=http://zipkin:9411/api/v2/spans
otel.traces.sampler=traceidratio
otel.traces.sampler.arg=0.1

# OpenTelemetry Metrics Configuration
otel.metrics.exporter=console
otel.metric.export.interval=30s

# OpenTelemetry Logs Configuration
otel.logs.exporter=console

# Logging Configuration
logging.level.com.ascentbusiness.dms_svc=INFO
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=1800000
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dms:

# Elasticsearch Configuration - Docker
elasticsearch.enabled=false
elasticsearch.host=elasticsearch
elasticsearch.port=9200
elasticsearch.protocol=http

# Virus Scanning Configuration
dms.virus-scanning.enabled=true
dms.virus-scanning.fail-on-unavailable=false
dms.virus-scanning.default-scanner=MOCK

# Mock Scanner Configuration (for Docker environment)
dms.virus-scanner.mock.enabled=true

# Disable multipart GraphQL Upload scalar auto-registration to avoid conflicts
spring.graphql.multipart.springboot.enabled=false
