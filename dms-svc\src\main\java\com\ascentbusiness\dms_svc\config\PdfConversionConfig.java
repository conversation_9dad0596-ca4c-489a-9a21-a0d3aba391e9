package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for PDF to Word conversion functionality.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dms.pdf-conversion")
public class PdfConversionConfig {

    /**
     * Maximum file size for PDF conversion in bytes.
     * Default: 52428800 (50MB)
     */
    private long maxFileSize = 52428800L;

    /**
     * Conversion timeout in seconds.
     * Default: 300 (5 minutes)
     */
    private int timeoutSeconds = 300;

    /**
     * Default virus scanner type for PDF conversion.
     * Default: MOCK
     */
    private VirusScannerType virusScanner = VirusScannerType.MOCK;

    /**
     * Whether PDF conversion feature is enabled.
     * Default: true
     */
    private boolean enabled = true;

    /**
     * Temporary directory for conversion operations.
     * If empty, uses system temp directory.
     */
    private String tempDirectory = "";

    /**
     * Auto-cleanup converted files after specified hours.
     * Default: 24 hours
     */
    private int cleanupAfterHours = 24;

    /**
     * Get the maximum file size in a human-readable format.
     * 
     * @return formatted file size string
     */
    public String getMaxFileSizeFormatted() {
        if (maxFileSize >= 1024 * 1024 * 1024) {
            return String.format("%.1f GB", maxFileSize / (1024.0 * 1024.0 * 1024.0));
        } else if (maxFileSize >= 1024 * 1024) {
            return String.format("%.1f MB", maxFileSize / (1024.0 * 1024.0));
        } else if (maxFileSize >= 1024) {
            return String.format("%.1f KB", maxFileSize / 1024.0);
        } else {
            return maxFileSize + " bytes";
        }
    }

    /**
     * Get the timeout in a human-readable format.
     * 
     * @return formatted timeout string
     */
    public String getTimeoutFormatted() {
        if (timeoutSeconds >= 3600) {
            return String.format("%.1f hours", timeoutSeconds / 3600.0);
        } else if (timeoutSeconds >= 60) {
            return String.format("%.1f minutes", timeoutSeconds / 60.0);
        } else {
            return timeoutSeconds + " seconds";
        }
    }

    /**
     * Check if a file size is within the allowed limit.
     * 
     * @param fileSize the file size to check
     * @return true if within limit, false otherwise
     */
    public boolean isFileSizeAllowed(long fileSize) {
        return fileSize <= maxFileSize;
    }

    /**
     * Get the cleanup interval in milliseconds.
     * 
     * @return cleanup interval in milliseconds
     */
    public long getCleanupIntervalMs() {
        return cleanupAfterHours * 60L * 60L * 1000L;
    }

    /**
     * Check if temp directory is configured.
     * 
     * @return true if temp directory is configured, false if using system default
     */
    public boolean hasTempDirectory() {
        return tempDirectory != null && !tempDirectory.trim().isEmpty();
    }
}
