#!/bin/bash

# Local Development - Docker Management Script
# This script manages the local development environment with shared infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

COMPOSE_FILE="docker-compose.local-dev.yml"
ENV_FILE=".env.local-dev"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
}

# Function to check environment file
check_env_file() {
    if [ ! -f "$ENV_FILE" ]; then
        print_warning "Environment file $ENV_FILE not found. Using default values."
        print_status "You can copy .env.local-dev to customize settings if needed."
    fi
}

# Function to start shared infrastructure only
start_infrastructure() {
    print_header "Starting Shared Infrastructure for Local Development"
    check_docker
    check_docker_compose
    check_env_file
    
    print_status "Starting shared infrastructure services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d mysql-shared redis-shared rabbitmq-shared elasticsearch-shared
    
    print_status "Waiting for infrastructure services to be ready..."
    sleep 30
    
    print_status "Shared infrastructure started successfully!"
    show_infrastructure_status
}

# Function to start DMS service only (requires infrastructure)
start_dms_only() {
    print_header "Starting DMS Service Only (using shared infrastructure)"
    check_docker
    check_docker_compose
    check_env_file
    
    # Check if infrastructure is running
    if ! docker ps | grep -q "mysql-shared-local"; then
        print_error "Shared infrastructure is not running. Please start it first with: $0 start-infra"
        exit 1
    fi
    
    print_status "Starting DMS service..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d dms-svc
    
    print_status "DMS service started successfully!"
    show_dms_status
}

# Function to start notification service only (requires infrastructure)
start_notification_only() {
    print_header "Starting Notification Service Only (using shared infrastructure)"
    check_docker
    check_docker_compose
    check_env_file
    
    # Check if infrastructure is running
    if ! docker ps | grep -q "mysql-shared-local"; then
        print_error "Shared infrastructure is not running. Please start it first with: $0 start-infra"
        exit 1
    fi
    
    print_status "Starting Notification service..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d notification-svc
    
    print_status "Notification service started successfully!"
    show_notification_status
}

# Function to start both application services
start_apps() {
    print_header "Starting Both Application Services (using shared infrastructure)"
    check_docker
    check_docker_compose
    check_env_file
    
    # Check if infrastructure is running
    if ! docker ps | grep -q "mysql-shared-local"; then
        print_error "Shared infrastructure is not running. Please start it first with: $0 start-infra"
        exit 1
    fi
    
    print_status "Starting both application services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d dms-svc notification-svc
    
    print_status "Both application services started successfully!"
    show_apps_status
}

# Function to start monitoring services
start_monitoring() {
    print_header "Starting Monitoring Services"
    check_docker
    check_docker_compose
    check_env_file
    
    print_status "Starting monitoring services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d zipkin-shared prometheus-shared grafana-shared
    
    print_status "Monitoring services started successfully!"
    show_monitoring_status
}

# Function to start everything
start_all() {
    print_header "Starting Complete Local Development Environment"
    check_docker
    check_docker_compose
    check_env_file
    
    print_status "Step 1: Starting shared infrastructure..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d mysql-shared redis-shared rabbitmq-shared elasticsearch-shared
    
    print_status "Waiting for infrastructure to be ready..."
    sleep 30
    
    print_status "Step 2: Starting application services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d dms-svc notification-svc
    
    print_status "Step 3: Starting monitoring services..."
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d zipkin-shared prometheus-shared grafana-shared
    
    print_status "Complete local development environment started successfully!"
    show_complete_status
}

# Function to show infrastructure status
show_infrastructure_status() {
    print_header "Shared Infrastructure Status"
    docker-compose -f $COMPOSE_FILE ps mysql-shared redis-shared rabbitmq-shared elasticsearch-shared
    
    echo ""
    print_header "Infrastructure URLs"
    echo "  MySQL:                      localhost:3306 (root/root_password)"
    echo "  Redis:                      localhost:6379 (local_redis_password)"
    echo "  RabbitMQ Management:        http://localhost:15672 (admin/admin123)"
    echo "  Elasticsearch:              http://localhost:9200"
}

# Function to show DMS service status
show_dms_status() {
    print_header "DMS Service Status"
    docker-compose -f $COMPOSE_FILE ps dms-svc
    
    echo ""
    print_header "DMS Service URLs"
    echo "  GraphQL API:                http://localhost:9093/dms/graphql"
    echo "  GraphiQL UI:                http://localhost:9093/graphiql"
    echo "  Health Check:               http://localhost:9093/actuator/health"
    echo "  Metrics:                    http://localhost:9464/actuator/prometheus"
}

# Function to show notification service status
show_notification_status() {
    print_header "Notification Service Status"
    docker-compose -f $COMPOSE_FILE ps notification-svc
    
    echo ""
    print_header "Notification Service URLs"
    echo "  GraphQL API:                http://localhost:9091/graphql"
    echo "  GraphiQL UI:                http://localhost:9091/graphiql"
    echo "  Health Check:               http://localhost:9091/actuator/health"
    echo "  Metrics:                    http://localhost:9091/actuator/prometheus"
}

# Function to show both apps status
show_apps_status() {
    show_dms_status
    echo ""
    show_notification_status
}

# Function to show monitoring status
show_monitoring_status() {
    print_header "Monitoring Services Status"
    docker-compose -f $COMPOSE_FILE ps zipkin-shared prometheus-shared grafana-shared
    
    echo ""
    print_header "Monitoring URLs"
    echo "  Zipkin:                     http://localhost:9411"
    echo "  Prometheus:                 http://localhost:9090"
    echo "  Grafana:                    http://localhost:3000 (admin/admin)"
}

# Function to show complete status
show_complete_status() {
    show_infrastructure_status
    echo ""
    show_apps_status
    echo ""
    show_monitoring_status
}

# Function to show logs
show_logs() {
    case "$2" in
        "infra"|"infrastructure")
            docker-compose -f $COMPOSE_FILE logs -f mysql-shared redis-shared rabbitmq-shared elasticsearch-shared
            ;;
        "dms")
            docker-compose -f $COMPOSE_FILE logs -f dms-svc
            ;;
        "notification")
            docker-compose -f $COMPOSE_FILE logs -f notification-svc
            ;;
        "monitoring")
            docker-compose -f $COMPOSE_FILE logs -f zipkin-shared prometheus-shared grafana-shared
            ;;
        "")
            print_header "Showing All Service Logs"
            docker-compose -f $COMPOSE_FILE logs -f
            ;;
        *)
            print_header "Showing Logs for: $2"
            docker-compose -f $COMPOSE_FILE logs -f "$2"
            ;;
    esac
}

# Function to stop services
stop_dms() {
    print_header "Stopping DMS Service"
    docker-compose -f $COMPOSE_FILE stop dms-svc
    print_status "DMS service stopped!"
}

stop_notification() {
    print_header "Stopping Notification Service"
    docker-compose -f $COMPOSE_FILE stop notification-svc
    print_status "Notification service stopped!"
}

stop_apps() {
    print_header "Stopping Application Services"
    docker-compose -f $COMPOSE_FILE stop dms-svc notification-svc
    print_status "Application services stopped!"
}

stop_infrastructure() {
    print_header "Stopping Shared Infrastructure"
    docker-compose -f $COMPOSE_FILE stop mysql-shared redis-shared rabbitmq-shared elasticsearch-shared
    print_status "Shared infrastructure stopped!"
}

stop_monitoring() {
    print_header "Stopping Monitoring Services"
    docker-compose -f $COMPOSE_FILE stop zipkin-shared prometheus-shared grafana-shared
    print_status "Monitoring services stopped!"
}

stop_all() {
    print_header "Stopping All Services"
    docker-compose -f $COMPOSE_FILE down
    print_status "All services stopped!"
}

# Function to clean up everything
cleanup() {
    print_warning "This will stop all services and remove all data!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_header "Cleaning Up Everything"
        docker-compose -f $COMPOSE_FILE down -v --rmi local
        docker system prune -f
        print_status "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show health status
health_check() {
    print_header "Health Check"
    
    echo "Checking DMS Service..."
    if curl -f http://localhost:9093/actuator/health > /dev/null 2>&1; then
        print_status "DMS Service: HEALTHY"
    else
        print_warning "DMS Service: NOT RUNNING or UNHEALTHY"
    fi
    
    echo "Checking Notification Service..."
    if curl -f http://localhost:9091/actuator/health > /dev/null 2>&1; then
        print_status "Notification Service: HEALTHY"
    else
        print_warning "Notification Service: NOT RUNNING or UNHEALTHY"
    fi
    
    echo "Checking Shared Infrastructure..."
    if docker ps | grep -q "mysql-shared-local"; then
        print_status "MySQL: RUNNING"
    else
        print_warning "MySQL: NOT RUNNING"
    fi
    
    if docker ps | grep -q "redis-shared-local"; then
        print_status "Redis: RUNNING"
    else
        print_warning "Redis: NOT RUNNING"
    fi
    
    if docker ps | grep -q "rabbitmq-shared-local"; then
        print_status "RabbitMQ: RUNNING"
    else
        print_warning "RabbitMQ: NOT RUNNING"
    fi
    
    if docker ps | grep -q "elasticsearch-shared-local"; then
        print_status "Elasticsearch: RUNNING"
    else
        print_warning "Elasticsearch: NOT RUNNING"
    fi
}

# Function to show help
show_help() {
    echo "Local Development - Docker Management Script"
    echo ""
    echo "This script manages the local development environment with shared infrastructure."
    echo "It demonstrates the step-by-step approach: Infrastructure → DMS → Notification → Monitoring"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Infrastructure Commands:"
    echo "  start-infra        Start shared infrastructure (MySQL, Redis, RabbitMQ, Elasticsearch)"
    echo ""
    echo "Application Commands (require infrastructure):"
    echo "  start-dms          Start DMS service only (connects to shared infrastructure)"
    echo "  start-notification Start Notification service only (connects to shared infrastructure)"
    echo "  start-apps         Start both application services"
    echo ""
    echo "Monitoring Commands:"
    echo "  start-monitoring   Start monitoring services (Zipkin, Prometheus, Grafana)"
    echo ""
    echo "Combined Commands:"
    echo "  start-all          Start everything (infrastructure + apps + monitoring)"
    echo ""
    echo "Stop Commands:"
    echo "  stop-dms           Stop DMS service only"
    echo "  stop-notification  Stop Notification service only"
    echo "  stop-apps          Stop both application services"
    echo "  stop-infra         Stop shared infrastructure"
    echo "  stop-monitoring    Stop monitoring services"
    echo "  stop-all           Stop all services"
    echo ""
    echo "Monitoring Commands:"
    echo "  status             Show status of all components"
    echo "  logs [SERVICE]     Show logs (infra, dms, notification, monitoring, or specific service)"
    echo "  health             Check health status of all services"
    echo "  cleanup            Stop all services and remove all data (WARNING: destructive)"
    echo ""
    echo "Step-by-Step Example:"
    echo "  $0 start-infra           # Step 1: Start shared infrastructure"
    echo "  $0 start-dms             # Step 2: Start DMS service (uses shared infra)"
    echo "  $0 start-notification    # Step 3: Start Notification service (uses shared infra)"
    echo "  $0 start-monitoring      # Step 4: Start monitoring (optional)"
    echo ""
    echo "Quick Start:"
    echo "  $0 start-all             # Start everything at once"
}

# Main script logic
case "$1" in
    "start-infra"|"start-infrastructure")
        start_infrastructure
        ;;
    "start-dms")
        start_dms_only
        ;;
    "start-notification")
        start_notification_only
        ;;
    "start-apps")
        start_apps
        ;;
    "start-monitoring")
        start_monitoring
        ;;
    "start-all")
        start_all
        ;;
    "stop-dms")
        stop_dms
        ;;
    "stop-notification")
        stop_notification
        ;;
    "stop-apps")
        stop_apps
        ;;
    "stop-infra"|"stop-infrastructure")
        stop_infrastructure
        ;;
    "stop-monitoring")
        stop_monitoring
        ;;
    "stop-all")
        stop_all
        ;;
    "status")
        show_complete_status
        ;;
    "logs")
        show_logs "$@"
        ;;
    "health")
        health_check
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        print_error "No command specified. Use '$0 help' for usage information."
        exit 1
        ;;
    *)
        print_error "Unknown command: $1. Use '$0 help' for usage information."
        exit 1
        ;;
esac
