--liquibase formatted sql

-- Create webhook_endpoints table
CREATE TABLE webhook_endpoints (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    url VARCHAR(2000) NOT NULL,
    
    -- Webhook configuration
    http_method VARCHAR(10) NOT NULL DEFAULT 'POST',
    content_type VARCHAR(100) NOT NULL DEFAULT 'application/json',
    timeout_seconds INTEGER NOT NULL DEFAULT 30,
    
    -- Authentication
    auth_type VARCHAR(50), -- NONE, BASIC, BEARER, API_KEY, CUSTOM
    auth_config JSON, -- Authentication configuration
    
    -- Headers and payload
    custom_headers JSON, -- Custom HTTP headers
    payload_template TEXT, -- Template for webhook payload
    
    -- Event filtering
    event_types JSON, -- Array of event types to listen for
    event_filters JSON, -- Filters for events (e.g., document types, departments)
    
    -- Status and configuration
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    verification_token VARCHAR(255),
    secret_key VARCHAR(255), -- For signature verification
    
    -- Retry configuration
    max_retries INTEGER NOT NULL DEFAULT 3,
    retry_delay_seconds INTEGER NOT NULL DEFAULT 60,
    exponential_backoff BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Rate limiting
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    
    -- Monitoring
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    last_success_date TIMESTAMP,
    last_failure_date TIMESTAMP,
    last_failure_reason TEXT,
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    last_modified_by VARCHAR(255),
    
    -- Constraints
    INDEX idx_webhook_active (is_active),
    INDEX idx_webhook_verified (is_verified),
    INDEX idx_webhook_created_by (created_by),
    INDEX idx_webhook_last_success (last_success_date),
    INDEX idx_webhook_last_failure (last_failure_date)
);

-- Create system_events table
CREATE TABLE system_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    event_category VARCHAR(50) NOT NULL, -- DOCUMENT, WORKFLOW, TEMPLATE, USER, SYSTEM
    event_name VARCHAR(255) NOT NULL,
    
    -- Event source
    source_entity_type VARCHAR(50), -- DOCUMENT, WORKFLOW_INSTANCE, TEMPLATE, etc.
    source_entity_id BIGINT,
    
    -- Event details
    event_data JSON, -- Event payload data
    event_metadata JSON, -- Additional metadata
    
    -- Actor information
    actor_user_id VARCHAR(255),
    actor_type VARCHAR(50), -- USER, SYSTEM, SERVICE
    
    -- Context
    correlation_id VARCHAR(100),
    session_id VARCHAR(100),
    request_id VARCHAR(100),
    
    -- Timing
    event_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Processing status
    processing_status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, PROCESSING, COMPLETED, FAILED
    webhook_delivery_count INTEGER DEFAULT 0,
    
    -- Constraints
    INDEX idx_event_type (event_type),
    INDEX idx_event_category (event_category),
    INDEX idx_event_timestamp (event_timestamp),
    INDEX idx_event_actor (actor_user_id),
    INDEX idx_event_correlation (correlation_id),
    INDEX idx_event_processing_status (processing_status),
    INDEX idx_event_source (source_entity_type, source_entity_id)
);

-- Create webhook_deliveries table
CREATE TABLE webhook_deliveries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    webhook_endpoint_id BIGINT NOT NULL,
    system_event_id BIGINT NOT NULL,
    
    -- Delivery details
    delivery_attempt INTEGER NOT NULL DEFAULT 1,
    delivery_status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, SUCCESS, FAILED, CANCELLED
    
    -- HTTP details
    http_status_code INTEGER,
    response_body TEXT,
    response_headers JSON,
    request_payload TEXT,
    request_headers JSON,
    
    -- Timing
    scheduled_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    attempted_date TIMESTAMP,
    completed_date TIMESTAMP,
    duration_ms INTEGER,
    
    -- Error handling
    error_message TEXT,
    error_code VARCHAR(50),
    retry_after_seconds INTEGER,
    
    -- Correlation
    correlation_id VARCHAR(100),
    
    -- Constraints
    FOREIGN KEY (webhook_endpoint_id) REFERENCES webhook_endpoints(id) ON DELETE CASCADE,
    FOREIGN KEY (system_event_id) REFERENCES system_events(id) ON DELETE CASCADE,
    INDEX idx_delivery_status (delivery_status),
    INDEX idx_delivery_scheduled (scheduled_date),
    INDEX idx_delivery_attempted (attempted_date),
    INDEX idx_delivery_webhook (webhook_endpoint_id),
    INDEX idx_delivery_event (system_event_id),
    INDEX idx_delivery_correlation (correlation_id)
);

-- Create event_subscriptions table
CREATE TABLE event_subscriptions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Subscription configuration
    event_types JSON NOT NULL, -- Array of event types to subscribe to
    event_filters JSON, -- Filters for events
    
    -- Subscriber information
    subscriber_type VARCHAR(50) NOT NULL, -- WEBHOOK, EMAIL, SMS, INTERNAL
    subscriber_config JSON NOT NULL, -- Configuration for subscriber
    
    -- Status
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_paused BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Processing options
    batch_size INTEGER DEFAULT 1,
    batch_timeout_seconds INTEGER DEFAULT 300,
    delivery_mode VARCHAR(50) NOT NULL DEFAULT 'IMMEDIATE', -- IMMEDIATE, BATCH, SCHEDULED
    
    -- Scheduling (for scheduled delivery mode)
    schedule_cron VARCHAR(100), -- Cron expression for scheduled delivery
    schedule_timezone VARCHAR(50) DEFAULT 'UTC',
    
    -- Monitoring
    events_processed INTEGER DEFAULT 0,
    last_processed_date TIMESTAMP,
    
    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    last_modified_by VARCHAR(255),
    
    -- Constraints
    INDEX idx_subscription_active (is_active),
    INDEX idx_subscription_paused (is_paused),
    INDEX idx_subscription_type (subscriber_type),
    INDEX idx_subscription_created_by (created_by)
);

-- Create event_processing_log table
CREATE TABLE event_processing_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    system_event_id BIGINT NOT NULL,
    event_subscription_id BIGINT,
    
    -- Processing details
    processing_stage VARCHAR(50) NOT NULL, -- RECEIVED, FILTERED, QUEUED, PROCESSING, DELIVERED, FAILED
    processing_status VARCHAR(50) NOT NULL, -- SUCCESS, FAILED, SKIPPED
    
    -- Processing metadata
    processor_name VARCHAR(100),
    processing_duration_ms INTEGER,
    
    -- Error information
    error_message TEXT,
    error_stack_trace TEXT,
    
    -- Timing
    processing_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Correlation
    correlation_id VARCHAR(100),
    
    -- Constraints
    FOREIGN KEY (system_event_id) REFERENCES system_events(id) ON DELETE CASCADE,
    FOREIGN KEY (event_subscription_id) REFERENCES event_subscriptions(id) ON DELETE SET NULL,
    INDEX idx_processing_event (system_event_id),
    INDEX idx_processing_subscription (event_subscription_id),
    INDEX idx_processing_stage (processing_stage),
    INDEX idx_processing_status (processing_status),
    INDEX idx_processing_timestamp (processing_timestamp)
);

-- Create event_templates table for customizable event payloads
CREATE TABLE event_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    event_type VARCHAR(100) NOT NULL,

    -- Template configuration
    template_format VARCHAR(50) NOT NULL DEFAULT 'JSON', -- JSON, XML, PLAIN_TEXT
    template_content TEXT NOT NULL,

    -- Template variables
    available_variables JSON, -- List of available variables for this event type
    required_variables JSON, -- List of required variables

    -- Template metadata
    is_system_template BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version VARCHAR(50) NOT NULL DEFAULT '1.0',

    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_date TIMESTAMP,

    -- Metadata
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    last_modified_by VARCHAR(255),

    -- Constraints
    INDEX idx_template_event_type (event_type),
    INDEX idx_template_active (is_active),
    INDEX idx_template_system (is_system_template)
);

-- Create webhook_security_log table for security monitoring
CREATE TABLE webhook_security_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    webhook_endpoint_id BIGINT,

    -- Security event details
    security_event_type VARCHAR(50) NOT NULL, -- SIGNATURE_MISMATCH, RATE_LIMIT_EXCEEDED, SUSPICIOUS_ACTIVITY
    severity VARCHAR(20) NOT NULL DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, CRITICAL

    -- Request details
    source_ip VARCHAR(45),
    user_agent VARCHAR(500),
    request_headers JSON,

    -- Event description
    event_description TEXT,
    additional_data JSON,

    -- Response taken
    action_taken VARCHAR(100), -- BLOCKED, RATE_LIMITED, LOGGED_ONLY, WEBHOOK_DISABLED

    -- Timing
    event_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (webhook_endpoint_id) REFERENCES webhook_endpoints(id) ON DELETE SET NULL,
    INDEX idx_security_webhook (webhook_endpoint_id),
    INDEX idx_security_event_type (security_event_type),
    INDEX idx_security_severity (severity),
    INDEX idx_security_timestamp (event_timestamp),
    INDEX idx_security_source_ip (source_ip)
);

-- Insert default event templates
INSERT INTO event_templates (name, description, event_type, template_content, available_variables, is_system_template, created_by) VALUES
('Document Created Template', 'Default template for document creation events', 'DOCUMENT_CREATED',
 '{"event": "document.created", "timestamp": "{{timestamp}}", "document": {"id": "{{document.id}}", "name": "{{document.name}}", "type": "{{document.type}}", "size": "{{document.size}}", "created_by": "{{document.created_by}}"}}',
 JSON_ARRAY('timestamp', 'document.id', 'document.name', 'document.type', 'document.size', 'document.created_by', 'document.department'),
 TRUE, 'system'),

('Document Updated Template', 'Default template for document update events', 'DOCUMENT_UPDATED',
 '{"event": "document.updated", "timestamp": "{{timestamp}}", "document": {"id": "{{document.id}}", "name": "{{document.name}}", "version": "{{document.version}}", "updated_by": "{{document.updated_by}}", "changes": "{{changes}}"}}',
 JSON_ARRAY('timestamp', 'document.id', 'document.name', 'document.version', 'document.updated_by', 'changes'),
 TRUE, 'system'),

('Workflow Completed Template', 'Default template for workflow completion events', 'WORKFLOW_COMPLETED',
 '{"event": "workflow.completed", "timestamp": "{{timestamp}}", "workflow": {"id": "{{workflow.id}}", "name": "{{workflow.name}}", "document_id": "{{workflow.document_id}}", "status": "{{workflow.status}}", "completed_by": "{{workflow.completed_by}}"}}',
 JSON_ARRAY('timestamp', 'workflow.id', 'workflow.name', 'workflow.document_id', 'workflow.status', 'workflow.completed_by'),
 TRUE, 'system'),

('Template Used Template', 'Default template for template usage events', 'TEMPLATE_USED',
 '{"event": "template.used", "timestamp": "{{timestamp}}", "template": {"id": "{{template.id}}", "name": "{{template.name}}", "category": "{{template.category}}", "used_by": "{{template.used_by}}", "document_created": "{{document.id}}"}}',
 JSON_ARRAY('timestamp', 'template.id', 'template.name', 'template.category', 'template.used_by', 'document.id'),
 TRUE, 'system');

-- Insert sample webhook endpoints for testing
INSERT INTO webhook_endpoints (name, description, url, event_types, is_active, created_by) VALUES
('Development Webhook', 'Development webhook for testing', 'http://localhost:8080/webhooks/test',
 JSON_ARRAY('DOCUMENT_CREATED', 'DOCUMENT_UPDATED', 'WORKFLOW_COMPLETED'),
 FALSE, 'system'),

('Document Management Integration', 'Integration with external document management system', 'https://api.example.com/dms/webhooks',
 JSON_ARRAY('DOCUMENT_CREATED', 'DOCUMENT_UPDATED', 'DOCUMENT_DELETED'),
 FALSE, 'system');

-- Insert sample event subscriptions
INSERT INTO event_subscriptions (name, description, event_types, subscriber_type, subscriber_config, created_by) VALUES
('Admin Notifications', 'Email notifications for administrators',
 JSON_ARRAY('SYSTEM_ERROR', 'SECURITY_VIOLATION', 'COMPLIANCE_VIOLATION'),
 'EMAIL',
 JSON_OBJECT('recipients', JSON_ARRAY('<EMAIL>'), 'subject_template', 'DMS Alert: {{event_type}}'),
 'system'),

('Workflow Notifications', 'Internal notifications for workflow events',
 JSON_ARRAY('WORKFLOW_STARTED', 'WORKFLOW_COMPLETED', 'WORKFLOW_FAILED'),
 'INTERNAL',
 JSON_OBJECT('handler', 'WorkflowNotificationHandler'),
 'system');

-- Add comments for documentation
ALTER TABLE webhook_endpoints COMMENT = 'Stores webhook endpoint configurations for external integrations';
ALTER TABLE system_events COMMENT = 'Stores all system events for webhook delivery and audit purposes';
ALTER TABLE webhook_deliveries COMMENT = 'Tracks webhook delivery attempts and their results';
ALTER TABLE event_subscriptions COMMENT = 'Manages event subscriptions for different delivery mechanisms';
ALTER TABLE event_processing_log COMMENT = 'Logs event processing stages and results for monitoring';
ALTER TABLE event_templates COMMENT = 'Stores customizable templates for event payloads';
ALTER TABLE webhook_security_log COMMENT = 'Logs security-related events for webhook endpoints';
