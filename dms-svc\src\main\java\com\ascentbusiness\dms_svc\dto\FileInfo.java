package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for file information.
 * Corresponds to FileInfo GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileInfo {

    /**
     * Original filename of the file.
     */
    private String originalFileName;

    /**
     * Size of the file in bytes.
     */
    private Long fileSize;

    /**
     * MIME type of the file.
     */
    private String mimeType;

    /**
     * File extension (optional).
     */
    private String extension;

    /**
     * Whether the file is encrypted.
     */
    private Boolean isEncrypted;

    /**
     * File checksum (optional).
     */
    private String checksum;

    /**
     * Virus scan result for the file.
     */
    private VirusScanResult virusScanResult;
}
