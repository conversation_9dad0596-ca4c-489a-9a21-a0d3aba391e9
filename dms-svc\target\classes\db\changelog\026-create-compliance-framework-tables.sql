-- Liquibase formatted SQL
-- changeset dms:026-create-compliance-framework-tables

-- Create compliance_frameworks table
CREATE TABLE compliance_frameworks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    framework_type VARCHAR(50) NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    effective_date TIMESTAMP,
    expiry_date TIMESTAMP,
    authority VARCHAR(255),
    website_url VARCHAR(500),
    implementation_notes TEXT,
    notification_requirements TEXT,
    penalty_information TEXT,
    requires_data_subject_rights BOOLEAN DEFAULT FALSE,
    requires_consent_management BOOLEAN DEFAULT FALSE,
    requires_data_localization BOOLEAN DEFAULT FALSE,
    requires_encryption BOOLEAN DEFAULT FALSE,
    requires_audit_trail BOOLEAN DEFAULT FALSE,
    max_retention_days INTEGER,
    priority INTEGER DEFAULT 0,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    last_modified_by VARCHAR(100),
    INDEX idx_framework_type (framework_type),
    INDEX idx_framework_active (is_active),
    INDEX idx_framework_effective (effective_date),
    INDEX idx_framework_priority (priority)
);

-- Create compliance_classifications table
CREATE TABLE compliance_classifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    classification_level VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    effective_date TIMESTAMP,
    expiry_date TIMESTAMP,
    color_code VARCHAR(7),
    icon VARCHAR(100),
    requires_approval BOOLEAN DEFAULT FALSE,
    approval_role VARCHAR(100),
    requires_encryption BOOLEAN DEFAULT FALSE,
    requires_audit_logging BOOLEAN DEFAULT FALSE,
    max_retention_days INTEGER,
    access_restrictions TEXT,
    handling_instructions TEXT,
    disposal_instructions TEXT,
    priority INTEGER DEFAULT 0,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    last_modified_by VARCHAR(100),
    INDEX idx_classification_level (classification_level),
    INDEX idx_classification_active (is_active),
    INDEX idx_classification_priority (priority)
);

-- Create document_compliance_mappings table
CREATE TABLE document_compliance_mappings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    compliance_classification_id BIGINT NOT NULL,
    storage_region VARCHAR(50),
    classification_reason TEXT,
    classified_by VARCHAR(255),
    classification_date TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    compliance_status VARCHAR(50),
    compliance_notes TEXT,
    violation_count INTEGER DEFAULT 0,
    last_validation_date TIMESTAMP,
    next_review_date TIMESTAMP,
    access_log_retention_days INTEGER,
    anonymization_method VARCHAR(100),
    encryption_algorithm VARCHAR(100),
    encryption_status VARCHAR(50),
    is_anonymized BOOLEAN DEFAULT FALSE,
    is_pseudonymized BOOLEAN DEFAULT FALSE,
    requires_consent BOOLEAN DEFAULT FALSE,
    consent_obtained BOOLEAN DEFAULT FALSE,
    consent_date TIMESTAMP,
    consent_expiry_date TIMESTAMP,
    data_subject_rights_applicable BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    last_modified_by VARCHAR(100),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (compliance_classification_id) REFERENCES compliance_classifications(id),
    INDEX idx_document_compliance_document (document_id),
    INDEX idx_document_compliance_classification (compliance_classification_id),
    INDEX idx_document_compliance_active (is_active),
    INDEX idx_document_compliance_status (compliance_status)
);

-- Create regulation_mappings table
CREATE TABLE regulation_mappings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    compliance_framework_id BIGINT NOT NULL,
    regulation_reference VARCHAR(255),
    compliance_requirement TEXT,
    control_objective TEXT,
    compliance_status VARCHAR(50),
    compliance_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_mandatory BOOLEAN DEFAULT FALSE,
    priority INTEGER DEFAULT 0,
    risk_level VARCHAR(50),
    responsible_party VARCHAR(255),
    review_frequency_months INTEGER,
    next_review_date TIMESTAMP,
    last_review_date TIMESTAMP,
    remediation_deadline TIMESTAMP,
    remediation_plan TEXT,
    evidence_requirements TEXT,
    testing_procedures TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    last_modified_by VARCHAR(100),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (compliance_framework_id) REFERENCES compliance_frameworks(id),
    INDEX idx_regulation_document (document_id),
    INDEX idx_regulation_framework (compliance_framework_id),
    INDEX idx_regulation_active (is_active),
    INDEX idx_regulation_status (compliance_status),
    INDEX idx_regulation_risk (risk_level)
);

-- Create compliance_violations table
CREATE TABLE compliance_violations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_compliance_mapping_id BIGINT NOT NULL,
    violation_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    description TEXT,
    detected_by VARCHAR(255),
    detection_method VARCHAR(50),
    violation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    correlation_id VARCHAR(100),
    user_id VARCHAR(100),
    regulation_violated VARCHAR(255),
    control_failed VARCHAR(255),
    is_resolved BOOLEAN DEFAULT FALSE,
    resolution_date TIMESTAMP,
    resolution_notes TEXT,
    resolved_by VARCHAR(255),
    requires_notification BOOLEAN DEFAULT FALSE,
    notification_sent BOOLEAN DEFAULT FALSE,
    notification_date TIMESTAMP,
    requires_regulatory_report BOOLEAN DEFAULT FALSE,
    regulatory_report_sent BOOLEAN DEFAULT FALSE,
    regulatory_report_date TIMESTAMP,
    remediation_deadline TIMESTAMP,
    remediation_plan TEXT,
    remediation_completed BOOLEAN DEFAULT FALSE,
    remediation_completion_date TIMESTAMP,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_compliance_mapping_id) REFERENCES document_compliance_mappings(id),
    INDEX idx_violation_mapping (document_compliance_mapping_id),
    INDEX idx_violation_type (violation_type),
    INDEX idx_violation_severity (severity),
    INDEX idx_violation_resolved (is_resolved),
    INDEX idx_violation_date (violation_date),
    INDEX idx_violation_correlation (correlation_id)
);

-- Create junction tables for many-to-many relationships

-- Compliance framework regions
CREATE TABLE compliance_framework_regions (
    framework_id BIGINT NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (framework_id, region),
    FOREIGN KEY (framework_id) REFERENCES compliance_frameworks(id) ON DELETE CASCADE
);

-- Classification data subjects
CREATE TABLE classification_data_subjects (
    classification_id BIGINT NOT NULL,
    data_subject_category VARCHAR(50) NOT NULL,
    PRIMARY KEY (classification_id, data_subject_category),
    FOREIGN KEY (classification_id) REFERENCES compliance_classifications(id) ON DELETE CASCADE
);

-- Classification allowed regions
CREATE TABLE classification_allowed_regions (
    classification_id BIGINT NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (classification_id, region),
    FOREIGN KEY (classification_id) REFERENCES compliance_classifications(id) ON DELETE CASCADE
);

-- Document compliance mapping data subjects
CREATE TABLE document_compliance_data_subjects (
    mapping_id BIGINT NOT NULL,
    data_subject_category VARCHAR(50) NOT NULL,
    PRIMARY KEY (mapping_id, data_subject_category),
    FOREIGN KEY (mapping_id) REFERENCES document_compliance_mappings(id) ON DELETE CASCADE
);

-- Add comments for documentation
ALTER TABLE compliance_frameworks COMMENT = 'Stores compliance framework definitions like GDPR, HIPAA, SOX, etc.';
ALTER TABLE compliance_classifications COMMENT = 'Stores compliance classification levels and requirements';
ALTER TABLE document_compliance_mappings COMMENT = 'Maps documents to compliance classifications with specific requirements';
ALTER TABLE regulation_mappings COMMENT = 'Maps documents to specific regulatory requirements and controls';
ALTER TABLE compliance_violations COMMENT = 'Tracks compliance violations and their resolution status';
