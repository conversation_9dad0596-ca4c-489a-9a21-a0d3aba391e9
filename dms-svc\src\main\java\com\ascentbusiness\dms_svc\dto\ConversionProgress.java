package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionStatus;
import com.ascentbusiness.dms_svc.enums.ConversionType;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Progress tracking DTO for conversion operations from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionProgress {
    private String sessionId;
    private String conversionId; // Added for test compatibility
    private ConversionType conversionType;
    private ConversionStatus status;
    private Float progress; // 0.0 to 1.0
    private Float progressPercentage; // Added for test compatibility (0-100)
    private String currentStep;
    private Integer totalSteps;
    private Integer completedSteps;
    private LocalDateTime startedAt;
    private LocalDateTime lastUpdatedAt;
    private Long estimatedTimeRemaining; // milliseconds
    private Float processingRate; // pages/files per second
    private String errorMessage;
    private String message; // Added for test compatibility
}
