/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * DTO for webhook endpoint statistics
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebhookEndpointStatistics {
    
    private Integer totalEndpoints;
    private Integer activeEndpoints;
    private Integer verifiedEndpoints;
    private Long totalDeliveries;
    private Long successfulDeliveries;
    private Long failedDeliveries;
    private Float averageResponseTime;
    private Float successRate;
    private OffsetDateTime lastDeliveryTime;
    private Integer healthyEndpoints;
    private Integer unhealthyEndpoints;
}
