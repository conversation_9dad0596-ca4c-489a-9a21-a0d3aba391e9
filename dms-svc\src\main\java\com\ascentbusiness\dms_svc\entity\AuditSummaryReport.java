package com.ascentbusiness.dms_svc.entity;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Entity representing scheduled audit summary reports for compliance dashboards
 */
@Entity
@Table(name = "audit_summary_reports")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuditSummaryReport {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "report_id", nullable = false, unique = true, length = 100)
    private String reportId;
    
    @Column(name = "report_type", nullable = false, length = 50)
    private String reportType;
    
    @Column(name = "report_period", nullable = false, length = 20)
    private String reportPeriod;
    
    @Column(name = "period_start", nullable = false)
    private LocalDateTime periodStart;
    
    @Column(name = "period_end", nullable = false)
    private LocalDateTime periodEnd;
    
    @Column(name = "generated_date")
    private LocalDateTime generatedDate;
    
    @Column(name = "generated_by", length = 100)
    private String generatedBy;
    
    @Column(name = "total_events")
    private Long totalEvents;
    
    @Column(name = "compliance_events")
    private Long complianceEvents;
    
    @Column(name = "security_events")
    private Long securityEvents;
    
    @Column(name = "violation_events")
    private Long violationEvents;
    
    @Column(name = "high_risk_events")
    private Long highRiskEvents;
    
    @Column(name = "summary_data", columnDefinition = "JSON")
    private String summaryData;
    
    @Column(name = "report_file_path", length = 500)
    private String reportFilePath;
    
    @Column(name = "report_file_size")
    private Long reportFileSize;
    
    @Column(name = "report_hash", length = 128)
    private String reportHash;
    
    @Column(name = "digital_signature", columnDefinition = "TEXT")
    private String digitalSignature;
    
    @Column(name = "is_published")
    @Builder.Default
    private Boolean isPublished = false;
    
    @Column(name = "publication_date")
    private LocalDateTime publicationDate;
    
    @Column(name = "retention_period_days")
    @Builder.Default
    private Integer retentionPeriodDays = 2555; // 7 years default
    
    @Column(name = "archive_date")
    private LocalDateTime archiveDate;
    
    @Column(name = "is_archived")
    @Builder.Default
    private Boolean isArchived = false;
    
    @Column(name = "correlation_id", length = 100)
    private String correlationId;
    
    @Column(name = "created_date")
    private LocalDateTime createdDate;
    
    @PrePersist
    protected void onCreate() {
        if (createdDate == null) {
            createdDate = LocalDateTime.now();
        }
        if (generatedDate == null) {
            generatedDate = LocalDateTime.now();
        }
    }
    
    /**
     * Check if report is published
     */
    @Transient
    public boolean isReportPublished() {
        return Boolean.TRUE.equals(isPublished);
    }
    
    /**
     * Check if report is due for archival
     */
    @Transient
    public boolean isDueForArchival() {
        if (retentionPeriodDays == null || generatedDate == null) {
            return false;
        }
        LocalDateTime archivalDue = generatedDate.plusDays(retentionPeriodDays);
        return LocalDateTime.now().isAfter(archivalDue) && !isArchived;
    }
    
    /**
     * Get report period duration in days
     */
    @Transient
    public Long getReportPeriodDays() {
        if (periodStart == null || periodEnd == null) {
            return null;
        }
        return java.time.Duration.between(periodStart, periodEnd).toDays();
    }
    
    /**
     * Get compliance event percentage
     */
    @Transient
    public Double getComplianceEventPercentage() {
        if (totalEvents == null || totalEvents == 0 || complianceEvents == null) {
            return 0.0;
        }
        return (complianceEvents.doubleValue() / totalEvents.doubleValue()) * 100.0;
    }
    
    /**
     * Get security event percentage
     */
    @Transient
    public Double getSecurityEventPercentage() {
        if (totalEvents == null || totalEvents == 0 || securityEvents == null) {
            return 0.0;
        }
        return (securityEvents.doubleValue() / totalEvents.doubleValue()) * 100.0;
    }
    
    /**
     * Get violation event percentage
     */
    @Transient
    public Double getViolationEventPercentage() {
        if (totalEvents == null || totalEvents == 0 || violationEvents == null) {
            return 0.0;
        }
        return (violationEvents.doubleValue() / totalEvents.doubleValue()) * 100.0;
    }
    
    /**
     * Get high risk event percentage
     */
    @Transient
    public Double getHighRiskEventPercentage() {
        if (totalEvents == null || totalEvents == 0 || highRiskEvents == null) {
            return 0.0;
        }
        return (highRiskEvents.doubleValue() / totalEvents.doubleValue()) * 100.0;
    }
    
    /**
     * Get overall risk score (0-100)
     */
    @Transient
    public int getOverallRiskScore() {
        int score = 0;
        
        // Base score from violation percentage
        Double violationPercentage = getViolationEventPercentage();
        if (violationPercentage > 10) {
            score += 40;
        } else if (violationPercentage > 5) {
            score += 25;
        } else if (violationPercentage > 1) {
            score += 10;
        }
        
        // Add score from high risk events
        Double highRiskPercentage = getHighRiskEventPercentage();
        if (highRiskPercentage > 20) {
            score += 30;
        } else if (highRiskPercentage > 10) {
            score += 20;
        } else if (highRiskPercentage > 5) {
            score += 10;
        }
        
        // Add score from security events
        Double securityPercentage = getSecurityEventPercentage();
        if (securityPercentage > 15) {
            score += 20;
        } else if (securityPercentage > 10) {
            score += 15;
        } else if (securityPercentage > 5) {
            score += 10;
        }
        
        // Add score for compliance events (positive indicator)
        Double compliancePercentage = getComplianceEventPercentage();
        if (compliancePercentage > 50) {
            score += 10;
        }
        
        return Math.min(score, 100);
    }
    
    /**
     * Get risk level based on overall score
     */
    @Transient
    public String getRiskLevel() {
        int score = getOverallRiskScore();
        if (score >= 70) {
            return "CRITICAL";
        } else if (score >= 50) {
            return "HIGH";
        } else if (score >= 30) {
            return "MEDIUM";
        } else {
            return "LOW";
        }
    }
    
    /**
     * Publish the report
     */
    public void publishReport() {
        this.isPublished = true;
        this.publicationDate = LocalDateTime.now();
    }
    
    /**
     * Set report file details
     */
    public void setReportFileDetails(String filePath, Long fileSize, String fileHash) {
        this.reportFilePath = filePath;
        this.reportFileSize = fileSize;
        this.reportHash = fileHash;
    }
    
    @Override
    public String toString() {
        return String.format("AuditSummaryReport{id=%d, reportId='%s', type='%s', period='%s', events=%d, risk='%s'}", 
                           id, reportId, reportType, reportPeriod, totalEvents, getRiskLevel());
    }
}
