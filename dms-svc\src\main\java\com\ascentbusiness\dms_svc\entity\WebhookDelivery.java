package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.enums.DeliveryStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;

/**
 * Entity representing a webhook delivery attempt
 */
@Entity
@Table(name = "webhook_deliveries")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WebhookDelivery extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "webhook_endpoint_id", nullable = false)
    @JsonIgnore
    private WebhookEndpoint webhookEndpoint;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "system_event_id", nullable = false)
    @JsonIgnore
    private SystemEvent systemEvent;

    // Delivery details
    @Column(name = "delivery_attempt", nullable = false)
    @Builder.Default
    private Integer deliveryAttempt = 1;

    @Enumerated(EnumType.STRING)
    @Column(name = "delivery_status", nullable = false, length = 50)
    @Builder.Default
    private DeliveryStatus deliveryStatus = DeliveryStatus.PENDING;

    // HTTP details
    @Column(name = "http_status_code")
    private Integer httpStatusCode;

    @Column(name = "response_body", columnDefinition = "TEXT")
    private String responseBody;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "response_headers", columnDefinition = "JSON")
    private JsonNode responseHeaders;

    @Column(name = "request_payload", columnDefinition = "TEXT")
    private String requestPayload;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "request_headers", columnDefinition = "JSON")
    private JsonNode requestHeaders;

    // Timing
    @Column(name = "scheduled_date", nullable = false)
    @Builder.Default
    private LocalDateTime scheduledDate = LocalDateTime.now();

    @Column(name = "attempted_date")
    private LocalDateTime attemptedDate;

    @Column(name = "completed_date")
    private LocalDateTime completedDate;

    @Column(name = "duration_ms")
    private Integer durationMs;

    // Error handling
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "error_code", length = 50)
    private String errorCode;

    @Column(name = "retry_after_seconds")
    private Integer retryAfterSeconds;

    @Column(name = "correlation_id", length = 100)
    private String correlationId;

    /**
     * Check if delivery is pending
     */
    @Transient
    public boolean isPending() {
        return DeliveryStatus.PENDING.equals(deliveryStatus);
    }

    /**
     * Check if delivery was successful
     */
    @Transient
    public boolean isSuccessful() {
        return DeliveryStatus.SUCCESS.equals(deliveryStatus);
    }

    /**
     * Check if delivery failed
     */
    @Transient
    public boolean isFailed() {
        return DeliveryStatus.FAILED.equals(deliveryStatus);
    }

    /**
     * Check if delivery is being retried
     */
    @Transient
    public boolean isRetrying() {
        return DeliveryStatus.RETRYING.equals(deliveryStatus);
    }

    /**
     * Check if delivery was cancelled
     */
    @Transient
    public boolean isCancelled() {
        return DeliveryStatus.CANCELLED.equals(deliveryStatus);
    }

    /**
     * Check if this is the first delivery attempt
     */
    @Transient
    public boolean isFirstAttempt() {
        return deliveryAttempt != null && deliveryAttempt == 1;
    }

    /**
     * Check if this is a retry attempt
     */
    @Transient
    public boolean isRetryAttempt() {
        return deliveryAttempt != null && deliveryAttempt > 1;
    }

    /**
     * Check if HTTP status indicates success (2xx)
     */
    @Transient
    public boolean isHttpSuccess() {
        return httpStatusCode != null && httpStatusCode >= 200 && httpStatusCode < 300;
    }

    /**
     * Check if HTTP status indicates client error (4xx)
     */
    @Transient
    public boolean isHttpClientError() {
        return httpStatusCode != null && httpStatusCode >= 400 && httpStatusCode < 500;
    }

    /**
     * Check if HTTP status indicates server error (5xx)
     */
    @Transient
    public boolean isHttpServerError() {
        return httpStatusCode != null && httpStatusCode >= 500 && httpStatusCode < 600;
    }

    /**
     * Check if delivery should be retried based on HTTP status
     */
    @Transient
    public boolean shouldRetry() {
        // Retry on server errors (5xx) and some client errors, but not on 4xx generally
        return isHttpServerError() || 
               (httpStatusCode != null && (httpStatusCode == 408 || httpStatusCode == 429));
    }

    /**
     * Get delivery duration in seconds
     */
    @Transient
    public double getDurationSeconds() {
        return durationMs != null ? durationMs / 1000.0 : 0.0;
    }

    /**
     * Check if delivery was fast (under 1 second)
     */
    @Transient
    public boolean isFastDelivery() {
        return durationMs != null && durationMs < 1000;
    }

    /**
     * Check if delivery was slow (over 10 seconds)
     */
    @Transient
    public boolean isSlowDelivery() {
        return durationMs != null && durationMs > 10000;
    }

    /**
     * Get the webhook endpoint name
     */
    @Transient
    public String getWebhookEndpointName() {
        return webhookEndpoint != null ? webhookEndpoint.getName() : null;
    }

    /**
     * Get the webhook endpoint URL
     */
    @Transient
    public String getWebhookEndpointUrl() {
        return webhookEndpoint != null ? webhookEndpoint.getUrl() : null;
    }

    /**
     * Get the event type
     */
    @Transient
    public String getEventType() {
        return systemEvent != null && systemEvent.getEventType() != null ? 
               systemEvent.getEventType().name() : null;
    }

    /**
     * Mark delivery as started
     */
    public void markAsStarted() {
        this.deliveryStatus = DeliveryStatus.PENDING;
        this.attemptedDate = LocalDateTime.now();
    }

    /**
     * Mark delivery as successful
     */
    public void markAsSuccessful(int httpStatus, String response) {
        this.deliveryStatus = DeliveryStatus.SUCCESS;
        this.httpStatusCode = httpStatus;
        this.responseBody = response;
        this.completedDate = LocalDateTime.now();
        this.errorMessage = null;
        this.errorCode = null;
        
        if (attemptedDate != null) {
            this.durationMs = (int) java.time.Duration.between(attemptedDate, completedDate).toMillis();
        }
    }

    /**
     * Mark delivery as failed
     */
    public void markAsFailed(String error, String code) {
        this.deliveryStatus = DeliveryStatus.FAILED;
        this.errorMessage = error;
        this.errorCode = code;
        this.completedDate = LocalDateTime.now();
        
        if (attemptedDate != null) {
            this.durationMs = (int) java.time.Duration.between(attemptedDate, completedDate).toMillis();
        }
    }

    /**
     * Mark delivery as cancelled
     */
    public void markAsCancelled() {
        this.deliveryStatus = DeliveryStatus.CANCELLED;
        this.completedDate = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("WebhookDelivery{id=%d, webhook='%s', event='%s', attempt=%d, status=%s}", 
                           getId(), getWebhookEndpointName(), getEventType(), deliveryAttempt, deliveryStatus);
    }
}
