package com.ascentbusiness.dms_svc.entity;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;

/**
 * Entity representing reusable workflow templates
 */
@Entity
@Table(name = "workflow_templates")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowTemplate extends BaseEntity {

    @Column(name = "name", nullable = false, unique = true, length = 255)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "category", nullable = false, length = 100)
    private String category; // APPROVAL, REVIEW, COMPLIANCE, CUSTOM

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "template_json", nullable = false, columnDefinition = "JSON")
    private JsonNode templateJson;

    @Builder.Default
    @Column(name = "is_system_template", nullable = false)
    private Boolean isSystemTemplate = false;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "usage_count")
    @Builder.Default
    private Integer usageCount = 0;

    @Column(name = "last_used_date")
    private LocalDateTime lastUsedDate;

    @Column(name = "created_by", nullable = false, length = 255)
    private String createdBy;

    @Column(name = "last_modified_by", length = 255)
    private String lastModifiedBy;

    /**
     * Check if this template is currently active
     */
    @Transient
    public boolean isCurrentlyActive() {
        return isActive != null && isActive;
    }

    /**
     * Check if this is a system-provided template
     */
    @Transient
    public boolean isSystemProvided() {
        return isSystemTemplate != null && isSystemTemplate;
    }

    /**
     * Check if this template has been used recently (within last 30 days)
     */
    @Transient
    public boolean isRecentlyUsed() {
        return lastUsedDate != null && 
               lastUsedDate.isAfter(LocalDateTime.now().minusDays(30));
    }

    /**
     * Check if this template is popular (used more than 10 times)
     */
    @Transient
    public boolean isPopular() {
        return usageCount != null && usageCount > 10;
    }

    /**
     * Increment usage count and update last used date
     */
    public void recordUsage() {
        this.usageCount = (this.usageCount != null ? this.usageCount : 0) + 1;
        this.lastUsedDate = LocalDateTime.now();
    }

    /**
     * Get the template category display name
     */
    @Transient
    public String getCategoryDisplayName() {
        if (category == null) return "Unknown";
        
        switch (category.toUpperCase()) {
            case "APPROVAL":
                return "Approval Workflow";
            case "REVIEW":
                return "Review Workflow";
            case "COMPLIANCE":
                return "Compliance Workflow";
            case "CUSTOM":
                return "Custom Workflow";
            default:
                return category;
        }
    }

    /**
     * Check if template has valid JSON configuration
     */
    @Transient
    public boolean hasValidConfiguration() {
        return templateJson != null && !templateJson.isNull() && templateJson.has("stages");
    }

    @Override
    public String toString() {
        return String.format("WorkflowTemplate{id=%d, name='%s', category='%s', active=%s, usage=%d}", 
                           getId(), name, category, isActive, usageCount);
    }
}
