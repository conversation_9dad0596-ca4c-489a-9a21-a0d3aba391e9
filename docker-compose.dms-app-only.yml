# DMS Service Application Only - Connects to Shared Infrastructure
# Use this when shared infrastructure is already running
version: '3.8'

services:
  # DMS Service Application
  dms-service-app:
    build:
      context: ./dms-svc
      dockerfile: Dockerfile
      target: runtime
    container_name: dms-service-app
    ports:
      - "9093:9093"
      - "9464:9464"  # Prometheus metrics endpoint
    environment:
      # Database Configuration - Connect to shared MySQL
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=9093
      - SPRING_DATASOURCE_URL=**********************************/${MYSQL_DMS_DATABASE:-dms_db}?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
      - SPRING_DATASOURCE_USERNAME=${MYSQL_DMS_USER:-dms_user}
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_DMS_PASSWORD:-dms_password}
      
      # Redis Configuration - Connect to shared Redis
      - SPRING_REDIS_HOST=grc-redis-shared
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-shared_redis_password}
      
      # Storage Configuration
      - DMS_STORAGE_PROVIDER=${DMS_STORAGE_PROVIDER:-LOCAL}
      - DMS_STORAGE_LOCAL_BASE_PATH=${DMS_STORAGE_LOCAL_BASE_PATH:-/app/storage}
      
      # Security Configuration
      - JWT_SECRET=${JWT_SECRET:-sharedSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      
      # Tracing Configuration - Connect to shared Zipkin
      - OTEL_TRACES_EXPORTER=zipkin
      - OTEL_EXPORTER_ZIPKIN_ENDPOINT=http://grc-zipkin-shared:9411/api/v2/spans
      - OTEL_METRICS_EXPORTER=console
      - OTEL_LOGS_EXPORTER=console
      
      # JVM Configuration
      - JAVA_OPTS=${DMS_JAVA_OPTS:--Xmx2g -Xms1g}
      
    volumes:
      - dms_storage:/app/storage
      - dms_logs:/app/logs
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9093/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # Note: No depends_on since infrastructure should already be running

volumes:
  dms_storage:
    driver: local
  dms_logs:
    driver: local

networks:
  grc-shared-network:
    external: true
