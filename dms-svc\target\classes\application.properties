# Application Configuration
spring.application.name=dms-svc
server.port=9093

# Document Sharing Configuration
dms.application.base-url=${DMS_BASE_URL:http://localhost:9093}

# MySQL Database Configuration with Performance Optimizations
spring.datasource.url=${DB_URL:*************************************************************************************************************************************************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:root}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# HikariCP Connection Pool Configuration for Performance
spring.datasource.hikari.pool-name=DmsHikariPool
spring.datasource.hikari.maximum-pool-size=${DB_POOL_MAX_SIZE:20}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:5}
spring.datasource.hikari.idle-timeout=${DB_POOL_IDLE_TIMEOUT:300000}
spring.datasource.hikari.max-lifetime=${DB_POOL_MAX_LIFETIME:1800000}
spring.datasource.hikari.connection-timeout=${DB_POOL_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.validation-timeout=${DB_POOL_VALIDATION_TIMEOUT:5000}
spring.datasource.hikari.leak-detection-threshold=${DB_POOL_LEAK_DETECTION:60000}
spring.datasource.hikari.connection-test-query=SELECT 1

# JPA Configuration with Performance Optimizations
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=${JPA_SHOW_SQL:false}
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Hibernate Performance Optimizations
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.jpa.properties.hibernate.generate_statistics=${HIBERNATE_STATS:false}
spring.jpa.properties.hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS=1000

# Liquibase Configuration (disabled due to checksum validation issues - using Hibernate DDL instead)
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml
spring.liquibase.drop-first=false
spring.liquibase.contexts=default
spring.liquibase.enabled=false

# Redis Configuration for Performance Optimization
spring.data.redis.host=${SPRING_REDIS_HOST:${REDIS_HOST:localhost}}
spring.data.redis.port=${SPRING_REDIS_PORT:${REDIS_PORT:6379}}
spring.data.redis.password=${SPRING_REDIS_PASSWORD:${REDIS_PASSWORD:}}
spring.data.redis.database=${SPRING_REDIS_DATABASE:${REDIS_DATABASE:0}}
spring.data.redis.timeout=5000ms
spring.data.redis.connect-timeout=3000ms

# Redis Connection Pool Configuration (Lettuce)
spring.data.redis.lettuce.pool.max-active=20
spring.data.redis.lettuce.pool.max-idle=10
spring.data.redis.lettuce.pool.min-idle=2
spring.data.redis.lettuce.pool.max-wait=2000ms
spring.data.redis.lettuce.pool.time-between-eviction-runs=30s

# Redis health check configuration - conditional based on environment
management.health.redis.enabled=${REDIS_HEALTH_ENABLED:false}

# GraphQL Configuration
spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:true}
spring.graphql.graphiql.path=/graphiql
spring.graphql.path=/graphql
spring.graphql.schema.printer.enabled=true
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.file-extensions=.graphqls,.gqls
spring.graphql.websocket.path=/graphql
#spring.graphql.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8080,http://localhost:9093}
# Use allowedOriginPatterns instead of allowedOrigins when allowCredentials is true
spring.graphql.cors.allowed-origin-patterns=${CORS_ALLOWED_ORIGIN_PATTERNS:*}
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=Content-Type,Authorization,X-Correlation-ID,Accept
spring.graphql.cors.allow-credentials=true

# Disable multipart GraphQL Upload scalar auto-registration to avoid conflicts
spring.graphql.multipart.springboot.enabled=false



# File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB

# Document Storage Configuration - COMMENTED OUT FOR DYNAMIC DATABASE CONFIGURATION TESTING
# dms.storage.provider=SHAREPOINT
# dms.storage.local.base-path=./storage/documents

# S3 Storage Configuration - Use environment variables for credentials
# dms.storage.s3.bucket-name=${S3_BUCKET_NAME:grc-dms-bucket}
# dms.storage.s3.region=${S3_REGION:ap-south-1}
# dms.storage.s3.access-key=${S3_ACCESS_KEY:}
# dms.storage.s3.secret-key=${S3_SECRET_KEY:}
# dms.storage.s3.endpoint=${S3_ENDPOINT:}

# SharePoint Storage Configuration - Use environment variables for credentials
# dms.storage.sharepoint.client-id=${SHAREPOINT_CLIENT_ID:}
# dms.storage.sharepoint.client-secret=${SHAREPOINT_CLIENT_SECRET:}
# dms.storage.sharepoint.tenant-id=${SHAREPOINT_TENANT_ID:}
# dms.storage.sharepoint.scopes=User.Read,Sites.ReadWrite.All,Files.ReadWrite.All

# Microsoft Graph API Configuration - COMMENTED OUT FOR DYNAMIC DATABASE CONFIGURATION TESTING
# dms.storage.sharepoint.graph-api-url=https://graph.microsoft.com/v1.0
# dms.storage.sharepoint.token-url=https://login.microsoftonline.com/8c28f5eb-e6dc-46a5-8e12-da46dcea72e2/oauth2/v2.0/token

# SharePoint Site Configuration - COMMENTED OUT FOR DYNAMIC DATABASE CONFIGURATION TESTING
# dms.storage.sharepoint.site-url=https://ascenttechnologyconsulting.sharepoint.com/sites/AscentSharepoint
# dms.storage.sharepoint.site-url=https://ascenttechnologyconsulting.sharepoint.com/sites/Ascent-Test
# dms.storage.sharepoint.document-library=Documents

# SharePoint Performance Configuration - COMMENTED OUT FOR DYNAMIC DATABASE CONFIGURATION TESTING
# dms.storage.sharepoint.connection-timeout=30000
# dms.storage.sharepoint.read-timeout=60000
# dms.storage.sharepoint.max-retries=3
# dms.storage.sharepoint.retry-delay-ms=1000

# SharePoint Resilience Configuration - COMMENTED OUT FOR DYNAMIC DATABASE CONFIGURATION TESTING
# dms.storage.sharepoint.enable-circuit-breaker=true
# dms.storage.sharepoint.circuit-breaker-threshold=5
# dms.storage.sharepoint.circuit-breaker-timeout-ms=60000
# dms.storage.sharepoint.fail-safe-startup=true

# SharePoint Debugging Configuration - COMMENTED OUT FOR DYNAMIC DATABASE CONFIGURATION TESTING
# dms.storage.sharepoint.debug=false
# dms.storage.sharepoint.enable-request-logging=false

# Migration Settings
dms.migration.batch-size=100
dms.migration.verify-transfers=true
dms.migration.cleanup-after-migration=false

# JWT Configuration
dms.jwt.secret=${JWT_SECRET:mySecretKey123456789012345678901234567890}
dms.jwt.expiration=${JWT_EXPIRATION:86400000}
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always
management.health.defaults.enabled=true

# Enable all health indicators
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.redis.enabled=true
management.health.ping.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Health indicator thresholds
management.health.diskspace.threshold=100MB
management.metrics.export.prometheus.enabled=true

# OpenTelemetry Configuration
otel.service.name=dms-svc
otel.service.version=1.0.0
otel.resource.attributes.service.name=dms-svc
otel.resource.attributes.service.version=1.0.0
otel.resource.attributes.deployment.environment=${ENVIRONMENT:local}

# OpenTelemetry Tracing Configuration - Use Zipkin exporter for local development
otel.traces.exporter=zipkin
otel.exporter.zipkin.endpoint=http://localhost:9411/api/v2/spans
otel.traces.sampler=traceidratio
otel.traces.sampler.arg=0.1

# OpenTelemetry Metrics Configuration - Use console exporter for local development
otel.metrics.exporter=console
otel.metric.export.interval=30s

# OpenTelemetry Logs Configuration - Use console exporter for local development
otel.logs.exporter=console

# OTLP Configuration (commented out for local development)
# otel.traces.exporter=otlp
# otel.exporter.otlp.traces.endpoint=http://localhost:4318
# otel.exporter.otlp.traces.protocol=grpc
# otel.metrics.exporter=otlp
# otel.exporter.otlp.metrics.endpoint=http://localhost:4318
# otel.exporter.otlp.metrics.protocol=grpc
# otel.logs.exporter=otlp
# otel.exporter.otlp.logs.endpoint=http://localhost:4318
# otel.exporter.otlp.logs.protocol=grpc

# Spring Boot Actuator Tracing
management.tracing.enabled=true
management.tracing.sampling.probability=0.1
management.tracing.baggage.correlation.enabled=true
management.tracing.baggage.correlation.fields=correlationId,userId

# Enable Zipkin for distributed tracing (Zipkin is now running)
management.zipkin.tracing.endpoint=http://localhost:9411/api/v2/spans
management.tracing.zipkin.enabled=true

# Console exporter configuration
otel.exporter.console.enabled=true

# Logging Configuration
logging.level.com.ascentbusiness.dms_svc=INFO
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Correlation ID Configuration
dms.correlation.header-name=X-Correlation-ID

# Rate Limiting Configuration
dms.rate-limit.enabled=${RATE_LIMIT_ENABLED:true}
dms.rate-limit.use-redis=${RATE_LIMIT_USE_REDIS:true}

# GraphQL Rate Limiting
dms.rate-limit.graphql.enabled=${GRAPHQL_RATE_LIMIT_ENABLED:true}
dms.rate-limit.graphql.query.limit=${GRAPHQL_QUERY_RATE_LIMIT:100}
dms.rate-limit.graphql.query.window=${GRAPHQL_QUERY_RATE_WINDOW:60}
dms.rate-limit.graphql.mutation.limit=${GRAPHQL_MUTATION_RATE_LIMIT:50}
dms.rate-limit.graphql.mutation.window=${GRAPHQL_MUTATION_RATE_WINDOW:60}

# Operation-specific Rate Limiting
dms.rate-limit.document.upload.limit=${DOCUMENT_UPLOAD_RATE_LIMIT:10}
dms.rate-limit.document.upload.window=${DOCUMENT_UPLOAD_RATE_WINDOW:300}
dms.rate-limit.document.download.limit=${DOCUMENT_DOWNLOAD_RATE_LIMIT:50}
dms.rate-limit.document.download.window=${DOCUMENT_DOWNLOAD_RATE_WINDOW:60}
dms.rate-limit.search.limit=${SEARCH_RATE_LIMIT:30}
dms.rate-limit.search.window=${SEARCH_RATE_WINDOW:60}
dms.rate-limit.permission.limit=${PERMISSION_RATE_LIMIT:20}
dms.rate-limit.permission.window=${PERMISSION_RATE_WINDOW:300}

# Audit Log Encryption Configuration
dms.audit.encryption.enabled=${AUDIT_ENCRYPTION_ENABLED:false}
dms.audit.encryption.key-rotation.enabled=${AUDIT_ENCRYPTION_KEY_ROTATION_ENABLED:true}
dms.audit.encryption.key-rotation.interval-hours=${AUDIT_ENCRYPTION_KEY_ROTATION_INTERVAL_HOURS:24}
dms.audit.encryption.master-key=${AUDIT_ENCRYPTION_MASTER_KEY:}
dms.audit.encryption.sensitive-events-only=${AUDIT_ENCRYPTION_SENSITIVE_EVENTS_ONLY:true}
dms.audit.encryption.performance-mode=${AUDIT_ENCRYPTION_PERFORMANCE_MODE:true}

# PII Field-Level Encryption Configuration
dms.pii.encryption.enabled=${PII_ENCRYPTION_ENABLED:false}
dms.pii.encryption.key-rotation.enabled=${PII_ENCRYPTION_KEY_ROTATION_ENABLED:true}
dms.pii.encryption.key-rotation.interval-hours=${PII_ENCRYPTION_KEY_ROTATION_INTERVAL_HOURS:168}
dms.pii.encryption.master-key=${PII_ENCRYPTION_MASTER_KEY:}
dms.pii.encryption.auto-detect=${PII_ENCRYPTION_AUTO_DETECT:true}

# Security Headers Configuration
# Note: These are relaxed defaults for local development. Override in production.
dms.security.headers.csp.enabled=${SECURITY_HEADERS_CSP_ENABLED:true}
dms.security.headers.csp.policy=${SECURITY_HEADERS_CSP_POLICY:default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://esm.sh https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://esm.sh https://unpkg.com https://cdn.jsdelivr.net; img-src 'self' data: https:; font-src 'self' data: https://esm.sh https://unpkg.com https://cdn.jsdelivr.net; connect-src 'self' ws: wss: https://esm.sh; frame-ancestors 'self'; base-uri 'self'; form-action 'self'}
dms.security.headers.csp.report-only=${SECURITY_HEADERS_CSP_REPORT_ONLY:false}
dms.security.headers.hsts.enabled=${SECURITY_HEADERS_HSTS_ENABLED:true}
dms.security.headers.hsts.max-age=${SECURITY_HEADERS_HSTS_MAX_AGE:31536000}
dms.security.headers.hsts.include-subdomains=${SECURITY_HEADERS_HSTS_INCLUDE_SUBDOMAINS:true}
dms.security.headers.hsts.preload=${SECURITY_HEADERS_HSTS_PRELOAD:true}
dms.security.headers.frame-options=${SECURITY_HEADERS_FRAME_OPTIONS:SAMEORIGIN}
dms.security.headers.content-type-options=${SECURITY_HEADERS_CONTENT_TYPE_OPTIONS:nosniff}
dms.security.headers.referrer-policy=${SECURITY_HEADERS_REFERRER_POLICY:strict-origin-when-cross-origin}
dms.security.headers.permissions-policy=${SECURITY_HEADERS_PERMISSIONS_POLICY:geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(self), sync-xhr=()}
dms.security.headers.cross-origin-embedder-policy=${SECURITY_HEADERS_COEP:unsafe-none}
dms.security.headers.cross-origin-opener-policy=${SECURITY_HEADERS_COOP:unsafe-none}
dms.security.headers.cross-origin-resource-policy=${SECURITY_HEADERS_CORP:cross-origin}
dms.security.headers.cross-origin-resource-policy=${SECURITY_HEADERS_CORP:same-origin}
dms.security.headers.expect-ct.enabled=${SECURITY_HEADERS_EXPECT_CT_ENABLED:false}
dms.security.headers.expect-ct.max-age=${SECURITY_HEADERS_EXPECT_CT_MAX_AGE:86400}
dms.security.headers.expect-ct.enforce=${SECURITY_HEADERS_EXPECT_CT_ENFORCE:false}
dms.security.headers.expect-ct.report-uri=${SECURITY_HEADERS_EXPECT_CT_REPORT_URI:}

# API Versioning Configuration
dms.api.version.current=${API_VERSION_CURRENT:1.0}
dms.api.version.supported=${API_VERSION_SUPPORTED:1.0,1.1}
dms.api.version.deprecation-warning=${API_VERSION_DEPRECATION_WARNING:true}
dms.api.version.strict-mode=${API_VERSION_STRICT_MODE:false}

# Cache Configuration for Performance Optimization
# Conditional Redis cache configuration
spring.cache.type=${CACHE_TYPE:simple}
spring.cache.redis.time-to-live=1800000
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dms:

# Cache-specific TTL settings (in milliseconds)
# Documents: 1 hour = 3600000ms
# Search results: 15 minutes = 900000ms
# Permissions: 30 minutes = 1800000ms
# Security config: 4 hours = 14400000ms

# Test Case Configuration
testcase.directory=tests/test-cases

# Storage Configuration Test Settings
dms.storage.test.create-sample-configurations=false
dms.storage.test.run-tests-on-startup=false

# Storage Configuration Initialization
dms.storage.initialize-default-configuration=false

# Elasticsearch Configuration - conditional for local development
elasticsearch.enabled=${ELASTICSEARCH_ENABLED:false}
elasticsearch.host=${ELASTICSEARCH_HOST:localhost}
elasticsearch.port=${ELASTICSEARCH_PORT:9200}
elasticsearch.protocol=${ELASTICSEARCH_PROTOCOL:http}
elasticsearch.username=${ELASTICSEARCH_USERNAME:elastic}
elasticsearch.password=${ELASTICSEARCH_PASSWORD:}
elasticsearch.connection-timeout=${ELASTICSEARCH_CONNECTION_TIMEOUT:10000}
elasticsearch.socket-timeout=${ELASTICSEARCH_SOCKET_TIMEOUT:30000}

# Retention Policy Configuration
dms.retention.processing.enabled=true
dms.retention.processing.batch-size=100
dms.retention.processing.cron=0 0 2 * * *
dms.retention.notification.enabled=true
dms.retention.notification.days-before-expiry=30,7,1
dms.retention.auto-assignment.enabled=true
dms.retention.legal-hold.enabled=true

# File Processing Configuration
# Thresholds for determining processing strategy
dms.file-processing.direct-processing-threshold=10485760
dms.file-processing.async-processing-threshold=104857600
dms.file-processing.max-file-size=1073741824

# Chunk upload configuration
dms.file-processing.default-chunk-size=5242880
dms.file-processing.max-chunk-size=52428800
dms.file-processing.min-chunk-size=1048576

# Async processing configuration
dms.file-processing.max-concurrent-async-jobs=10
dms.file-processing.async-job-timeout-seconds=3600

# Session and cleanup configuration
dms.file-processing.chunk-session-timeout-seconds=86400
dms.file-processing.cleanup-interval-seconds=3600
dms.file-processing.temp-directory=./temp/processing

# PDF to Word Conversion Configuration
# Maximum file size for PDF conversion (50MB default)
dms.pdf-conversion.max-file-size=52428800
# Conversion timeout in seconds (5 minutes default)
dms.pdf-conversion.timeout-seconds=300
# Default virus scanner for PDF conversion
dms.pdf-conversion.virus-scanner=MOCK
# Enable/disable PDF conversion feature
dms.pdf-conversion.enabled=true
# Temp directory for conversion operations (uses system temp if not specified)
dms.pdf-conversion.temp-directory=
# Auto-cleanup converted files after specified hours (24 hours default)
dms.pdf-conversion.cleanup-after-hours=24

# Word to PDF Conversion Configuration
# Maximum file size for Word conversion (50MB default)
dms.word-conversion.max-file-size=52428800
# Conversion timeout in seconds (5 minutes default)
dms.word-conversion.timeout-seconds=300
# Default virus scanner for Word conversion
dms.word-conversion.virus-scanner=MOCK
# Enable/disable Word conversion feature
dms.word-conversion.enabled=true
# Temp directory for conversion operations (uses system temp if not specified)
dms.word-conversion.temp-directory=
# Auto-cleanup converted files after specified hours (24 hours default)
dms.word-conversion.cleanup-after-hours=24

# Pandoc Configuration for Enhanced Document Conversion
# Path to Pandoc executable (assumes it's in PATH if not specified)
dms.pandoc.executable-path=pandoc
# Enable/disable Pandoc conversion feature
dms.pandoc.enabled=true
# Enable fallback to existing implementations when Pandoc is not available
dms.pandoc.enable-fallback=true
# Conversion timeout in seconds (5 minutes default)
dms.pandoc.timeout-seconds=300
# Maximum file size for Pandoc conversion (50MB default)
dms.pandoc.max-file-size=52428800
# Default virus scanner for Pandoc conversions
dms.pandoc.virus-scanner=MOCK
# Temp directory for Pandoc operations (uses system temp if not specified)
dms.pandoc.temp-directory=
# Auto-cleanup converted files after specified hours (24 hours default)
dms.pandoc.cleanup-after-hours=24
# Check Pandoc availability on startup
dms.pandoc.check-availability-on-startup=true
# Additional Pandoc command line arguments (comma-separated)
dms.pandoc.additional-args=

# Feature flags
dms.file-processing.enable-auto-cleanup=true
dms.file-processing.enable-progress-tracking=true
dms.file-processing.enable-detailed-logging=false

# ========================================
# VIRUS SCANNING CONFIGURATION
# ========================================

# Global Virus Scanning Settings
dms.virus-scanning.enabled=${VIRUS_SCANNING_ENABLED:true}
dms.virus-scanning.fail-on-unavailable=${VIRUS_SCANNING_FAIL_ON_UNAVAILABLE:true}
dms.virus-scanning.default-scanner=${VIRUS_SCANNING_DEFAULT_SCANNER:MOCK}

# Mock Scanner Configuration (for testing and development)
dms.virus-scanner.mock.enabled=${VIRUS_SCANNER_MOCK_ENABLED:true}

# ClamAV Scanner Configuration
dms.virus-scanner.clamav.enabled=${VIRUS_SCANNER_CLAMAV_ENABLED:false}
dms.virus-scanner.clamav.host=${VIRUS_SCANNER_CLAMAV_HOST:localhost}
dms.virus-scanner.clamav.port=${VIRUS_SCANNER_CLAMAV_PORT:3310}
dms.virus-scanner.clamav.timeout=${VIRUS_SCANNER_CLAMAV_TIMEOUT:10000}
dms.virus-scanner.clamav.chunk-size=${VIRUS_SCANNER_CLAMAV_CHUNK_SIZE:8192}
dms.virus-scanner.clamav.max-file-size=${VIRUS_SCANNER_CLAMAV_MAX_FILE_SIZE:104857600}

# Windows Defender Scanner Configuration
dms.virus-scanner.windows-defender.enabled=${VIRUS_SCANNER_WINDOWS_DEFENDER_ENABLED:false}
dms.virus-scanner.windows-defender.executable-path=${VIRUS_SCANNER_WINDOWS_DEFENDER_EXECUTABLE:C:\\Program Files\\Windows Defender\\MpCmdRun.exe}
dms.virus-scanner.windows-defender.timeout=${VIRUS_SCANNER_WINDOWS_DEFENDER_TIMEOUT:60}
dms.virus-scanner.windows-defender.temp-dir=${VIRUS_SCANNER_WINDOWS_DEFENDER_TEMP_DIR:${java.io.tmpdir}}
dms.virus-scanner.windows-defender.max-file-size=${VIRUS_SCANNER_WINDOWS_DEFENDER_MAX_FILE_SIZE:104857600}

# Sophos Scanner Configuration (placeholder for future implementation)
dms.virus-scanner.sophos.enabled=${VIRUS_SCANNER_SOPHOS_ENABLED:false}
dms.virus-scanner.sophos.endpoint=${VIRUS_SCANNER_SOPHOS_ENDPOINT:}
dms.virus-scanner.sophos.api-key=${VIRUS_SCANNER_SOPHOS_API_KEY:}
dms.virus-scanner.sophos.timeout=${VIRUS_SCANNER_SOPHOS_TIMEOUT:30000}
dms.virus-scanner.sophos.max-file-size=${VIRUS_SCANNER_SOPHOS_MAX_FILE_SIZE:104857600}

# VirusTotal Scanner Configuration (placeholder for future implementation)
dms.virus-scanner.virus-total.enabled=${VIRUS_SCANNER_VIRUS_TOTAL_ENABLED:false}
dms.virus-scanner.virus-total.api-key=${VIRUS_SCANNER_VIRUS_TOTAL_API_KEY:}
dms.virus-scanner.virus-total.endpoint=${VIRUS_SCANNER_VIRUS_TOTAL_ENDPOINT:https://www.virustotal.com/vtapi/v2/file/scan}
dms.virus-scanner.virus-total.timeout=${VIRUS_SCANNER_VIRUS_TOTAL_TIMEOUT:60000}
dms.virus-scanner.virus-total.max-file-size=${VIRUS_SCANNER_VIRUS_TOTAL_MAX_FILE_SIZE:33554432}
dms.virus-scanner.virus-total.rate-limit-delay=${VIRUS_SCANNER_VIRUS_TOTAL_RATE_LIMIT_DELAY:15000}
