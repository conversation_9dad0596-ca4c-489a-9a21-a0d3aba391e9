package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;

import java.util.List;

@Data
public class DocumentUploadFromPathInput {
    private String name;
    private StorageProvider storageProvider;    // Optional - defaults to application.properties setting
    private String sourceFilePath;              // Renamed from filePath to match GraphQL schema
    private List<String> keywords;              // Optional in GraphQL (renamed from tags to match GraphQL schema)
    private Boolean overrideFile = false;       // NEW - duplicate control (default: false)
    private String comment;                     // NEW - optional comment for audit trail

    // Virus scanning configuration
    private VirusScannerType scannerType; // Optional - will use default scanner if not provided

    // Metadata fields (optional)
    private DocumentClassificationMetadataInput classificationMetadata;
    private DocumentOwnershipMetadataInput ownershipMetadata;
    private DocumentComplianceMetadataInput complianceMetadata;
}
