package com.ascentbusiness.dms_svc.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;

/**
 * Elasticsearch Repository Configuration for Document Management System
 *
 * This configuration is separate from the main ElasticsearchConfig to ensure
 * that repository scanning only happens when Elasticsearch is enabled.
 *
 * The @EnableElasticsearchRepositories annotation is processed during Spring's
 * component scanning phase, so it needs to be on a conditionally loaded class.
 */
@Configuration
@ConditionalOnProperty(name = "elasticsearch.enabled", havingValue = "true", matchIfMissing = false)
@EnableElasticsearchRepositories(basePackages = "com.ascentbusiness.dms_svc.search.repository")
public class ElasticsearchRepositoryConfig {
    // This class only exists to conditionally enable Elasticsearch repositories
    // The actual client configuration is in ElasticsearchConfig
}
