package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.DocumentPermission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response DTO for bulk document permission operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentPermissionListResponse {
    
    private Boolean success;
    private String message;
    private List<DocumentPermission> permissions;
    
    public static DocumentPermissionListResponse success(String message, List<DocumentPermission> permissions) {
        return DocumentPermissionListResponse.builder()
                .success(true)
                .message(message)
                .permissions(permissions)
                .build();
    }
    
    public static DocumentPermissionListResponse success(String message) {
        return DocumentPermissionListResponse.builder()
                .success(true)
                .message(message)
                .build();
    }
    
    public static DocumentPermissionListResponse error(String message) {
        return DocumentPermissionListResponse.builder()
                .success(false)
                .message(message)
                .build();
    }
}
