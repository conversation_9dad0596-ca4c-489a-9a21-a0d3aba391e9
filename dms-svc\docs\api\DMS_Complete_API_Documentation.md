# DMS Complete API Documentation

This document provides comprehensive API documentation for the Document Management Service (DMS) including all GraphQL and REST endpoints, business features, core document operations, security, audit, and administrative functions.

## Table of Contents

1. [Overview](#overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Core Document Operations](#core-document-operations)
4. [Document Query Operations](#document-query-operations)
5. [Document Version Management](#document-version-management)
6. [Document Metadata Operations](#document-metadata-operations)
7. [Security & Audit Operations](#security--audit-operations)
8. [Search & Filter Operations](#search--filter-operations)
9. [Workflow Management API](#workflow-management-api)
10. [Document Templates API](#document-templates-api)
11. [Webhook & Event System API](#webhook--event-system-api)
12. [Document Sharing API](#document-sharing-api)
13. [Administrative Operations](#administrative-operations)
14. [Error Handling](#error-handling)
15. [Rate Limiting](#rate-limiting)
16. [Examples & Use Cases](#examples--use-cases)

## Overview

### Base URLs
- **GraphQL Endpoint**: `http://localhost:9092/graphql`
- **GraphiQL Interface**: `http://localhost:9092/graphiql`
- **REST API Base**: `http://localhost:9092/api/v1`
- **Health Check**: `http://localhost:9092/actuator/health`

### Supported Operations
- **Document Management**: Upload, download, versioning, deletion
- **Metadata Management**: Classification, ownership, compliance
- **Security Operations**: Permissions, audit logs, violations
- **Search Operations**: Full-text search, filtering, faceted search
- **Administrative**: User management, system configuration

## Authentication & Authorization

### JWT Token Format
```http
Authorization: Bearer <jwt-token>
```

### Generate Test Token
```graphql
mutation GenerateTestToken($input: JwtTokenRequest!) {
  generateTestToken(input: $input) {
    token
    expiresAt
    userId
    roles
    permissions
  }
}
```

**Example:**
```json
{
  "input": {
    "userId": "user123",
    "roles": ["USER", "ADMIN"],
    "permissions": ["READ", "WRITE", "DELETE"],
    "expirationMinutes": 60
  }
}
```

## Document Operations

### Core Document Operations

### 1. Upload Document

**GraphQL Mutation:**
```graphql
mutation UploadDocument($input: UploadDocumentInput!) {
  uploadDocument(input: $input) {
    id
    name
    version
    status
    storageProvider
    storagePath
    originalFileName
    fileSize
    mimeType
    createdDate
    lastModifiedDate
    creatorUserId
    tags
    permissions {
      id
      userId
      permissionType
      isActive
      expiresAt
    }
    classificationMetadata {
      id
      module
      subModule
      confidentialityLevel
      dataClassification
      securityLevel
    }
    ownershipMetadata {
      id
      owner
      department
      businessUnit
      expiryDate
      renewalReminder
      retentionPeriod
    }
    complianceMetadata {
      id
      complianceStandard
      auditRelevance
      linkedRisksControls
      controlId
      thirdPartyId
      policyId
    }
  }
}
```

**Input Schema:**
```graphql
input UploadDocumentInput {
  file: Upload!
  name: String!
  description: String
  keywords: [String!]
  storageProvider: StorageProvider
  overrideFile: Boolean = false
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}

input DocumentClassificationMetadataInput {
  module: String!
  subModule: String
  confidentialityLevel: String!
  dataClassification: String
  securityLevel: String
  accessRestrictions: String
  handlingInstructions: String
}

input DocumentOwnershipMetadataInput {
  owner: String!
  department: String!
  businessUnit: String
  expiryDate: DateTime
  renewalReminder: DateTime
  retentionPeriod: String
  disposalMethod: String
  archivalDate: DateTime
}

input DocumentComplianceMetadataInput {
  complianceStandard: String!
  auditRelevance: String
  linkedRisksControls: String
  controlId: String
  thirdPartyId: String
  policyId: String
}

enum StorageProvider {
  LOCAL
  S3
  SHAREPOINT
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:9092/graphql \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -F 'operations={"query":"mutation UploadDocument($input: UploadDocumentInput!) { uploadDocument(input: $input) { id name version status storageProvider } }","variables":{"input":{"name":"Financial Report Q4","description":"Quarterly financial report","keywords":["finance","report","Q4"],"storageProvider":"LOCAL","classificationMetadata":{"module":"Finance","confidentialityLevel":"Confidential"},"ownershipMetadata":{"owner":"<EMAIL>","department":"Finance"}}}}' \
  -F 'map={"0":["variables.input.file"]}' \
  -F '0=@/path/to/financial-report.pdf'
```

### 2. Upload Document from Path

**GraphQL Mutation:**
```graphql
mutation UploadDocumentFromPath($input: DocumentUploadFromPathInput!) {
  uploadDocumentFromPath(input: $input) {
    id
    name
    version
    status
    storageProvider
    storagePath
    originalFileName
    fileSize
    mimeType
    createdDate
    lastModifiedDate
    creatorUserId
  }
}
```

**Input Schema:**
```graphql
input DocumentUploadFromPathInput {
  sourceFilePath: String!
  name: String!
  description: String
  keywords: [String!]
  storageProvider: StorageProvider
  overrideFile: Boolean = false
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}
```

**Example:**
```json
{
  "input": {
    "sourceFilePath": "/server/documents/contract.pdf",
    "name": "Service Contract 2024",
    "description": "Annual service contract",
    "keywords": ["contract", "service", "2024"],
    "storageProvider": "S3",
    "classificationMetadata": {
      "module": "Legal",
      "confidentialityLevel": "Restricted"
    },
    "ownershipMetadata": {
      "owner": "<EMAIL>",
      "department": "Legal",
      "expiryDate": "2024-12-31T23:59:59Z"
    }
  }
}
```

### 3. Upload New Version

**GraphQL Mutation:**
```graphql
mutation UploadDocumentNewVersion($input: UploadNewVersionInput!) {
  uploadDocumentNewVersion(input: $input) {
    id
    name
    version
    status
    parentDocument {
      id
      name
      version
    }
    createdDate
    lastModifiedDate
  }
}
```

**Input Schema:**
```graphql
input UploadNewVersionInput {
  documentId: ID!
  file: Upload!
  name: String
  description: String
  keywords: [String!]
  storageProvider: StorageProvider
  overrideFile: Boolean = false
}
```

### 4. Delete Document

**GraphQL Mutation:**
```graphql
mutation DeleteDocument($id: ID!) {
  deleteDocument(id: $id)
}
```

**Example:**
```json
{
  "id": "123"
}
```

**Response:**
```json
{
  "data": {
    "deleteDocument": true
  }
}
```

## Document Query Operations

### 1. Get Document by ID

**GraphQL Query:**
```graphql
query GetDocumentById($id: ID!) {
  getDocumentById(id: $id) {
    id
    name
    description
    version
    status
    storageProvider
    storagePath
    originalFileName
    fileSize
    mimeType
    createdDate
    lastModifiedDate
    creatorUserId
    tags
    permissions {
      id
      userId
      permissionType
      isActive
      expiresAt
      createdDate
      createdBy
    }
    parentDocument {
      id
      name
      version
    }
    classificationMetadata {
      id
      module
      subModule
      confidentialityLevel
      dataClassification
      securityLevel
      accessRestrictions
      handlingInstructions
    }
    ownershipMetadata {
      id
      owner
      department
      businessUnit
      expiryDate
      renewalReminder
      retentionPeriod
      disposalMethod
      archivalDate
    }
    complianceMetadata {
      id
      complianceStandard
      auditRelevance
      linkedRisksControls
      controlId
      thirdPartyId
      policyId
    }
  }
}
```

**Example:**
```json
{
  "id": "123"
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:9092/graphql \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query GetDocumentById($id: ID!) { getDocumentById(id: $id) { id name version status createdDate } }",
    "variables": { "id": "123" }
  }'
```

### 2. List Document Versions

**GraphQL Query:**
```graphql
query ListDocumentVersions($documentId: ID!) {
  listDocumentVersions(documentId: $documentId) {
    id
    name
    version
    status
    createdDate
    lastModifiedDate
    creatorUserId
    fileSize
    mimeType
    parentDocument {
      id
      name
    }
  }
}
```

### 3. Search Documents

**GraphQL Query:**
```graphql
query SearchDocuments($filter: DocumentSearchInput, $pagination: PaginationInput) {
  searchDocuments(filter: $filter, pagination: $pagination) {
    content {
      id
      name
      description
      version
      status
      storageProvider
      originalFileName
      fileSize
      mimeType
      createdDate
      lastModifiedDate
      creatorUserId
      tags
      classificationMetadata {
        module
        subModule
        confidentialityLevel
      }
      ownershipMetadata {
        owner
        department
        expiryDate
      }
    }
    totalElements
    totalPages
    size
    number
    first
    last
  }
}
```

**Input Schemas:**
```graphql
input DocumentSearchInput {
  name: String
  description: String
  keywords: [String!]
  storageProvider: StorageProvider
  status: DocumentStatus
  creatorUserId: String
  dateFrom: DateTime
  dateTo: DateTime
  fileType: String
  minFileSize: Long
  maxFileSize: Long
  tags: [String!]
  module: String
  confidentialityLevel: String
  owner: String
  department: String
}

input PaginationInput {
  page: Int = 0
  size: Int = 10
  sortBy: String = "createdDate"
  sortDirection: SortDirection = DESC
}

enum SortDirection {
  ASC
  DESC
}

enum DocumentStatus {
  ACTIVE
  HISTORICAL
  DELETED
}
```

**Example Search:**
```json
{
  "filter": {
    "name": "financial",
    "storageProvider": "S3",
    "status": "ACTIVE",
    "dateFrom": "2024-01-01T00:00:00Z",
    "dateTo": "2024-12-31T23:59:59Z",
    "tags": ["finance", "report"],
    "module": "Finance",
    "confidentialityLevel": "Confidential"
  },
  "pagination": {
    "page": 0,
    "size": 20,
    "sortBy": "createdDate",
    "sortDirection": "DESC"
  }
}
```

### 4. Download Document

**GraphQL Query:**
```graphql
query DownloadDocument($id: ID!) {
  downloadDocument(id: $id) {
    id
    name
    originalFileName
    mimeType
    fileSize
    fileContent
    downloadUrl
  }
}
```

**REST Endpoint Alternative:**
```http
GET /api/v1/documents/{id}/download
Authorization: Bearer YOUR_JWT_TOKEN
```

**cURL Example:**
```bash
curl -X GET http://localhost:9092/api/v1/documents/123/download \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o downloaded-file.pdf
```

## Document Version Management

### 1. Get Document Versions

**GraphQL Query:**
```graphql
query GetDocumentVersions($documentId: ID!) {
  getDocumentVersions(documentId: $documentId) {
    id
    name
    version
    status
    createdDate
    lastModifiedDate
    creatorUserId
    fileSize
    mimeType
    parentDocument {
      id
      name
      version
    }
    childVersions {
      id
      version
      status
      createdDate
    }
  }
}
```

### 2. Get Current Version

**GraphQL Query:**
```graphql
query GetCurrentVersion($documentId: ID!) {
  getCurrentVersion(documentId: $documentId) {
    id
    name
    version
    status
    createdDate
    lastModifiedDate
    isCurrentVersion
  }
}
```

### 3. Restore Version

**GraphQL Mutation:**
```graphql
mutation RestoreDocumentVersion($documentId: ID!, $versionId: ID!) {
  restoreDocumentVersion(documentId: $documentId, versionId: $versionId) {
    id
    name
    version
    status
    restoredFromVersion {
      id
      version
      createdDate
    }
  }
}
```

## Document Metadata Operations

### 1. Update Classification Metadata

**GraphQL Mutation:**
```graphql
mutation UpdateClassificationMetadata($documentId: ID!, $input: DocumentClassificationMetadataInput!) {
  updateClassificationMetadata(documentId: $documentId, input: $input) {
    id
    module
    subModule
    confidentialityLevel
    dataClassification
    securityLevel
    accessRestrictions
    handlingInstructions
    lastModifiedDate
  }
}
```

### 2. Update Ownership Metadata

**GraphQL Mutation:**
```graphql
mutation UpdateOwnershipMetadata($documentId: ID!, $input: DocumentOwnershipMetadataInput!) {
  updateOwnershipMetadata(documentId: $documentId, input: $input) {
    id
    owner
    department
    businessUnit
    expiryDate
    renewalReminder
    retentionPeriod
    disposalMethod
    archivalDate
    lastModifiedDate
  }
}
```

### 3. Update Compliance Metadata

**GraphQL Mutation:**
```graphql
mutation UpdateComplianceMetadata($documentId: ID!, $input: DocumentComplianceMetadataInput!) {
  updateComplianceMetadata(documentId: $documentId, input: $input) {
    id
    complianceStandard
    auditRelevance
    linkedRisksControls
    controlId
    thirdPartyId
    policyId
    lastModifiedDate
  }
}
```

### 4. Get Documents by Classification

**GraphQL Query:**
```graphql
query GetDocumentsByClassification($module: String!, $confidentialityLevel: String, $pagination: PaginationInput) {
  getDocumentsByClassification(module: $module, confidentialityLevel: $confidentialityLevel, pagination: $pagination) {
    content {
      id
      name
      version
      status
      classificationMetadata {
        module
        subModule
        confidentialityLevel
        dataClassification
        securityLevel
      }
    }
    totalElements
    totalPages
  }
}
```

### 5. Get Documents Expiring Soon

**GraphQL Query:**
```graphql
query GetDocumentsExpiringBefore($date: DateTime!) {
  getDocumentsExpiringBefore(date: $date) {
    id
    name
    version
    status
    ownershipMetadata {
      owner
      department
      expiryDate
      renewalReminder
    }
    daysUntilExpiry
  }
}
```

## Security & Audit Operations

### 1. Get Audit Logs

**GraphQL Query:**
```graphql
query GetAuditLogs($filter: AuditLogFilterInput, $pagination: PaginationInput) {
  getAuditLogs(filter: $filter, pagination: $pagination) {
    content {
      id
      documentId
      action
      userId
      details
      timestamp
      correlationId
      ipAddress
      userAgent
      eventType
      eventCategory
      riskLevel
      complianceFrameworkId
      regulationReference
      dataSubjectCategory
      geographicRegion
      chainSequence
      previousHash
      currentHash
    }
    totalElements
    totalPages
    size
    number
  }
}
```

**Input Schema:**
```graphql
input AuditLogFilterInput {
  documentId: ID
  userId: String
  action: AuditAction
  eventType: AuditEventType
  eventCategory: AuditEventCategory
  riskLevel: String
  complianceFrameworkId: ID
  geographicRegion: String
  dateFrom: DateTime
  dateTo: DateTime
  correlationId: String
}

enum AuditAction {
  UPLOAD
  DOWNLOAD
  UPDATE
  DELETE
  VIEW
  PERMISSION_GRANT
  PERMISSION_REVOKE
  LOGIN
  LOGOUT
  SECURITY_VIOLATION
  COMPLIANCE_CHECK
}

enum AuditEventType {
  DOCUMENT_OPERATION
  SECURITY_EVENT
  COMPLIANCE_EVENT
  SYSTEM_EVENT
  USER_EVENT
}

enum AuditEventCategory {
  CREATE
  READ
  UPDATE
  DELETE
  SECURITY
  COMPLIANCE
  ADMINISTRATIVE
}
```

**Example:**
```json
{
  "filter": {
    "documentId": "123",
    "action": "UPLOAD",
    "dateFrom": "2024-01-01T00:00:00Z",
    "dateTo": "2024-12-31T23:59:59Z",
    "riskLevel": "HIGH"
  },
  "pagination": {
    "page": 0,
    "size": 50,
    "sortBy": "timestamp",
    "sortDirection": "DESC"
  }
}
```

### 2. Get Document Audit Trail

**GraphQL Query:**
```graphql
query GetDocumentAuditTrail($documentId: ID!, $pagination: PaginationInput) {
  getDocumentAuditTrail(documentId: $documentId, pagination: $pagination) {
    content {
      id
      action
      userId
      details
      timestamp
      correlationId
      ipAddress
      userAgent
      chainSequence
      previousHash
      currentHash
    }
    totalElements
    totalPages
  }
}
```

### 3. Get Security Violations

**GraphQL Query:**
```graphql
query GetSecurityViolations($filter: SecurityViolationFilterInput, $pagination: PaginationInput) {
  getSecurityViolations(filter: $filter, pagination: $pagination) {
    content {
      id
      documentId
      userId
      violationType
      severity
      violationDetails
      correlationId
      ipAddress
      userAgent
      timestamp
      resolved
      resolvedBy
      resolvedDate
      resolutionNotes
    }
    totalElements
    totalPages
  }
}
```

**Input Schema:**
```graphql
input SecurityViolationFilterInput {
  documentId: ID
  userId: String
  violationType: SecurityViolationType
  severity: ViolationSeverity
  resolved: Boolean
  dateFrom: DateTime
  dateTo: DateTime
  correlationId: String
}

enum SecurityViolationType {
  UNAUTHORIZED_ACCESS
  PERMISSION_VIOLATION
  SUSPICIOUS_ACTIVITY
  DATA_BREACH
  POLICY_VIOLATION
  AUTHENTICATION_FAILURE
  RATE_LIMIT_EXCEEDED
}

enum ViolationSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}
```

### 4. Resolve Security Violation

**GraphQL Mutation:**
```graphql
mutation ResolveSecurityViolation($violationId: ID!, $resolvedBy: String!, $resolutionNotes: String) {
  resolveSecurityViolation(violationId: $violationId, resolvedBy: $resolvedBy, resolutionNotes: $resolutionNotes) {
    id
    resolved
    resolvedBy
    resolvedDate
    resolutionNotes
  }
}
```

### 5. Export Audit Logs

**GraphQL Query:**
```graphql
query ExportAuditLogs($filter: AuditLogFilterInput!, $format: ExportFormat!) {
  exportAuditLogs(filter: $filter, format: $format) {
    downloadUrl
    fileName
    fileSize
    expiresAt
  }
}
```

**Input Schema:**
```graphql
enum ExportFormat {
  PDF
  CSV
  JSON
  EXCEL
}
```

### 6. Verify Audit Chain

**GraphQL Query:**
```graphql
query VerifyAuditChain($fromSequence: Long, $toSequence: Long) {
  verifyAuditChain(fromSequence: $fromSequence, toSequence: $toSequence) {
    isValid
    totalRecords
    verifiedRecords
    invalidRecords {
      sequence
      id
      reason
      expectedHash
      actualHash
    }
    verificationTimestamp
  }
}
```

## Workflow Management API

### Overview
The Workflow Management API provides comprehensive document approval and review workflows with support for sequential, parallel, and conditional approval processes.

### Base URL
```
/api/v1/workflows
```

### Workflow Definitions

#### Get All Workflow Definitions
```http
GET /api/v1/workflows/definitions
```

**Parameters:**
- `page` (optional): Page number (default: 0)
- `size` (optional): Page size (default: 10)
- `sortBy` (optional): Sort field (default: createdDate)
- `sortDirection` (optional): Sort direction (default: DESC)

**Response:**
```json
{
  "content": [
    {
      "id": 1,
      "name": "Document Approval Workflow",
      "description": "Standard document approval process",
      "version": "1.0",
      "workflowType": "DOCUMENT_APPROVAL",
      "approvalType": "SEQUENTIAL",
      "isActive": true,
      "isDefault": false,
      "autoStart": false,
      "timeoutHours": 72,
      "escalationEnabled": true,
      "escalationHours": 24,
      "createdBy": "admin",
      "createdDate": "2024-01-15T10:30:00",
      "lastModifiedDate": "2024-01-15T10:30:00"
    }
  ],
  "totalElements": 1,
  "totalPages": 1,
  "size": 10,
  "number": 0,
  "first": true,
  "last": true
}
```

#### Create Workflow Definition
```http
POST /api/v1/workflows/definitions
```

**Request Body:**
```json
{
  "name": "New Approval Workflow",
  "description": "Custom approval workflow for contracts",
  "workflowType": "DOCUMENT_APPROVAL",
  "approvalType": "PARALLEL",
  "autoStart": false,
  "timeoutHours": 48,
  "escalationEnabled": true,
  "escalationHours": 12
}
```

**Response:** `201 Created` with workflow definition object

#### Update Workflow Definition
```http
PUT /api/v1/workflows/definitions/{id}
```

**Response:** `200 OK` with updated workflow definition

#### Activate/Deactivate Workflow Definition
```http
PUT /api/v1/workflows/definitions/{id}/activate
PUT /api/v1/workflows/definitions/{id}/deactivate
```

### Workflow Instances

#### Start Workflow
```http
POST /api/v1/workflows/instances/start
```

**Request Body:**
```json
{
  "documentId": 123,
  "workflowDefinitionId": 1,
  "priority": "HIGH",
  "comments": "Urgent approval required"
}
```

**Response:** `201 Created` with workflow instance object

#### Complete Workflow
```http
PUT /api/v1/workflows/instances/{id}/complete
```

**Request Body:**
```json
{
  "completionReason": "All approvals received"
}
```

#### Cancel Workflow
```http
PUT /api/v1/workflows/instances/{id}/cancel
```

**Request Body:**
```json
{
  "cancellationReason": "Document no longer needed"
}
```

### Workflow Tasks

#### Get Tasks for User
```http
GET /api/v1/workflows/tasks/user/{userId}
```

**Parameters:**
- `activeOnly` (optional): Filter for active tasks only (default: false)
- Standard pagination parameters

#### Complete Task
```http
PUT /api/v1/workflows/tasks/{id}/complete
```

**Request Body:**
```json
{
  "action": "APPROVE",
  "comments": "Document approved with minor suggestions"
}
```

### GraphQL Workflow Operations

#### Workflow Queries
```graphql
query GetWorkflowDefinitions($pagination: WorkflowPaginationInput) {
  getWorkflowDefinitions(pagination: $pagination) {
    content {
      id
      name
      description
      workflowType
      isActive
      stageCount
      estimatedCompletionHours
    }
    totalElements
    totalPages
  }
}

query GetWorkflowInstance($id: ID!) {
  getWorkflowInstance(id: $id) {
    id
    instanceName
    status
    priority
    startedDate
    dueDate
    isOverdue
    completionPercentage
    workflowDefinition {
      name
      workflowType
    }
    tasks {
      id
      taskName
      status
      assignedToUserId
      dueDate
    }
  }
}
```

## Document Templates API

### Overview
The Document Templates API enables creation, management, and usage of document templates for standardized document generation.

### Base URL
```
/api/v1/templates
```

### Template Management

#### Get All Templates
```http
GET /api/v1/templates
```

**Parameters:**
- `category` (optional): Filter by template category
- `templateType` (optional): Filter by template type
- `ownerUserId` (optional): Filter by template owner
- Standard pagination parameters

#### Get Accessible Templates
```http
GET /api/v1/templates/accessible
```

Returns templates accessible to the current user based on permissions.

#### Get Published Templates
```http
GET /api/v1/templates/published
```

#### Get Popular Templates
```http
GET /api/v1/templates/popular?minUsageCount=5
```

#### Search Templates
```http
GET /api/v1/templates/search?query=contract
```

#### Create Template
```http
POST /api/v1/templates
```

**Request Body:**
```json
{
  "name": "Contract Template",
  "description": "Standard contract template",
  "category": "CONTRACTS",
  "templateType": "DOCUMENT",
  "templateFormat": "DOCX",
  "mimeType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "templateContent": "base64-encoded-content",
  "accessLevel": "DEPARTMENT"
}
```

#### Update Template
```http
PUT /api/v1/templates/{id}
```

#### Publish Template
```http
PUT /api/v1/templates/{id}/publish
```

#### Approve/Reject Template
```http
PUT /api/v1/templates/{id}/approve
PUT /api/v1/templates/{id}/reject
```

### Document Generation

#### Create Document from Template
```http
POST /api/v1/templates/{id}/create-document
```

**Request Body:**
```json
{
  "fieldValues": {
    "contractTitle": "Software License Agreement",
    "clientName": "Acme Corporation",
    "effectiveDate": "2024-01-15",
    "contractValue": "50000"
  },
  "documentName": "Acme Software License Agreement",
  "documentDescription": "Software license agreement for Acme Corp"
}
```

#### Preview Template
```http
POST /api/v1/templates/{id}/preview
```

**Request Body:**
```json
{
  "fieldValues": {
    "contractTitle": "Preview Contract",
    "clientName": "Preview Client"
  }
}
```

**Response:**
```json
{
  "content": "base64-encoded-preview-content",
  "mimeType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
}
```

### GraphQL Template Operations

#### Template Queries
```graphql
query GetAccessibleTemplates($userId: String!, $department: String) {
  getAccessibleTemplates(userId: $userId, department: $department) {
    id
    name
    description
    category
    templateType
    isPublished
    usageCount
    lastUsedDate
  }
}

mutation CreateDocumentFromTemplate($input: CreateDocumentFromTemplateInput!) {
  createDocumentFromTemplate(input: $input) {
    id
    name
    description
    mimeType
    fileSize
    createdDate
    sourceTemplate {
      name
      category
    }
  }
}
```

## Webhook & Event System API

### Overview
The Webhook & Event System API provides real-time event notifications and webhook management for system integration.

### Base URL
```
/api/v1/webhooks
```

### Webhook Endpoints

#### Get Webhook Endpoints
```http
GET /api/v1/webhooks/endpoints
```

**Parameters:**
- `active` (optional): Filter by active status
- `verified` (optional): Filter by verified status
- Standard pagination parameters

#### Create Webhook Endpoint
```http
POST /api/v1/webhooks/endpoints
```

**Request Body:**
```json
{
  "name": "Document Events Webhook",
  "description": "Webhook for document-related events",
  "url": "https://api.example.com/webhooks/dms",
  "httpMethod": "POST",
  "contentType": "application/json",
  "timeoutSeconds": 30,
  "authType": "BEARER",
  "authConfig": "{\"token\": \"your-bearer-token\"}",
  "eventTypes": ["DOCUMENT_CREATED", "DOCUMENT_UPDATED", "WORKFLOW_COMPLETED"],
  "maxRetries": 3,
  "retryDelaySeconds": 60,
  "exponentialBackoff": true,
  "rateLimitPerMinute": 60,
  "rateLimitPerHour": 1000
}
```

#### Verify Webhook Endpoint
```http
PUT /api/v1/webhooks/endpoints/{id}/verify
```

**Request Body:**
```json
{
  "verificationToken": "verification-token-received"
}
```

#### Activate/Deactivate Webhook
```http
PUT /api/v1/webhooks/endpoints/{id}/activate
PUT /api/v1/webhooks/endpoints/{id}/deactivate
```

### Event Management

#### Get System Events
```http
GET /api/v1/webhooks/events
```

**Parameters:**
- `eventType` (optional): Filter by event type
- `eventCategory` (optional): Filter by event category
- `actorUserId` (optional): Filter by actor user
- Standard pagination parameters

#### Get Recent Events
```http
GET /api/v1/webhooks/events/recent?hoursBack=24
```

#### Publish System Event
```http
POST /api/v1/webhooks/events
```

**Request Body:**
```json
{
  "eventType": "DOCUMENT_CREATED",
  "eventCategory": "DOCUMENT",
  "eventName": "Document Created",
  "sourceEntityType": "DOCUMENT",
  "sourceEntityId": 123,
  "eventData": {
    "documentName": "Important Contract",
    "documentType": "CONTRACT",
    "createdBy": "john.doe"
  }
}
```

### Webhook Deliveries

#### Get Webhook Deliveries
```http
GET /api/v1/webhooks/deliveries
```

#### Get Failed Deliveries
```http
GET /api/v1/webhooks/deliveries/failed
```

#### Retry Failed Delivery
```http
POST /api/v1/webhooks/deliveries/{id}/retry
```

### Statistics

#### Get Webhook Statistics
```http
GET /api/v1/webhooks/statistics
```

**Response:**
```json
{
  "totalEndpoints": 5,
  "verifiedEndpoints": 4,
  "totalSuccesses": 1250,
  "totalFailures": 23,
  "successRate": 98.2
}
```

#### Get Event Statistics
```http
GET /api/v1/webhooks/events/statistics
```

### GraphQL Event Operations

#### Event Queries
```graphql
query GetSystemEvents($pagination: EventPaginationInput) {
  getSystemEvents(pagination: $pagination) {
    content {
      id
      eventType
      eventCategory
      eventName
      eventTimestamp
      actorUserId
      processingStatus
    }
    totalElements
  }
}

query GetWebhookStatistics {
  getWebhookStatistics {
    totalEndpoints
    verifiedEndpoints
    totalSuccesses
    totalFailures
    successRate
  }
}
```

## Document Sharing API

### Overview
The Document Sharing API provides secure document sharing capabilities with configurable access controls and expiration settings.

### Base URL
```
/api/v1/documents/{documentId}/sharing
```

### Share Management

#### Create Share Link
```http
POST /api/v1/documents/{documentId}/sharing/links
```

**Request Body:**
```json
{
  "shareType": "PUBLIC_LINK",
  "accessLevel": "READ_ONLY",
  "expirationDate": "2024-02-15T23:59:59",
  "passwordProtected": true,
  "password": "secure-password",
  "allowDownload": false,
  "maxAccessCount": 10,
  "description": "Quarterly report sharing"
}
```

#### Get Share Links
```http
GET /api/v1/documents/{documentId}/sharing/links
```

#### Revoke Share Link
```http
DELETE /api/v1/documents/{documentId}/sharing/links/{linkId}
```

#### Share with Users
```http
POST /api/v1/documents/{documentId}/sharing/users
```

**Request Body:**
```json
{
  "userIds": ["user1", "user2"],
  "accessLevel": "READ_WRITE",
  "expirationDate": "2024-02-15T23:59:59",
  "notifyUsers": true,
  "message": "Please review this document"
}
```

## Error Handling

### HTTP Status Codes

The API uses standard HTTP status codes to indicate the success or failure of requests:

#### Success Codes
- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **204 No Content**: Request successful, no content to return

#### Client Error Codes
- **400 Bad Request**: Invalid request syntax or parameters
- **401 Unauthorized**: Authentication required or invalid credentials
- **403 Forbidden**: Access denied - insufficient permissions
- **404 Not Found**: Requested resource not found
- **409 Conflict**: Request conflicts with current state (e.g., duplicate file)
- **413 Payload Too Large**: File size exceeds maximum allowed
- **415 Unsupported Media Type**: File type not supported
- **422 Unprocessable Entity**: Request valid but contains semantic errors
- **429 Too Many Requests**: Rate limit exceeded

#### Server Error Codes
- **500 Internal Server Error**: Unexpected server error
- **502 Bad Gateway**: Invalid response from upstream server
- **503 Service Unavailable**: Service temporarily unavailable
- **504 Gateway Timeout**: Request timeout

### Custom Error Codes

The API returns custom error codes for application-specific errors:

#### Document Errors
- **DOCUMENT_NOT_FOUND**: Document with specified ID not found
- **DUPLICATE_FILE_EXCEPTION**: File with same name already exists
- **FILE_SIZE_EXCEEDED**: File size exceeds maximum allowed limit
- **INVALID_FILE_TYPE**: File type not supported
- **DOCUMENT_LOCKED**: Document is locked for editing

#### Authentication & Authorization Errors
- **UNAUTHORIZED**: Invalid or expired JWT token
- **INSUFFICIENT_PERMISSIONS**: User lacks required permissions
- **TOKEN_EXPIRED**: JWT token has expired
- **INVALID_CREDENTIALS**: Invalid username or password

#### Storage Errors
- **STORAGE_UNAVAILABLE**: Storage provider temporarily unavailable
- **STORAGE_QUOTA_EXCEEDED**: Storage quota exceeded
- **STORAGE_ACCESS_DENIED**: Access denied to storage location

#### Validation Errors
- **VALIDATION_ERROR**: Request validation failed
- **MISSING_REQUIRED_FIELD**: Required field missing from request
- **INVALID_FIELD_VALUE**: Field contains invalid value

### Error Response Format

All errors follow a consistent JSON format:

```json
{
  "error": {
    "code": "DOCUMENT_NOT_FOUND",
    "message": "Document with ID '123' not found",
    "details": {
      "documentId": "123",
      "timestamp": "2024-12-21T10:30:00Z",
      "correlationId": "abc-123-def"
    }
  }
}
```

### GraphQL Error Format

GraphQL errors are returned in the standard GraphQL error format:

```json
{
  "data": null,
  "errors": [
    {
      "message": "Document not found",
      "locations": [{"line": 2, "column": 3}],
      "path": ["getDocumentById"],
      "extensions": {
        "code": "DOCUMENT_NOT_FOUND",
        "documentId": "123",
        "correlationId": "abc-123-def"
      }
    }
  ]
}
```

## Rate Limiting

The API implements rate limiting to ensure fair usage and system stability.

### Rate Limit Headers

All responses include rate limiting headers:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640095200
X-RateLimit-Window: 3600
```

### Rate Limit Policies

- **Standard Users**: 1000 requests per hour
- **Premium Users**: 5000 requests per hour
- **Admin Users**: 10000 requests per hour
- **File Upload**: 100 uploads per hour (all users)
- **Search Operations**: 500 searches per hour (all users)

### Rate Limit Exceeded Response

When rate limit is exceeded, the API returns:

```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Try again in 3600 seconds.",
    "details": {
      "limit": 1000,
      "window": 3600,
      "retryAfter": 3600
    }
  }
}
```

## GraphQL API

The DMS provides a comprehensive GraphQL API that supports all document management operations. GraphQL offers several advantages:

- **Single Endpoint**: All operations through `/graphql`
- **Flexible Queries**: Request exactly the data you need
- **Type Safety**: Strong typing with introspection support
- **Real-time**: Subscription support for live updates

### GraphQL Endpoint
- **URL**: `http://localhost:9092/graphql`
- **Interactive Interface**: `http://localhost:9092/graphiql`

### Core GraphQL Operations

#### Mutations
- `uploadDocument` - Upload new documents
- `uploadDocumentFromPath` - Upload from server path
- `uploadDocumentNewVersion` - Create new version
- `deleteDocument` - Soft delete documents
- `updateDocumentMetadata` - Update metadata
- `assignDocumentPermission` - Manage permissions

#### Queries
- `getDocumentById` - Retrieve single document
- `searchDocuments` - Search and filter documents
- `listDocumentVersions` - Get version history
- `getAuditLogsByFilter` - Retrieve audit logs
- `downloadDocument` - Get download URL

### GraphQL Schema Introspection
```graphql
{
  __schema {
    types {
      name
      kind
      description
    }
  }
}
```

## REST API

The DMS also provides REST API endpoints for traditional HTTP operations:

### REST Base URL
- **Base URL**: `http://localhost:9092/api/v1`

### Core REST Endpoints

#### Document Operations
- `POST /documents` - Upload document
- `GET /documents/{id}` - Get document by ID
- `GET /documents/{id}/download` - Download document
- `DELETE /documents/{id}` - Delete document
- `PUT /documents/{id}/metadata` - Update metadata

#### Search Operations
- `GET /documents/search` - Search documents
- `GET /documents/search/advanced` - Advanced search

#### Administrative Operations
- `GET /admin/health` - System health check
- `GET /admin/metrics` - System metrics
- `POST /admin/audit/export` - Export audit logs

### REST Response Format
```json
{
  "success": true,
  "data": {
    "id": "doc-123",
    "name": "example.pdf",
    "status": "ACTIVE"
  },
  "timestamp": "2024-06-21T10:30:00Z",
  "correlationId": "req-456"
}
```

## Examples & Use Cases

### Complete Document Upload Workflow

```bash
# 1. Generate authentication token
curl -X POST http://localhost:9092/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation GenerateTestToken($input: JwtTokenRequest!) { generateTestToken(input: $input) { token expiresAt } }",
    "variables": {
      "input": {
        "userId": "<EMAIL>",
        "roles": ["USER"],
        "permissions": ["READ", "WRITE"],
        "expirationMinutes": 60
      }
    }
  }'

# 2. Upload document with metadata
curl -X POST http://localhost:9092/graphql \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -F 'operations={"query":"mutation UploadDocument($input: UploadDocumentInput!) { uploadDocument(input: $input) { id name version status } }","variables":{"input":{"name":"Financial Report Q4 2024","description":"Quarterly financial report","keywords":["finance","report","Q4","2024"],"storageProvider":"S3","classificationMetadata":{"module":"Finance","confidentialityLevel":"Confidential","dataClassification":"Financial","securityLevel":"High"},"ownershipMetadata":{"owner":"<EMAIL>","department":"Finance","businessUnit":"Corporate","retentionPeriod":"7 years"},"complianceMetadata":{"complianceStandard":"SOX","auditRelevance":"High","controlId":"FIN-001"}}}}' \
  -F 'map={"0":["variables.input.file"]}' \
  -F '0=@/path/to/financial-report.pdf'

# 3. Search for uploaded document
curl -X POST http://localhost:9092/graphql \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query SearchDocuments($filter: DocumentSearchInput, $pagination: PaginationInput) { searchDocuments(filter: $filter, pagination: $pagination) { content { id name version status createdDate } totalElements } }",
    "variables": {
      "filter": {
        "name": "Financial Report",
        "module": "Finance",
        "confidentialityLevel": "Confidential"
      },
      "pagination": {
        "page": 0,
        "size": 10,
        "sortBy": "createdDate",
        "sortDirection": "DESC"
      }
    }
  }'
```

---

**Last Updated**: December 21, 2024
**Version**: 2.1.0
**Generated**: 2024-12-21T17:00:00Z
