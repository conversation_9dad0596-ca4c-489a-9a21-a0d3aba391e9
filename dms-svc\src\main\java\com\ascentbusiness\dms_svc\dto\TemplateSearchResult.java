package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.DocumentTemplate;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * DTO for template search results
 */
@Data
@Builder
public class TemplateSearchResult {
    private List<DocumentTemplate> templates;
    private Integer totalCount;
    private TemplateSearchFacets facets;
    private List<String> suggestions;
}
