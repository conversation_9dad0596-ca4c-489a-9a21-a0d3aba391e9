package com.ascentbusiness.dms_svc.dto;

import java.time.OffsetDateTime;

public class JwtTokenResponse {
    private String token;
    private String tokenType = "Bearer";
    private OffsetDateTime expiresAt;

    public JwtTokenResponse() {}

    public JwtTokenResponse(String token, OffsetDateTime expiresAt) {
        this.token = token;
        this.expiresAt = expiresAt;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public OffsetDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(OffsetDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
}
