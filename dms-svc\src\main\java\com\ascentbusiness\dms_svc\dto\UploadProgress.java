package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.entity.AsyncProcessingJob;
import com.ascentbusiness.dms_svc.entity.ChunkedUploadSession;
import com.ascentbusiness.dms_svc.enums.ProcessingStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * DTO for upload progress tracking.
 * Corresponds to UploadProgress GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadProgress {

    /**
     * Unique identifier for the upload operation.
     */
    private String uploadId;

    /**
     * Name of the file being uploaded.
     */
    private String fileName;

    /**
     * Total size of the file in bytes.
     */
    private Long totalSize;

    /**
     * Number of bytes uploaded so far.
     */
    private Long uploadedSize;

    /**
     * Upload progress as a percentage (0.0 to 100.0).
     */
    private Float progress;

    /**
     * Current status of the upload.
     */
    private ProcessingStatus status;

    /**
     * When the upload started.
     */
    private OffsetDateTime startedAt;

    /**
     * When the upload was last updated.
     */
    private OffsetDateTime lastUpdatedAt;

    /**
     * Estimated time remaining in milliseconds.
     */
    private Long estimatedTimeRemaining;

    /**
     * Transfer rate in bytes per second.
     */
    private Float transferRate;

    /**
     * Error message if upload failed.
     */
    private String errorMessage;

    /**
     * Create UploadProgress from AsyncProcessingJob.
     */
    public static UploadProgress fromAsyncJob(AsyncProcessingJob job) {
        return UploadProgress.builder()
                .uploadId(job.getJobId())
                .fileName(job.getFileName())
                .totalSize(job.getFileSize())
                .uploadedSize(job.getFileSize()) // For async jobs, file is already uploaded
                .progress(job.getProgressAsFloat())
                .status(job.getStatus())
                .startedAt(job.getCreatedDate() != null ? job.getCreatedDate().atOffset(java.time.ZoneOffset.UTC) : null)
                .lastUpdatedAt(job.getLastModifiedDate() != null ? job.getLastModifiedDate().atOffset(java.time.ZoneOffset.UTC) : null)
                .errorMessage(job.getErrorMessage())
                .build();
    }

    /**
     * Create UploadProgress from ChunkedUploadSession.
     */
    public static UploadProgress fromChunkedSession(ChunkedUploadSession session) {
        long uploadedSize = (long) session.getReceivedChunks() * session.getChunkSize();
        float progress = session.getTotalChunks() > 0 ?
            (float) session.getReceivedChunks() / session.getTotalChunks() * 100.0f : 0.0f;

        return UploadProgress.builder()
                .uploadId(session.getSessionId())
                .fileName(session.getFileName())
                .totalSize(session.getTotalSize())
                .uploadedSize(uploadedSize)
                .progress(progress)
                .status(ProcessingStatus.valueOf(session.getStatus()))
                .startedAt(session.getCreatedDate() != null ? session.getCreatedDate().atOffset(java.time.ZoneOffset.UTC) : null)
                .lastUpdatedAt(session.getLastActivityAt() != null ? session.getLastActivityAt().atOffset(java.time.ZoneOffset.UTC) : null)
                .errorMessage(session.getErrorMessage())
                .build();
    }
}
