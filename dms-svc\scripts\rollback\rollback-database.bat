@echo off
REM ============================================================================
REM DMS Database Rollback Script
REM ============================================================================
REM This script performs automated rollback of database migrations using
REM Liquibase rollback capabilities and database backup restoration.
REM
REM Features:
REM - Liquibase migration rollback
REM - Database backup and restore
REM - Data integrity validation
REM - Rollback verification
REM - Emergency database recovery
REM
REM Prerequisites:
REM - MySQL client tools installed
REM - Database connection credentials
REM - Liquibase changelog files
REM - Database backup files
REM ============================================================================

setlocal enabledelayedexpansion

REM Set script variables
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..\..
set BACKUP_DIR=%PROJECT_ROOT%\backups\database
set LOG_FILE=%PROJECT_ROOT%\logs\rollback-database.log
set LIQUIBASE_CHANGELOG=%PROJECT_ROOT%\src\main\resources\db\changelog\db.changelog-master.xml
set TIMESTAMP=%DATE:~-4,4%%DATE:~-10,2%%DATE:~-7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

REM Database connection settings (from application.properties)
set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=dms_db
set DB_USER=dms_user
set DB_PASSWORD=dms_password

echo ============================================================================
echo DMS Database Rollback Script
echo ============================================================================
echo Timestamp: %TIMESTAMP%
echo Project Root: %PROJECT_ROOT%
echo Backup Directory: %BACKUP_DIR%
echo Log File: %LOG_FILE%
echo Database: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo ============================================================================

REM Parse command line arguments
set ROLLBACK_TYPE=%1
set ROLLBACK_TARGET=%2
set CONFIRM_FLAG=%3

if "%ROLLBACK_TYPE%"=="" (
    echo ERROR: Rollback type not specified
    goto :show_help
)

REM Create necessary directories
if not exist "%PROJECT_ROOT%\logs" mkdir "%PROJECT_ROOT%\logs"
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

REM Initialize logging
echo [%TIME%] Starting database rollback: %ROLLBACK_TYPE% %ROLLBACK_TARGET% > "%LOG_FILE%"
echo [%TIME%] Starting database rollback: %ROLLBACK_TYPE% %ROLLBACK_TARGET%

REM Route to appropriate rollback method
if "%ROLLBACK_TYPE%"=="steps" goto :rollback_steps
if "%ROLLBACK_TYPE%"=="tag" goto :rollback_tag
if "%ROLLBACK_TYPE%"=="date" goto :rollback_date
if "%ROLLBACK_TYPE%"=="backup" goto :restore_backup
if "%ROLLBACK_TYPE%"=="emergency" goto :emergency_restore
goto :show_help

:rollback_steps
if "%ROLLBACK_TARGET%"=="" (
    echo ERROR: Number of steps not specified
    echo Usage: %~nx0 steps ^<number^> [--confirm]
    goto :error_exit
)

echo [%TIME%] Rolling back %ROLLBACK_TARGET% migration steps...
echo [%TIME%] Rolling back %ROLLBACK_TARGET% migration steps... >> "%LOG_FILE%"

REM Safety confirmation
if not "%CONFIRM_FLAG%"=="--confirm" (
    echo.
    echo WARNING: This will rollback %ROLLBACK_TARGET% database migration steps
    echo This operation may result in data loss and cannot be easily undone.
    echo.
    set /p CONFIRM="Are you sure you want to proceed? (yes/no): "
    if not "!CONFIRM!"=="yes" (
        echo [INFO] Database rollback cancelled by user
        echo [INFO] Database rollback cancelled by user >> "%LOG_FILE%"
        goto :end
    )
)

REM Create database backup before rollback
call :create_backup

REM Test database connectivity
call :test_connection
if %ERRORLEVEL% neq 0 goto :error_exit

REM Execute Liquibase rollback
echo [%TIME%] Executing Liquibase rollback...
echo [%TIME%] Executing Liquibase rollback... >> "%LOG_FILE%"

cd /d "%PROJECT_ROOT%"
mvn liquibase:rollback -Dliquibase.rollbackCount=%ROLLBACK_TARGET% >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Liquibase rollback failed
    echo [ERROR] Liquibase rollback failed >> "%LOG_FILE%"
    goto :rollback_failed
)

echo [SUCCESS] Database rollback completed successfully
echo [SUCCESS] Database rollback completed successfully >> "%LOG_FILE%"
goto :validate_rollback

:rollback_tag
if "%ROLLBACK_TARGET%"=="" (
    echo ERROR: Tag not specified
    echo Usage: %~nx0 tag ^<tag_name^> [--confirm]
    goto :error_exit
)

echo [%TIME%] Rolling back to tag: %ROLLBACK_TARGET%...
echo [%TIME%] Rolling back to tag: %ROLLBACK_TARGET%... >> "%LOG_FILE%"

REM Safety confirmation
if not "%CONFIRM_FLAG%"=="--confirm" (
    echo.
    echo WARNING: This will rollback database to tag: %ROLLBACK_TARGET%
    echo This operation may result in data loss and cannot be easily undone.
    echo.
    set /p CONFIRM="Are you sure you want to proceed? (yes/no): "
    if not "!CONFIRM!"=="yes" (
        echo [INFO] Database rollback cancelled by user
        echo [INFO] Database rollback cancelled by user >> "%LOG_FILE%"
        goto :end
    )
)

REM Create database backup before rollback
call :create_backup

REM Test database connectivity
call :test_connection
if %ERRORLEVEL% neq 0 goto :error_exit

REM Execute Liquibase rollback to tag
echo [%TIME%] Executing Liquibase rollback to tag...
echo [%TIME%] Executing Liquibase rollback to tag... >> "%LOG_FILE%"

cd /d "%PROJECT_ROOT%"
mvn liquibase:rollback -Dliquibase.rollbackTag=%ROLLBACK_TARGET% >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Liquibase rollback to tag failed
    echo [ERROR] Liquibase rollback to tag failed >> "%LOG_FILE%"
    goto :rollback_failed
)

echo [SUCCESS] Database rollback to tag completed successfully
echo [SUCCESS] Database rollback to tag completed successfully >> "%LOG_FILE%"
goto :validate_rollback

:rollback_date
if "%ROLLBACK_TARGET%"=="" (
    echo ERROR: Date not specified
    echo Usage: %~nx0 date ^<YYYY-MM-DD^> [--confirm]
    goto :error_exit
)

echo [%TIME%] Rolling back to date: %ROLLBACK_TARGET%...
echo [%TIME%] Rolling back to date: %ROLLBACK_TARGET%... >> "%LOG_FILE%"

REM Safety confirmation
if not "%CONFIRM_FLAG%"=="--confirm" (
    echo.
    echo WARNING: This will rollback database to date: %ROLLBACK_TARGET%
    echo This operation may result in data loss and cannot be easily undone.
    echo.
    set /p CONFIRM="Are you sure you want to proceed? (yes/no): "
    if not "!CONFIRM!"=="yes" (
        echo [INFO] Database rollback cancelled by user
        echo [INFO] Database rollback cancelled by user >> "%LOG_FILE%"
        goto :end
    )
)

REM Create database backup before rollback
call :create_backup

REM Test database connectivity
call :test_connection
if %ERRORLEVEL% neq 0 goto :error_exit

REM Execute Liquibase rollback to date
echo [%TIME%] Executing Liquibase rollback to date...
echo [%TIME%] Executing Liquibase rollback to date... >> "%LOG_FILE%"

cd /d "%PROJECT_ROOT%"
mvn liquibase:rollback -Dliquibase.rollbackDate=%ROLLBACK_TARGET% >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Liquibase rollback to date failed
    echo [ERROR] Liquibase rollback to date failed >> "%LOG_FILE%"
    goto :rollback_failed
)

echo [SUCCESS] Database rollback to date completed successfully
echo [SUCCESS] Database rollback to date completed successfully >> "%LOG_FILE%"
goto :validate_rollback

:restore_backup
if "%ROLLBACK_TARGET%"=="" (
    echo ERROR: Backup file not specified
    echo Usage: %~nx0 backup ^<backup_file^> [--confirm]
    goto :error_exit
)

set BACKUP_FILE=%BACKUP_DIR%\%ROLLBACK_TARGET%
if not exist "%BACKUP_FILE%" (
    echo ERROR: Backup file not found: %BACKUP_FILE%
    echo ERROR: Backup file not found: %BACKUP_FILE% >> "%LOG_FILE%"
    goto :error_exit
)

echo [%TIME%] Restoring from backup: %ROLLBACK_TARGET%...
echo [%TIME%] Restoring from backup: %ROLLBACK_TARGET%... >> "%LOG_FILE%"

REM Safety confirmation
if not "%CONFIRM_FLAG%"=="--confirm" (
    echo.
    echo WARNING: This will restore database from backup: %ROLLBACK_TARGET%
    echo This will completely replace the current database content.
    echo.
    set /p CONFIRM="Are you sure you want to proceed? (yes/no): "
    if not "!CONFIRM!"=="yes" (
        echo [INFO] Database restore cancelled by user
        echo [INFO] Database restore cancelled by user >> "%LOG_FILE%"
        goto :end
    )
)

REM Create current database backup
call :create_backup

REM Test database connectivity
call :test_connection
if %ERRORLEVEL% neq 0 goto :error_exit

REM Drop and recreate database
echo [%TIME%] Dropping and recreating database...
echo [%TIME%] Dropping and recreating database... >> "%LOG_FILE%"

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "DROP DATABASE IF EXISTS %DB_NAME%;" >> "%LOG_FILE%" 2>&1
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE %DB_NAME%;" >> "%LOG_FILE%" 2>&1

REM Restore from backup
echo [%TIME%] Restoring database from backup...
echo [%TIME%] Restoring database from backup... >> "%LOG_FILE%"

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "%BACKUP_FILE%" >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Database restore failed
    echo [ERROR] Database restore failed >> "%LOG_FILE%"
    goto :rollback_failed
)

echo [SUCCESS] Database restore completed successfully
echo [SUCCESS] Database restore completed successfully >> "%LOG_FILE%"
goto :validate_rollback

:emergency_restore
echo [%TIME%] Performing emergency database restore...
echo [%TIME%] Performing emergency database restore... >> "%LOG_FILE%"

REM Find latest backup
set LATEST_BACKUP=
for /f "delims=" %%f in ('dir /b /o-d "%BACKUP_DIR%\*.sql" 2^>nul') do (
    if not defined LATEST_BACKUP set LATEST_BACKUP=%%f
)

if not defined LATEST_BACKUP (
    echo [ERROR] No backup files found for emergency restore
    echo [ERROR] No backup files found for emergency restore >> "%LOG_FILE%"
    goto :error_exit
)

echo [INFO] Using latest backup: %LATEST_BACKUP%
echo [INFO] Using latest backup: %LATEST_BACKUP% >> "%LOG_FILE%"

set ROLLBACK_TARGET=%LATEST_BACKUP%
goto :restore_backup

:create_backup
echo [%TIME%] Creating database backup...
echo [%TIME%] Creating database backup... >> "%LOG_FILE%"

set BACKUP_FILE=%BACKUP_DIR%\dms_db_backup_%TIMESTAMP%.sql
mysqldump -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% --single-transaction --routines --triggers %DB_NAME% > "%BACKUP_FILE%" 2>> "%LOG_FILE%"

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Database backup failed
    echo [ERROR] Database backup failed >> "%LOG_FILE%"
    exit /b 1
)

echo [INFO] Database backup created: %BACKUP_FILE%
echo [INFO] Database backup created: %BACKUP_FILE% >> "%LOG_FILE%"
exit /b 0

:test_connection
echo [%TIME%] Testing database connection...
echo [%TIME%] Testing database connection... >> "%LOG_FILE%"

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" > nul 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Database connection failed
    echo [ERROR] Database connection failed >> "%LOG_FILE%"
    exit /b 1
)

echo [INFO] Database connection successful
echo [INFO] Database connection successful >> "%LOG_FILE%"
exit /b 0

:validate_rollback
echo [%TIME%] Validating database rollback...
echo [%TIME%] Validating database rollback... >> "%LOG_FILE%"

REM Test basic database operations
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SELECT COUNT(*) FROM documents;" > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Database validation failed - documents table not accessible
    echo [ERROR] Database validation failed - documents table not accessible >> "%LOG_FILE%"
    goto :rollback_failed
)

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SELECT COUNT(*) FROM audit_logs;" > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Database validation failed - audit_logs table not accessible
    echo [ERROR] Database validation failed - audit_logs table not accessible >> "%LOG_FILE%"
    goto :rollback_failed
)

echo [SUCCESS] Database validation completed successfully
echo [SUCCESS] Database validation completed successfully >> "%LOG_FILE%"

REM Generate rollback report
call :generate_report
goto :end

:generate_report
echo [%TIME%] Generating database rollback report...
(
echo Database Rollback Report
echo ========================
echo Timestamp: %TIMESTAMP%
echo Rollback Type: %ROLLBACK_TYPE%
echo Rollback Target: %ROLLBACK_TARGET%
echo Database: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo Status: SUCCESS
echo.
echo Backup Information:
echo - Pre-rollback backup: %BACKUP_FILE%
echo - Backup directory: %BACKUP_DIR%
echo.
echo Validation Results:
echo - Database connection: PASSED
echo - Documents table: ACCESSIBLE
echo - Audit logs table: ACCESSIBLE
echo.
echo Next Steps:
echo 1. Restart application services
echo 2. Verify application functionality
echo 3. Monitor for any data inconsistencies
echo 4. Update incident documentation
) > "%PROJECT_ROOT%\logs\database-rollback-report-%TIMESTAMP%.txt"

echo [INFO] Database rollback report generated
echo [INFO] Database rollback report generated >> "%LOG_FILE%"
exit /b 0

:rollback_failed
echo [ERROR] Database rollback failed!
echo [ERROR] Database rollback failed! >> "%LOG_FILE%"
echo [ERROR] Manual intervention required. Check log file: %LOG_FILE%
goto :error_exit

:show_help
echo.
echo DMS Database Rollback Script
echo.
echo Usage: %~nx0 ^<type^> ^<target^> [--confirm]
echo.
echo Rollback Types:
echo   steps ^<number^>     - Rollback specified number of migration steps
echo   tag ^<tag_name^>     - Rollback to specific Liquibase tag
echo   date ^<YYYY-MM-DD^>  - Rollback to specific date
echo   backup ^<filename^>  - Restore from specific backup file
echo   emergency           - Emergency restore from latest backup
echo.
echo Examples:
echo   %~nx0 steps 3 --confirm
echo   %~nx0 tag v1.2.3 --confirm
echo   %~nx0 date 2024-12-01 --confirm
echo   %~nx0 backup dms_db_backup_20241201.sql --confirm
echo   %~nx0 emergency --confirm
echo.
echo Available Backups:
if exist "%BACKUP_DIR%" (
    dir /b "%BACKUP_DIR%\*.sql" 2>nul
)
echo.
goto :end

:error_exit
echo [%TIME%] Script execution failed. Check log file: %LOG_FILE%
exit /b 1

:end
echo [%TIME%] Database rollback script completed.
endlocal
if not "%CONFIRM_FLAG%"=="--confirm" pause
