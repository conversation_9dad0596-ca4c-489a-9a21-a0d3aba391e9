#!/bin/bash
# Local Development Environment Setup Script for Linux/Mac
# This script sets up secure environment variables for local development

echo "Setting up DMS Service Local Development Environment..."
echo

# Database Configuration
export DB_URL="***********************************************************************************************"
export DB_USERNAME="root"
export DB_PASSWORD="root"

# JWT Configuration - Generate a secure random secret for local development
# You can generate a new secret using: openssl rand -base64 64
export JWT_SECRET="localDevSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ"
export JWT_EXPIRATION="86400000"

# CORS Configuration - Allow local development origins
export CORS_ALLOWED_ORIGINS="http://localhost:3000,http://localhost:8080,http://localhost:9093"

# GraphiQL Configuration
export GRAPHIQL_ENABLED="true"

# Storage Configuration (leave empty for local development)
export S3_BUCKET_NAME=""
export S3_REGION=""
export S3_ACCESS_KEY=""
export S3_SECRET_KEY=""
export S3_ENDPOINT=""

# SharePoint Configuration (leave empty for local development)
export SHAREPOINT_CLIENT_ID=""
export SHAREPOINT_CLIENT_SECRET=""
export SHAREPOINT_TENANT_ID=""

# Elasticsearch Configuration (disabled for local development)
export ELASTICSEARCH_ENABLED="false"
export ELASTICSEARCH_HOST="localhost"
export ELASTICSEARCH_PORT="9200"
export ELASTICSEARCH_USERNAME=""
export ELASTICSEARCH_PASSWORD=""
export ELASTICSEARCH_PROTOCOL="http"

echo "Environment variables set for local development."
echo
echo "To run the application with these settings:"
echo "  mvn spring-boot:run -Dspring.profiles.active=local"
echo
echo "To make these variables persistent, add them to your shell profile:"
echo "  ~/.bashrc (for Bash)"
echo "  ~/.zshrc (for Zsh)"
echo "  ~/.profile (for general shell)"
echo
echo "For production deployment, use different values and never commit secrets to version control!"
echo

# Make the script executable
chmod +x "$0"
