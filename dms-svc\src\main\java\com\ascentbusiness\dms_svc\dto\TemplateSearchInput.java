package com.ascentbusiness.dms_svc.dto;

import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * DTO for template search input matching GraphQL schema
 */
@Data
public class TemplateSearchInput {
    private String query;
    private List<String> categories;
    private List<String> templateTypes;
    private List<String> approvalStatuses;
    private List<String> accessLevels;
    private List<String> ownerUserIds;
    private List<String> departments;
    private Boolean isActive;
    private Boolean isPublic;
    private OffsetDateTime dateFrom;
    private OffsetDateTime dateTo;
    private Integer minUsageCount;
    private Integer maxUsageCount;
    private String sortBy;
    private String sortDirection;
    private PaginationInput pagination;
}
