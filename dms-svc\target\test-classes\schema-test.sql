-- Test database schema for compliance framework integration tests
-- This file is used by @DataJpaTest to create the database schema

-- Create compliance_frameworks table
CREATE TABLE IF NOT EXISTS compliance_frameworks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    framework_type VARCHAR(50) NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    effective_date TIMESTAMP,
    expiry_date TIMESTAMP,
    authority VARCHAR(255),
    website_url VARCHAR(500),
    implementation_notes TEXT,
    notification_requirements TEXT,
    penalty_information TEXT,
    requires_data_subject_rights BOOLEAN DEFAULT FALSE,
    requires_consent_management BOOLEAN DEFAULT FALSE,
    requires_data_localization BOOLEAN DEFAULT FALSE,
    requires_encryption BOOLEAN DEFAULT FALSE,
    requires_audit_trail BOOLEAN DEFAULT FALSE,
    max_retention_days INTEGER,
    priority INTEGER DEFAULT 0,
    created_date TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMES<PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    last_modified_by VA<PERSON><PERSON>R(255)
);

-- Create compliance_classifications table
CREATE TABLE IF NOT EXISTS compliance_classifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    classification_level VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    effective_date TIMESTAMP,
    expiry_date TIMESTAMP,
    color_code VARCHAR(7),
    icon VARCHAR(100),
    requires_approval BOOLEAN DEFAULT FALSE,
    approval_role VARCHAR(100),
    requires_encryption BOOLEAN DEFAULT FALSE,
    requires_audit_logging BOOLEAN DEFAULT FALSE,
    max_retention_days INTEGER,
    access_restrictions TEXT,
    handling_instructions TEXT,
    disposal_instructions TEXT,
    priority INTEGER DEFAULT 0,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255)
);

-- Create documents table
CREATE TABLE IF NOT EXISTS documents (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255),
    description TEXT,
    mime_type VARCHAR(100),
    file_size BIGINT,
    storage_provider VARCHAR(50),
    storage_path VARCHAR(1000),
    storage_region VARCHAR(50),
    creator_user_id VARCHAR(255),
    status VARCHAR(50),
    version INTEGER DEFAULT 1,
    tags TEXT,
    file_content LONGBLOB,
    creator_roles TEXT,
    creator_permissions TEXT,
    last_modifier_roles TEXT,
    last_modifier_permissions TEXT,
    parent_document_id BIGINT,
    compliance_classification_level VARCHAR(50),
    requires_consent BOOLEAN DEFAULT FALSE,
    consent_reference VARCHAR(255),
    is_anonymized BOOLEAN DEFAULT FALSE,
    is_pseudonymized BOOLEAN DEFAULT FALSE,
    encryption_status VARCHAR(50),
    compliance_notes TEXT,
    retention_policy_id BIGINT,
    retention_expiry_date TIMESTAMP,
    legal_hold_status VARCHAR(20) DEFAULT 'NONE',
    disposition_status VARCHAR(20) DEFAULT 'ACTIVE',
    legal_hold_reason TEXT,
    legal_hold_applied_date TIMESTAMP,
    legal_hold_applied_by VARCHAR(100),
    disposition_review_date TIMESTAMP,
    disposition_notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (parent_document_id) REFERENCES documents(id)
);

-- Create document_compliance_mappings table
CREATE TABLE IF NOT EXISTS document_compliance_mappings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    compliance_classification_id BIGINT NOT NULL,
    storage_region VARCHAR(50),
    classification_reason TEXT,
    classified_by VARCHAR(255),
    classification_date TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    compliance_status VARCHAR(50),
    compliance_notes TEXT,
    violation_count INTEGER DEFAULT 0,
    last_validation_date TIMESTAMP,
    next_review_date TIMESTAMP,
    access_log_retention_days INTEGER,
    anonymization_method VARCHAR(100),
    encryption_algorithm VARCHAR(100),
    encryption_status VARCHAR(50),
    is_anonymized BOOLEAN DEFAULT FALSE,
    is_pseudonymized BOOLEAN DEFAULT FALSE,
    requires_consent BOOLEAN DEFAULT FALSE,
    consent_reference VARCHAR(255),
    last_compliance_check TIMESTAMP,
    last_violation_date TIMESTAMP,
    review_date TIMESTAMP,
    declassification_date TIMESTAMP,
    special_handling_instructions TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (document_id) REFERENCES documents(id),
    FOREIGN KEY (compliance_classification_id) REFERENCES compliance_classifications(id)
);

-- Create regulation_mappings table
CREATE TABLE IF NOT EXISTS regulation_mappings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    compliance_framework_id BIGINT NOT NULL,
    regulation_reference VARCHAR(255),
    compliance_requirement TEXT,
    control_objective TEXT,
    compliance_status VARCHAR(50),
    compliance_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_mandatory BOOLEAN DEFAULT FALSE,
    priority INTEGER DEFAULT 0,
    risk_level VARCHAR(50),
    responsible_party VARCHAR(255),
    review_frequency_months INTEGER,
    next_review_date TIMESTAMP,
    last_review_date TIMESTAMP,
    remediation_deadline TIMESTAMP,
    remediation_plan TEXT,
    evidence_requirements TEXT,
    testing_procedures TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (document_id) REFERENCES documents(id),
    FOREIGN KEY (compliance_framework_id) REFERENCES compliance_frameworks(id)
);

-- Create compliance_violations table
CREATE TABLE IF NOT EXISTS compliance_violations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_compliance_mapping_id BIGINT NOT NULL,
    violation_type VARCHAR(100) NOT NULL,
    severity VARCHAR(50) NOT NULL,
    description TEXT,
    detected_by VARCHAR(255),
    detection_method VARCHAR(100),
    violation_date TIMESTAMP,
    user_id VARCHAR(255),
    regulation_violated VARCHAR(255),
    control_failed VARCHAR(255),
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by VARCHAR(255),
    resolved_date TIMESTAMP,
    resolution_notes TEXT,
    requires_notification BOOLEAN DEFAULT FALSE,
    requires_regulatory_report BOOLEAN DEFAULT FALSE,
    remediation_deadline TIMESTAMP,
    recurrence_count INTEGER DEFAULT 0,
    action_attempted VARCHAR(255),
    actual_fine_amount DECIMAL(15,2),
    business_impact TEXT,
    correlation_id VARCHAR(100),
    ip_address VARCHAR(45),
    lessons_learned TEXT,
    notification_recipient VARCHAR(255),
    notification_sent_date TIMESTAMP,
    potential_fine_amount DECIMAL(15,2),
    previous_violation_id BIGINT,
    regulatory_authority VARCHAR(255),
    regulatory_report_sent_date TIMESTAMP,
    remediation_action TEXT,
    user_agent TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (document_compliance_mapping_id) REFERENCES document_compliance_mappings(id),
    FOREIGN KEY (previous_violation_id) REFERENCES compliance_violations(id)
);

-- Create junction tables for many-to-many relationships

-- Compliance framework regions
CREATE TABLE IF NOT EXISTS compliance_framework_regions (
    framework_id BIGINT NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (framework_id, region),
    FOREIGN KEY (framework_id) REFERENCES compliance_frameworks(id)
);

-- Classification data subjects
CREATE TABLE IF NOT EXISTS classification_data_subjects (
    classification_id BIGINT NOT NULL,
    data_subject_category VARCHAR(50) NOT NULL,
    PRIMARY KEY (classification_id, data_subject_category),
    FOREIGN KEY (classification_id) REFERENCES compliance_classifications(id)
);

-- Classification allowed regions
CREATE TABLE IF NOT EXISTS classification_regions (
    classification_id BIGINT NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (classification_id, region),
    FOREIGN KEY (classification_id) REFERENCES compliance_classifications(id)
);

-- Document data subject categories
CREATE TABLE IF NOT EXISTS document_data_subject_categories (
    document_id BIGINT NOT NULL,
    data_subject_category VARCHAR(50) NOT NULL,
    PRIMARY KEY (document_id, data_subject_category),
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Document compliance mapping data subjects
CREATE TABLE IF NOT EXISTS document_compliance_mapping_data_subjects (
    document_compliance_mapping_id BIGINT NOT NULL,
    data_subject_category VARCHAR(50) NOT NULL,
    PRIMARY KEY (document_compliance_mapping_id, data_subject_category),
    FOREIGN KEY (document_compliance_mapping_id) REFERENCES document_compliance_mappings(id)
);

-- Document geographic restrictions
CREATE TABLE IF NOT EXISTS document_geographic_restrictions (
    document_id BIGINT NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (document_id, region),
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Enhanced audit logs table with GRC fields
CREATE TABLE IF NOT EXISTS audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT,
    user_id VARCHAR(100) NOT NULL,
    action VARCHAR(20) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    correlation_id VARCHAR(100),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    event_type VARCHAR(50) DEFAULT 'DOCUMENT_OPERATION',
    event_category VARCHAR(50) DEFAULT 'DOCUMENT_LIFECYCLE',
    compliance_framework_id BIGINT,
    risk_level VARCHAR(20) DEFAULT 'LOW',
    data_subject_category VARCHAR(50),
    geographic_region VARCHAR(50),
    regulation_reference VARCHAR(255),
    business_impact VARCHAR(50),
    technical_details TEXT,
    before_state TEXT,
    after_state TEXT,
    session_id VARCHAR(100),
    request_id VARCHAR(100),
    client_info TEXT,
    hash_value VARCHAR(128),
    previous_hash VARCHAR(128),
    chain_sequence BIGINT,
    digital_signature TEXT,
    signature_algorithm VARCHAR(50),
    signature_timestamp TIMESTAMP,
    verification_status VARCHAR(20) DEFAULT 'PENDING',
    is_tampered BOOLEAN DEFAULT FALSE,
    tamper_detection_date TIMESTAMP,
    retention_period_days INTEGER,
    archive_date TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE,
    export_count INTEGER DEFAULT 0,
    last_export_date TIMESTAMP
);

-- Document permissions table
CREATE TABLE IF NOT EXISTS document_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    user_id VARCHAR(100),
    role_name VARCHAR(100),
    permission_type VARCHAR(20) NOT NULL,
    granted_by VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Document versions table
CREATE TABLE IF NOT EXISTS document_versions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    version_number INTEGER NOT NULL,
    version_notes TEXT,
    file_size BIGINT,
    mime_type VARCHAR(100),
    storage_path VARCHAR(1000),
    is_current BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Document classification metadata table
CREATE TABLE IF NOT EXISTS document_classification_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL UNIQUE,
    classification_level VARCHAR(50),
    classification_reason TEXT,
    classification_date TIMESTAMP,
    classified_by VARCHAR(100),
    review_date TIMESTAMP,
    declassification_date TIMESTAMP,
    handling_instructions TEXT,
    marking_requirements TEXT,
    access_control_list TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Document ownership metadata table
CREATE TABLE IF NOT EXISTS document_ownership_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL UNIQUE,
    owner_user_id VARCHAR(100),
    owner_department VARCHAR(100),
    business_owner VARCHAR(100),
    technical_owner VARCHAR(100),
    data_steward VARCHAR(100),
    lifecycle_stage VARCHAR(50),
    business_purpose TEXT,
    data_source VARCHAR(255),
    update_frequency VARCHAR(50),
    next_review_date TIMESTAMP,
    archival_date TIMESTAMP,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Document compliance metadata table
CREATE TABLE IF NOT EXISTS document_compliance_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL UNIQUE,
    compliance_standard VARCHAR(100),
    audit_relevance BOOLEAN DEFAULT FALSE,
    linked_risks_controls TEXT,
    control_id VARCHAR(100),
    third_party_id VARCHAR(100),
    policy_id VARCHAR(100),
    regulatory_requirements TEXT,
    compliance_notes TEXT,
    last_compliance_review TIMESTAMP,
    next_compliance_review TIMESTAMP,
    compliance_status VARCHAR(50),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Retention policies table
CREATE TABLE IF NOT EXISTS retention_policies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    scope VARCHAR(100),
    retention_period INTEGER NOT NULL,
    retention_period_unit VARCHAR(10) NOT NULL,
    trigger_event VARCHAR(50),
    disposition_action VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    effective_date TIMESTAMP,
    expiry_date TIMESTAMP,
    business_justification TEXT,
    legal_basis TEXT,
    review_frequency_months INTEGER,
    notification_before_days INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255)
);

-- Retention policy assignments table
CREATE TABLE IF NOT EXISTS retention_policy_assignments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    retention_policy_id BIGINT NOT NULL,
    document_id BIGINT,
    document_type VARCHAR(100),
    department VARCHAR(100),
    assignment_reason TEXT,
    assigned_by VARCHAR(100),
    assignment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    FOREIGN KEY (retention_policy_id) REFERENCES retention_policies(id),
    FOREIGN KEY (document_id) REFERENCES documents(id)
);

-- Audit chain metadata table for cryptographic chaining
CREATE TABLE IF NOT EXISTS audit_chain_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chain_id VARCHAR(100) NOT NULL UNIQUE,
    chain_name VARCHAR(255) NOT NULL,
    description TEXT,
    hash_algorithm VARCHAR(50) NOT NULL DEFAULT 'SHA-256',
    signature_algorithm VARCHAR(50) NOT NULL DEFAULT 'RSA-2048',
    current_sequence BIGINT NOT NULL DEFAULT 0,
    genesis_hash VARCHAR(128) NOT NULL,
    last_block_hash VARCHAR(128),
    last_block_timestamp TIMESTAMP,
    total_entries BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    verification_key TEXT,
    signing_key_fingerprint VARCHAR(64),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

-- Audit exports table for tracking audit log exports
CREATE TABLE IF NOT EXISTS audit_exports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    export_id VARCHAR(100) NOT NULL UNIQUE,
    export_type VARCHAR(50) NOT NULL,
    export_format VARCHAR(20) NOT NULL,
    export_reason VARCHAR(100),
    requested_by VARCHAR(100) NOT NULL,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    filter_criteria TEXT,
    total_records BIGINT,
    file_path VARCHAR(500),
    file_size_bytes BIGINT,
    file_hash VARCHAR(128),
    export_status VARCHAR(20) DEFAULT 'PENDING',
    completion_date TIMESTAMP,
    error_message TEXT,
    digital_signature TEXT,
    signature_timestamp TIMESTAMP,
    retention_period_days INTEGER DEFAULT 2555,
    archive_date TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    last_download_date TIMESTAMP,
    correlation_id VARCHAR(100),
    compliance_framework_id BIGINT,
    regulatory_requirement VARCHAR(255),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

-- Audit verification log table
CREATE TABLE IF NOT EXISTS audit_verification_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    verification_id VARCHAR(100) NOT NULL UNIQUE,
    audit_log_id BIGINT,
    chain_id VARCHAR(100),
    verification_type VARCHAR(50) NOT NULL,
    verification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_by VARCHAR(100),
    verification_method VARCHAR(50),
    expected_hash VARCHAR(128),
    actual_hash VARCHAR(128),
    hash_match BOOLEAN,
    signature_valid BOOLEAN,
    chain_integrity_valid BOOLEAN,
    overall_status VARCHAR(20),
    anomalies_detected TEXT,
    verification_details TEXT,
    correlation_id VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Audit summary reports table
CREATE TABLE IF NOT EXISTS audit_summary_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    report_id VARCHAR(100) NOT NULL UNIQUE,
    report_type VARCHAR(50) NOT NULL,
    report_period VARCHAR(20) NOT NULL,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    generated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generated_by VARCHAR(100),
    total_events BIGINT,
    compliance_events BIGINT,
    security_events BIGINT,
    violation_events BIGINT,
    high_risk_events BIGINT,
    summary_data TEXT,
    report_file_path VARCHAR(500),
    report_file_size BIGINT,
    report_hash VARCHAR(128),
    digital_signature TEXT,
    is_published BOOLEAN DEFAULT FALSE,
    publication_date TIMESTAMP,
    retention_period_days INTEGER DEFAULT 2555,
    archive_date TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE,
    correlation_id VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Foreign key constraints are handled automatically by Hibernate
