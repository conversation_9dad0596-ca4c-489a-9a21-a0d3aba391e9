package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Individual facet count for a specific value
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FacetCount {

    /**
     * The facet value (e.g., "PDF", "2023", "Finance")
     */
    private String value;

    /**
     * Number of documents matching this facet value
     */
    private Long count;

    /**
     * Whether this facet is currently selected in the search
     */
    private Boolean selected;
}
