package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * DTO for upload trend data.
 * Corresponds to UploadTrendData GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadTrendData {

    /**
     * Date for this trend data point.
     */
    private OffsetDateTime date;

    /**
     * Number of uploads on this date.
     */
    private Long uploadCount;

    /**
     * Total size of uploads on this date in bytes.
     */
    private Long totalSize;

    /**
     * Average processing time for uploads on this date in milliseconds.
     */
    private Long averageProcessingTime;
}
