# Correlation ID Implementation Guide

## Overview

This document describes the comprehensive correlation ID implementation in the DMS service. The implementation provides end-to-end request tracing capabilities by handling correlation IDs passed by clients and propagating them throughout the application.

## Architecture

### Components

1. **CorrelationIdFilter** - Servlet filter for HTTP request/response processing
2. **CorrelationIdInterceptor** - GraphQL instrumentation for GraphQL-specific handling
3. **CorrelationIdUtil** - Utility class for correlation ID management
4. **Updated AuditService** - Enhanced to use the new correlation ID utilities

## Implementation Details

### 1. CorrelationIdFilter

**Location**: `src/main/java/com/ascentbusiness/dms_svc/filter/CorrelationIdFilter.java`

**Responsibilities**:
- Extracts correlation ID from client request headers (`X-Correlation-ID`)
- Sets correlation ID in MDC (Mapped Diagnostic Context) for logging
- Adds correlation ID and service metadata to response headers
- Cleans up MDC after request processing
- Generates fallback correlation ID if client doesn't provide one

**Key Features**:
- Configurable header name via `dms.correlation.header-name` property
- Performance optimization by skipping static resources
- Automatic MDC cleanup to prevent memory leaks
- Service metadata injection in response headers

### 2. CorrelationIdInterceptor

**Location**: `src/main/java/com/ascentbusiness/dms_svc/interceptor/CorrelationIdInterceptor.java`

**Responsibilities**:
- Handles correlation ID for GraphQL operations
- Adds GraphQL-specific response headers
- Tracks execution time for performance monitoring
- Ensures correlation ID availability during GraphQL field resolution

**Key Features**:
- Operation name extraction from GraphQL queries
- Execution time tracking
- GraphQL-specific response headers (`X-GraphQL-Operation`, `X-Execution-Time-Ms`)

### 3. CorrelationIdUtil

**Location**: `src/main/java/com/ascentbusiness/dms_svc/util/CorrelationIdUtil.java`

**Responsibilities**:
- Centralized correlation ID management
- Utility methods for getting/setting correlation ID
- Support for async operations with correlation ID context

**Key Methods**:
- `getCurrentCorrelationId()` - Get current correlation ID from MDC or request
- `setCorrelationId(String)` - Set correlation ID in MDC
- `clearCorrelationId()` - Clear correlation ID from MDC
- `executeWithCorrelationId(String, Runnable)` - Execute code with specific correlation ID

## Configuration

### Application Properties

```properties
# Correlation ID Configuration
dms.correlation.header-name=X-Correlation-ID

# Logging Configuration (includes correlation ID in logs)
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
```

### Security Configuration

The `CorrelationIdFilter` is registered in the Spring Security filter chain before the JWT authentication filter to ensure correlation ID is available throughout the request processing.

## Usage Examples

### Client Request with Correlation ID

```bash
curl -H "X-Correlation-ID: my-trace-123" \
     -H "Content-Type: application/json" \
     -d '{"query": "query { __typename }"}' \
     http://localhost:8080/graphql
```

### Response Headers

The service will return the following headers:

```
X-Correlation-ID: my-trace-123
X-Service-Name: dms-service
X-Service-Version: 1.0.0
X-GraphQL-Operation: __typename
X-Execution-Time-Ms: 45
```

### Log Output

With correlation ID in place, logs will include the correlation ID:

```
2024-06-11 21:43:05.123 [http-nio-8080-exec-1] INFO  [my-trace-123] c.a.d.service.DocumentService - Document retrieved: 123
2024-06-11 21:43:05.124 [http-nio-8080-exec-1] INFO  [my-trace-123] c.a.d.service.AuditService - Audit log created: action=VIEW, documentId=123
```

## Benefits

### 1. **End-to-End Tracing**
- Client-provided correlation IDs are preserved throughout the request lifecycle
- All logs include correlation ID for easy request tracing
- Audit logs automatically capture correlation ID

### 2. **Performance Monitoring**
- GraphQL execution time tracking
- Response headers provide performance metrics
- Service metadata for debugging

### 3. **Debugging Support**
- Easy correlation of logs across different components
- Client can verify their correlation ID was received
- Service metadata helps with troubleshooting

### 4. **Production Ready**
- Automatic fallback correlation ID generation
- Memory leak prevention with MDC cleanup
- Performance optimization for static resources

## Testing

### Unit Tests
- `CorrelationIdFilterTest` - Tests filter functionality
- Covers scenarios with/without client-provided correlation IDs
- Tests static resource filtering

### Integration Tests
- `CorrelationIdIntegrationTest` - End-to-end testing
- Verifies response headers are correctly set
- Tests GraphQL endpoint integration

## Best Practices

### For Clients
1. Always provide a unique correlation ID in the `X-Correlation-ID` header
2. Use meaningful correlation IDs (e.g., include request type, timestamp)
3. Log the correlation ID on the client side for end-to-end tracing

### For Developers
1. Use `CorrelationIdUtil.getCurrentCorrelationId()` to get correlation ID in code
2. For async operations, use `CorrelationIdUtil.executeWithCorrelationId()`
3. Include correlation ID in custom log messages when needed

### For Operations
1. Monitor correlation ID presence in logs for debugging
2. Use correlation IDs to trace requests across microservices
3. Set up log aggregation to search by correlation ID

## Future Enhancements

1. **OpenTelemetry Integration** - Add distributed tracing support
2. **Metrics Collection** - Track correlation ID usage metrics
3. **Custom Header Support** - Allow multiple correlation headers
4. **Async Propagation** - Enhanced support for async operations
