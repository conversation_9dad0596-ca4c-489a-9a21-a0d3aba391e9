# GraphQL Schema Extensions for API Version 1.1
# This file contains new fields and types introduced in version 1.1

# Extended Document type with new fields (v1.1)
extend type Document {
  # New fields added in v1.1
  contentHash: String @deprecated(reason: "Use checksumSha256 instead")
  checksumSha256: String @apiVersion(since: "1.1", description: "SHA-256 checksum of document content")
  checksumMd5: String
  lastAccessedDate: DateTime
  accessCount: Int

  # Enhanced metadata
  documentMetrics: DocumentMetrics @apiVersion(since: "1.1", description: "Document usage metrics")
  securityClassification: SecurityClassification @apiVersion(since: "1.1", description: "Security classification information")
}

# New types introduced in v1.1
type DocumentMetrics {
  totalViews: Int!
  totalDownloads: Int!
  lastViewedDate: DateTime
  lastDownloadedDate: DateTime
  averageViewDuration: Float
  popularityScore: Float
}

type SecurityClassification {
  level: SecurityLevel!
  markings: [String!]
  caveatCodes: [String!]
  classificationDate: DateTime!
  classifiedBy: String!
  declassificationDate: DateTime
  reviewDate: DateTime
}

enum SecurityLevel {
  UNCLASSIFIED
  CONFIDENTIAL
  SECRET
  TOP_SECRET
}

# Extended search capabilities (v1.1)
extend type AdvancedSearchResult {
  # New search result fields
  searchMetrics: SearchMetrics
  relatedDocuments: [Document!]
  searchSuggestions: [String!]
}

type SearchMetrics {
  totalResults: Int!
  searchTime: Float!
  indexVersion: String!
  relevanceScores: [Float!]
}

# New query operations (v1.1)
extend type Query {
  # Enhanced document operations
  getDocumentMetrics(documentId: ID!): DocumentMetrics
  getDocumentSecurityClassification(documentId: ID!): SecurityClassification
  
  # New search operations
  searchDocumentsByContent(
    content: String!
    similarity: Float = 0.8
    pagination: PaginationInput
  ): AdvancedSearchResult!
  
  getDocumentRecommendations(
    documentId: ID!
    limit: Int = 10
  ): [Document!]!
  
  # Analytics queries
  getDocumentAnalytics(
    dateRange: DateRangeInput!
    groupBy: AnalyticsGroupBy = DAY
  ): [DocumentAnalytics!]!
}

# New mutation operations (v1.1)
extend type Mutation {
  # Enhanced document operations
  updateDocumentMetrics(
    documentId: ID!
    input: DocumentMetricsInput!
  ): DocumentMetrics!
  
  setSecurityClassification(
    documentId: ID!
    input: SecurityClassificationInput!
  ): SecurityClassification!
  
  # Bulk operations
  bulkUpdateDocuments(
    documentIds: [ID!]!
    input: BulkUpdateInput!
  ): BulkUpdateResult!
  
  # Document workflow operations
  submitDocumentForReview(
    documentId: ID!
    reviewers: [String!]!
    dueDate: DateTime
  ): DocumentReview!
}

# New input types (v1.1)
input DocumentMetricsInput {
  incrementViews: Boolean = false
  incrementDownloads: Boolean = false
  viewDuration: Float
}

input SecurityClassificationInput {
  level: SecurityLevel!
  markings: [String!]
  caveatCodes: [String!]
  classifiedBy: String!
  declassificationDate: DateTime
  reviewDate: DateTime
}

input BulkUpdateInput {
  status: DocumentStatus
  tags: [String!]
  securityClassification: SecurityClassificationInput
}

input DateRangeInput {
  startDate: DateTime!
  endDate: DateTime!
}

# New result types (v1.1)
type BulkUpdateResult {
  successCount: Int!
  failureCount: Int!
  errors: [BulkUpdateError!]!
}

type BulkUpdateError {
  documentId: ID!
  error: String!
}

type DocumentReview {
  id: ID!
  documentId: ID!
  reviewers: [String!]!
  status: ReviewStatus!
  dueDate: DateTime
  createdDate: DateTime!
  completedDate: DateTime
}

type DocumentAnalytics {
  date: DateTime!
  totalDocuments: Int!
  totalViews: Int!
  totalDownloads: Int!
  newDocuments: Int!
  deletedDocuments: Int!
}

enum ReviewStatus {
  PENDING
  IN_PROGRESS
  APPROVED
  REJECTED
  CANCELLED
}

enum AnalyticsGroupBy {
  HOUR
  DAY
  WEEK
  MONTH
  YEAR
}

# Deprecated fields and types (marked for removal in v2.0)
# Note: @deprecated directive cannot be used on OBJECT types, only on fields
type DeprecatedDocumentInfo {
  id: ID! @deprecated(reason: "Use Document.id instead")
  name: String! @deprecated(reason: "Use Document.name instead")
  # ... other deprecated fields
}

# API versioning directives
directive @apiVersion(
  since: String!
  deprecated: String = ""
  removedIn: String = ""
  description: String = ""
) on FIELD_DEFINITION | OBJECT | INTERFACE | UNION | ENUM

directive @experimental(
  description: String = ""
) on FIELD_DEFINITION | OBJECT | INTERFACE | UNION | ENUM

# Note: API versioning directives have been moved to the main Document extension above to avoid duplicate field definitions
