package com.ascentbusiness.dms_svc.entity;

import com.ascentbusiness.dms_svc.converter.PIIEncryptionConverter;
import jakarta.persistence.*;
import lombok.*;

/**
 * Entity representing compliance metadata for documents.
 * Stores compliance-related information such as standards, audit relevance,
 * linked risks and controls, and third-party associations.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@Entity
@Table(name = "document_compliance_metadata")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentComplianceMetadata extends BaseEntity {

    /** Primary key for the compliance metadata record */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** The document this compliance metadata is associated with */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;

    /** Compliance standard applicable to the document (e.g., ISO 27001, HIPAA, SOX, GDPR) */
    @Column(name = "compliance_standard", length = 100)
    private String complianceStandard;

    /** Audit relevance information (e.g., Yes, No, Internal Audit 2024) */
    @Column(name = "audit_relevance", length = 200)
    private String auditRelevance;

    /** References to associated risks or controls */
    @Column(name = "linked_risks_controls", columnDefinition = "TEXT")
    private String linkedRisksControls;

    /** Control ID if document maps to a specific control */
    @Column(name = "control_id", length = 100)
    private String controlId;

    /** Third party ID if related to vendors/suppliers (encrypted for PII protection) */
    @Column(name = "third_party_id", length = 500)
    @Convert(converter = PIIEncryptionConverter.class)
    private String thirdPartyId;

    /** Policy ID if linked to a policy document */
    @Column(name = "policy_id", length = 100)
    private String policyId;
}
