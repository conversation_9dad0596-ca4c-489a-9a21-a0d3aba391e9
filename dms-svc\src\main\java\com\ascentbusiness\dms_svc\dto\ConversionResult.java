package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.ConversionMethod;
import com.ascentbusiness.dms_svc.enums.ConversionStatus;
import com.ascentbusiness.dms_svc.enums.ConversionType;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Result DTO for conversion operations from conversion-schema.graphqls.
 */
@Data
@Builder
public class ConversionResult {
    private String sessionId;
    private ConversionType conversionType;
    private String originalFileName;
    private String convertedFileName;
    private String downloadPath;
    private String downloadUrl;
    private Long fileSize;
    private ConversionStatus status;
    private Boolean success;
    private String message;
    private String errorDetails;
    private LocalDateTime startedAt;
    private LocalDateTime completedAt;
    private Long processingTimeMs;
    private ConversionMethod conversionMethod;
    private Boolean usedPandoc;
    private VirusScanResponse virusScanResponse;
    private ConversionMetadata metadata;
}
