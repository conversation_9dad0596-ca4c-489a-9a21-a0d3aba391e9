-- MySQL Initialization Script for Shared Infrastructure
-- This script creates databases and users for both DMS and Notification services

-- Create databases
CREATE DATABASE IF NOT EXISTS `dms_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `notification_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create users and grant permissions
-- DMS Service User
CREATE USER IF NOT EXISTS 'dms_user'@'%' IDENTIFIED BY 'dms_password';
GRANT ALL PRIVILEGES ON `dms_db`.* TO 'dms_user'@'%';

-- Notification Service User
CREATE USER IF NOT EXISTS 'notification_user'@'%' IDENTIFIED BY 'notification_password';
GRANT ALL PRIVILEGES ON `notification_db`.* TO 'notification_user'@'%';

-- Grant cross-service access for future integrations
-- <PERSON><PERSON> can read from notification tables for audit purposes
GRANT SELECT ON `notification_db`.* TO 'dms_user'@'%';

-- Notification service can read from D<PERSON> for document-related notifications
GRANT SELECT ON `dms_db`.* TO 'notification_user'@'%';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Display created databases
SHOW DATABASES;

-- Display users
SELECT User, Host FROM mysql.user WHERE User IN ('dms_user', 'notification_user');
