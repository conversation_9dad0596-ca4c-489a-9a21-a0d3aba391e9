package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for upload statistics.
 * Corresponds to UploadStatistics GraphQL type.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadStatistics {

    /**
     * Total number of uploads.
     */
    private Long totalUploads;

    /**
     * Number of successful uploads.
     */
    private Long successfulUploads;

    /**
     * Number of failed uploads.
     */
    private Long failedUploads;

    /**
     * Total size of all uploaded files in bytes.
     */
    private Long totalSizeUploaded;

    /**
     * Average file size in bytes.
     */
    private Long averageFileSize;

    /**
     * Upload counts by processing strategy.
     */
    private List<StrategyCount> uploadsByStrategy;

    /**
     * Upload counts by MIME type.
     */
    private List<MimeTypeCount> uploadsByMimeType;

    /**
     * Upload trend data over time.
     */
    private List<UploadTrendData> uploadTrends;
}
