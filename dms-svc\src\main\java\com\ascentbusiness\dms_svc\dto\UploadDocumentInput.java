package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class UploadDocumentInput {
    private String name;
    private String description;
    private StorageProvider storageProvider;
    private MultipartFile file;
    private List<DocumentAccessRoleInput> accessRoles;
    private List<String> keywords;
    private String userId; // Optional - will be filled from security context if not provided
    private Boolean overrideFile = false; // Default to false

    // Virus scanning configuration
    private VirusScannerType scannerType; // Optional - will use default scanner if not provided

    // Metadata fields (optional)
    private DocumentClassificationMetadataInput classificationMetadata;
    private DocumentOwnershipMetadataInput ownershipMetadata;
    private DocumentComplianceMetadataInput complianceMetadata;

    // getters and setters
}
